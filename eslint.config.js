import parserTs from '@typescript-eslint/parser';
import tseslint from 'typescript-eslint';
import tsPlugin from '@typescript-eslint/eslint-plugin';
import pluginJs from '@eslint/js';
import reactHooks from 'eslint-plugin-react-hooks';
// import prettier from 'eslint-config-prettier'; // Uncomment if you have the flat config version

const ignores = [
  '**/dist/**',
  '**/node_modules/**',
  '**/.turbo/**',
  '**/.next/**',
  '**/build/**',
  '**/coverage/**',
  '.eslintrc.js',
];

// Shared TypeScript rules
const tsRules = {
  '@typescript-eslint/interface-name-prefix': 'off',
  '@typescript-eslint/explicit-function-return-type': 'off',
  '@typescript-eslint/explicit-module-boundary-types': 'off',
  '@typescript-eslint/no-explicit-any': 'off',
  '@typescript-eslint/no-unused-vars': [
    'warn',
    {
      ignoreRestSiblings: true,
      varsIgnorePattern: '^_',
      argsIgnorePattern: '^_',
      destructuredArrayIgnorePattern: '^_',
      caughtErrorsIgnorePattern: '^_?error$',
    },
  ],
};

export default [
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
  // prettier, // Uncomment if you have the flat config version

  // Models package
  {
    files: ['packages/models/src/**/*.ts'],
    languageOptions: {
      parser: parserTs,
      parserOptions: {
        project: './packages/models/tsconfig.json',
        tsconfigRootDir: process.cwd(),
        sourceType: 'module',
      },
    },
    plugins: { '@typescript-eslint': tsPlugin },
    rules: tsRules,
  },
  // API Client package
  {
    files: ['packages/api-client/src/**/*.ts'],
    languageOptions: {
      parser: parserTs,
      parserOptions: {
        project: './packages/api-client/tsconfig.json',
        tsconfigRootDir: process.cwd(),
        sourceType: 'module',
      },
    },
    plugins: { '@typescript-eslint': tsPlugin },
    rules: tsRules,
  },
  // Notification Processor app
  {
    files: ['apps/notification-processor/src/**/*.ts'],
    languageOptions: {
      parser: parserTs,
      parserOptions: {
        project: './apps/notification-processor/tsconfig.json',
        tsconfigRootDir: process.cwd(),
        sourceType: 'module',
      },
    },
    plugins: { '@typescript-eslint': tsPlugin },
    rules: tsRules,
  },
  // API app
  {
    files: ['apps/api/src/**/*.ts'],
    languageOptions: {
      parser: parserTs,
      parserOptions: {
        project: './apps/api/tsconfig.json',
        tsconfigRootDir: process.cwd(),
        sourceType: 'module',
      },
    },
    plugins: { '@typescript-eslint': tsPlugin },
    rules: tsRules,
  },
  // Portal app
  {
    files: ['apps/portal/src/**/*.ts', 'apps/portal/src/**/*.tsx'],
    languageOptions: {
      parser: parserTs,
      parserOptions: {
        project: './apps/portal/tsconfig.json',
        tsconfigRootDir: process.cwd(),
        sourceType: 'module',
      },
    },
    plugins: { '@typescript-eslint': tsPlugin, 'react-hooks': reactHooks },
    rules: {
      ...tsRules,
      'no-console': ['error', { allow: ['error', 'warn', 'info'] }],
      '@typescript-eslint/ban-ts-comment': [
        'error',
        {
          'ts-ignore': true, // allow @ts-ignore
          'ts-expect-error': false,
          'ts-nocheck': true,
          'ts-check': false,
        },
      ],
    },
  },
  // Ignore patterns for all
  { ignores },
];
