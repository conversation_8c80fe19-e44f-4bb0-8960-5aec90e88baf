## TLDR;

[Provide a short few sentences to summarize what you're solving with this change]

## Description

[Provide a brief description of the changes made in this pull request.]

## Related Issue(s)

[Link to the Jira ticket or reference for this issue]()

## Figma Design

[Link to Figma design if applicable]

## Proposed Changes

[List the specific changes made in this PR and/or provide examples of each.]

- [Change 1]
- [Change 2]
- [Change 3]
  ...

## Checklist

- [ ] I have tested these changes locally, and they are functioning as expected.
- [ ] My code follows the project's coding conventions and style guidelines.
- [ ] I have documented any necessary changes in the project's documentation.
- [ ] I have added/updated tests to ensure adequate test coverage.

## Screenshots (if applicable)

[Include screenshots or videos as applicable.]

## Additional Notes

[Add any additional notes, context, or considerations that may be helpful for the reviewers.]

## Gif

[╰( ͡ ° ͜ʖ ͡° )つ ──☆*:・ﾟ Optional - for fun or to lighten the mood]

![alt text](https://media.giphy.com/media/d7fTn7iSd2ivS/giphy.gif)
