// .eslintrc.js
module.exports = {
  extends: 'eslint:recommended',
  parserOptions: {
    ecmaVersion: 2021,
    sourceType: 'module',
  },
  env: {
    browser: true,
    node: true,
    es6: true,
  },
  rules: {
    semi: ['error', 'always'],
    quotes: ['error', 'single'],
  },
  ignorePatterns: [
    '**/dist/**',
    '**/node_modules/**',
    '.turbo/**',
    '.next/**',
    'build/**',
    'coverage/**',
  ],
  overrides: [
    {
      files: ['**/*.ts', '**/*.tsx'],
      excludedFiles: ['**/dist/**', '**/node_modules/**'],
    },
  ],
};
