#!/usr/bin/env python3
"""
ETL script to migrate data from ltp_families MySQL table to Child PostgreSQL table.
"""
import json
import uuid
import pymysql
import psycopg2
from datetime import datetime, timedelta
import re
import logging
import sys
from etl_utils import int_to_uuid, get_id_mapping, create_id_mapping

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.StreamHandler(sys.stdout)  # Output to terminal
    ]
)

# Database connection parameters
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'db': 'my_app_db',
    'charset': 'utf8mb4',
    'port' : 3306
}
POSTGRES_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',  # Use the role you created
    'password': 'root',  # Use the password you set
    'dbname': 'postgres',
    'port': '5432'
}

# ID mapping table (can be in PostgreSQL or a separate file)
ID_MAPPING_TABLE = 'id_mapping'

def connect_mysql():
    """Connect to MySQL database."""
    logging.info("Connecting to MySQL database...")
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        logging.info("Connected to MySQL database")
        return conn
    except Exception as e:
        logging.error(f"Error connecting to MySQL: {e}")
        raise

def connect_postgres():
    """Connect to PostgreSQL database."""
    try:
        conn = psycopg2.connect(**POSTGRES_CONFIG)
        logging.info("Connected to PostgreSQL database")
        return conn
    except Exception as e:
        logging.error(f"Error connecting to PostgreSQL: {e}")
        raise

def extract_children_data(family_record):
    """
    Extract children data from family record.
    Returns a list of child records.
    """
    children = []
    
    # Try to parse JSON data if available
    json_data = {}
    if family_record.get('json_data'):
        try:
            json_data = json.loads(family_record['json_data'])
        except (json.JSONDecodeError, TypeError):
            logging.warning(f"Invalid JSON data for family ID {family_record['id_family']}")
    
    # Extract child count from JSON if available
    child_count = 1  # Default to 1 if not specified
    if json_data and 'intake_form' in json_data:
        intake = json_data['intake_form']
        if 'num_children' in intake and intake['num_children']:
            try:
                child_count = int(intake['num_children'])
            except (ValueError, TypeError):
                logging.warning(f"Invalid child count for family ID {family_record['id_family']}")
    
    # Extract child ages if available
    child_ages = []
    if json_data and 'intake_form' in json_data:
        intake = json_data['intake_form']
        if 'ages_of_children' in intake and intake['ages_of_children']:
            # Parse ages (could be comma-separated, space-separated, etc.)
            ages_str = str(intake['ages_of_children'])
            # Try different patterns to extract ages
            ages = re.findall(r'\d+', ages_str)
            child_ages = [int(age) for age in ages if int(age) < 100]  # Filter out unreasonable ages
    
    # Extract child names if available
    child_names = []
    if json_data and 'intake_form' in json_data:
        intake = json_data['intake_form']
        if 'names_of_children' in intake and intake['names_of_children']:
            names_str = str(intake['names_of_children'])
            # Split by common separators
            for separator in [',', ';', '\n']:
                if separator in names_str:
                    child_names = [name.strip() for name in names_str.split(separator) if name.strip()]
                    break
            
            # If no separators found, use the whole string as one name
            if not child_names:
                child_names = [names_str.strip()]
    
    # Create child records based on available information
    for i in range(max(child_count, len(child_names), len(child_ages))):
        child = {
            'name': f"Child {i+1}",  # Default placeholder name
            'age': child_ages[i] if i < len(child_ages) else None,
            'id': str(uuid.uuid4())  # Generate a UUID for each child
        }
        # Only set name if it's available
        if i < len(child_names):
            child['name'] = child_names[i]
            
        children.append(child)
    
    # If no children were extracted but we know there are children, create placeholder records
    if not children and child_count > 0:
        for i in range(child_count):
            child = {
                'name': f"Child {i+1}",  # Default placeholder name
                'age': None,
                'id': str(uuid.uuid4())
            }
            children.append(child)
    
    return children

def transform_family_to_children(family_record, pg_conn, mysql_conn):
    """
    Transform a family record into one or more child records.
    Returns a list of child records ready for insertion.
    """
    children_data = extract_children_data(family_record)
    transformed_children = []
    
    # Get JSON data from the family record
    family_json_data = {}
    if family_record.get('json_data'):
        try:
            family_json_data = json.loads(family_record['json_data'])
        except (json.JSONDecodeError, TypeError):
            logging.warning(f"Invalid JSON data for family ID {family_record['id_family']}")
    
    # Get mom_id from mapping table or create it
    mom_id = None
    if family_record.get('id_people_primary'):
        mom_id = get_or_create_mom_id(pg_conn, mysql_conn, family_record['id_people_primary'])
        
    # If still no mom_id, create a placeholder Mom record
    if not mom_id:
        logging.info(f"No mom_id found for family {family_record.get('id_family')}, creating placeholder")
        mom_id = create_placeholder_mom(pg_conn, family_record)
        if not mom_id:
            logging.warning(f"Skipping children for family ID {family_record.get('id_family')} - cannot create mom record")
            return []
        logging.info(f"Created placeholder mom_id {mom_id} for family {family_record.get('id_family')}")
    
    # Get created_by_id from id_add field
    created_by_id = None
    if family_record.get('id_add'):
        # Get or create user ID mapping
        created_by_id = get_or_create_user_id(pg_conn, mysql_conn, family_record['id_add'])
        logging.info(f"Using created_by_id {created_by_id} for family {family_record.get('id_family')}")
    
    # Get creator name
    created_by_name = None
    if created_by_id:
        created_by_name = get_person_name(mysql_conn, family_record.get('id_add'))
    
    # Process each child
    for child_data in children_data:
        # Calculate approximate birthdate from age if available
        birthdate = None
        if child_data.get('age') is not None:
            # Approximate birthdate by subtracting age from current date
            today = datetime.now()
            birthdate = datetime(today.year - child_data['age'], today.month, today.day)
        
        # Determine living situation based on family structure
        lives_with = 'mother'
        if family_record.get('family_structure') == 'single_mom':
            lives_with = 'mother'
        elif family_record.get('family_structure') == 'foster_care':
            lives_with = 'foster_care'
        elif family_record.get('family_structure') == 'grandparents':
            lives_with = 'grandparents'
        else:
            lives_with = 'other_family'
        
        # Determine custody status
        custody_status = 'other'
        if family_json_data and 'intake_form' in family_json_data and 'custody' in family_json_data['intake_form']:
            if family_json_data['intake_form']['custody'] == 'in_foster_care':
                custody_status = 'temporary_custody'
            elif family_json_data['intake_form']['custody'] == 'no_custody':
                custody_status = 'no_custody'
        
        # Determine father involvement
        father_involved = ['{unknown}']
        father_involvement = 'Unknown father involvement.'
        
        if family_json_data and 'intake_form' in family_json_data:
            if 'father_involved' in family_json_data['intake_form']:
                father_status = family_json_data['intake_form']['father_involved']
                if father_status == 'Yes':
                    father_involved = ['{physical_presence,decision_making,financial_support}']
                    father_involvement = 'Father is actively involved in child\'s life.'
                elif father_status == 'No':
                    father_involved = ['{not_involved}']
                    father_involvement = 'Father is not involved in child\'s life.'
        
        # Create transformed child record
        # Note: deleted_at is a bigint, not a timestamp or NULL
        deleted_at = 0  # Default value for active records
        if family_record.get('state', 0) <= 0:
            # For deleted records, use current timestamp in milliseconds
            deleted_at = int(datetime.now().timestamp() * 1000)
        
        # Ensure first_name has a value (NOT NULL constraint)
        first_name = child_data.get('name')
        if not first_name:
            # Generate a random name if none is available
            first_name = f"Child_{uuid.uuid4().hex[:8]}"
        
        # Double-check that mom_id is not None
        if not mom_id:
            logging.warning(f"Skipping child - no mom_id available for family {family_record.get('id_family')}")
            continue
        
        transformed_child = {
            'id': child_data.get('id', str(uuid.uuid4())),
            'deleted_at': deleted_at,  # Use bigint timestamp
            'created_at': datetime.fromtimestamp(family_record['date_add']) if family_record.get('date_add') else datetime.now(),
            'updated_at': datetime.fromtimestamp(family_record['date_mod']) if family_record.get('date_mod') else datetime.now(),
            'created_by_id': created_by_id,  # Now properly mapped
            'created_by_name': created_by_name,  # Include creator name
            'first_name': first_name,  # Ensure NOT NULL
            'birthdate': birthdate,
            'gender': 'unknown',  # Default value, no direct source
            'lives_with': lives_with,
            'legal_custody_status': custody_status,
            'father_involved': father_involved,
            'father_involvement': father_involvement,
            'additional_info': '',
            'mom_id': mom_id,  # Now guaranteed to have a value
            'active_child_welfare_involvement': None,
            'date_of_child_welfare_involvement': None,
            'family_preservation_goal': None,
            'family_preservation_impact': None
        }
        
        transformed_children.append(transformed_child)
    
    return transformed_children

def load_children(pg_conn, children):
    """Load transformed children into PostgreSQL."""
    if not children:
        return
    
    cursor = pg_conn.cursor()
    
    # Get column names from Child table
    try:
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'Child' AND table_schema = 'public'
        """)
        db_columns = [row[0] for row in cursor.fetchall()]
        logging.info(f"Child table has {len(db_columns)} columns")
    except Exception as e:
        logging.error(f"Error getting Child table columns: {e}")
        cursor.close()
        return
    
    for child in children:
        try:
            # Filter child dict to only include columns that exist in the database
            child_data = {k: v for k, v in child.items() if k in db_columns}
            
            # Handle special data types
            if 'father_involved' in child_data and isinstance(child_data['father_involved'], list):
                # Convert list to PostgreSQL array format
                child_data['father_involved'] = '{' + ','.join(str(item).strip('{}') for item in child_data['father_involved']) + '}'
            
            # Ensure deleted_at is a bigint
            if 'deleted_at' in child_data:
                if child_data['deleted_at'] is None:
                    child_data['deleted_at'] = 0
                elif isinstance(child_data['deleted_at'], datetime):
                    child_data['deleted_at'] = int(child_data['deleted_at'].timestamp() * 1000)
            
            # Build SQL query
            columns = ', '.join([f'"{col}"' for col in child_data.keys()])
            placeholders = ', '.join(['%s'] * len(child_data))
            values = list(child_data.values())
            
            # Insert child record
            cursor.execute(f"""
                INSERT INTO public."Child" ({columns})
                VALUES ({placeholders})
                ON CONFLICT (id) DO UPDATE
                SET updated_at = EXCLUDED.updated_at
                RETURNING id
            """, values)
            
            # Get inserted ID
            result = cursor.fetchone()
            if result:
                child_id = result[0]
                logging.info(f"Inserted child with ID {child_id}")
                
                # Create mapping if family_id is available
                if 'family_id' in child:
                    create_id_mapping(pg_conn, str(child['family_id']), child_id, 'Child')
            
            pg_conn.commit()
            
        except Exception as e:
            logging.error(f"Error inserting child: {e}")
            pg_conn.rollback()
    
    cursor.close()

def ensure_id_mapping_table(pg_conn):
    """Ensure the ID mapping table exists."""
    cursor = pg_conn.cursor()
    try:
        cursor.execute(f"""
            CREATE TABLE IF NOT EXISTS {ID_MAPPING_TABLE} (
                old_id VARCHAR(255) NOT NULL,
                new_id UUID NOT NULL,
                entity_type VARCHAR(50) NOT NULL,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (old_id, entity_type)
            )
        """)
        pg_conn.commit()
        cursor.close()
        logging.info("ID mapping table exists or was created")
        return True
    except Exception as e:
        logging.error(f"Error ensuring ID mapping table: {e}")
        pg_conn.rollback()
        cursor.close()
        return False

def create_mom_mappings(mysql_conn, pg_conn):
    """Create mappings from MySQL ltp_people to PostgreSQL Mom table."""
    mysql_cursor = mysql_conn.cursor(pymysql.cursors.DictCursor)
    pg_cursor = pg_conn.cursor()
    
    try:
        # Get people who are primary contacts for families
        mysql_cursor.execute("""
            SELECT DISTINCT p.id_people, p.name_first, p.name_last
            FROM ltp_people p
            JOIN ltp_families f ON p.id_people = f.id_people_primary
            WHERE p.id_people IS NOT NULL
        """)
        
        people = mysql_cursor.fetchall()
        logging.info(f"Found {len(people)} people to map as Moms")
        
        # Check if Mom table exists
        pg_cursor.execute("SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Mom'")
        if not pg_cursor.fetchone():
            logging.error("Mom table does not exist in PostgreSQL")
            return
        
        # Get valid MomStatus values from enum
        try:
            pg_cursor.execute("""
                SELECT enum_range(NULL::public."MomStatus")
            """)
            status_values = pg_cursor.fetchone()[0]
            default_status = 'active'
            if status_values and default_status not in status_values:
                default_status = status_values[0]
        except Exception as e:
            logging.warning(f"Error getting MomStatus enum values: {e}")
            default_status = 'active'  # Default fallback
        
        # For each person, create a Mom record if it doesn't exist
        for person in people:
            old_id = str(person['id_people'])
            
            # Check if mapping already exists
            pg_cursor.execute(
                f"SELECT new_id FROM {ID_MAPPING_TABLE} WHERE old_id = %s AND entity_type = %s",
                (old_id, 'Mom')
            )
            mapping = pg_cursor.fetchone()
            
            if mapping:
                logging.info(f"Mapping already exists for person {old_id}")
                continue
            
            # Generate deterministic UUID for Mom based on person ID
            new_id = int_to_uuid(person['id_people'])
            
            # Check if Mom record with this ID already exists
            pg_cursor.execute("""
                SELECT 1 FROM public."Mom" WHERE id = %s
            """, (new_id,))
            
            if pg_cursor.fetchone():
                logging.info(f"Mom record already exists with ID {new_id}")
                # Create mapping if it doesn't exist
                create_id_mapping(pg_conn, old_id, new_id, 'Mom')
                continue
            
            # Create Mom record if it doesn't exist
            try:
                # Insert basic Mom record
                pg_cursor.execute("""
                    INSERT INTO public."Mom" (
                        id, first_name, last_name, created_at, updated_at, 
                        number_of_children_c, children_in_home, status
                    )
                    VALUES (%s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0, 0, %s)
                    ON CONFLICT (id) DO NOTHING
                """, (
                    new_id, 
                    person.get('name_first', 'Unknown'), 
                    person.get('name_last', 'Unknown'),
                    default_status
                ))
                
                # Create mapping
                create_id_mapping(pg_conn, old_id, new_id, 'Mom')
                
                logging.info(f"Created mapping for person {old_id} to Mom {new_id}")
            except Exception as e:
                pg_conn.rollback()
                logging.error(f"Error creating Mom record for person {old_id}: {e}")
                continue
        
        pg_conn.commit()
        logging.info("Mom mappings created successfully")
    except Exception as e:
        pg_conn.rollback()
        logging.error(f"Error creating Mom mappings: {e}")
        raise
    finally:
        mysql_cursor.close()
        pg_cursor.close()

def create_placeholder_mom(pg_conn, family_record):
    """Create a placeholder Mom record for orphaned children."""
    try:
        cursor = pg_conn.cursor()
        
        # Generate a new UUID for the Mom
        mom_id = str(uuid.uuid4())
        family_id = family_record.get('id_family', 'unknown')
        
        # Get valid MomStatus values from enum
        try:
            cursor.execute("""
                SELECT enum_range(NULL::public."MomStatus")
            """)
            status_values = cursor.fetchone()[0]
            default_status = 'active'
            if status_values and default_status not in status_values:
                default_status = status_values[0]
        except Exception as e:
            logging.warning(f"Error getting MomStatus enum values: {e}")
            default_status = 'active'  # Default fallback
        
        # Get created_by_id from id_add field
        created_by_id = None
        if family_record.get('id_add'):
            # First try to get from mapping table
            created_by_id = get_id_mapping(pg_conn, str(family_record['id_add']), 'User')
            # If not found, generate deterministic UUID
            if not created_by_id:
                created_by_id = int_to_uuid(family_record['id_add'])
                logging.info(f"Generated created_by_id {created_by_id} for user {family_record['id_add']}")
        
        logging.info(f"Creating placeholder Mom with ID {mom_id} for family {family_id}")
        
        # Insert basic Mom record
        cursor.execute("""
            INSERT INTO public."Mom" (
                id, first_name, last_name, created_at, updated_at, 
                number_of_children_c, children_in_home, status
            )
            VALUES (%s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0, 0, %s)
            ON CONFLICT (id) DO NOTHING
            RETURNING id
        """, (
            mom_id, 
            f"Placeholder_Mom_{family_id}", 
            f"Family_{family_id}",
            default_status
        ))
        
        result = cursor.fetchone()
        pg_conn.commit()
        
        if result:
            logging.info(f"Created placeholder Mom record with ID {mom_id} for family {family_id}")
            return mom_id
        else:
            logging.warning(f"Failed to create placeholder Mom record for family {family_id}")
            return None
            
    except Exception as e:
        pg_conn.rollback()
        logging.error(f"Error creating placeholder Mom record: {e}")
        return None

def check_mom_table(pg_conn):
    """Check if the Mom table exists and has the expected structure."""
    cursor = pg_conn.cursor()
    try:
        # Check if Mom table exists
        cursor.execute("SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Mom'")
        if not cursor.fetchone():
            logging.error("Mom table does not exist in the database")
            return False
        
        # Check required columns
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'Mom'
        """)
        columns = [row[0] for row in cursor.fetchall()]
        required_columns = ['id', 'first_name', 'last_name', 'status', 'number_of_children_c', 'children_in_home']
        
        for col in required_columns:
            if col not in columns:
                logging.error(f"Required column '{col}' missing from Mom table")
                return False
        
        logging.info(f"Mom table exists with all required columns: {', '.join(required_columns)}")
        return True
    except Exception as e:
        logging.error(f"Error checking Mom table: {e}")
        return False
    finally:
        cursor.close()

def get_person_name(mysql_conn, person_id):
    """Get person's name from MySQL database."""
    if not person_id:
        return None
    
    cursor = mysql_conn.cursor(pymysql.cursors.DictCursor)
    try:
        cursor.execute(
            "SELECT name_first, name_last FROM ltp_people WHERE id_people = %s",
            (person_id,)
        )
        person = cursor.fetchone()
        cursor.close()
        
        if person:
            first_name = person.get('name_first', '')
            last_name = person.get('name_last', '')
            if first_name or last_name:
                return f"{first_name} {last_name}".strip()
        
        return None
    except Exception as e:
        logging.error(f"Error getting person name for ID {person_id}: {e}")
        cursor.close()
        return None

def get_or_create_mom_id(pg_conn, mysql_conn, person_id):
    """
    Get Mom ID from mapping table or create it if not found.
    
    Args:
        pg_conn: PostgreSQL connection
        mysql_conn: MySQL connection
        person_id: Person ID from ltp_people
        
    Returns:
        UUID string or None if person not found
    """
    if not person_id:
        return None
    
    # First try to get from mapping table
    mom_id = get_id_mapping(pg_conn, str(person_id), 'Mom')
    if mom_id:
        logging.info(f"Found mom_id {mom_id} in mapping table for person {person_id}")
        return mom_id
    
    # If not in mapping table, look up in ltp_people
    mysql_cursor = mysql_conn.cursor(pymysql.cursors.DictCursor)
    try:
        mysql_cursor.execute(
            "SELECT name_first, name_last FROM ltp_people WHERE id_people = %s",
            (person_id,)
        )
        person = mysql_cursor.fetchone()
        mysql_cursor.close()
        
        if not person:
            logging.warning(f"Person {person_id} not found in ltp_people")
            return None
        
        # Generate deterministic UUID
        mom_id = int_to_uuid(person_id)
        
        # Get valid MomStatus values from enum
        pg_cursor = pg_conn.cursor()
        try:
            pg_cursor.execute("SELECT enum_range(NULL::public.\"MomStatus\")")
            status_values = pg_cursor.fetchone()[0]
            default_status = 'active'
            if status_values and default_status not in status_values:
                default_status = status_values[0]
        except Exception as e:
            logging.warning(f"Error getting MomStatus enum values: {e}")
            default_status = 'active'  # Default fallback
        
        # Check if Mom record already exists
        pg_cursor.execute("SELECT 1 FROM public.\"Mom\" WHERE id = %s", (mom_id,))
        if not pg_cursor.fetchone():
            # Create Mom record
            pg_cursor.execute("""
                INSERT INTO public."Mom" (
                    id, first_name, last_name, created_at, updated_at, 
                    number_of_children_c, children_in_home, status
                )
                VALUES (%s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0, 0, %s)
                ON CONFLICT (id) DO NOTHING
            """, (
                mom_id, 
                person.get('name_first', 'Unknown'), 
                person.get('name_last', 'Unknown'),
                default_status
            ))
        
        # Create mapping
        create_id_mapping(pg_conn, str(person_id), mom_id, 'Mom')
        pg_conn.commit()
        
        logging.info(f"Created mom_id {mom_id} for person {person_id}")
        return mom_id
        
    except Exception as e:
        logging.error(f"Error creating mom_id for person {person_id}: {e}")
        pg_conn.rollback()
        return None

def get_or_create_user_id(pg_conn, mysql_conn, person_id):
    """
    Get User ID from mapping table or create it if not found.
    
    Args:
        pg_conn: PostgreSQL connection
        mysql_conn: MySQL connection
        person_id: Person ID from ltp_people
        
    Returns:
        UUID string or None if person not found
    """
    if not person_id:
        return None
    
    # First try to get from mapping table
    user_id = get_id_mapping(pg_conn, str(person_id), 'User')
    if user_id:
        logging.info(f"Found user_id {user_id} in mapping table for person {person_id}")
        return user_id
    
    # If not in mapping table, look up in ltp_people
    mysql_cursor = mysql_conn.cursor(pymysql.cursors.DictCursor)
    try:
        mysql_cursor.execute(
            "SELECT name_first, name_last, username, people_email_primary FROM ltp_people WHERE id_people = %s",
            (person_id,)
        )
        person = mysql_cursor.fetchone()
        mysql_cursor.close()
        
        if not person:
            logging.warning(f"Person {person_id} not found in ltp_people")
            # Generate deterministic UUID even if person not found
            user_id = int_to_uuid(person_id)
            create_id_mapping(pg_conn, str(person_id), user_id, 'User')
            logging.info(f"Created mapping for unknown person {person_id} to User {user_id}")
            return user_id
        
        # Generate deterministic UUID
        user_id = int_to_uuid(person_id)
        
        # Check if User table exists and if we should create a User record
        pg_cursor = pg_conn.cursor()
        pg_cursor.execute("SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'User'")
        
        if pg_cursor.fetchone():
            # Check if User record already exists
            pg_cursor.execute("SELECT 1 FROM public.\"User\" WHERE id = %s", (user_id,))
            
            if not pg_cursor.fetchone():
                # Only create User record if we have an email and username
                if person.get('people_email_primary') and person.get('username'):
                    try:
                        # Create User record with minimal required fields
                        pg_cursor.execute("""
                            INSERT INTO public."User" (
                                id, email, username, "passwordHash", created_at, updated_at, 
                                "firstName", "lastName", status
                            )
                            VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, %s, %s, %s)
                            ON CONFLICT (id) DO NOTHING
                        """, (
                            user_id, 
                            person.get('people_email_primary', f"user_{person_id}@example.com"),
                            person.get('username', f"user_{person_id}"),
                            'placeholder_hash',  # Placeholder password hash
                            person.get('name_first'),
                            person.get('name_last'),
                            'Inactive'  # Default status
                        ))
                        logging.info(f"Created User record for person {person_id}")
                    except Exception as e:
                        logging.error(f"Error creating User record: {e}")
                        # Continue even if User creation fails
        
        # Create mapping regardless of whether User record was created
        create_id_mapping(pg_conn, str(person_id), user_id, 'User')
        pg_conn.commit()
        
        logging.info(f"Created mapping for person {person_id} to User {user_id}")
        return user_id
        
    except Exception as e:
        logging.error(f"Error creating user_id for person {person_id}: {e}")
        pg_conn.rollback()
        # Generate deterministic UUID as fallback
        user_id = int_to_uuid(person_id)
        return user_id

def main():
    """Main ETL process."""
    mysql_conn = None
    pg_conn = None
    
    try:
        # Connect to databases
        mysql_conn = connect_mysql()
        pg_conn = connect_postgres()
        
        # Ensure ID mapping table exists
        ensure_id_mapping_table(pg_conn)
        
        # Check Mom table
        if not check_mom_table(pg_conn):
            logging.error("Cannot proceed without valid Mom table")
            return
        
        # First, check the structure of ltp_people_params table
        mysql_cursor = mysql_conn.cursor(pymysql.cursors.DictCursor)
        try:
            mysql_cursor.execute("DESCRIBE ltp_people_params")
            columns = [row['Field'] for row in mysql_cursor.fetchall()]
            logging.info(f"ltp_people_params columns: {columns}")
            
            # Check if json_data column exists
            json_data_column = 'param_value'  # Default to param_value if json_data doesn't exist
            if 'json_data' in columns:
                json_data_column = 'json_data'
            elif 'param_value' in columns:
                logging.info("Using param_value column instead of json_data")
            else:
                logging.warning("Neither json_data nor param_value column found in ltp_people_params")
        except Exception as e:
            logging.error(f"Error checking ltp_people_params structure: {e}")
            json_data_column = 'param_value'  # Default fallback
        
        # Extract families from MySQL with the correct column name
        mysql_cursor.execute(f"""
            SELECT f.*, p.{json_data_column} AS json_data
            FROM ltp_families f
            LEFT JOIN ltp_people_params p ON p.id_people = f.id_people_primary AND p.param_type = 'intake_form'
            WHERE f.id_people_primary IS NOT NULL
        """)
        
        families = mysql_cursor.fetchall()
        logging.info(f"Extracted {len(families)} families from MySQL")
        
        if not families:
            logging.warning("No families found to process")
            return
            
        # Process each family
        total_children = 0
        processed_families = 0
        skipped_families = 0
        
        for family in families:
            try:
                # Get creator name if available
                creator_name = None
                if family.get('id_add'):
                    creator_name = get_person_name(mysql_conn, family['id_add'])
                
                # Transform family to children
                children = transform_family_to_children(family, pg_conn, mysql_conn)
                
                # Set creator name for each child
                for child in children:
                    child['created_by_name'] = creator_name
                
                if not children:
                    logging.warning(f"No children extracted for family ID {family['id_family']}")
                    skipped_families += 1
                    continue
                    
                total_children += len(children)
                
                # Load children into PostgreSQL
                load_children(pg_conn, children)
                processed_families += 1
                
                # Log progress every 100 families
                if processed_families % 100 == 0:
                    logging.info(f"Processed {processed_families} families so far")
                    
            except Exception as e:
                logging.error(f"Error processing family ID {family.get('id_family', 'unknown')}: {e}")
                skipped_families += 1
        
        logging.info(f"Migration complete. Created {total_children} children from {processed_families} families.")
        logging.info(f"Skipped {skipped_families} families due to errors.")
        
    except Exception as e:
        logging.error(f"Migration failed: {e}")
        raise
    finally:
        # Close connections
        if mysql_conn:
            mysql_conn.close()
            logging.info("MySQL connection closed")
        if pg_conn:
            pg_conn.close()
            logging.info("PostgreSQL connection closed")

if __name__ == "__main__":
    main()
