#!/usr/bin/env python3
import pandas as pd
import pymysql
import psycopg2
from sqlalchemy import create_engine
import logging
import uuid


def int_to_uuid(int_id):
    # Use a fixed namespace for deterministic UUID generation
    namespace = uuid.NAMESPACE_DNS
    # Convert integer to string for uuid5
    return str(uuid.uuid5(namespace, str(int_id)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection parameters
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'db': 'my_app_db',
    'charset': 'utf8mb4',
    'port' : 3306
}

POSTGRES_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',  # Use the role you created
    'password': 'root',  # Use the password you set
    'dbname': 'postgres',
    'port': '5432'
}

# Test with a single table mapping
TABLE_MAPPING = {
    'ltp_people': 'User'
}

def connect_mysql():
    """Connect to MySQL database"""
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        logger.info("Connected to MySQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to MySQL: {e}")
        raise

def connect_postgres():
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(**POSTGRES_CONFIG)
        logger.info("Connected to PostgreSQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to PostgreSQL: {e}")
        raise


def extract_mysql_table(conn, table_name):
    """Extract data from MySQL table"""
    try:
        # Create SQLAlchemy engine for MySQL
        engine = create_engine(
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['db']}?charset={MYSQL_CONFIG['charset']}"
        )
        # For testing, limit to 30 rows
        query = f"SELECT * FROM {table_name}"
        df = pd.read_sql(query, engine)
        logger.info(f"Extracted {len(df)} rows from MySQL table {table_name}")
        
        # For ltp_people table, make sure we have the right columns
        if table_name == 'ltp_people':
            needed_columns = [
                'id_people', 'name_first', 'name_last', 'people_email_primary',
                'people_phone_mobile', 'people_phone_home', 'people_phone_office',
                'address_street_1', 'address_city', 'address_state', 'address_zip',
                'time_zone', 'home_church', 'username', 'password', 'date_add',
                'date_mod', 'date_birth', 'people_email_secondary', 'contact_emergency_phone',
                'url_avatar', 'people_geocode', 'date_removed', 'people_languages',
                'people_languages_other', 'pass_reset', 'state'
            ]
            for col in needed_columns:
                if col not in df.columns:
                    df[col] = None
        
        return df
    except Exception as e:
        logger.error(f"Error extracting data from {table_name}: {e}")
        raise


def transform_data(df, source_table, target_table):
    """Transform data from MySQL schema to PostgreSQL schema"""
    logger.info(f"Transforming data from {source_table} to {target_table}")
    
    # Get PostgreSQL table schema to understand column names and types
    postgres_conn = connect_postgres()
    cursor = postgres_conn.cursor()
    
    # Get column information
    cursor.execute("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = %s
        ORDER BY ordinal_position
    """, (target_table,))
    
    # Create a new DataFrame for transformed data
    new_df = pd.DataFrame()
    
    # Get column names from PostgreSQL
    pg_columns = [row[0] for row in cursor.fetchall()]
    logger.info(f"Target PostgreSQL columns: {pg_columns}")
    
    # Valid language enum values from public."Language"
    valid_languages = [
        'english', 'spanish', 'chinese_mandarin', 'vietnamese', 'tagalog',
        'arabic', 'korean', 'russian', 'french', 'hindi', 'portuguese',
        'bengali', 'urdu', 'german', 'haitian_creole', 'polish', 'italian',
        'japanese', 'persian_farsi', 'gujarati', 'other'
    ]
    
    # Example mapping for ltp_people to User
    if source_table == 'ltp_people' and target_table == 'User':
        # Map columns based on analysis
        column_mapping = {
            'id_people': 'id',
            'date_add': 'created_at',
            'date_mod': 'updated_at',
            'people_email_primary': 'email',
            'password': 'passwordHash',
            'username': 'username',
            'name_first': 'firstName',
            'name_last': 'lastName',
            'date_birth': 'date_of_birth',
            'home_church': 'home_church',
            'people_email_secondary': 'secondary_email',
            'people_phone_home': 'phone_home',
            'people_phone_mobile': 'phone_mobile',
            'contact_emergency_phone': 'phone_other',
            'people_phone_office': 'phone_work',
            'time_zone': 'timezone',
            'url_avatar': 'profilePicExternalUrl',
            'address_city': 'address_city',
            'people_geocode': 'address_postalcode',
            'address_state': 'address_state',
            'address_street_1': 'address_street',
            'date_removed': 'deleted_at',
            'people_languages': 'language_preference_c',
            'people_languages_other': 'languages_c',
            'pass_reset': 'mustChangePassword',
        }
        
        # Add mapped columns
        for mysql_col, pg_col in column_mapping.items():
            if mysql_col in df.columns and pg_col in pg_columns:
                # Handle special conversions
                if mysql_col == 'id_people':
                    # Convert int(11) to UUID
                    new_df[pg_col] = df[mysql_col].apply(lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else str(uuid.uuid4()))
                elif mysql_col in ['date_add', 'date_mod', 'date_birth']:
                    # Convert int(11) Unix timestamp to timestamp(3) without time zone
                    new_df[pg_col] = pd.to_datetime(df[mysql_col], unit='s', errors='coerce')
                elif mysql_col == 'date_removed':
                    # Convert int(11) Unix timestamp to timestamp(3), default to 1970-01-01 for NULL
                    new_df[pg_col] = pd.to_datetime(df[mysql_col], unit='s', errors='coerce').fillna(pd.Timestamp('1970-01-01'))
                elif mysql_col == 'pass_reset':
                    # Convert varchar(200) to boolean (True if non-empty, False otherwise)
                    new_df[pg_col] = df[mysql_col].apply(lambda x: bool(x) if pd.notnull(x) else False)
                elif mysql_col == 'people_languages':
                    # Convert varchar(75) to public."Language" enum (first valid language or None)
                    new_df[pg_col] = df[mysql_col].apply(
                        lambda x: next((lang.strip().lower() for lang in x.split(',') if lang.strip().lower() in valid_languages), None)
                        if isinstance(x, str) and x else None
                    )
                elif mysql_col == 'people_languages_other':
                    # Convert varchar(75) to public."Language"[] (array of valid languages)
                    new_df[pg_col] = df[mysql_col].apply(
                        lambda x: [lang.strip().lower() for lang in x.split(',') if lang.strip().lower() in valid_languages]
                        if isinstance(x, str) and x else []
                    )
                elif mysql_col == 'contact_emergency_phone':
                    # Convert int(12) to text (format as string)
                    new_df[pg_col] = df[mysql_col].apply(lambda x: str(x) if pd.notnull(x) else None)
                elif mysql_col == 'username':
                    # Map username, allow None for now (handled below)
                    new_df[pg_col] = df[mysql_col].apply(
                        lambda x: x if pd.notnull(x) and isinstance(x, str) and x.strip() else None
                    )
                elif mysql_col == 'password':
                    # Map password, ensure non-NULL
                    new_df[pg_col] = df[mysql_col].apply(
                        lambda x: x if pd.notnull(x) and isinstance(x, str) and x.strip() else 'temporary_hash_placeholder'
                    )
                elif mysql_col == 'people_email_primary':
                    # Map email, allow None for now (handled below)
                    new_df[pg_col] = df[mysql_col].apply(
                        lambda x: x if pd.notnull(x) and isinstance(x, str) and x.strip() else None
                    )
                else:
                    # Direct mapping for varchar/text columns
                    new_df[pg_col] = df[mysql_col]
        
        # Ensure NOT NULL columns have valid values
        if 'id' in pg_columns:
            new_df['id'] = new_df['id'].fillna(pd.Series([str(uuid.uuid4()) for _ in range(len(new_df))], index=new_df.index))
        
        if 'email' in pg_columns:
            # Create a mask for invalid emails (NULL or empty)
            invalid_email_mask = new_df['email'].isna() | (new_df['email'] == '')
            # Generate dummy emails using the index as a unique identifier
            new_df.loc[invalid_email_mask, 'email'] = [f"user_{i}@example.com" for i in new_df.index[invalid_email_mask]]
            # Log rows with generated emails
            generated_emails = new_df[invalid_email_mask][['email']]
            if not generated_emails.empty:
                logger.warning(f"Generated emails for {len(generated_emails)} rows: {generated_emails.to_dict('records')}")
        
        if 'username' in pg_columns:
            # Create a mask for invalid usernames (NULL or empty)
            invalid_username_mask = new_df['username'].isna() | (new_df['username'] == '')
            # Generate usernames from email where possible
            new_df.loc[invalid_username_mask, 'username'] = new_df.loc[invalid_username_mask, 'email'].apply(
                lambda x: x.split('@')[0] if pd.notnull(x) and isinstance(x, str) and '@' in x else f"user_{pd.util.hash_pandas_object(pd.Series([x])).iloc[0]}"
            )
            # Log rows with generated usernames
            generated_usernames = new_df[invalid_username_mask][['email', 'username']]
            if not generated_usernames.empty:
                logger.warning(f"Generated usernames for {len(generated_usernames)} rows: {generated_usernames.to_dict('records')}")
        
        if 'passwordHash' in pg_columns:
            new_df['passwordHash'] = new_df['passwordHash'].fillna('temporary_hash_placeholder')
        
        if 'created_at' in pg_columns:
            new_df['created_at'] = new_df['created_at'].fillna(pd.Timestamp.now())
        
        if 'updated_at' in pg_columns:
            new_df['updated_at'] = new_df['updated_at'].fillna(pd.Timestamp.now())
        
        if 'deleted_at' in pg_columns:
            new_df['deleted_at'] = new_df['deleted_at'].fillna(pd.Timestamp('1970-01-01'))
            # Log rows with default deleted_at
            default_deleted_at = new_df[new_df['deleted_at'] == pd.Timestamp('1970-01-01')][['email', 'deleted_at']]
            if not default_deleted_at.empty:
                logger.warning(f"Set default deleted_at for {len(default_deleted_at)} rows: {default_deleted_at.to_dict('records')}")
        
        if 'mustChangePassword' in pg_columns:
            new_df['mustChangePassword'] = new_df['mustChangePassword'].fillna(False)
        
        # Add required columns with default values
        if 'status' in pg_columns and 'status' not in new_df.columns:
            # Map MySQL state (tinyint(4)) to public."UserStatusType" enum
            if 'state' in df.columns:
                new_df['status'] = df['state'].apply(
                    lambda x: 'Active' if pd.notnull(x) and x == 1 else 'Inactive'
                )
            else:
                new_df['status'] = 'Inactive'
        
        # Handle all other User table columns with defaults
        for col in pg_columns:
            if col not in new_df.columns:
                if col in ['refreshTokenHash', 'affiliateId', 'created_by_id', 'created_by_name', 
                          'phone', 'secondary_phone', 'profilePicMimeType', 'description', 
                          'availability', 'language_notes_c', 'firstName', 'lastName', 
                          'home_church', 'secondary_email', 'phone_home', 'phone_mobile', 
                          'phone_other', 'phone_work', 'timezone', 'profilePicExternalUrl', 
                          'address_city', 'address_postalcode', 'address_state', 'address_street']:
                    new_df[col] = None
                elif col == 'sms_message_opt_in':
                    new_df[col] = False
                elif col == 'advocate_capacity_for_moms':
                    new_df[col] = 0
                elif col == 'communication_preference':
                    new_df[col] = 'email'  # Default to a valid enum value
                elif col == 'advocate_status':
                    new_df[col] = 'Inactive'  # Default to a valid enum value
                elif col == 'language_preference_c':
                    new_df[col] = 'english'  # Default to a valid enum value
                elif col == 'languages_c':
                    new_df[col] = []  # Default to empty array
                elif col == 'date_of_birth':
                    new_df[col] = None
    
    logger.info(f"Transformed DataFrame has {len(new_df.columns)} columns: {new_df.columns.tolist()}")
    return new_df


def load_to_postgres(df, target_table):
    """Load transformed data to PostgreSQL"""
    try:
        # Create SQLAlchemy engine for pandas to_sql
        engine = create_engine(
            f'postgresql://{POSTGRES_CONFIG["user"]}:{POSTGRES_CONFIG["password"]}@{POSTGRES_CONFIG["host"]}:{POSTGRES_CONFIG["port"]}/{POSTGRES_CONFIG["dbname"]}'
        )
        
        # Log the DataFrame before loading
        logger.info(f"DataFrame to load has {len(df)} rows and columns: {df.columns.tolist()}")
        
        # Check for required columns
        required_columns = [
            'id',
            'created_at',
            'updated_at',
            'email',
            'passwordHash',
            'username',
            'firstName',
            'lastName',
            'date_of_birth',
            'home_church',
            'secondary_email',
            'phone_home',
            'phone_mobile',
            'phone_other',
            'phone_work',
            'timezone',
            'profilePicExternalUrl',
            'address_city',
            'address_postalcode',
            'address_state',
            'address_street',
            'deleted_at',
            'language_preference_c',
            'languages_c',
            'mustChangePassword'
        ]
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Load data to PostgreSQL
        schema = 'public'
        
        # Use if_exists='append' to add to existing table
        df.to_sql(target_table, engine, schema=schema, if_exists='append', index=False)
        
        logger.info(f"Loaded {len(df)} rows to PostgreSQL table {schema}.{target_table}")
        return True
    except Exception as e:
        logger.error(f"Error loading data to {target_table}: {e}")
        raise

def main():
    """Test ETL for a single table"""
    mysql_conn = None
    
    try:
        # Connect to MySQL
        mysql_conn = connect_mysql()
        
        # Test with ltp_people table
        source_table = 'ltp_people'
        target_table = TABLE_MAPPING[source_table]
        
        # Extract
        df = extract_mysql_table(mysql_conn, source_table)
        
        # Transform
        df_transformed = transform_data(df, source_table, target_table)
        
        # Print sample of transformed data
        logger.info(f"Transformed data sample:\n{df_transformed.head()}")
        
        # Load data to PostgreSQL
        load_result = load_to_postgres(df_transformed, target_table)
        
        if load_result:
            logger.info("ETL process completed successfully")
        
    except Exception as e:
        logger.error(f"ETL test failed: {e}")
    finally:
        if mysql_conn:
            mysql_conn.close()

if __name__ == "__main__":
    main()
