#!/usr/bin/env python3
"""
ETL script to migrate s (MySQL) → Event (Postgres),
using PyMySQL instead of the mysql-connector module.
"""
import logging
import pymysql
import psycopg2
from psycopg2.extras import execute_values
from datetime import datetime
import json

from etl_scripts.etl_utils import (
    int_to_uuid,
    ensure_id_mapping_table,
    create_id_mapping,
    get_id_mapping,
    safe_json_parse
)

# --- CONFIGURATION ---
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'db': 'my_app_db',
    'charset': 'utf8mb4',
    'port' : 3306,
    'cursorclass': pymysql.cursors.DictCursor
}
POSTGRES_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',  # Use the role you created
    'password': 'root',  # Use the password you set
    'dbname': 'postgres',
    'port': '5432'
}

BATCH_SIZE = 500

# --- HELPERS ---
def build_location(row):
    parts = [
        row.get('event_location_name'),
        row.get('event_address_street'),
        row.get('event_address_city'),
        row.get('event_address_state'),
        row.get('event_address_zip'),
        row.get('event_address_country'),
    ]
    return ', '.join(p for p in parts if p)

def to_ts(ts_int):
    try:
        return datetime.fromtimestamp(int(ts_int)) if ts_int else None
    except Exception:
        return None

# --- ETL FLOW ---
def migrate_events():
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s %(levelname)s %(message)s')

    # Connect to MySQL via PyMySQL
        # Connect to MySQL via PyMySQL
    mysql_cnx = pymysql.connect(**MYSQL_CONFIG)
    pg_cnx    = psycopg2.connect(**POSTGRES_CONFIG)
    pg_cnx.autocommit = False

    # 1) create unique index so ON CONFLICT(id) works
    with pg_cnx.cursor() as idx_cur:
        idx_cur.execute("""
            CREATE UNIQUE INDEX IF NOT EXISTS idx_event_id
              ON public."Event"(id)
        """)
        pg_cnx.commit()

    # 2) ensure mapping table (unchanged)
    ensure_id_mapping_table(pg_cnx)


    with mysql_cnx.cursor() as mysql_cur:
        mysql_cur.execute("SELECT COUNT(*) AS cnt FROM ltp_events")
        total = mysql_cur.fetchone()['cnt']
        logging.info(f"Found {total} rows in ltp_events")

    offset = 0
    while True:
        with mysql_cnx.cursor() as mysql_cur:
            mysql_cur.execute(
                "SELECT * FROM ltp_events ORDER BY id_event "
                "LIMIT %s OFFSET %s",
                (BATCH_SIZE, offset)
            )
            rows = mysql_cur.fetchall()

        if not rows:
            break

        to_insert = []
        for row in rows:
            old_id = row['id_event']
            new_id = get_id_mapping(pg_cnx, old_id, 'Event')
            if not new_id:
                new_id = int_to_uuid(old_id)
                create_id_mapping(pg_cnx, old_id, new_id, 'Event')

            params = safe_json_parse(row.get('params'))
            raw_removed = row.get('date_removed') or 0
            rec = (
                new_id,
                row.get('event_name'),
                row.get('event_desc'),
                to_ts(row.get('event_date_start')),
                to_ts(row.get('event_date_end')),
                build_location(row),
                row.get('event_max_size'),
                row.get('virtual_event_url_external'),
                to_ts(row.get('date_add')),
                to_ts(row.get('date_mod')),
                int(raw_removed),
                str(row.get('id_add')) if row.get('id_add') else None,
                str(row.get('id_mod')) if row.get('id_mod') else None
            )
            to_insert.append(rec)

        with pg_cnx.cursor() as pg_cur:
            insert_sql = """
            INSERT INTO public."Event" (
              id, event_title, description, start_date, end_date,
              location, max_attendees, join_url,
              created_at, updated_at, deleted_at, created_by_id, updated_by_id
            ) VALUES %s
            ON CONFLICT (id) DO UPDATE SET
              event_title   = EXCLUDED.event_title,
              description   = EXCLUDED.description,
              start_date    = EXCLUDED.start_date,
              end_date      = EXCLUDED.end_date,
              location      = EXCLUDED.location,
              max_attendees = EXCLUDED.max_attendees,
              join_url      = EXCLUDED.join_url,
              updated_at    = EXCLUDED.updated_at,
              deleted_at    = EXCLUDED.deleted_at,
              updated_by_id = EXCLUDED.updated_by_id
            """
            execute_values(pg_cur, insert_sql, to_insert, page_size=BATCH_SIZE)
            pg_cnx.commit()

        offset += BATCH_SIZE
        logging.info(f"Migrated {min(offset, total)}/{total} rows")

    mysql_cnx.close()
    pg_cnx.close()
    logging.info("Migration complete.")

if __name__ == "__main__":
    migrate_events()
