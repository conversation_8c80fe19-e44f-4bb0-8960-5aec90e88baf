## Documnted-not-finalized-yet.

# ltp_people -> User 
## Table Information
Total columns in ltp_people : 130
Total Columns in User : 40

## Table Mapping (Used Columns)
User - ltp_people
--

id - id_people
created_at - date_add
updated_at - date_mod
email - people_email_primary
passwordHash - password
refreshTokenHash -- __
username - username
firstName - name_first
lastName - name_last
affiliateId - __  found this in ltp_people_param inside param_value column value dictionary; contains id_affiliate

advocate_capacity_for_moms - __
date_of_birth - date_birth
home_church - home_church
secondary_email - people_email_secondary
phone - __
secondary_phone - __ 
phone_home -- people_phone_home
phone_mobile -- people_phone_mobile
phone_other -- __ contact_emergency_phone
phone_work -- people_phone_office
sms_message_opt_in -- __
timezone -- time_zone
profilePicExternalUrl -- url_avatar
profilePicMimeType -- __ (url_avatar_one_pager)
created_by_id -- __
created_by_name --  __
address_city -- address_city
address_postalcode -- people_geocode -- previous user's only have geocode

address_state -- address_state
address_street -- address_street_1 , address_street_2 , 
description -- __
communication_preference -- __
status -- __
advocate_status -- 
deleted_at(big-int) -- date_removed (int)
availability -- 
language_notes_c -- __
language_preference_c -- people_languages
languages_c  -- people_languages_other
mustChangePassword --  __ (pass_reset , pass_reset_force)


## Concerns
deleted_at(big-int): this should be timestamp(3) without time zone NOT NULL,



## My-current-work-strategy
- Create table first using script in pg_scema.
- now run ETL to load data from mysql.
- One thing to keep in mind, ETL is designed as per data types of columns in Postgres, any change in that may require some change in ETL.


- Below are the columns that are present in both tables and I have migrate there data.
ltp_people_column_in_mysql : User_column_in_postgres

for rest of the columns, I will sync with JJ and Brad.

column_mapping = {
            'id_people': 'id',
            'date_add': 'created_at',
            'date_mod': 'updated_at',
            'people_email_primary': 'email',
            'password': 'passwordHash',
            'username': 'username',
            'name_first': 'firstName',
            'name_last': 'lastName',
            'date_birth': 'date_of_birth',
            'home_church': 'home_church',
            'people_email_secondary': 'secondary_email',
            'people_phone_home': 'phone_home',
            'people_phone_mobile': 'phone_mobile',
            'contact_emergency_phone': 'phone_other',
            'people_phone_office': 'phone_work',
            'time_zone': 'timezone',
            'url_avatar': 'profilePicExternalUrl',
            'address_city': 'address_city',
            'people_geocode': 'address_postalcode',
            'address_state': 'address_state',
            'address_street_1': 'address_street',
            'date_removed': 'deleted_at',
            'people_languages': 'language_preference_c',
            'people_languages_other': 'languages_c',
            'pass_reset': 'mustChangePassword',
        }

## ETL

#!/usr/bin/env python3
import pandas as pd
import pymysql
import psycopg2
from sqlalchemy import create_engine
import logging
import uuid


def int_to_uuid(int_id):
    # Use a fixed namespace for deterministic UUID generation
    namespace = uuid.NAMESPACE_DNS
    # Convert integer to string for uuid5
    return str(uuid.uuid5(namespace, str(int_id)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection parameters
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'db': 'my_app_db',
    'charset': 'utf8mb4',
    'port' : 3306
}

POSTGRES_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',  # Use the role you created
    'password': 'root',  # Use the password you set
    'dbname': 'postgres',
    'port': '5432'
}

# Test with a single table mapping
TABLE_MAPPING = {
    'ltp_people': 'User'
}

def connect_mysql():
    """Connect to MySQL database"""
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        logger.info("Connected to MySQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to MySQL: {e}")
        raise

def connect_postgres():
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(**POSTGRES_CONFIG)
        logger.info("Connected to PostgreSQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to PostgreSQL: {e}")
        raise


def extract_mysql_table(conn, table_name):
    """Extract data from MySQL table"""
    try:
        # Create SQLAlchemy engine for MySQL
        engine = create_engine(
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['db']}?charset={MYSQL_CONFIG['charset']}"
        )
        # For testing, limit to 30 rows
        query = f"SELECT * FROM {table_name}"
        df = pd.read_sql(query, engine)
        logger.info(f"Extracted {len(df)} rows from MySQL table {table_name}")
        
        # For ltp_people table, make sure we have the right columns
        if table_name == 'ltp_people':
            needed_columns = [
                'id_people', 'name_first', 'name_last', 'people_email_primary',
                'people_phone_mobile', 'people_phone_home', 'people_phone_office',
                'address_street_1', 'address_city', 'address_state', 'address_zip',
                'time_zone', 'home_church', 'username', 'password', 'date_add',
                'date_mod', 'date_birth', 'people_email_secondary', 'contact_emergency_phone',
                'url_avatar', 'people_geocode', 'date_removed', 'people_languages',
                'people_languages_other', 'pass_reset', 'state'
            ]
            for col in needed_columns:
                if col not in df.columns:
                    df[col] = None
        
        return df
    except Exception as e:
        logger.error(f"Error extracting data from {table_name}: {e}")
        raise


def transform_data(df, source_table, target_table):
    """Transform data from MySQL schema to PostgreSQL schema"""
    logger.info(f"Transforming data from {source_table} to {target_table}")
    
    # Get PostgreSQL table schema to understand column names and types
    postgres_conn = connect_postgres()
    cursor = postgres_conn.cursor()
    
    # Get column information
    cursor.execute("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = %s
        ORDER BY ordinal_position
    """, (target_table,))
    
    # Create a new DataFrame for transformed data
    new_df = pd.DataFrame()
    
    # Get column names from PostgreSQL
    pg_columns = [row[0] for row in cursor.fetchall()]
    logger.info(f"Target PostgreSQL columns: {pg_columns}")
    
    # Valid language enum values from public."Language"
    valid_languages = [
        'english', 'spanish', 'chinese_mandarin', 'vietnamese', 'tagalog',
        'arabic', 'korean', 'russian', 'french', 'hindi', 'portuguese',
        'bengali', 'urdu', 'german', 'haitian_creole', 'polish', 'italian',
        'japanese', 'persian_farsi', 'gujarati', 'other'
    ]
    
    # Example mapping for ltp_people to User
    if source_table == 'ltp_people' and target_table == 'User':
        # Map columns based on analysis
        column_mapping = {
            'id_people': 'id',
            'date_add': 'created_at',
            'date_mod': 'updated_at',
            'people_email_primary': 'email',
            'password': 'passwordHash',
            'username': 'username',
            'name_first': 'firstName',
            'name_last': 'lastName',
            'date_birth': 'date_of_birth',
            'home_church': 'home_church',
            'people_email_secondary': 'secondary_email',
            'people_phone_home': 'phone_home',
            'people_phone_mobile': 'phone_mobile',
            'contact_emergency_phone': 'phone_other',
            'people_phone_office': 'phone_work',
            'time_zone': 'timezone',
            'url_avatar': 'profilePicExternalUrl',
            'address_city': 'address_city',
            'people_geocode': 'address_postalcode',
            'address_state': 'address_state',
            'address_street_1': 'address_street',
            'date_removed': 'deleted_at',
            'people_languages': 'language_preference_c',
            'people_languages_other': 'languages_c',
            'pass_reset': 'mustChangePassword',
        }
        
        # Add mapped columns
        for mysql_col, pg_col in column_mapping.items():
            if mysql_col in df.columns and pg_col in pg_columns:
                # Handle special conversions
                if mysql_col == 'id_people':
                    # Convert int(11) to UUID
                    new_df[pg_col] = df[mysql_col].apply(lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else str(uuid.uuid4()))
                elif mysql_col in ['date_add', 'date_mod', 'date_birth']:
                    # Convert int(11) Unix timestamp to timestamp(3) without time zone
                    new_df[pg_col] = pd.to_datetime(df[mysql_col], unit='s', errors='coerce')
                elif mysql_col == 'date_removed':
                    # Convert int(11) Unix timestamp to timestamp(3), default to 1970-01-01 for NULL
                    new_df[pg_col] = pd.to_datetime(df[mysql_col], unit='s', errors='coerce').fillna(pd.Timestamp('1970-01-01'))
                elif mysql_col == 'pass_reset':
                    # Convert varchar(200) to boolean (True if non-empty, False otherwise)
                    new_df[pg_col] = df[mysql_col].apply(lambda x: bool(x) if pd.notnull(x) else False)
                elif mysql_col == 'people_languages':
                    # Convert varchar(75) to public."Language" enum (first valid language or None)
                    new_df[pg_col] = df[mysql_col].apply(
                        lambda x: next((lang.strip().lower() for lang in x.split(',') if lang.strip().lower() in valid_languages), None)
                        if isinstance(x, str) and x else None
                    )
                elif mysql_col == 'people_languages_other':
                    # Convert varchar(75) to public."Language"[] (array of valid languages)
                    new_df[pg_col] = df[mysql_col].apply(
                        lambda x: [lang.strip().lower() for lang in x.split(',') if lang.strip().lower() in valid_languages]
                        if isinstance(x, str) and x else []
                    )
                elif mysql_col == 'contact_emergency_phone':
                    # Convert int(12) to text (format as string)
                    new_df[pg_col] = df[mysql_col].apply(lambda x: str(x) if pd.notnull(x) else None)
                elif mysql_col == 'username':
                    # Map username, allow None for now (handled below)
                    new_df[pg_col] = df[mysql_col].apply(
                        lambda x: x if pd.notnull(x) and isinstance(x, str) and x.strip() else None
                    )
                elif mysql_col == 'password':
                    # Map password, ensure non-NULL
                    new_df[pg_col] = df[mysql_col].apply(
                        lambda x: x if pd.notnull(x) and isinstance(x, str) and x.strip() else 'temporary_hash_placeholder'
                    )
                elif mysql_col == 'people_email_primary':
                    # Map email, allow None for now (handled below)
                    new_df[pg_col] = df[mysql_col].apply(
                        lambda x: x if pd.notnull(x) and isinstance(x, str) and x.strip() else None
                    )
                else:
                    # Direct mapping for varchar/text columns
                    new_df[pg_col] = df[mysql_col]
        
        # Ensure NOT NULL columns have valid values
        if 'id' in pg_columns:
            new_df['id'] = new_df['id'].fillna(pd.Series([str(uuid.uuid4()) for _ in range(len(new_df))], index=new_df.index))
        
        if 'email' in pg_columns:
            # Create a mask for invalid emails (NULL or empty)
            invalid_email_mask = new_df['email'].isna() | (new_df['email'] == '')
            # Generate dummy emails using the index as a unique identifier
            new_df.loc[invalid_email_mask, 'email'] = [f"user_{i}@example.com" for i in new_df.index[invalid_email_mask]]
            # Log rows with generated emails
            generated_emails = new_df[invalid_email_mask][['email']]
            if not generated_emails.empty:
                logger.warning(f"Generated emails for {len(generated_emails)} rows: {generated_emails.to_dict('records')}")
        
        if 'username' in pg_columns:
            # Create a mask for invalid usernames (NULL or empty)
            invalid_username_mask = new_df['username'].isna() | (new_df['username'] == '')
            # Generate usernames from email where possible
            new_df.loc[invalid_username_mask, 'username'] = new_df.loc[invalid_username_mask, 'email'].apply(
                lambda x: x.split('@')[0] if pd.notnull(x) and isinstance(x, str) and '@' in x else f"user_{pd.util.hash_pandas_object(pd.Series([x])).iloc[0]}"
            )
            # Log rows with generated usernames
            generated_usernames = new_df[invalid_username_mask][['email', 'username']]
            if not generated_usernames.empty:
                logger.warning(f"Generated usernames for {len(generated_usernames)} rows: {generated_usernames.to_dict('records')}")
        
        if 'passwordHash' in pg_columns:
            new_df['passwordHash'] = new_df['passwordHash'].fillna('temporary_hash_placeholder')
        
        if 'created_at' in pg_columns:
            new_df['created_at'] = new_df['created_at'].fillna(pd.Timestamp.now())
        
        if 'updated_at' in pg_columns:
            new_df['updated_at'] = new_df['updated_at'].fillna(pd.Timestamp.now())
        
        if 'deleted_at' in pg_columns:
            new_df['deleted_at'] = new_df['deleted_at'].fillna(pd.Timestamp('1970-01-01'))
            # Log rows with default deleted_at
            default_deleted_at = new_df[new_df['deleted_at'] == pd.Timestamp('1970-01-01')][['email', 'deleted_at']]
            if not default_deleted_at.empty:
                logger.warning(f"Set default deleted_at for {len(default_deleted_at)} rows: {default_deleted_at.to_dict('records')}")
        
        if 'mustChangePassword' in pg_columns:
            new_df['mustChangePassword'] = new_df['mustChangePassword'].fillna(False)
        
        # Add required columns with default values
        if 'status' in pg_columns and 'status' not in new_df.columns:
            # Map MySQL state (tinyint(4)) to public."UserStatusType" enum
            if 'state' in df.columns:
                new_df['status'] = df['state'].apply(
                    lambda x: 'Active' if pd.notnull(x) and x == 1 else 'Inactive'
                )
            else:
                new_df['status'] = 'Inactive'
        
        # Handle all other User table columns with defaults
        for col in pg_columns:
            if col not in new_df.columns:
                if col in ['refreshTokenHash', 'affiliateId', 'created_by_id', 'created_by_name', 
                          'phone', 'secondary_phone', 'profilePicMimeType', 'description', 
                          'availability', 'language_notes_c', 'firstName', 'lastName', 
                          'home_church', 'secondary_email', 'phone_home', 'phone_mobile', 
                          'phone_other', 'phone_work', 'timezone', 'profilePicExternalUrl', 
                          'address_city', 'address_postalcode', 'address_state', 'address_street']:
                    new_df[col] = None
                elif col == 'sms_message_opt_in':
                    new_df[col] = False
                elif col == 'advocate_capacity_for_moms':
                    new_df[col] = 0
                elif col == 'communication_preference':
                    new_df[col] = 'email'  # Default to a valid enum value
                elif col == 'advocate_status':
                    new_df[col] = 'Inactive'  # Default to a valid enum value
                elif col == 'language_preference_c':
                    new_df[col] = 'english'  # Default to a valid enum value
                elif col == 'languages_c':
                    new_df[col] = []  # Default to empty array
                elif col == 'date_of_birth':
                    new_df[col] = None
    
    logger.info(f"Transformed DataFrame has {len(new_df.columns)} columns: {new_df.columns.tolist()}")
    return new_df


def load_to_postgres(df, target_table):
    """Load transformed data to PostgreSQL"""
    try:
        # Create SQLAlchemy engine for pandas to_sql
        engine = create_engine(
            f'postgresql://{POSTGRES_CONFIG["user"]}:{POSTGRES_CONFIG["password"]}@{POSTGRES_CONFIG["host"]}:{POSTGRES_CONFIG["port"]}/{POSTGRES_CONFIG["dbname"]}'
        )
        
        # Log the DataFrame before loading
        logger.info(f"DataFrame to load has {len(df)} rows and columns: {df.columns.tolist()}")
        
        # Check for required columns
        required_columns = [
            'id',
            'created_at',
            'updated_at',
            'email',
            'passwordHash',
            'username',
            'firstName',
            'lastName',
            'date_of_birth',
            'home_church',
            'secondary_email',
            'phone_home',
            'phone_mobile',
            'phone_other',
            'phone_work',
            'timezone',
            'profilePicExternalUrl',
            'address_city',
            'address_postalcode',
            'address_state',
            'address_street',
            'deleted_at',
            'language_preference_c',
            'languages_c',
            'mustChangePassword'
        ]
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Load data to PostgreSQL
        schema = 'public'
        
        # Use if_exists='append' to add to existing table
        df.to_sql(target_table, engine, schema=schema, if_exists='append', index=False)
        
        logger.info(f"Loaded {len(df)} rows to PostgreSQL table {schema}.{target_table}")
        return True
    except Exception as e:
        logger.error(f"Error loading data to {target_table}: {e}")
        raise

def main():
    """Test ETL for a single table"""
    mysql_conn = None
    
    try:
        # Connect to MySQL
        mysql_conn = connect_mysql()
        
        # Test with ltp_people table
        source_table = 'ltp_people'
        target_table = TABLE_MAPPING[source_table]
        
        # Extract
        df = extract_mysql_table(mysql_conn, source_table)
        
        # Transform
        df_transformed = transform_data(df, source_table, target_table)
        
        # Print sample of transformed data
        logger.info(f"Transformed data sample:\n{df_transformed.head()}")
        
        # Load data to PostgreSQL
        load_result = load_to_postgres(df_transformed, target_table)
        
        if load_result:
            logger.info("ETL process completed successfully")
        
    except Exception as e:
        logger.error(f"ETL test failed: {e}")
    finally:
        if mysql_conn:
            mysql_conn.close()

if __name__ == "__main__":
    main()



## ltp_affiliates -> Affliates

49 columns : 22 columns

Now I will find each column in Affiliates table and map it to ltp_affiliates table.

Below are the columns in Affiliates table and their mapping to ltp_affiliates table, but some of them are missing, will sync with Brad and JJ on that.


id : id_affiliate
name : affiliate_name
description : affiliate_desc
billing_address_street : affiliate_address_street
billing_address_street_2
billing_address_street_3
billing_address_street_4
billing_address_city : affiliate_address_city
billing_address_state : affiliate_address_state
billing_address_postalcode : affiliate_address_zip
billing_address_country : affiliate_address_country
phone_office : afflliate_contact_phone
website : affiliate_website
email1 : affiliate_contact_email
created_at : date_add
updated_at : date_mod
created_by_id : 
created_by_name : 
deleted_at : date_removed
agency_nickname : affiliate_name_short
contact_name : affiliate_contact_name
status : state

# ETL for ltp_affiliates to Affiliates

#!/usr/bin/env python3
import pandas as pd
import pymysql
import psycopg2
from sqlalchemy import create_engine
import logging
import uuid
from datetime import datetime

def int_to_uuid(int_id):
    """Convert integer ID to deterministic UUID using a fixed namespace."""
    namespace = uuid.NAMESPACE_DNS
    return str(uuid.uuid5(namespace, str(int_id)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection parameters
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'db': 'my_app_db',
    'charset': 'utf8mb4',
    'port' : 3306
}
POSTGRES_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',  # Use the role you created
    'password': 'root',  # Use the password you set
    'dbname': 'postgres',
    'port': '5432'
}

def connect_mysql():
    """Connect to MySQL database."""
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        logger.info("Connected to MySQL database")
        return conn
    except pymysql.Error as e:
        logger.error(f"Error connecting to MySQL: {e}")
        raise

def connect_postgres():
    """Connect to PostgreSQL database."""
    try:
        conn = psycopg2.connect(**POSTGRES_CONFIG)
        logger.info("Connected to PostgreSQL database")
        return conn
    except psycopg2.Error as e:
        logger.error(f"Error connecting to PostgreSQL: {e}")
        raise

def extract_mysql_table(conn, table_name):
    """Extract data from MySQL table."""
    try:
        engine = create_engine(
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['db']}?charset={MYSQL_CONFIG['charset']}"
        )
        query = f"""
        SELECT 
            id_affiliate, affiliate_name, affiliate_desc, affiliate_address_street,
            affiliate_address_city, affiliate_address_state, affiliate_address_zip,
            affiliate_address_country, afflliate_contact_phone, affiliate_website,
            affiliate_contact_email, date_add, date_mod, date_removed,
            affiliate_name_short, affiliate_contact_name, state
        FROM {table_name}
        """
        df = pd.read_sql(query, engine)
        logger.info(f"Extracted {len(df)} rows from MySQL table {table_name}")
        
        # Ensure all needed columns exist
        needed_columns = [
            'id_affiliate', 'affiliate_name', 'affiliate_desc', 'affiliate_address_street',
            'affiliate_address_city', 'affiliate_address_state', 'affiliate_address_zip',
            'affiliate_address_country', 'afflliate_contact_phone', 'affiliate_website',
            'affiliate_contact_email', 'date_add', 'date_mod', 'date_removed',
            'affiliate_name_short', 'affiliate_contact_name', 'state'
        ]
        for col in needed_columns:
            if col not in df.columns:
                df[col] = None
        
        return df
    except Exception as e:
        logger.error(f"Error extracting data from {table_name}: {e}")
        raise

def transform_data(df, source_table, target_table):
    """Transform data from MySQL ltp_affiliates to PostgreSQL Affiliate schema."""
    logger.info(f"Transforming data from {source_table} to {target_table}")
    
    # Get PostgreSQL table schema
    postgres_conn = connect_postgres()
    cursor = postgres_conn.cursor()
    cursor.execute("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = %s
        ORDER BY ordinal_position
    """, (target_table,))

    pg_columns = [row[0] for row in cursor.fetchall()]
    logger.info(f"Target PostgreSQL columns: {pg_columns}")
    cursor.close()
    postgres_conn.close()
    
    # Define column mapping
    column_mapping = {
        'id_affiliate': 'id',
        'affiliate_name': 'name',
        'affiliate_desc': 'description',
        'affiliate_address_street': 'billing_address_street',
        'affiliate_address_city': 'billing_address_city',
        'affiliate_address_state': 'billing_address_state',
        'affiliate_address_zip': 'billing_address_postalcode',
        'affiliate_address_country': 'billing_address_country',
        'afflliate_contact_phone': 'phone_office',
        'affiliate_website': 'website',
        'affiliate_contact_email': 'email1',
        'date_add': 'created_at',
        'date_mod': 'updated_at',
        'date_removed': 'deleted_at',
        'affiliate_name_short': 'agency_nickname',
        'affiliate_contact_name': 'contact_name',
        'state': 'status'
    }
    
    # Create new DataFrame for transformed data
    new_df = pd.DataFrame()
    
    # Map columns
    for mysql_col, pg_col in column_mapping.items():
        if mysql_col in df.columns and pg_col in pg_columns:
            if mysql_col == 'id_affiliate':
                new_df[pg_col] = df[mysql_col].apply(lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else str(uuid.uuid4()))
            elif mysql_col in ['date_add', 'date_mod', 'date_removed']:
                new_df[pg_col] = pd.to_datetime(df[mysql_col], unit='s', errors='coerce')
            elif mysql_col == 'state':
                new_df[pg_col] = df[mysql_col].apply(lambda x: 'Active' if pd.notnull(x) and x == 1 else 'Inactive')
            else:
                new_df[pg_col] = df[mysql_col]
    
    # Handle NULLs and required fields
    if 'id' in pg_columns:
        new_df['id'] = new_df['id'].fillna(pd.Series([str(uuid.uuid4()) for _ in range(len(new_df))], index=new_df.index))
        generated_ids = new_df[new_df['id'].str.startswith('uuid')][['id']]
        if not generated_ids.empty:
            logger.warning(f"Generated IDs for {len(generated_ids)} rows: {generated_ids.to_dict('records')}")
    
    if 'name' in pg_columns:
        new_df['name'] = new_df['name'].fillna('')
        empty_names = new_df[new_df['name'] == ''][['id', 'name']]
        if not empty_names.empty:
            logger.warning(f"Set empty name for {len(empty_names)} rows: {empty_names.to_dict('records')}")
    
    if 'created_at' in pg_columns:
        new_df['created_at'] = new_df['created_at'].fillna(pd.Timestamp.now())
        default_created = new_df[new_df['created_at'] == pd.Timestamp.now()][['id', 'created_at']]
        if not default_created.empty:
            logger.warning(f"Set default created_at for {len(default_created)} rows: {default_created.to_dict('records')}")
    
    if 'updated_at' in pg_columns:
        new_df['updated_at'] = new_df['updated_at'].fillna(pd.Timestamp.now())
        default_updated = new_df[new_df['updated_at'] == pd.Timestamp.now()][['id', 'updated_at']]
        if not default_updated.empty:
            logger.warning(f"Set default updated_at for {len(default_updated)} rows: {default_updated.to_dict('records')}")
    
    if 'deleted_at' in pg_columns:
        new_df['deleted_at'] = new_df['deleted_at'].fillna(pd.Timestamp.now())
        default_deleted = new_df[new_df['deleted_at'] == pd.Timestamp.now()][['id', 'deleted_at']]
        if not default_deleted.empty:
            logger.warning(f"Set default deleted_at for {len(default_deleted)} rows: {default_deleted.to_dict('records')}")
    
    # Add unmapped columns with NULLs
    for col in pg_columns:
        if col not in new_df.columns:
            if col in ['billing_address_street_2', 'billing_address_street_3', 'billing_address_street_4', 
                       'created_by_id', 'created_by_name']:
                new_df[col] = None
            elif col == 'status':
                new_df[col] = 'Inactive'
    
    logger.info(f"Transformed DataFrame has {len(new_df.columns)} columns: {new_df.columns.tolist()}")
    return new_df

def load_to_postgres(df, target_table):
    """Load transformed data to PostgreSQL."""
    try:
        engine = create_engine(
            f"postgresql://{POSTGRES_CONFIG['user']}:{POSTGRES_CONFIG['password']}@{POSTGRES_CONFIG['host']}:{POSTGRES_CONFIG['port']}/{POSTGRES_CONFIG['dbname']}"
        )
        logger.info(f"DataFrame to load has {len(df)} rows and columns: {df.columns.tolist()}")
        
        # Check required columns
        required_columns = ['id', 'name', 'created_at', 'updated_at', 'deleted_at']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Load data
        df.to_sql(target_table, engine, schema='public', if_exists='append', index=False)
        logger.info(f"Loaded {len(df)} rows to PostgreSQL table public.{target_table}")
        return True
    except Exception as e:
        logger.error(f"Error loading data to {target_table}: {e}")
        raise

def main():
    """Execute ETL for ltp_affiliates to Affiliate."""
    mysql_conn = None
    try:
        mysql_conn = connect_mysql()
        source_table = 'ltp_affiliates'
        target_table = 'Affiliate'
        
        # Extract
        df = extract_mysql_table(mysql_conn, source_table)
        
        # Transform
        df_transformed = transform_data(df, source_table, target_table)
        
        # Log sample
        logger.info(f"Transformed data sample:\n{df_transformed.head()}")
        
        # Load
        load_result = load_to_postgres(df_transformed, target_table)
        if load_result:
            logger.info("ETL process completed successfully")
    except Exception as e:
        logger.error(f"ETL process failed: {e}")
    finally:
        if mysql_conn:
            mysql_conn.close()

if __name__ == "__main__":
    main()



# ltp_people -> Moms

# Columns Mapping 
**Note**: ltp_people_params is linked to ltp_people via `id_people` column in ltp_people_params. And __ means not mapped.

Moms: ltp_people

id : id_people
first_name : name_first
last_name : name_last
phone_other : people_phone_mobile
currently_pregnant_c : pregnant (ltp_people_params -> param_value -> this `pregnant` is a key, we have to get its value)
number_of_children_c : num_children (ltp_people_params -> param_value -> this `num_children` is a key, we have to get its value)
need_details_c : additional_needs (ltp_people_params -> param_value -> this `additional_needs` is a key, we have to get its value)
what_else_c : __
caregiver_type_c : interest_interm_caregiver
gender_c : people_gender
email1 : people_email_primary
referral_type_c : referring_for (ltp_people_params -> param_value -> this `referring_for` is a key, we have to get its value)
supports_court_order_c : __
service_selected_c : __
created_by_name : __
account_name : username
affiliate_id : id_affiliate
agency_id : id_agency
consent_obtained_c : client_consent (ltp_people_params -> param_value -> this `client_consent` is a key, we have to get its value)
referring_contact_first_name_c : referrer_first_name (ltp_people_params -> param_value -> this `referrer_first_name` is a key, we have to get its value)
referring_contact_last_name_c : referrer_last_name (ltp_people_params -> param_value -> this `referrer_last_name` is a key, we have to get its value)
referring_contact_email_c : referrer_email (ltp_people_params -> param_value -> this `referrer_email` is a key, we have to get its value)
referring_contact_phone_c : referrer_phone (ltp_people_params -> param_value -> this `referrer_phone` is a key, we have to get its value)
birthdate : date_birth
discharge_reason_c : discharge_reason (ltp_people_params -> param_value -> this `discharge_reason` is a key, we have to get its value)
closed_date_c : __
language_notes_c : __
primary_address_street : address_street_1
primary_address_city : address_city
primary_address_state : address_state
primary_address_postalcode : address_zip
primary_address_county_c : address_county
connected_benevolance_c : programs_benevolance_prev (ltp_people_params -> param_value -> this `programs_benevolance_prev` is a key, we have to get its value)
connected_childcare_c : programs_childcare_prev (ltp_people_params -> param_value -> this `programs_childcare_prev` is a key, we have to get its value)
connected_closet_c : programs_closet_visit_prev (ltp_people_params -> param_value -> this `programs_closet_visit_prev` is a key, we have to get its value)
connected_education_c : programs_education_prev (ltp_people_params -> param_value -> this `programs_education_prev` is a key, we have to get its value)
connected_health_c : programs_health_prev (ltp_people_params -> param_value -> this `programs_health_prev` is a key, we have to get its value)
connected_housing_c : programs_housing_prev (ltp_people_params -> param_value -> this `programs_housing_prev` is a key, we have to get its value)
connected_legal_c : programs_legal_prev (ltp_people_params -> param_value -> this `programs_legal_prev` is a key, we have to get its value)
connected_mental_health_c : programs_mental_health_prev (ltp_people_params -> param_value -> this `programs_mental_health_prev` is a key, we have to get its value)
connected_substance_c : programs_substance_prev (ltp_people_params -> param_value -> this `programs_substance_prev` is a key, we have to get its value)
date_entered : date_intake (ltp_people_params -> param_value -> this `date_intake` is a key, we have to get its value)
address_access_c : address_access_instructions
assigned_user_id : __
created_at : date_add
updated_at : date_mod
created_by_id : id_add
deleted_at : date_removed
prospect_status : __ people_status_text
referral_sub_status : __
referred_to_agency_id : cert_mentor_agency_ids | cert_ff_agency_ids
referred_to_agency_reason : __
status : state
sms_message_opt_in : __
preferred_contact_method_c : __
language_preference_c : people_languages (first element)
languages_c : people_languages (all elements)
children_in_home : num_children (ltp_people_params -> param_value -> this `num_children` is a key, we have to get its value)
photo_url : url_avatar
photo_s3_file_name : photo_url (extract filename from url)
cultural_heritage_c : __
emergency_contact_name_c : contact_emergency
emergency_contact_number_c : contact_emergency_phone
emergency_contact_relation_c : emergency_contact_relationship (ltp_people_params -> param_value -> this `emergency_contact_relationship` is a key, we have to get its value)
languages : people_languages
martial_status : marital_status (ltp_people_params -> param_value -> this `marital_status` is a key, we have to get its value)
number_of_children_in_home_c : num_children (ltp_people_params -> param_value -> this `num_children` is a key, we have to get its value)
phone_alternate_c : people_phone_home
pregnant_due_date : __
primary_address_street_two_c : address_street_2
race_c : race (ltp_people_params -> param_value -> this `race` is a key, we have to get its value)


## ETL for ltp_people to Moms

#!/usr/bin/env python3
import pandas as pd
import pymysql
import psycopg2
from sqlalchemy import create_engine
import logging
import uuid
import json
import re  # Add this import for regex

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define utility functions
def safe_get_param_value(param_value, is_numeric=False):
    """Safely get a parameter value, handling different formats."""
    # Handle None values
    if param_value is None:
        return None
    
    # Handle dictionary values
    if isinstance(param_value, dict):
        # For dictionaries, return the whole dict
        return param_value
    
    # Handle list values
    if isinstance(param_value, list):
        # For lists, return the whole list
        return param_value
    
    # Handle string values
    if isinstance(param_value, str):
        # Try to parse as JSON if it looks like JSON
        if (param_value.startswith('{') and param_value.endswith('}')) or \
           (param_value.startswith('[') and param_value.endswith(']')):
            try:
                return json.loads(param_value)
            except json.JSONDecodeError:
                pass
        
        # If numeric extraction is requested
        if is_numeric:
            return extract_number_from_string(param_value)
        
        # Otherwise return the string as is
        return param_value
    
    # For other types, return as is
    return param_value
def extract_number_from_string(value):
    """Extract the first number from a string, handling various formats."""
    if value is None or not str(value).strip():
        return None
    
    value_str = str(value).strip()
    
    # Try to extract the first number using regex
    numbers = re.findall(r'\d+', value_str)
    if numbers:
        return int(numbers[0])
    
    # If no numbers found, return None
    return None

# Database connection parameters
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'db': 'my_app_db',
    'charset': 'utf8mb4',
    'port': 3306
}

POSTGRES_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',
    'password': 'root',
    'dbname': 'postgres',
    'port': '5432'
}
def safe_json_parse(json_str):
    """Safely parse JSON string to list, handling errors gracefully"""
    if not json_str or not isinstance(json_str, str):
        return []
    
    try:
        return json.loads(json_str)
    except json.JSONDecodeError:
        # Try to handle common issues
        if ',' in json_str:
            # Might be a comma-separated list
            return [lang.strip() for lang in json_str.split(',') if lang.strip()]
        else:
            # Just return as a single item list
            return [json_str] if json_str.strip() else []
    except Exception:
        return []

def safe_get_first_language(json_str):
    """Safely get the first language from a JSON string"""
    languages = safe_json_parse(json_str)
    if languages and isinstance(languages, list) and len(languages) > 0:
        return languages[0]
    return 'english'  # Default value

def safe_param_parse(param_value):
    """Safely parse parameter value which might be in JSON format"""
    if not param_value or not isinstance(param_value, str):
        return param_value
    
    # Try to parse as JSON
    try:
        parsed_value = json.loads(param_value)
        return parsed_value
    except json.JSONDecodeError:
        logger.info(f"Not valid JSON: {param_value}")
        # Not valid JSON, return as is
        return param_value
    except Exception:
        logger.info(f"Error parsing JSON: {param_value}")
        # Any other error, return as is
        return param_value

def int_to_uuid(int_id):
    # Use a fixed namespace for deterministic UUID generation
    namespace = uuid.NAMESPACE_DNS
    # Convert integer to string for uuid5
    return str(uuid.uuid5(namespace, str(int_id)))

def connect_mysql():
    """Connect to MySQL database"""
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        logger.info("Connected to MySQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to MySQL: {e}")
        raise

def connect_postgres():
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(**POSTGRES_CONFIG)
        logger.info("Connected to PostgreSQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to PostgreSQL: {e}")
        raise

def extract_mysql_data():
    """Extract data from MySQL tables ltp_people, ltp_people_params, and ltp_assignments"""
    try:
        engine = create_engine(
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['db']}?charset={MYSQL_CONFIG['charset']}"
        )
        
        # First, extract mom IDs from ltp_assignments
        mom_query = """
        SELECT DISTINCT id_people 
        FROM ltp_assignments 
        WHERE id_role IN (2, 10, 16) 
        AND date_removed IS NULL OR date_removed = 0
        """
        mom_ids_df = pd.read_sql(mom_query, engine)
        logger.info(f"Extracted {len(mom_ids_df)} mom IDs from ltp_assignments")
        
        # Get the list of mom IDs
        mom_ids = mom_ids_df['id_people'].tolist()
        
        if not mom_ids:
            logger.warning("No mom IDs found in ltp_assignments table!")
            return pd.DataFrame(), pd.DataFrame()
        
        # Format the list for SQL IN clause
        mom_ids_str = ','.join(str(id) for id in mom_ids)
        
        # Extract ltp_people filtered by mom IDs
        people_query = f"""
        SELECT * FROM ltp_people
        WHERE id_people IN ({mom_ids_str})
        """
        people_df = pd.read_sql(people_query, engine)
        logger.info(f"Extracted {len(people_df)} rows from ltp_people for moms")
        
        # Extract ltp_people_params filtered by mom IDs
        params_query = f"""
        SELECT * FROM ltp_people_params
        WHERE id_people IN ({mom_ids_str})
        """
        params_df = pd.read_sql(params_query, engine)
        logger.info(f"Extracted {len(params_df)} rows from ltp_people_params for moms")
        
        return people_df, params_df
    except Exception as e:
        logger.error(f"Error extracting data: {e}")
        raise

def transform_data(people_df, params_df):
    """Transform data from MySQL schema to PostgreSQL schema"""
    logger.info("Transforming data from ltp_people to mom")
    
    # Get PostgreSQL table schema
    postgres_conn = connect_postgres()
    cursor = postgres_conn.cursor()
    cursor.execute("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = %s
        ORDER BY ordinal_position
    """, ('Mom',))
    
    pg_columns = [row[0] for row in cursor.fetchall()]
    logger.info(f"Target PostgreSQL columns: {pg_columns}")
    cursor.close()
    postgres_conn.close()
    
    # Create new DataFrame for transformed data
    logger.info("Creating new DataFrame for transformed data")
    new_df = pd.DataFrame()
    
    # Process params_df to create a dictionary of param values for each person
    logger.info("Processing params_df to create dictionary of param values")
    params_dict = {}
    for _, row in params_df.iterrows():
        person_id = row['id_people']
        param_name = row['param_name']
        param_value = row['param_value']
        
        if person_id not in params_dict:
            params_dict[person_id] = {}
        
        # Handle intake_form specially - it contains many nested parameters
        if param_name == 'intake_form':
            try:
                intake_data = json.loads(param_value)
                # Add each field from intake_form as a separate parameter
                for key, value in intake_data.items():
                    params_dict[person_id][key] = value
                logger.info(f"Parsed intake_form for person_id={person_id} with {len(intake_data)} fields")
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse intake_form JSON for person_id={person_id}")
                params_dict[person_id][param_name] = param_value
            except Exception as e:
                logger.error(f"Error processing intake_form for person_id={person_id}: {e}")
                params_dict[person_id][param_name] = param_value
        else:
            params_dict[person_id][param_name] = param_value
        
        # Log a sample of parameter values for debugging
        if _ < 10:  # Only log the first 10 rows
            logger.info(f"Sample param: person_id={person_id}, param_name={param_name}, param_value={param_value}")
    
    logger.info(f"Created params dictionary with {len(params_dict)} entries")
    
    # Apply direct column mappings first
    logger.info("Applying direct column mappings")
    # Direct column mappings
    direct_mappings = {
        'id_people': 'id',
        'name_first': 'first_name',
        'name_last': 'last_name',
        'people_phone_mobile': 'phone_other',
        'people_gender': 'gender_c',
        'people_email_primary': 'email1',
        'username': 'account_name',
        'id_affiliate': 'affiliate_id',
        'id_agency': 'agency_id',
        'date_birth': 'birthdate',
        'address_street_1': 'primary_address_street',
        'address_city': 'primary_address_city',
        'address_state': 'primary_address_state',
        'address_zip': 'primary_address_postalcode',
        'address_county': 'primary_address_county_c',
        'address_access_instructions': 'address_access_c',
        'date_add': 'created_at',
        'date_mod': 'updated_at',
        'id_add': 'created_by_id',
        'date_removed': 'deleted_at',
        'people_status_text': 'prospect_status',
        'state': 'status',
        'url_avatar': 'photo_url',
        'people_phone_home': 'phone_alternate_c',
        'address_street_2': 'primary_address_street_two_c',
        'contact_emergency': 'emergency_contact_name_c',
        'contact_emergency_phone': 'emergency_contact_number_c',
        'people_languages': 'languages'
    }
    
    # Apply direct mappings
    for mysql_col, pg_col in direct_mappings.items():
        if mysql_col in people_df.columns and pg_col in pg_columns:
            logger.debug(f"Mapping {mysql_col} to {pg_col}")
            try:
                if mysql_col == 'id_people':
                    new_df[pg_col] = people_df[mysql_col].apply(lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else str(uuid.uuid4()))
                elif mysql_col in ['date_add', 'date_mod', 'date_removed', 'date_birth']:
                    new_df[pg_col] = pd.to_datetime(people_df[mysql_col], unit='s', errors='coerce')
                elif mysql_col == 'state':
                    # Map state values to appropriate status
                    new_df[pg_col] = people_df[mysql_col].apply(
                        lambda x: 'active' if pd.notnull(x) and x in [1, 46, 42] else 
                                     ('inactive' if pd.notnull(x) and (20 <= x < 30) else
                                     ('inactive' if pd.notnull(x) and x < 0 else 'inactive'))
                    )
                    # Log the mapping for verification
                    state_mapping = people_df[['id_people', mysql_col]].copy()
                    state_mapping['mapped_status'] = new_df[pg_col]
                    logger.info(f"State mapping sample (first 5 rows):\n{state_mapping.head().to_string()}")
                elif mysql_col == 'people_languages':
                    # Handle languages as JSON array with better error handling
                    new_df[pg_col] = people_df[mysql_col].apply(
                        lambda x: safe_json_parse(x)
                    )
                    # Also set language_preference_c to first language
                    if 'language_preference_c' in pg_columns:
                        new_df['language_preference_c'] = people_df[mysql_col].apply(
                            lambda x: safe_get_first_language(x)
                        )
                    # Also set languages_c to all languages
                    if 'languages_c' in pg_columns:
                        new_df['languages_c'] = people_df[mysql_col].apply(
                            lambda x: safe_json_parse(x)
                        )
                elif mysql_col == 'url_avatar':
                    new_df[pg_col] = people_df[mysql_col]
                    # Extract filename for photo_s3_file_name
                    if 'photo_s3_file_name' in pg_columns:
                        new_df['photo_s3_file_name'] = people_df[mysql_col].apply(
                            lambda x: x.split('/')[-1] if pd.notnull(x) and x else None
                        )
                else:
                    new_df[pg_col] = people_df[mysql_col]
            except Exception as e:
                logger.error(f"Error mapping {mysql_col} to {pg_col}: {e}")
                raise
    
    # Apply param mappings
    logger.info("Applying param mappings to new DataFrame")
    param_mappings = {
        # Direct parameters
        'pregnant': 'currently_pregnant_c',
        'num_children': ['number_of_children_c', 'children_in_home', 'number_of_children_in_home_c'],
        'additional_needs': 'need_details_c',
        'interest_interm_caregiver': 'caregiver_type_c',
        'referring_for': 'referral_type_c',
        'client_consent': 'consent_obtained_c',
        'referrer_first_name': 'referring_contact_first_name_c',
        'referrer_last_name': 'referring_contact_last_name_c',
        'referrer_email': 'referring_contact_email_c',
        'referrer_phone': 'referring_contact_phone_c',
        'discharge_reason': 'discharge_reason_c',
        'programs_benevolance_prev': 'connected_benevolance_c',
        'programs_childcare_prev': 'connected_childcare_c',
        'programs_closet_visit_prev': 'connected_closet_c',
        'programs_education_prev': 'connected_education_c',
        'programs_health_prev': 'connected_health_c',
        'programs_housing_prev': 'connected_housing_c',
        'programs_legal_prev': 'connected_legal_c',
        'programs_mental_health_prev': 'connected_mental_health_c',
        'programs_substance_prev': 'connected_substance_c',
        'date_intake': 'date_entered',
        'emergency_contact_relationship': 'emergency_contact_relation_c',
        'marital_status': 'martial_status',
        'race': 'race_c',
        
        # Fields from intake_form (now directly accessible)
        'caregiver_type': 'caregiver_type_c',
        'housing_status': 'housing_status_c',
        'additional_comments': 'what_else_c',
        'employment_status': 'employment_status_c',
        'education_level': 'education_level_c',
        'income': 'income_c',
        'has_health_insurance': 'has_health_insurance_c',
        'health_insurance_provider': 'health_insurance_provider_c',
        'history_of_substance_abuse': 'history_of_substance_abuse_c',
        'substance_last_use': 'substance_last_use_c',
        'attending_recovery': 'attending_recovery_c',
        'receiving_substance_treatment': 'receiving_substance_treatment_c',
        'received_medical_treatment': 'received_medical_treatment_c',
        'taking_medications': 'taking_medications_c',
        'prescription_compliance': 'prescription_compliance_c',
        'previous_cps_past_involvement': 'previous_cps_past_involvement_c',
        'previous_cps_current_investigation': 'previous_cps_current_investigation_c',
        'previous_cps_current_plan': 'previous_cps_current_plan_c',
        'additional_comments_cps': 'additional_comments_cps_c',
        'transportation': 'transportation_c',
        'custody': 'custody_c',
        'school_status': 'school_status_c',
        'nationality': 'nationality_c',
        'ages_of_children': 'ages_of_children_c',
        
        # Fields from interest_form (with interest_ prefix)
        'interest_housing_status': 'housing_status_c',
        'interest_marital_status': 'martial_status',
        'interest_race': 'race_c',
        'interest_employment_status': 'employment_status_c',
        'interest_education_level': 'education_level_c',
        'interest_income': 'income_c',
        'interest_additional_comments': 'what_else_c'
    }
    
    # Log the number of people in the DataFrame
    logger.info(f"Number of people in DataFrame: {len(new_df)}")
    
    # Log the first few IDs to verify
    if not new_df.empty:
        logger.info(f"First few IDs in new_df: {new_df['id'].head().tolist()}")
    
    # Create a mapping from person_id to UUID for faster lookups
    id_to_uuid = {id_val: int_to_uuid(id_val) for id_val in people_df['id_people'].unique()}
    logger.info(f"Created ID to UUID mapping with {len(id_to_uuid)} entries")
    
    # Apply param mappings with improved error handling
    param_mapping_counts = {param: 0 for param in param_mappings.keys()}

    for person_id, person_params in params_dict.items():
        # Convert person_id to UUID for lookup
        uuid_id = id_to_uuid.get(person_id)
        if not uuid_id:
            logger.warning(f"Person ID {person_id} not found in ID to UUID mapping")
            continue
        
        # Find the row index for this person
        idx = new_df[new_df['id'] == uuid_id].index
        
        if len(idx) == 0:
            logger.warning(f"Person ID {person_id} (UUID: {uuid_id}) not found in transformed DataFrame")
            continue
        
        for param_name, pg_cols in param_mappings.items():
            if param_name in person_params:
                raw_param_value = person_params[param_name]
                
                # Handle multiple target columns for same param
                if isinstance(pg_cols, list):
                    for pg_col in pg_cols:
                        if pg_col in pg_columns:
                            try:
                                # Determine if this is a numeric field
                                is_numeric = pg_col in ['number_of_children_c', 'children_in_home', 'number_of_children_in_home_c']
                                
                                # Get the processed parameter value
                                param_value = safe_get_param_value(raw_param_value, is_numeric)
                                
                                # Use a safer approach for setting values
                                for i in idx:
                                    new_df.at[i, pg_col] = param_value
                                param_mapping_counts[param_name] += 1
                            except Exception as e:
                                logger.error(f"Error setting {pg_col} for person {person_id}: {e}")
                else:
                    pg_col = pg_cols
                    if pg_col in pg_columns:
                        try:
                            # Determine if this is a numeric field
                            is_numeric = pg_col in ['number_of_children_c', 'children_in_home', 'number_of_children_in_home_c']
                            
                            # Get the processed parameter value
                            param_value = safe_get_param_value(raw_param_value, is_numeric)
                            
                            # Use a safer approach for setting values
                            for i in idx:
                                new_df.at[i, pg_col] = param_value
                            param_mapping_counts[param_name] += 1
                        except Exception as e:
                            logger.error(f"Error setting {pg_col} for person {person_id}: {e}")
    
    # Log the number of mappings applied for each parameter
    logger.info("Parameter mapping counts:")
    for param, count in param_mapping_counts.items():
        logger.info(f"  {param}: {count}")
    
    # Handle special cases
    logger.info("Handling special cases")
    if 'referred_to_agency_id' in pg_columns:
        new_df['referred_to_agency_id'] = people_df.apply(
            lambda row: row.get('cert_mentor_agency_ids', '') or row.get('cert_ff_agency_ids', ''),
            axis=1
        )
    
    # Ensure required fields have values
    logger.info("Ensuring required fields have values")
    required_fields = ['id', 'first_name', 'last_name', 'created_at', 'updated_at']
    for field in required_fields:
        if field in pg_columns and field in new_df.columns:
            try:
                if field == 'id':
                    new_df[field] = new_df[field].fillna(pd.Series([str(uuid.uuid4()) for _ in range(len(new_df))], index=new_df.index))
                elif field in ['first_name', 'last_name']:
                    new_df[field] = new_df[field].fillna('')
                elif field in ['created_at', 'updated_at']:
                    new_df[field] = new_df[field].fillna(pd.Timestamp.now())
            except Exception as e:
                logger.error(f"Error ensuring required field {field} has value: {e}")
    
    # Set prospect_status to 'prospect' by default
    if 'prospect_status' in pg_columns:
        logger.info("Setting default prospect_status to 'prospect'")
        new_df['prospect_status'] = 'prospect'
    
    # Add default values for any missing columns
    logger.info("Adding default values for missing columns")
    for col in pg_columns:
        if col not in new_df.columns:
            if col in ['what_else_c', 'supports_court_order_c', 'service_selected_c', 'created_by_name',
                      'closed_date_c', 'language_notes_c', 'assigned_user_id', 'referral_sub_status',
                      'referred_to_agency_reason', 'sms_message_opt_in', 'preferred_contact_method_c',
                      'cultural_heritage_c', 'pregnant_due_date']:
                new_df[col] = None
    
    logger.info(f"Transformed DataFrame has {len(new_df.columns)} columns: {new_df.columns.tolist()}")
    return new_df

def load_to_postgres(df, target_table):
    """Load transformed data to PostgreSQL"""
    try:
        # First, check and alter column types if needed
        logger.info("Checking and updating column types in PostgreSQL table")
        postgres_conn = connect_postgres()
        cursor = postgres_conn.cursor()
        
        # Get column types from PostgreSQL
        cursor.execute("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = %s
        """, (target_table,))
        
        column_info = {row[0]: {'data_type': row[1], 'is_nullable': row[2]} for row in cursor.fetchall()}
        logger.info(f"Retrieved {len(column_info)} column types from PostgreSQL")
        
        # Process numeric columns
        numeric_columns = ['number_of_children_c', 'children_in_home', 'number_of_children_in_home_c']
        for col in numeric_columns:
            if col in df.columns:
                logger.info(f"Converting column {col} to integer")
                try:
                    # Convert to integer, handling various formats
                    df[col] = df[col].apply(lambda x: extract_number_from_string(x) if isinstance(x, str) else x)
                    # Convert to numeric, coercing errors to NaN
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                    
                    # Check if column is NOT NULL and set default value if needed
                    if col in column_info and column_info[col]['is_nullable'] == 'NO':
                        logger.info(f"Setting default value 0 for NOT NULL column {col}")
                        df[col] = df[col].fillna(0)
                except Exception as e:
                    logger.error(f"Error converting {col} to integer: {e}")
                    # Set to default value if conversion fails and column is NOT NULL
                    if col in column_info and column_info[col]['is_nullable'] == 'NO':
                        df[col] = 0
                    else:
                        df[col] = None
        
        # Process all columns based on PostgreSQL types
        for col, info in column_info.items():
            dtype = info['data_type']
            is_nullable = info['is_nullable']
            
            if col in df.columns:
                if dtype in ('integer', 'bigint', 'smallint'):
                    if col not in numeric_columns:  # Skip columns we already processed
                        logger.info(f"Converting column {col} to integer")
                        try:
                            # Convert to numeric, coercing errors to NaN
                            df[col] = pd.to_numeric(df[col], errors='coerce')
                            
                            # Set default value for NOT NULL columns
                            if is_nullable == 'NO':
                                logger.info(f"Setting default value 0 for NOT NULL column {col}")
                                df[col] = df[col].fillna(0)
                        except Exception as e:
                            logger.error(f"Error converting {col} to integer: {e}")
                            # Set to default value if conversion fails and column is NOT NULL
                            if is_nullable == 'NO':
                                df[col] = 0
                            else:
                                df[col] = None
                
                elif dtype in ('numeric', 'decimal', 'real', 'double precision'):
                    logger.info(f"Converting column {col} to float")
                    try:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                        
                        # Set default value for NOT NULL columns
                        if is_nullable == 'NO':
                            logger.info(f"Setting default value 0.0 for NOT NULL column {col}")
                            df[col] = df[col].fillna(0.0)
                    except Exception as e:
                        logger.error(f"Error converting {col} to float: {e}")
                        # Set to default value if conversion fails and column is NOT NULL
                        if is_nullable == 'NO':
                            df[col] = 0.0
                        else:
                            df[col] = None
        
        # Convert DataFrame columns to appropriate types before loading
        for col in df.columns:
            if col in ('created_at', 'updated_at', 'date_of_birth') and col in df.columns:
                # Ensure these columns are timestamp type in the DataFrame
                if df[col].dtype != 'datetime64[ns]':
                    logger.info(f"Converting DataFrame column {col} to timestamp")
                    df[col] = pd.to_datetime(df[col], errors='coerce')
            elif col == 'deleted_at' and col in df.columns:
                # For deleted_at, use the Unix epoch (1970-01-01) as default
                logger.info(f"Setting default value for DataFrame column {col}")
                df[col] = pd.to_datetime(df[col], errors='coerce').fillna(pd.Timestamp('1970-01-01'))
        
        # Now load the data
        engine = create_engine(
            f"postgresql://{POSTGRES_CONFIG['user']}:{POSTGRES_CONFIG['password']}@{POSTGRES_CONFIG['host']}:{POSTGRES_CONFIG['port']}/{POSTGRES_CONFIG['dbname']}"
        )
        
        # Check required columns
        required_columns = ['id', 'first_name', 'last_name', 'created_at', 'updated_at']
        missing_columns = [col for col in required_columns if col not in df.columns]
         
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Load data to PostgreSQL
        df.to_sql(target_table, engine, schema='public', if_exists='append', index=False)
        
        logger.info(f"Loaded {len(df)} rows to PostgreSQL table public.{target_table}")
        return True
    except Exception as e:
        logger.error(f"Error loading data to {target_table}: {e}")
        raise

def main():
    """Execute ETL for ltp_people to mom"""
    try:
        # Extract
        people_df, params_df = extract_mysql_data()
        
        # Transform
        mom_df = transform_data(people_df, params_df)
        
        # Print sample of transformed data
        logger.info(f"Transformed data sample:\n{mom_df.head()}")
        
        # Load
        load_result = load_to_postgres(mom_df, 'Mom')
        
        if load_result:
            logger.info("ETL process completed successfully")
        
    except Exception as e:
        logger.error(f"ETL process failed: {e}")

if __name__ == "__main__":
    main()




## ltp_resources to Document

id_resource : id
resource_name : document_name
resource_content : filecontents
resource_filename : filename
resource_format : mimeType
resource_desc_short and resource_desc_long : description
url_download : external_url_c
date_add : created_at
date_mod : updated_at
date_removed : deleted_at
url_download : s3_file_name


## ETl fro ltp_resources to Document

#!/usr/bin/env python3
import pandas as pd
import pymysql
import psycopg2
from sqlalchemy import create_engine
import logging
import uuid
from datetime import datetime

def int_to_uuid(int_id):
    """Convert integer ID to UUID using a fixed namespace."""
    namespace = uuid.NAMESPACE_DNS
    return str(uuid.uuid5(namespace, str(int_id)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection parameters
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'db': 'my_app_db',
    'charset': 'utf8mb4',
    'port': 3306
}

POSTGRES_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',
    'password': 'root',
    'dbname': 'postgres',
    'port': '5432'
}

# Table mapping
TABLE_MAPPING = {
    'ltp_resources': 'Document'
}

def connect_mysql():
    """Connect to MySQL database."""
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        logger.info("Connected to MySQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to MySQL: {e}")
        raise

def connect_postgres():
    """Connect to PostgreSQL database."""
    try:
        conn = psycopg2.connect(**POSTGRES_CONFIG)
        logger.info("Connected to PostgreSQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to PostgreSQL: {e}")
        raise

def extract_mysql_table(conn, table_name):
    """Extract data from MySQL table."""
    try:
        engine = create_engine(
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['db']}?charset={MYSQL_CONFIG['charset']}"
        )
        query = f"SELECT * FROM {table_name}"
        df = pd.read_sql(query, engine)
        logger.info(f"Extracted {len(df)} rows from MySQL table {table_name}")

        # Ensure all needed columns are present
        needed_columns = [
            'id_resource', 'resource_name', 'resource_content', 'resource_filename',
            'resource_format', 'resource_desc_short', 'resource_desc_long',
            'url_download', 'date_add', 'date_mod', 'date_removed'
        ]
        for col in needed_columns:
            if col not in df.columns:
                df[col] = None

        return df
    except Exception as e:
        logger.error(f"Error extracting data from {table_name}: {e}")
        raise

def transform_data(df, source_table, target_table):
    """Transform data from MySQL ltp_resources to PostgreSQL Document."""
    logger.info(f"Transforming data from {source_table} to {target_table}")

    # Get PostgreSQL table schema
    postgres_conn = connect_postgres()
    cursor = postgres_conn.cursor()
    cursor.execute("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = %s
        ORDER BY ordinal_position
    """, (target_table,))
    pg_columns = [row[0] for row in cursor.fetchall()]
    logger.info(f"Target PostgreSQL columns: {pg_columns}")
    cursor.close()
    postgres_conn.close()

    # Create new DataFrame for transformed data
    new_df = pd.DataFrame()

    # Column mappings
    column_mapping = {
        'id_resource': 'id',
        'resource_name': 'document_name',
        'resource_content': 'filecontents',
        'resource_filename': 'filename',
        'resource_format': 'mimeType',
        'url_download': 'external_url_c',
        'date_add': 'created_at',
        'date_mod': 'updated_at',
        'date_removed': 'deleted_at'
    }

    # Map columns
    for mysql_col, pg_col in column_mapping.items():
        if mysql_col in df.columns and pg_col in pg_columns:
            if mysql_col == 'id_resource':
                new_df[pg_col] = df[mysql_col].apply(lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else str(uuid.uuid4()))
            elif mysql_col in ['date_add', 'date_mod', 'date_removed']:
                new_df[pg_col] = pd.to_datetime(df[mysql_col], unit='s', errors='coerce')
            elif mysql_col == 'resource_format':
                # Map resource_format to mimeType (e.g., 'PDF' to 'application/pdf')
                mime_mapping = {
                    'pdf': 'application/pdf',
                    'doc': 'application/msword',
                    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'txt': 'text/plain',
                    'jpg': 'image/jpeg',
                    'png': 'image/png'
                }
                new_df[pg_col] = df[mysql_col].str.lower().map(mime_mapping).fillna(df[mysql_col])
            else:
                new_df[pg_col] = df[mysql_col]

    # Handle s3_file_name from url_download (only for specific S3 URLs)
    if 's3_file_name' in pg_columns:
        base_url = 'https://famresources.s3.amazonaws.com/'
        new_df['s3_file_name'] = df['url_download'].apply(
            lambda x: x[len(base_url):] if isinstance(x, str) and x.startswith(base_url) else None
        )
        # Log rows with extracted s3_file_name
        extracted_s3_files = new_df[new_df['s3_file_name'].notnull()][['id', 's3_file_name']]
        if not extracted_s3_files.empty:
            logger.info(f"Extracted s3_file_name for {len(extracted_s3_files)} rows: {extracted_s3_files.to_dict('records')}")
        # Log rows with non-S3 URLs set to None
        non_s3_urls = df[(df['url_download'].notnull()) & (df['url_download'] != '') & (~df['url_download'].str.startswith(base_url, na=False))][['id_resource']]
        if not non_s3_urls.empty:
            non_s3_urls = non_s3_urls.merge(df[['id_resource', 'url_download']], on='id_resource', how='left')
            non_s3_urls = non_s3_urls.rename(columns={'id_resource': 'id'})
            non_s3_urls['id'] = non_s3_urls['id'].apply(lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else str(uuid.uuid4()))
            logger.info(f"Set s3_file_name to None for {len(non_s3_urls)} non-S3 URLs: {non_s3_urls[['id', 'url_download']].to_dict('records')}")

    # Handle description (merge resource_desc_short and resource_desc_long)
    if 'description' in pg_columns:
        new_df['description'] = df.apply(
            lambda row: (
                f"{row['resource_desc_short']}\n\n{row['resource_desc_long']}"
                if pd.notnull(row['resource_desc_short']) and pd.notnull(row['resource_desc_long'])
                else row['resource_desc_short'] if pd.notnull(row['resource_desc_short'])
                else row['resource_desc_long'] if pd.notnull(row['resource_desc_long'])
                else None
            ),
            axis=1
        )

    # Ensure NOT NULL columns have valid values
    if 'id' in pg_columns:
        new_df['id'] = new_df['id'].fillna(pd.Series([str(uuid.uuid4()) for _ in range(len(new_df))], index=new_df.index))

    if 'created_at' in pg_columns:
        new_df['created_at'] = new_df['created_at'].fillna(pd.Timestamp.now())

    if 'updated_at' in pg_columns:
        new_df['updated_at'] = new_df['updated_at'].fillna(pd.Timestamp.now())

    if 'deleted_at' in pg_columns:
        new_df['deleted_at'] = new_df['deleted_at'].fillna(pd.Timestamp('1970-01-01'))
        default_deleted_at = new_df[new_df['deleted_at'] == pd.Timestamp('1970-01-01')][['id', 'deleted_at']]
        if not default_deleted_at.empty:
            logger.warning(f"Set default deleted_at for {len(default_deleted_at)} rows: {default_deleted_at.to_dict('records')}")

    # Set default values for other Document columns
    for col in pg_columns:
        if col not in new_df.columns:
            if col in ['document_name', 'filecontents', 'filename', 'mimeType', 'description',
                       'external_url_c', 'subcategory_id', 'mom_id', 'created_by_id',
                       'created_by_name', 'advocate_id', 'coordinator_id', 'lesson_id',
                       'lesson_template_id', 's3_file_name']:
                new_df[col] = None
            elif col == 'is_primary_lesson_resource':
                new_df[col] = False

    logger.info(f"Transformed DataFrame has {len(new_df.columns)} columns: {new_df.columns.tolist()}")
    return new_df

def load_to_postgres(df, target_table):
    """Load transformed data to PostgreSQL."""
    try:
        engine = create_engine(
            f'postgresql://{POSTGRES_CONFIG["user"]}:{POSTGRES_CONFIG["password"]}@{POSTGRES_CONFIG["host"]}:{POSTGRES_CONFIG["port"]}/{POSTGRES_CONFIG["dbname"]}'
        )
        logger.info(f"DataFrame to load has {len(df)} rows and columns: {df.columns.tolist()}")

        # Check for required columns
        required_columns = ['id', 'created_at', 'updated_at', 'deleted_at', 'is_primary_lesson_resource']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            raise ValueError(f"Missing required columns: {missing_columns}")

        # Load data to PostgreSQL
        schema = 'public'
        df.to_sql(target_table, engine, schema=schema, if_exists='append', index=False)

        logger.info(f"Loaded {len(df)} rows to PostgreSQL table {schema}.{target_table}")
        return True
    except Exception as e:
        logger.error(f"Error loading data to {target_table}: {e}")
        raise

def main():
    """Run ETL for ltp_resources to Document."""
    mysql_conn = None
    try:
        mysql_conn = connect_mysql()
        source_table = 'ltp_resources'
        target_table = TABLE_MAPPING[source_table]

        # Extract
        df = extract_mysql_table(mysql_conn, source_table)

        # Transform
        df_transformed = transform_data(df, source_table, target_table)

        # Log sample
        logger.info(f"Transformed data sample:\n{df_transformed.head()}")

        # Load
        load_result = load_to_postgres(df_transformed, target_table)

        if load_result:
            logger.info("ETL process completed successfully")

    except Exception as e:
        logger.error(f"ETL process failed: {e}")
    finally:
        if mysql_conn:
            mysql_conn.close()

if __name__ == "__main__":
    main()




## ------------------------------------------------

## Next Columns

## Mapped Columns  (ltp_roles : Role)


id_role : id
role_name : name
role_desc : description

## ETL for ltp_roles to Role:

#!/usr/bin/env python3
import pandas as pd
import pymysql
import psycopg2
from sqlalchemy import create_engine
import logging
import uuid
from datetime import datetime

def int_to_uuid(int_id):
    """Convert integer ID to UUID using a fixed namespace."""
    namespace = uuid.NAMESPACE_DNS
    return str(uuid.uuid5(namespace, str(int_id)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection parameters
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'db': 'my_app_db',
    'charset': 'utf8mb4',
    'port': 3306
}

POSTGRES_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',
    'password': 'root',
    'dbname': 'postgres',
    'port': '5432'
}

# Table mapping
TABLE_MAPPING = {
    'ltp_roles': 'Role'
}

def connect_mysql():
    """Connect to MySQL database."""
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        logger.info("Connected to MySQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to MySQL: {e}")
        raise

def connect_postgres():
    """Connect to PostgreSQL database."""
    try:
        conn = psycopg2.connect(**POSTGRES_CONFIG)
        logger.info("Connected to PostgreSQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to PostgreSQL: {e}")
        raise

def extract_mysql_table(conn, table_name):
    """Extract data from MySQL table."""
    try:
        engine = create_engine(
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['db']}?charset={MYSQL_CONFIG['charset']}"
        )
        query = f"SELECT * FROM {table_name}"
        df = pd.read_sql(query, engine)
        logger.info(f"Extracted {len(df)} rows from MySQL table {table_name}")

        # Ensure all needed columns are present
        needed_columns = [
            'id_role', 'role_name', 'role_desc'
        ]
        for col in needed_columns:
            if col not in df.columns:
                df[col] = None

        return df
    except Exception as e:
        logger.error(f"Error extracting data from {table_name}: {e}")
        raise

def transform_data(df, source_table, target_table):
    """Transform data from MySQL ltp_roles to PostgreSQL Role."""
    logger.info(f"Transforming data from {source_table} to {target_table}")

    # Get PostgreSQL table schema
    postgres_conn = connect_postgres()
    cursor = postgres_conn.cursor()
    cursor.execute("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = %s
        ORDER BY ordinal_position
    """, (target_table,))
    pg_columns = [row[0] for row in cursor.fetchall()]
    logger.info(f"Target PostgreSQL columns: {pg_columns}")
    cursor.close()
    postgres_conn.close()

    # Create new DataFrame for transformed data
    new_df = pd.DataFrame()

    # Column mappings
    column_mapping = {
        'id_role': 'id',
        'role_name': 'name',
        'role_desc': 'description'
    }

    # Map columns
    for mysql_col, pg_col in column_mapping.items():
        if mysql_col in df.columns and pg_col in pg_columns:
            if mysql_col == 'id_role':
                new_df[pg_col] = df[mysql_col].apply(lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else str(uuid.uuid4()))
            else:
                new_df[pg_col] = df[mysql_col]

    # Handle key (derive from role_name)
    if 'key' in pg_columns:
        new_df['key'] = df['role_name'].apply(
            lambda x: x.lower().replace(' ', '_') if pd.notnull(x) and isinstance(x, str) and x.strip() else None
        )

    # Ensure NOT NULL columns have valid values
    if 'id' in pg_columns:
        new_df['id'] = new_df['id'].fillna(pd.Series([str(uuid.uuid4()) for _ in range(len(new_df))], index=new_df.index))

    if 'name' in pg_columns:
        invalid_name_mask = new_df['name'].isna() | (new_df['name'] == '')
        new_df.loc[invalid_name_mask, 'name'] = df.loc[invalid_name_mask, 'id_role'].apply(lambda x: f"role_{x}" if pd.notnull(x) else f"role_{uuid.uuid4().hex[:8]}")
        generated_names = new_df[invalid_name_mask][['id', 'name']]
        if not generated_names.empty:
            logger.warning(f"Generated names for {len(generated_names)} rows: {generated_names.to_dict('records')}")

    if 'key' in pg_columns:
        invalid_key_mask = new_df['key'].isna() | (new_df['key'] == '')
        new_df.loc[invalid_key_mask, 'key'] = new_df.loc[invalid_key_mask, 'name'].apply(lambda x: x.lower().replace(' ', '_'))
        generated_keys = new_df[invalid_key_mask][['id', 'key']]
        if not generated_keys.empty:
            logger.warning(f"Generated keys for {len(generated_keys)} rows: {generated_keys.to_dict('records')}")

    if 'created_at' in pg_columns:
        new_df['created_at'] = pd.Timestamp.now()

    if 'updated_at' in pg_columns:
        new_df['updated_at'] = pd.Timestamp.now()

    if 'deleted_at' in pg_columns:
        new_df['deleted_at'] = pd.Timestamp('1970-01-01')
        default_deleted_at = new_df[new_df['deleted_at'] == pd.Timestamp('1970-01-01')][['id', 'deleted_at']]
        if not default_deleted_at.empty:
            logger.warning(f"Set default deleted_at for {len(default_deleted_at)} rows: {default_deleted_at.to_dict('records')}")

    # Set default values for other Role columns
    for col in pg_columns:
        if col not in new_df.columns:
            if col in ['description', 'created_by_id', 'created_by_name']:
                new_df[col] = None

    logger.info(f"Transformed DataFrame has {len(new_df.columns)} columns: {new_df.columns.tolist()}")
    return new_df

def load_to_postgres(df, target_table):
    """Load transformed data to PostgreSQL."""
    try:
        engine = create_engine(
            f'postgresql://{POSTGRES_CONFIG["user"]}:{POSTGRES_CONFIG["password"]}@{POSTGRES_CONFIG["host"]}:{POSTGRES_CONFIG["port"]}/{POSTGRES_CONFIG["dbname"]}'
        )
        logger.info(f"DataFrame to load has {len(df)} rows and columns: {df.columns.tolist()}")

        # Check for required columns
        required_columns = ['id', 'created_at', 'updated_at', 'name', 'key', 'deleted_at']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            raise ValueError(f"Missing required columns: {missing_columns}")

        # Load data to PostgreSQL
        schema = 'public'
        df.to_sql(target_table, engine, schema=schema, if_exists='append', index=False)

        logger.info(f"Loaded {len(df)} rows to PostgreSQL table {schema}.{target_table}")
        return True
    except Exception as e:
        logger.error(f"Error loading data to {target_table}: {e}")
        raise

def main():
    """Run ETL for ltp_roles to Role."""
    mysql_conn = None
    try:
        mysql_conn = connect_mysql()
        source_table = 'ltp_roles'
        target_table = TABLE_MAPPING[source_table]

        # Extract
        df = extract_mysql_table(mysql_conn, source_table)

        # Transform
        df_transformed = transform_data(df, source_table, target_table)

        # Log sample
        logger.info(f"Transformed data sample:\n{df_transformed.head()}")

        # Load
        load_result = load_to_postgres(df_transformed, target_table)

        if load_result:
            logger.info("ETL process completed successfully")

    except Exception as e:
        logger.error(f"ETL process failed: {e}")
    finally:
        if mysql_conn:
            mysql_conn.close()

if __name__ == "__main__":
    main()


## ------------------------------------------------


# Mapped columns (ltp_affiliates : AffiliateAgency)

 : id (this need to be generated as uuid every time)
date_removed : deleted_at,
date_add : created_at,
date_mod : updated_at,
id_affiliate : affiliate_id,
id_agency : agency_id


```
# ETL file.

#!/usr/bin/env python3
import pandas as pd
import pymysql
import psycopg2
from sqlalchemy import create_engine
import logging
import uuid
from datetime import datetime

def int_to_uuid(int_id):
    """Convert integer ID to UUID using a fixed namespace."""
    namespace = uuid.NAMESPACE_DNS
    return str(uuid.uuid5(namespace, str(int_id)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection parameters
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'db': 'my_app_db',
    'charset': 'utf8mb4',
    'port': 3306
}

POSTGRES_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',
    'password': 'root',
    'dbname': 'postgres',
    'port': '5432'
}

# Table mapping
TABLE_MAPPING = {
    'ltp_affiliates': 'AffiliateAgency'
}

def connect_mysql():
    """Connect to MySQL database."""
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        logger.info("Connected to MySQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to MySQL: {e}")
        raise

def connect_postgres():
    """Connect to PostgreSQL database."""
    try:
        conn = psycopg2.connect(**POSTGRES_CONFIG)
        logger.info("Connected to PostgreSQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to PostgreSQL: {e}")
        raise

def extract_mysql_table(conn, table_name):
    """Extract data from MySQL table."""
    try:
        engine = create_engine(
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['db']}?charset={MYSQL_CONFIG['charset']}"
        )
        query = f"SELECT * FROM {table_name}"
        df = pd.read_sql(query, engine)
        logger.info(f"Extracted {len(df)} rows from MySQL table {table_name}")

        # Ensure all needed columns are present
        needed_columns = [
            'id_affiliate', 'id_agency', 'date_add', 'date_mod', 'date_removed'
        ]
        for col in needed_columns:
            if col not in df.columns:
                df[col] = None

        return df
    except Exception as e:
        logger.error(f"Error extracting data from {table_name}: {e}")
        raise

def transform_data(df, source_table, target_table):
    """Transform data from MySQL ltp_affiliates to PostgreSQL AffiliateAgency."""
    logger.info(f"Transforming data from {source_table} to {target_table}")

    # Get PostgreSQL table schema
    postgres_conn = connect_postgres()
    cursor = postgres_conn.cursor()
    cursor.execute("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = %s
        ORDER BY ordinal_position
    """, (target_table,))
    pg_columns = [row[0] for row in cursor.fetchall()]
    logger.info(f"Target PostgreSQL columns: {pg_columns}")
    cursor.close()
    postgres_conn.close()

    # Create new DataFrame for transformed data
    new_df = pd.DataFrame()

    # Column mappings
    column_mapping = {
        'id_affiliate': 'affiliate_id',
        'id_agency': 'agency_id',
        'date_add': 'created_at',
        'date_mod': 'updated_at',
        'date_removed': 'deleted_at'
    }

    # Map columns
    for mysql_col, pg_col in column_mapping.items():
        if mysql_col in df.columns and pg_col in pg_columns:
            if mysql_col == 'id_affiliate':
                new_df[pg_col] = df[mysql_col].apply(lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else str(uuid.uuid4()))
            elif mysql_col == 'id_agency':
                new_df[pg_col] = df[mysql_col].apply(lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else str(uuid.uuid4()))
            elif mysql_col in ['date_add', 'date_mod', 'date_removed']:
                new_df[pg_col] = pd.to_datetime(df[mysql_col], unit='s', errors='coerce')

    # Generate UUID for id
    if 'id' in pg_columns:
        new_df['id'] = [str(uuid.uuid4()) for _ in range(len(df))]

    # Ensure NOT NULL columns have valid values
    if 'created_at' in pg_columns:
        new_df['created_at'] = new_df['created_at'].fillna(pd.Timestamp.now())

    if 'updated_at' in pg_columns:
        new_df['updated_at'] = new_df['updated_at'].fillna(pd.Timestamp.now())

    if 'deleted_at' in pg_columns:
        new_df['deleted_at'] = new_df['deleted_at'].fillna(pd.Timestamp('1970-01-01'))
        default_deleted_at = new_df[new_df['deleted_at'] == pd.Timestamp('1970-01-01')][['id', 'deleted_at']]
        if not default_deleted_at.empty:
            logger.warning(f"Set default deleted_at for {len(default_deleted_at)} rows: {default_deleted_at.to_dict('records')}")

    if 'affiliate_id' in pg_columns:
        new_df['affiliate_id'] = new_df['affiliate_id'].fillna(pd.Series([str(uuid.uuid4()) for _ in range(len(new_df))], index=new_df.index))

    if 'agency_id' in pg_columns:
        new_df['agency_id'] = new_df['agency_id'].fillna(pd.Series([str(uuid.uuid4()) for _ in range(len(new_df))], index=new_df.index))

    # Set default values for other AffiliateAgency columns
    for col in pg_columns:
        if col not in new_df.columns:
            if col in ['created_by_id', 'created_by_name']:
                new_df[col] = None

    logger.info(f"Transformed DataFrame has {len(new_df.columns)} columns: {new_df.columns.tolist()}")
    return new_df

def load_to_postgres(df, target_table):
    """Load transformed data to PostgreSQL."""
    try:
        engine = create_engine(
            f'postgresql://{POSTGRES_CONFIG["user"]}:{POSTGRES_CONFIG["password"]}@{POSTGRES_CONFIG["host"]}:{POSTGRES_CONFIG["port"]}/{POSTGRES_CONFIG["dbname"]}'
        )
        logger.info(f"DataFrame to load has {len(df)} rows and columns: {df.columns.tolist()}")

        # Check for required columns
        required_columns = ['id', 'deleted_at', 'created_at', 'updated_at', 'affiliate_id', 'agency_id']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            raise ValueError(f"Missing required columns: {missing_columns}")

        # Load data to PostgreSQL
        schema = 'public'
        df.to_sql(target_table, engine, schema=schema, if_exists='append', index=False)

        logger.info(f"Loaded {len(df)} rows to PostgreSQL table {schema}.{target_table}")
        return True
    except Exception as e:
        logger.error(f"Error loading data to {target_table}: {e}")
        raise

def main():
    """Run ETL for ltp_affiliates to AffiliateAgency."""
    mysql_conn = None
    try:
        mysql_conn = connect_mysql()
        source_table = 'ltp_affiliates'
        target_table = TABLE_MAPPING[source_table]

        # Extract
        df = extract_mysql_table(mysql_conn, source_table)

        # Transform
        df_transformed = transform_data(df, source_table, target_table)

        # Log sample
        logger.info(f"Transformed data sample:\n{df_transformed.head()}")

        # Load
        load_result = load_to_postgres(df_transformed, target_table)

        if load_result:
            logger.info("ETL process completed successfully")

    except Exception as e:
        logger.error(f"ETL process failed: {e}")
    finally:
        if mysql_conn:
            mysql_conn.close()

if __name__ == "__main__":
    main()
```

## ---------------------------------
## Family to Child Migration Mapping

The migration from MySQL `ltp_families` to PostgreSQL `Child` involves several steps and data transformations:

### ID Mapping Process

1. **Mom Mapping Creation**:
   - Primary contacts from `ltp_families` (via `id_people_primary`) are first mapped to `Mom` records
   - Each mapping stores the old ID (`id_people`) and new UUID in the `id_mapping` table
   - This mapping is essential for establishing parent-child relationships

2. **Child Record Generation**:
   - For each family, one or more child records are created based on available data
   - Child count is determined from JSON data in `intake_form.num_children`
   - Child names and ages are extracted from `intake_form.names_of_children` and `intake_form.ages_of_children`

### Field Mappings

| MySQL Source | PostgreSQL Target | Transformation |
|--------------|-------------------|----------------|
| Generated | `Child.id` | New UUID generated for each child |
| `ltp_families.state` | `Child.deleted_at` | 0 for active, timestamp for deleted records |
| `ltp_families.date_add` | `Child.created_at` | Convert Unix timestamp to datetime |
| `ltp_families.id_add` | `Child.created_by_id` | Convert to UUID using int_to_uuid() |
| Person name from `ltp_people` | `Child.created_by_name` | Lookup name from person record |
| `ltp_families.date_mod` | `Child.updated_at` | Convert Unix timestamp to datetime |
| `intake_form.names_of_children` | `Child.first_name` | Parse comma/semicolon separated list, generate placeholder if not available |
| `intake_form.ages_of_children` | `Child.birthdate` | Convert age to approximate birthdate |
| Default value | `Child.gender` | Default to 'unknown' |
| `ltp_families.family_structure` | `Child.lives_with` | Map values (e.g., "single_mom" → "mother") |
| `intake_form.custody` | `Child.legal_custody_status` | Map values (e.g., "in_foster_care" → "temporary_custody") |
| `intake_form.father_involved` | `Child.father_involved` | Convert to PostgreSQL array format |
| `intake_form.father_involved` | `Child.father_involvement` | Text description of father involvement |
| Empty string | `Child.additional_info` | Default to empty string |
| `ltp_families.id_people_primary` | `Child.mom_id` | Lookup UUID from `id_mapping` table; create placeholder Mom if not found |
| Default value | `Child.active_child_welfare_involvement` | Default to NULL |
| Default value | `Child.date_of_child_welfare_involvement` | Default to NULL |
| Default value | `Child.family_preservation_goal` | Default to NULL |
| Default value | `Child.family_preservation_impact` | Default to NULL |

### Special Handling

1. **Multiple Children**:
   - When a family has multiple children, separate records are created for each
   - If child names are available, they're distributed to the records
   - If names are not available, a placeholder name is generated (e.g., "Child_1a2b3c4d")
   - This ensures the NOT NULL constraint on the `first_name` column is satisfied

2. **Mom ID Requirement**:
   - The `mom_id` column has a NOT NULL constraint
   - If no mapping exists for the family's primary contact, a placeholder Mom record is created
   - Placeholder Moms have names like "Placeholder_Mom_[family_id]"
   - This ensures all children have a valid Mom reference

3. **Father Involvement**:
   - Parsed from `intake_form.father_involved` field
   - Converted to PostgreSQL array format for the `father_involved` column
   - Additional context stored in `father_involvement` text field
   - Common values include: 'not_involved', 'unknown', 'decision_making', 'financial_support', 'physical_presence', 'emotional_support'

4. **Lives With Mapping**:
   - 'single_mom' → 'mother'
   - 'foster_care' → 'foster_care'
   - 'grandparents' → 'grandparents'
   - Other values → 'other_family'

5. **Legal Custody Status Mapping**:
   - 'in_foster_care' → 'temporary_custody'
   - 'no_custody' → 'no_custody'
   - Other values → 'other'

6. **Deleted Records**:
   - The `deleted_at` column uses a bigint timestamp (milliseconds since epoch)
   - Active records use 0, deleted records use current timestamp
   - Records are considered deleted if `ltp_families.state` <= 0

7. **Data Type Conversions**:
   - String IDs are converted to UUIDs
   - Unix timestamps are converted to PostgreSQL timestamps
   - Text arrays are formatted as PostgreSQL arrays with curly braces

### Implementation Notes

The migration process handles several edge cases:
- Missing or invalid JSON data
- Inconsistent child count vs. actual names/ages provided
- Various formats for separating child names (commas, semicolons, newlines)
- Ensuring not-null constraints are satisfied for required fields
- Approximate age calculation when only age (not birthdate) is available
- Default values for fields with no direct source in MySQL

### Code Implementation

The key functions in the migration script include:

1. `extract_children_data`: Parses JSON data to extract child names and ages
2. `transform_family_to_children`: Creates child records from family data
3. `load_children`: Inserts transformed child records into PostgreSQL
4. `create_mom_mappings`: Creates Mom records and ID mappings for primary contacts
5. `get_id_mapping`: Retrieves UUID mappings for old integer IDs



ltp_affiliates -few columns migration -done
ltp_affiliate_params  - contains 41 records -  not sure about postgres table
ltp_agreements - contains no record
ltp_agreements_signature contains contains 190 records, id_agreement but when I look into ltp_agreement table, there is no records, it means there's problem with dump.
ltp_assignments - contains 51601 records -  not sure about postgres table
ltp_certs - contain 3 record - not sure about postgres table
ltp_churches - contain 18 record - not sure about postgres table
ltp_churches_params - contains 27 records, not sure about postgres table
ltp_email_queue - contains no record
ltp_events - contains no record
ltp_families - contains 1982 records - few columns migration -done
ltp_lms_coursework contains 2557 records -  not sure about postgres table
ltp_messages - contains 12952 recprds - few columns migration -done
ltp_messaging_templates - contains no record
ltp_people - few columns migration -done
ltp_people_params - few columns migration -done
ltp_places - contains no record
ltp_places_zipcode_data - contains no record
ltp_resources -few columns migration -done (s3 bucket task remaining)
ltp_roles -few columns migration -done
ltp_rsvps - contains no record
