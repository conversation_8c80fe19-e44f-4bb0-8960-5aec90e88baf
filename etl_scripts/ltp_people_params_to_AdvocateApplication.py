#!/usr/bin/env python3
import logging
import uuid
import json
import pandas as pd
import pymysql
import psycopg2
from sqlalchemy import create_engine

# ——— CONFIG & LOGGING ——————————————————————————————————————————————
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s'
)
logger = logging.getLogger(__name__)

MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'db': 'my_app_db',
    'charset': 'utf8mb4',
    'port': 3306
}

POSTGRES_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',
    'password': 'root',
    'dbname': 'postgres',
    'port': '5432'
}

# ——— HELPERS ——————————————————————————————————————————————————————
def safe_json(val):
    if not isinstance(val, str):
        return val
    try:
        return json.loads(val)
    except json.JSONDecodeError:
        return val

def int_to_uuid(val):
    try:
        return str(uuid.uuid5(uuid.NAMESPACE_DNS, str(int(val))))
    except Exception:
        logger.warning(f"Invalid int for UUID5: {val!r}, falling back to random UUID4")
        return str(uuid.uuid4())

def app_uuid(src_id):
    return str(uuid.uuid5(uuid.NAMESPACE_DNS, f"advapp-{src_id}"))

def connect_mysql():
    return pymysql.connect(**MYSQL_CONFIG)

def connect_postgres():
    return psycopg2.connect(**POSTGRES_CONFIG)

def get_existing_app_ids():
    conn = connect_postgres()
    with conn.cursor() as cur:
        cur.execute('SELECT id FROM public."AdvocateApplication"')
        ids = {row[0] for row in cur.fetchall()}
    conn.close()
    logger.info(f"Found {len(ids)} existing AdvocateApplication IDs")
    return ids

# ——— EXTRACT ——————————————————————————————————————————————————————
def extract():
    mysql_url = (
        f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@"
        f"{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['db']}?"
        f"charset={MYSQL_CONFIG['charset']}"
    )
    engine = create_engine(mysql_url)

    # 1) workflow params
    df = pd.read_sql("""
        SELECT
          id_people_param,
          id_people,
          param_value,
          id_add,
          id_mod,
          date_add,
          date_mod
        FROM ltp_people_params
        WHERE param_type = 'workflow_people'
          AND param_name  = 'advocate_onboarding_ema'
    """, engine)
    logger.info(f"Extracted {len(df)} workflow_people rows")

    # 2) collect all people IDs we need: applicants + id_add + id_mod
    all_ids = set(df['id_people'].dropna().astype(int).tolist())
    all_ids.update(df['id_add'].dropna().astype(int).tolist())
    all_ids.update(df['id_mod'].dropna().astype(int).tolist())

    if all_ids:
        ids_csv = ",".join(str(i) for i in sorted(all_ids))
        people = pd.read_sql(f"""
            SELECT
              id_people,
              name_first,
              name_last,
              people_email_primary,
              people_phone_mobile,
              address_street_1,
              address_street_2,
              address_city,
              address_state,
              address_zip,
              people_dob
            FROM ltp_people
            WHERE id_people IN ({ids_csv})
        """, engine)
        ppl = people.set_index('id_people').to_dict(orient='index')

        # split into two lookups:
        #  a) applicant fields (everything already in ppl)
        applicant_lookup = ppl

        #  b) full name for creators/modifiers
        name_lookup = {
            pid: f"{info['name_first'] or ''} {info['name_last'] or ''}".strip()
            for pid, info in ppl.items()
        }

        logger.info(f"Fetched {len(ppl)} people records")
    else:
        applicant_lookup = {}
        name_lookup = {}
        logger.info("No people IDs to look up")

    return df, applicant_lookup, name_lookup

# ——— TRANSFORM ————————————————————————————————————————————————————
def transform(df, applicant_lookup, name_lookup):
    existing = get_existing_app_ids()
    rows = []

    for _, src in df.iterrows():
        app_id = app_uuid(src['id_people_param'])
        if app_id in existing:
            continue

        # Base IDs & timestamps
        mom_uuid   = int_to_uuid(src['id_people'])
        created_at = pd.to_datetime(src['date_add'], unit='s', errors='coerce')
        updated_at = pd.to_datetime(src['date_mod'], unit='s', errors='coerce') or created_at
        created_by = int_to_uuid(src['id_add']) if pd.notnull(src['id_add']) else None
        updated_by = int_to_uuid(src['id_mod']) if pd.notnull(src['id_mod']) else None

        # creator and modifier names
        created_by_name = name_lookup.get(int(src['id_add']), '')
        updated_by_name = name_lookup.get(int(src['id_mod']), '')

        # Applicant personal fields
        rec = applicant_lookup.get(int(src['id_people']), {})
        first_name = rec.get('name_first') or ''
        last_name  = rec.get('name_last')  or ''
        email      = rec.get('people_email_primary') or ''
        phone      = rec.get('people_phone_mobile') or ''
        dob        = rec.get('people_dob') 
        date_of_birth = (
        pd.to_datetime(dob, unit='s', errors='coerce')
        if pd.notnull(dob) else None
    )

        # address
        s1 = rec.get('address_street_1') or ''
        s2 = rec.get('address_street_2') or ''
        address_street = (s1 + ' ' + s2).strip()
        address_city    = rec.get('address_city')  or ''
        address_state   = rec.get('address_state') or ''
        address_postal  = rec.get('address_zip')   or ''

        # JSON payload
        payload = safe_json(src['param_value'])
        if not isinstance(payload, dict):
            continue
        status, roadmap = payload.get('status', {}), payload.get('roadmap', [])

        # last_step int coercion
        raw_last = status.get('last_step_completed')
        try:
            last_step = int(raw_last) if raw_last is not None else 0
        except (ValueError, TypeError):
            last_step = 0

        # map to enum
        if last_step >= 70:
            app_status = 'Approved'
        elif last_step >= 60:
            app_status = 'Rejected'
        else:
            app_status = 'Pending'

        # helper for completion timestamps
        def ts(step):
            info = status.get(str(step), {})
            dc   = info.get('date_complete')
            return pd.to_datetime(dc, unit='s') if dc else None

        can_bg = any(item.get('step') == 50 for item in roadmap)

        rows.append({
            # identifiers & audit
            'id':                   app_id,
            'deleted_at':           0,
            'created_at':           created_at,
            'updated_at':           updated_at,
            'created_by_id':        created_by,
            'created_by_name':      created_by_name,
            'updated_by_id':        updated_by,
            'updated_by_name':      updated_by_name,

            # personal info
            'firstName':            first_name,
            'lastName':             last_name,
            'email':                email,
            'phone':                phone,
            'addressStreet':        address_street,
            'addressCity':          address_city,
            'addressState':         address_state,
            'addressPostalcode':    address_postal,
            'dateOfBirth':          date_of_birth,

            # application fields
            'userId':               mom_uuid,
            'status':               app_status,
            'canCompleteBackground':can_bg,
            'interviewDate':        ts(20),
            'reviewedAt':           ts(60),
            'completedAt':          ts(70),
        })

    out = pd.DataFrame(rows)
    logger.info(f"Prepared {len(out)} new AdvocateApplication rows")
    return out

# ——— LOAD ——————————————————————————————————————————————————————
def load(df: pd.DataFrame):
    pg_url = (
        f"postgresql://{POSTGRES_CONFIG['user']}:{POSTGRES_CONFIG['password']}@"
        f"{POSTGRES_CONFIG['host']}:{POSTGRES_CONFIG['port']}/"
        f"{POSTGRES_CONFIG['dbname']}"
    )
    engine = create_engine(pg_url)
    df.to_sql(
        'AdvocateApplication', engine,
        schema='public',
        if_exists='append',
        index=False
    )
    logger.info(f"Inserted {len(df)} rows into AdvocateApplication")

# ——— MAIN ——————————————————————————————————————————————————————
def main():
    try:
        raw_df, applicant_lookup, name_lookup = extract()
        transformed = transform(raw_df, applicant_lookup, name_lookup)
        if not transformed.empty:
            load(transformed)
            logger.info("AdvocateApplication ETL completed successfully")
        else:
            logger.info("No new AdvocateApplication rows to migrate.")
    except Exception:
        logger.exception("AdvocateApplication ETL failed!")
        raise

if __name__ == "__main__":
    main()
