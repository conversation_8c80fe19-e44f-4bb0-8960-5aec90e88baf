#!/usr/bin/env python3
import pandas as pd
import pymysql
import psycopg2
from sqlalchemy import create_engine
import logging
import uuid
from datetime import datetime

def int_to_uuid(int_id):
    """Convert integer ID to deterministic UUID using a fixed namespace."""
    namespace = uuid.NAMESPACE_DNS
    return str(uuid.uuid5(namespace, str(int_id)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection parameters
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'db': 'my_app_db',
    'charset': 'utf8mb4',
    'port' : 3306
}
POSTGRES_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',  # Use the role you created
    'password': 'root',  # Use the password you set
    'dbname': 'postgres',
    'port': '5432'
}

def connect_mysql():
    """Connect to MySQL database."""
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        logger.info("Connected to MySQL database")
        return conn
    except pymysql.Error as e:
        logger.error(f"Error connecting to MySQL: {e}")
        raise

def connect_postgres():
    """Connect to PostgreSQL database."""
    try:
        conn = psycopg2.connect(**POSTGRES_CONFIG)
        logger.info("Connected to PostgreSQL database")
        return conn
    except psycopg2.Error as e:
        logger.error(f"Error connecting to PostgreSQL: {e}")
        raise

def extract_mysql_table(conn, table_name):
    """Extract data from MySQL table."""
    try:
        engine = create_engine(
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['db']}?charset={MYSQL_CONFIG['charset']}"
        )
        query = f"""
        SELECT 
            id_affiliate, affiliate_name, affiliate_desc, affiliate_address_street,
            affiliate_address_city, affiliate_address_state, affiliate_address_zip,
            affiliate_address_country, afflliate_contact_phone, affiliate_website,
            affiliate_contact_email, date_add, date_mod, date_removed,
            affiliate_name_short, affiliate_contact_name, state
        FROM {table_name}
        """
        df = pd.read_sql(query, engine)
        logger.info(f"Extracted {len(df)} rows from MySQL table {table_name}")
        
        # Ensure all needed columns exist
        needed_columns = [
            'id_affiliate', 'affiliate_name', 'affiliate_desc', 'affiliate_address_street',
            'affiliate_address_city', 'affiliate_address_state', 'affiliate_address_zip',
            'affiliate_address_country', 'afflliate_contact_phone', 'affiliate_website',
            'affiliate_contact_email', 'date_add', 'date_mod', 'date_removed',
            'affiliate_name_short', 'affiliate_contact_name', 'state'
        ]
        for col in needed_columns:
            if col not in df.columns:
                df[col] = None
        
        return df
    except Exception as e:
        logger.error(f"Error extracting data from {table_name}: {e}")
        raise

def transform_data(df, source_table, target_table):
    """Transform data from MySQL ltp_affiliates to PostgreSQL Affiliate schema."""
    logger.info(f"Transforming data from {source_table} to {target_table}")
    
    # Get PostgreSQL table schema
    postgres_conn = connect_postgres()
    cursor = postgres_conn.cursor()
    cursor.execute("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = %s
        ORDER BY ordinal_position
    """, (target_table,))

    pg_columns = [row[0] for row in cursor.fetchall()]
    logger.info(f"Target PostgreSQL columns: {pg_columns}")
    cursor.close()
    postgres_conn.close()
    
    # Define column mapping
    column_mapping = {
        'id_affiliate': 'id',
        'affiliate_name': 'name',
        'affiliate_desc': 'description',
        'affiliate_address_street': 'billing_address_street',
        'affiliate_address_city': 'billing_address_city',
        'affiliate_address_state': 'billing_address_state',
        'affiliate_address_zip': 'billing_address_postalcode',
        'affiliate_address_country': 'billing_address_country',
        'afflliate_contact_phone': 'phone_office',
        'affiliate_website': 'website',
        'affiliate_contact_email': 'email1',
        'date_add': 'created_at',
        'date_mod': 'updated_at',
        'date_removed': 'deleted_at',
        'affiliate_name_short': 'agency_nickname',
        'affiliate_contact_name': 'contact_name',
        'state': 'status'
    }
    
    # Create new DataFrame for transformed data
    new_df = pd.DataFrame()
    
    # Map columns
    for mysql_col, pg_col in column_mapping.items():
        if mysql_col in df.columns and pg_col in pg_columns:
            if mysql_col == 'id_affiliate':
                new_df[pg_col] = df[mysql_col].apply(lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else str(uuid.uuid4()))
            elif mysql_col in ['date_add', 'date_mod', 'date_removed']:
                new_df[pg_col] = pd.to_datetime(df[mysql_col], unit='s', errors='coerce')
            elif mysql_col == 'state':
                new_df[pg_col] = df[mysql_col].apply(lambda x: 'Active' if pd.notnull(x) and x == 1 else 'Inactive')
            else:
                new_df[pg_col] = df[mysql_col]
    
    # Handle NULLs and required fields
    if 'id' in pg_columns:
        new_df['id'] = new_df['id'].fillna(pd.Series([str(uuid.uuid4()) for _ in range(len(new_df))], index=new_df.index))
        generated_ids = new_df[new_df['id'].str.startswith('uuid')][['id']]
        if not generated_ids.empty:
            logger.warning(f"Generated IDs for {len(generated_ids)} rows: {generated_ids.to_dict('records')}")
    
    if 'name' in pg_columns:
        new_df['name'] = new_df['name'].fillna('')
        empty_names = new_df[new_df['name'] == ''][['id', 'name']]
        if not empty_names.empty:
            logger.warning(f"Set empty name for {len(empty_names)} rows: {empty_names.to_dict('records')}")
    
    if 'created_at' in pg_columns:
        new_df['created_at'] = new_df['created_at'].fillna(pd.Timestamp.now())
        default_created = new_df[new_df['created_at'] == pd.Timestamp.now()][['id', 'created_at']]
        if not default_created.empty:
            logger.warning(f"Set default created_at for {len(default_created)} rows: {default_created.to_dict('records')}")
    
    if 'updated_at' in pg_columns:
        new_df['updated_at'] = new_df['updated_at'].fillna(pd.Timestamp.now())
        default_updated = new_df[new_df['updated_at'] == pd.Timestamp.now()][['id', 'updated_at']]
        if not default_updated.empty:
            logger.warning(f"Set default updated_at for {len(default_updated)} rows: {default_updated.to_dict('records')}")
    
    if 'deleted_at' in pg_columns:
        new_df['deleted_at'] = new_df['deleted_at'].fillna(pd.Timestamp.now())
        default_deleted = new_df[new_df['deleted_at'] == pd.Timestamp.now()][['id', 'deleted_at']]
        if not default_deleted.empty:
            logger.warning(f"Set default deleted_at for {len(default_deleted)} rows: {default_deleted.to_dict('records')}")
    
    # Add unmapped columns with NULLs
    for col in pg_columns:
        if col not in new_df.columns:
            if col in ['billing_address_street_2', 'billing_address_street_3', 'billing_address_street_4', 
                       'created_by_id', 'created_by_name']:
                new_df[col] = None
            elif col == 'status':
                new_df[col] = 'Inactive'
    
    logger.info(f"Transformed DataFrame has {len(new_df.columns)} columns: {new_df.columns.tolist()}")
    return new_df

def load_to_postgres(df, target_table):
    """Load transformed data to PostgreSQL."""
    try:
        engine = create_engine(
            f"postgresql://{POSTGRES_CONFIG['user']}:{POSTGRES_CONFIG['password']}@{POSTGRES_CONFIG['host']}:{POSTGRES_CONFIG['port']}/{POSTGRES_CONFIG['dbname']}"
        )
        logger.info(f"DataFrame to load has {len(df)} rows and columns: {df.columns.tolist()}")
        
        # Check required columns
        required_columns = ['id', 'name', 'created_at', 'updated_at', 'deleted_at']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Load data
        df.to_sql(target_table, engine, schema='public', if_exists='append', index=False)
        logger.info(f"Loaded {len(df)} rows to PostgreSQL table public.{target_table}")
        return True
    except Exception as e:
        logger.error(f"Error loading data to {target_table}: {e}")
        raise

def main():
    """Execute ETL for ltp_affiliates to Affiliate."""
    mysql_conn = None
    try:
        mysql_conn = connect_mysql()
        source_table = 'ltp_affiliates'
        target_table = 'Affiliate'
        
        # Extract
        df = extract_mysql_table(mysql_conn, source_table)
        
        # Transform
        df_transformed = transform_data(df, source_table, target_table)
        
        # Log sample
        logger.info(f"Transformed data sample:\n{df_transformed.head()}")
        
        # Load
        load_result = load_to_postgres(df_transformed, target_table)
        if load_result:
            logger.info("ETL process completed successfully")
    except Exception as e:
        logger.error(f"ETL process failed: {e}")
    finally:
        if mysql_conn:
            mysql_conn.close()

if __name__ == "__main__":
    main()
