#!/usr/bin/env python3
import pandas as pd
import pymysql
import psycopg2
from sqlalchemy import create_engine
import logging
import uuid
from datetime import datetime

def int_to_uuid(int_id):
    """Convert integer ID to UUID using a fixed namespace."""
    namespace = uuid.NAMESPACE_DNS
    return str(uuid.uuid5(namespace, str(int_id)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection parameters
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'db': 'my_app_db',
    'charset': 'utf8mb4',
    'port': 3306
}

POSTGRES_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',
    'password': 'root',
    'dbname': 'postgres',
    'port': '5432'
}

# Table mapping
TABLE_MAPPING = {
    'ltp_roles': 'Role'
}

def connect_mysql():
    """Connect to MySQL database."""
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        logger.info("Connected to MySQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to MySQL: {e}")
        raise

def connect_postgres():
    """Connect to PostgreSQL database."""
    try:
        conn = psycopg2.connect(**POSTGRES_CONFIG)
        logger.info("Connected to PostgreSQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to PostgreSQL: {e}")
        raise

def extract_mysql_table(conn, table_name):
    """Extract data from MySQL table."""
    try:
        engine = create_engine(
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['db']}?charset={MYSQL_CONFIG['charset']}"
        )
        query = f"SELECT * FROM {table_name}"
        df = pd.read_sql(query, engine)
        logger.info(f"Extracted {len(df)} rows from MySQL table {table_name}")

        # Ensure all needed columns are present
        needed_columns = [
            'id_role', 'role_name', 'role_desc'
        ]
        for col in needed_columns:
            if col not in df.columns:
                df[col] = None

        return df
    except Exception as e:
        logger.error(f"Error extracting data from {table_name}: {e}")
        raise

def transform_data(df, source_table, target_table):
    """Transform data from MySQL ltp_roles to PostgreSQL Role."""
    logger.info(f"Transforming data from {source_table} to {target_table}")

    # Get PostgreSQL table schema
    postgres_conn = connect_postgres()
    cursor = postgres_conn.cursor()
    cursor.execute("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = %s
        ORDER BY ordinal_position
    """, (target_table,))
    pg_columns = [row[0] for row in cursor.fetchall()]
    logger.info(f"Target PostgreSQL columns: {pg_columns}")
    cursor.close()
    postgres_conn.close()

    # Create new DataFrame for transformed data
    new_df = pd.DataFrame()

    # Column mappings
    column_mapping = {
        'id_role': 'id',
        'role_name': 'name',
        'role_desc': 'description'
    }

    # Map columns
    for mysql_col, pg_col in column_mapping.items():
        if mysql_col in df.columns and pg_col in pg_columns:
            if mysql_col == 'id_role':
                new_df[pg_col] = df[mysql_col].apply(lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else str(uuid.uuid4()))
            else:
                new_df[pg_col] = df[mysql_col]

    # Handle key (derive from role_name)
    if 'key' in pg_columns:
        new_df['key'] = df['role_name'].apply(
            lambda x: x.lower().replace(' ', '_') if pd.notnull(x) and isinstance(x, str) and x.strip() else None
        )

    # Ensure NOT NULL columns have valid values
    if 'id' in pg_columns:
        new_df['id'] = new_df['id'].fillna(pd.Series([str(uuid.uuid4()) for _ in range(len(new_df))], index=new_df.index))

    if 'name' in pg_columns:
        invalid_name_mask = new_df['name'].isna() | (new_df['name'] == '')
        new_df.loc[invalid_name_mask, 'name'] = df.loc[invalid_name_mask, 'id_role'].apply(lambda x: f"role_{x}" if pd.notnull(x) else f"role_{uuid.uuid4().hex[:8]}")
        generated_names = new_df[invalid_name_mask][['id', 'name']]
        if not generated_names.empty:
            logger.warning(f"Generated names for {len(generated_names)} rows: {generated_names.to_dict('records')}")

    if 'key' in pg_columns:
        invalid_key_mask = new_df['key'].isna() | (new_df['key'] == '')
        new_df.loc[invalid_key_mask, 'key'] = new_df.loc[invalid_key_mask, 'name'].apply(lambda x: x.lower().replace(' ', '_'))
        generated_keys = new_df[invalid_key_mask][['id', 'key']]
        if not generated_keys.empty:
            logger.warning(f"Generated keys for {len(generated_keys)} rows: {generated_keys.to_dict('records')}")

    if 'created_at' in pg_columns:
        new_df['created_at'] = pd.Timestamp.now()

    if 'updated_at' in pg_columns:
        new_df['updated_at'] = pd.Timestamp.now()

    if 'deleted_at' in pg_columns:
        new_df['deleted_at'] = pd.Timestamp('1970-01-01')
        default_deleted_at = new_df[new_df['deleted_at'] == pd.Timestamp('1970-01-01')][['id', 'deleted_at']]
        if not default_deleted_at.empty:
            logger.warning(f"Set default deleted_at for {len(default_deleted_at)} rows: {default_deleted_at.to_dict('records')}")

    # Set default values for other Role columns
    for col in pg_columns:
        if col not in new_df.columns:
            if col in ['description', 'created_by_id', 'created_by_name']:
                new_df[col] = None

    logger.info(f"Transformed DataFrame has {len(new_df.columns)} columns: {new_df.columns.tolist()}")
    return new_df

def load_to_postgres(df, target_table):
    """Load transformed data to PostgreSQL."""
    try:
        engine = create_engine(
            f'postgresql://{POSTGRES_CONFIG["user"]}:{POSTGRES_CONFIG["password"]}@{POSTGRES_CONFIG["host"]}:{POSTGRES_CONFIG["port"]}/{POSTGRES_CONFIG["dbname"]}'
        )
        logger.info(f"DataFrame to load has {len(df)} rows and columns: {df.columns.tolist()}")

        # Check for required columns
        required_columns = ['id', 'created_at', 'updated_at', 'name', 'key', 'deleted_at']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            raise ValueError(f"Missing required columns: {missing_columns}")

        # Load data to PostgreSQL
        schema = 'public'
        df.to_sql(target_table, engine, schema=schema, if_exists='append', index=False)

        logger.info(f"Loaded {len(df)} rows to PostgreSQL table {schema}.{target_table}")
        return True
    except Exception as e:
        logger.error(f"Error loading data to {target_table}: {e}")
        raise

def main():
    """Run ETL for ltp_roles to Role."""
    mysql_conn = None
    try:
        mysql_conn = connect_mysql()
        source_table = 'ltp_roles'
        target_table = TABLE_MAPPING[source_table]

        # Extract
        df = extract_mysql_table(mysql_conn, source_table)

        # Transform
        df_transformed = transform_data(df, source_table, target_table)

        # Log sample
        logger.info(f"Transformed data sample:\n{df_transformed.head()}")

        # Load
        load_result = load_to_postgres(df_transformed, target_table)

        if load_result:
            logger.info("ETL process completed successfully")

    except Exception as e:
        logger.error(f"ETL process failed: {e}")
    finally:
        if mysql_conn:
            mysql_conn.close()

if __name__ == "__main__":
    main()
