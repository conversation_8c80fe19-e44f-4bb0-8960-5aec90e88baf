#!/usr/bin/env python3
import pandas as pd
import pymysql
import psycopg2
from sqlalchemy import create_engine
import logging
import uuid
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection parameters
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'db': 'my_app_db',
    'charset': 'utf8mb4',
    'port': 3306
}

POSTGRES_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',
    'password': 'root',
    'dbname': 'postgres',
    'port': 5432
}

# Utility functions (from reference ETL)
def int_to_uuid(int_id):
    """Convert integer ID to UUID."""
    try:
        int_id = int(int_id)  # Ensure it's an integer
        namespace = uuid.NAMESPACE_DNS
        return str(uuid.uuid5(namespace, str(int_id)))
    except (ValueError, TypeError):
        logger.warning(f"Invalid ID for UUID conversion: {int_id}")
        return str(uuid.uuid4())

def safe_param_parse(param_value):
    """Safely parse parameter value which might be in JSON format."""
    if not param_value or not isinstance(param_value, str):
        return param_value
    try:
        return json.loads(param_value)
    except json.JSONDecodeError:
        logger.info(f"Not valid JSON: {param_value}")
        return param_value
    except Exception:
        logger.error(f"Error parsing JSON: {param_value}")
        return param_value

def connect_mysql():
    """Connect to MySQL database."""
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        logger.info("Connected to MySQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to MySQL: {e}")
        raise

def connect_postgres():
    """Connect to PostgreSQL database."""
    try:
        conn = psycopg2.connect(**POSTGRES_CONFIG)
        logger.info("Connected to PostgreSQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to PostgreSQL: {e}")
        raise

def get_existing_ids():
    """Retrieve existing IDs from the Session table."""
    try:
        conn = connect_postgres()
        cursor = conn.cursor()
        cursor.execute('SELECT id FROM public."Session"')
        existing_ids = {row[0] for row in cursor.fetchall()}
        cursor.close()
        conn.close()
        logger.info(f"Retrieved {len(existing_ids)} existing IDs from Session table")
        return existing_ids
    except Exception as e:
        logger.error(f"Error retrieving existing IDs: {e}")
        raise

def extract_mysql_data():
    """Extract data from MySQL ltp_people_params, ltp_assignments, and ltp_people tables."""
    try:
        engine = create_engine(
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['db']}?charset={MYSQL_CONFIG['charset']}"
        )
        # Extract ltp_people_params with param_name = 'ema_session_report'
        params_query = """
        SELECT id_people_param, id_people, param_value, date_add, date_mod, date_removed, id_add, id_mod
        FROM ltp_people_params
        WHERE param_name = 'ema_session_report'
        """
        params_df = pd.read_sql(params_query, engine)
        logger.info(f"Extracted {len(params_df)} rows from ltp_people_params with param_name='ema_session_report'")

        # Check for duplicate id_people_param values (unlikely due to AUTO_INCREMENT)
        duplicates = params_df[params_df.duplicated(subset=['id_people_param'], keep=False)]
        if not duplicates.empty:
            logger.warning(f"Found {len(duplicates)} duplicate id_people_param values: {duplicates['id_people_param'].tolist()}")
            params_df = params_df.drop_duplicates(subset=['id_people_param'], keep='first')
            logger.info(f"After deduplication, {len(params_df)} rows remain")

        # Extract ltp_assignments for pairing_id mapping
        assignments_query = """
        SELECT id_assignment, id_people
        FROM ltp_assignments
        WHERE id_people IN (%s)
        """ % ','.join(str(id) for id in params_df['id_people'].unique())
        assignments_df = pd.read_sql(assignments_query, engine)
        logger.info(f"Extracted {len(assignments_df)} rows from ltp_assignments")

        # Extract ltp_people for created_by_name and updated_by_name
        people_ids = set(params_df['id_add'].dropna().astype(int).tolist() + params_df['id_mod'].dropna().astype(int).tolist())
        if people_ids:
            people_query = """
            SELECT id_people, name_first, name_last
            FROM ltp_people
            WHERE id_people IN (%s)
            """ % ','.join(str(id) for id in people_ids)
            people_df = pd.read_sql(people_query, engine)
            logger.info(f"Extracted {len(people_df)} rows from ltp_people for names")
        else:
            people_df = pd.DataFrame(columns=['id_people', 'name_first', 'name_last'])
            logger.info("No id_add or id_mod values found, skipping ltp_people extraction")

        return params_df, assignments_df, people_df
    except Exception as e:
        logger.error(f"Error extracting data: {e}")
        raise

def transform_data(params_df, assignments_df, people_df):
    """Transform data from MySQL ltp_people_params to PostgreSQL Session schema."""
    logger.info("Transforming data from ltp_people_params to Session")

    # Get PostgreSQL table schema
    postgres_conn = connect_postgres()
    cursor = postgres_conn.cursor()
    cursor.execute("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = %s
        ORDER BY ordinal_position
    """, ('Session',))
    pg_columns = {row[0]: row[1] for row in cursor.fetchall()}
    logger.info(f"Target PostgreSQL columns: {list(pg_columns.keys())}")
    cursor.close()
    postgres_conn.close()

    # Get existing IDs to avoid duplicates
    existing_ids = get_existing_ids()

    # Create new DataFrame for transformed data
    new_df = pd.DataFrame()

    # Direct column mappings
    direct_mappings = {
        'id_people_param': 'id',
        'date_add': 'created_at',
        'date_mod': 'updated_at',
        'date_removed': 'deleted_at',
        'id_add': 'created_by_id',
        'id_mod': 'updated_by_id'
    }

    # Create a dictionary for assignments mapping (id_people -> id_assignment)
    assignments_dict = assignments_df.set_index('id_people')['id_assignment'].to_dict()

    # Create a dictionary for people names (id_people -> full_name)
    people_df['full_name'] = people_df['name_first'].str.cat(people_df['name_last'], sep=' ', na_rep='').str.strip()
    people_dict = people_df.set_index('id_people')['full_name'].to_dict()

    # Apply direct mappings
    for mysql_col, pg_col in direct_mappings.items():
        if mysql_col in params_df.columns and pg_col in pg_columns:
            logger.debug(f"Mapping {mysql_col} to {pg_col}")
            try:
                if mysql_col == 'id_people_param':
                    new_df[pg_col] = params_df[mysql_col].apply(
                        lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else str(uuid.uuid4())
                    )
                elif mysql_col in ['id_add', 'id_mod']:
                    new_df[pg_col] = params_df[mysql_col].apply(
                        lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else None
                    )
                elif mysql_col in ['date_add', 'date_mod']:
                    new_df[pg_col] = pd.to_datetime(params_df[mysql_col], unit='s', errors='coerce')
                elif mysql_col == 'date_removed':
                    new_df[pg_col] = pd.to_datetime(params_df[mysql_col], unit='s', errors='coerce')  # Convert bigint to timestamp
            except Exception as e:
                logger.error(f"Error mapping {mysql_col} to {pg_col}: {e}")
                raise

    # Handle JSON fields from param_value
    json_fields = {
        'date_start': 'date_session',
        'date_end': 'date_next_session',
        'name': 'session_number',
        'location': 'next_session_location'
    }

    for pg_col, json_key in json_fields.items():
        if pg_col in pg_columns:
            logger.debug(f"Mapping param_value->{json_key} to {pg_col}")
            try:
                new_df[pg_col] = params_df['param_value'].apply(
                    lambda x: safe_param_parse(x).get(json_key) if isinstance(safe_param_parse(x), dict) else None
                )
                if pg_col in ['date_start', 'date_end']:
                    new_df[pg_col] = pd.to_datetime(new_df[pg_col], unit='s', errors='coerce')
            except Exception as e:
                logger.error(f"Error mapping param_value->{json_key} to {pg_col}: {e}")
                raise

    # Handle description (combine multiple JSON keys)
    if 'description' in pg_columns:
        logger.debug("Combining JSON keys for description")
        description_keys = ['session_notes', 'immediate_needs', 'immediate_concerns', 'immediate_recommendations']
        new_df['description'] = params_df['param_value'].apply(
            lambda x: {k: v for k, v in safe_param_parse(x).items() if k in description_keys}
            if isinstance(safe_param_parse(x), dict) else {}
        ).apply(
            lambda x: json.dumps(x) if x else None
        )

    # Handle created_by_name and updated_by_name
    if 'created_by_name' in pg_columns:
        new_df['created_by_name'] = params_df['id_add'].map(people_dict).fillna('')
    if 'updated_by_name' in pg_columns:
        new_df['updated_by_name'] = params_df['id_mod'].map(people_dict).fillna('')

    # Handle pairing_id
    if 'pairing_id' in pg_columns:
        logger.debug("Mapping id_people to pairing_id via ltp_assignments")
        new_df['pairing_id'] = params_df['id_people'].apply(
            lambda x: int_to_uuid(assignments_dict.get(x)) if x in assignments_dict else None
        )

    # Set null fields
    null_fields = ['join_url', 'session_type', 'status', 'session_group_id']
    for field in null_fields:
        if field in pg_columns:
            new_df[field] = None

    # Filter out rows with duplicate IDs
    initial_row_count = len(new_df)
    duplicate_mask = new_df['id'].isin(existing_ids)
    if duplicate_mask.any():
        duplicates = new_df[duplicate_mask][['id']].merge(
            params_df[['id_people_param']], left_index=True, right_index=True
        )
        logger.warning(f"Skipping {duplicate_mask.sum()} rows with duplicate IDs: {duplicates[['id', 'id_people_param']].to_dict('records')}")
        new_df = new_df[~duplicate_mask]
    logger.info(f"After filtering duplicates, {len(new_df)} rows remain (skipped {initial_row_count - len(new_df)})")

    # Handle required fields
    required_fields = ['id', 'created_at', 'updated_at', 'name', 'deleted_at']
    for field in required_fields:
        if field in pg_columns and field not in new_df.columns:
            if field == 'id':
                new_df[field] = [str(uuid.uuid4()) for _ in range(len(new_df))]
            elif field == 'name':
                new_df[field] = new_df['id'].apply(lambda x: f"Session_{x[:8]}")
            elif field in ['created_at', 'updated_at']:
                new_df[field] = pd.Timestamp.now()
            elif field == 'deleted_at':
                new_df[field] = pd.Timestamp('1970-01-01')
        elif field in new_df.columns:
            if field == 'id':
                new_df[field] = new_df[field].fillna(pd.Series([str(uuid.uuid4()) for _ in range(len(new_df))], index=new_df.index))
            elif field == 'name':
                new_df[field] = new_df[field].fillna(new_df['id'].apply(lambda x: f"Session_{x[:8]}"))
            elif field in ['created_at', 'updated_at']:
                new_df[field] = new_df[field].fillna(pd.Timestamp.now())
            elif field == 'deleted_at':
                new_df['deleted_at'] = new_df['deleted_at'].fillna(pd.Timestamp('1970-01-01'))
                # Log rows with default deleted_at
                default_deleted_at = new_df[new_df['deleted_at'] == pd.Timestamp('1970-01-01')][['id', 'deleted_at']]
                if not default_deleted_at.empty:
                    logger.warning(f"Set default deleted_at for {len(default_deleted_at)} rows: {default_deleted_at.to_dict('records')}")

    # Ensure text columns are strings
    for col, dtype in pg_columns.items():
        if col in new_df.columns and dtype == 'text':
            new_df[col] = new_df[col].apply(
                lambda x: json.dumps(x) if isinstance(x, dict) else str(x) if x is not None else None
            )

    logger.info(f"Transformed DataFrame has {len(new_df.columns)} columns: {new_df.columns.tolist()}")
    return new_df

def load_to_postgres(df, target_table):
    """Load transformed data to PostgreSQL."""
    try:
        engine = create_engine(
            f"postgresql://{POSTGRES_CONFIG['user']}:{POSTGRES_CONFIG['password']}@{POSTGRES_CONFIG['host']}:{POSTGRES_CONFIG['port']}/{POSTGRES_CONFIG['dbname']}"
        )

        # Check required columns
        required_columns = ['id', 'created_at', 'updated_at', 'name', 'deleted_at']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            raise ValueError(f"Missing required columns: {missing_columns}")

        # Convert timestamp columns
        for col in ['created_at', 'updated_at', 'date_start', 'date_end', 'deleted_at']:
            if col in df.columns and df[col].dtype != 'datetime64[ns]':
                df[col] = pd.to_datetime(df[col], errors='coerce')

        # Load data to PostgreSQL
        df.to_sql(target_table, engine, schema='public', if_exists='append', index=False)
        logger.info(f"Loaded {len(df)} rows to PostgreSQL table public.{target_table}")
        return True
    except Exception as e:
        logger.error(f"Error loading data to {target_table}: {e}")
        raise

def main():
    """Execute ETL for ltp_people_params to Session."""
    try:
        # Extract
        params_df, assignments_df, people_df = extract_mysql_data()

        # Transform
        session_df = transform_data(params_df, assignments_df, people_df)

        # Print sample of transformed data
        logger.info(f"Transformed data sample:\n{session_df.head()}")

        # Load
        load_result = load_to_postgres(session_df, 'Session')

        if load_result:
            logger.info("ETL process completed successfully")

    except Exception as e:
        logger.error(f"ETL process failed: {e}")

if __name__ == "__main__":
    main()
