#!/usr/bin/env python3
import pandas as pd
import pymysql
import psycopg2
from sqlalchemy import create_engine
import logging
import uuid
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection parameters
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'db': 'my_app_db',
    'charset': 'utf8mb4',
    'port': 3306
}

POSTGRES_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',
    'password': 'root',
    'dbname': 'postgres',
    'port': 5432
}

# Utility functions
def int_to_uuid(int_id):
    """Convert integer ID to UUID."""
    try:
        int_id = int(int_id)  # Ensure it's an integer
        namespace = uuid.NAMESPACE_DNS
        return str(uuid.uuid5(namespace, str(int_id)))
    except (ValueError, TypeError):
        logger.warning(f"Invalid ID for UUID conversion: {int_id}")
        return str(uuid.uuid4())

def safe_param_parse(param_value):
    """Safely parse parameter value which might be in JSON format."""
    if not param_value or not isinstance(param_value, str):
        return param_value
    try:
        return json.loads(param_value)
    except json.JSONDecodeError:
        logger.info(f"Not valid JSON: {param_value}")
        return param_value
    except Exception:
        logger.error(f"Error parsing JSON: {param_value}")
        return param_value

def connect_mysql():
    """Connect to MySQL database."""
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        logger.info("Connected to MySQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to MySQL: {e}")
        raise

def connect_postgres():
    """Connect to PostgreSQL database."""
    try:
        conn = psycopg2.connect(**POSTGRES_CONFIG)
        logger.info("Connected to PostgreSQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to PostgreSQL: {e}")
        raise

def get_existing_ids():
    """Retrieve existing IDs from the Pairing table."""
    try:
        conn = connect_postgres()
        cursor = conn.cursor()
        cursor.execute('SELECT id FROM public."Pairing"')
        existing_ids = {row[0] for row in cursor.fetchall()}
        cursor.close()
        conn.close()
        logger.info(f"Retrieved {len(existing_ids)} existing IDs from Pairing table")
        return existing_ids
    except Exception as e:
        logger.error(f"Error retrieving existing IDs: {e}")
        raise

def extract_mysql_data():
    """Extract data from MySQL ltp_assignments table."""
    try:
        engine = create_engine(
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['db']}?charset={MYSQL_CONFIG['charset']}"
        )
        # Select required columns, including id_role for momId filtering
        columns = ['id_assignment', 'date_add', 'date_mod', 'params', 'id_people', 'id_region_manager', 'date_removed', 'id_role']
        query = f"SELECT {', '.join(columns)} FROM ltp_assignments"
        assignments_df = pd.read_sql(query, engine)
        logger.info(f"Extracted {len(assignments_df)} rows from ltp_assignments")

        # Check for duplicate id_assignment values (unlikely due to AUTO_INCREMENT)
        duplicates = assignments_df[assignments_df.duplicated(subset=['id_assignment'], keep=False)]
        if not duplicates.empty:
            logger.warning(f"Found {len(duplicates)} duplicate id_assignment values: {duplicates['id_assignment'].tolist()}")
            assignments_df = assignments_df.drop_duplicates(subset=['id_assignment'], keep='first')
            logger.info(f"After deduplication, {len(assignments_df)} rows remain")
        
        return assignments_df
    except Exception as e:
        logger.error(f"Error extracting data: {e}")
        raise

def transform_data(assignments_df):
    """Transform data from MySQL ltp_assignments to PostgreSQL Pairing schema."""
    logger.info("Transforming data from ltp_assignments to Pairing")
    
    # Get PostgreSQL table schema
    postgres_conn = connect_postgres()
    cursor = postgres_conn.cursor()
    cursor.execute("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = %s
        ORDER BY ordinal_position
    """, ('Pairing',))
    pg_columns = {row[0]: row[1] for row in cursor.fetchall()}
    logger.info(f"Target PostgreSQL columns: {list(pg_columns.keys())}")
    cursor.close()
    postgres_conn.close()
    
    # Get existing IDs to avoid duplicates
    existing_ids = get_existing_ids()
    
    # Create new DataFrame for transformed data
    new_df = pd.DataFrame()
    
    # Direct column mappings as provided
    direct_mappings = {
        'id_assignment': 'id',
        'date_add': 'created_at',
        'date_mod': 'updated_at',
        'params': 'description',
        'id_people': 'momId',  # Will handle specially below
        'id_region_manager': 'advocateUserId',
        'date_removed': 'deleted_at'
    }
    
    # Apply direct mappings
    for mysql_col, pg_col in direct_mappings.items():
        if mysql_col in assignments_df.columns and pg_col in pg_columns:
            logger.debug(f"Mapping {mysql_col} to {pg_col}")
            try:
                if mysql_col == 'id_assignment':
                    new_df[pg_col] = assignments_df[mysql_col].apply(
                        lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else str(uuid.uuid4())
                    )
                elif mysql_col == 'id_people':
                    # Map id_people to momId only if id_role is 2, 10, or 16
                    new_df[pg_col] = assignments_df.apply(
                        lambda row: int_to_uuid(row['id_people']) if pd.notnull(row['id_people']) and row['id_people'] != 0 
                                    and pd.notnull(row['id_role']) and int(row['id_role']) in [2, 10, 16] else None,
                        axis=1
                    )
                elif mysql_col == 'id_region_manager':
                    new_df[pg_col] = assignments_df[mysql_col].apply(
                        lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else None
                    )
                elif mysql_col in ['date_add', 'date_mod']:
                    new_df[pg_col] = pd.to_datetime(assignments_df[mysql_col], unit='s', errors='coerce')
                elif mysql_col == 'date_removed':
                    new_df[pg_col] = pd.to_datetime(assignments_df[mysql_col], unit='s', errors='coerce').fillna(pd.Timestamp('1970-01-01'))
                elif mysql_col == 'params':
                    new_df[pg_col] = assignments_df[mysql_col].apply(
                        lambda x: json.dumps(safe_param_parse(x)) if isinstance(safe_param_parse(x), dict) else safe_param_parse(x)
                    )
            except Exception as e:
                logger.error(f"Error mapping {mysql_col} to {pg_col}: {e}")
                raise
    
    # Filter out rows with duplicate IDs
    initial_row_count = len(new_df)
    duplicate_mask = new_df['id'].isin(existing_ids)
    if duplicate_mask.any():
        duplicates = new_df[duplicate_mask][['id']].merge(
            assignments_df[['id_assignment']], left_index=True, right_index=True
        )
        logger.warning(f"Skipping {duplicate_mask.sum()} rows with duplicate IDs: {duplicates[['id', 'id_assignment']].to_dict('records')}")
        new_df = new_df[~duplicate_mask]
    logger.info(f"After filtering duplicates, {len(new_df)} rows remain (skipped {initial_row_count - len(new_df)})")
    
    # Generate required name field
    if 'name' in pg_columns:
        new_df['name'] = new_df['id'].apply(
            lambda x: f"Pairing_{x[:8]}" if pd.notnull(x) else f"Pairing_{str(uuid.uuid4())[:8]}"
        )
    
    # Handle required fields
    required_fields = ['id', 'created_at', 'updated_at', 'name', 'deleted_at']
    for field in required_fields:
        if field in pg_columns and field not in new_df.columns:
            if field == 'id':
                new_df[field] = [str(uuid.uuid4()) for _ in range(len(new_df))]
            elif field == 'name':
                new_df[field] = new_df['id'].apply(lambda x: f"Pairing_{x[:8]}")
            elif field in ['created_at', 'updated_at']:
                new_df[field] = pd.Timestamp.now()
            elif field == 'deleted_at':
                new_df[field] = pd.Timestamp('1970-01-01')
    
    # Ensure required fields are not null
    for field in required_fields:
        if field in new_df.columns:
            if field == 'id':
                new_df[field] = new_df[field].fillna(pd.Series([str(uuid.uuid4()) for _ in range(len(new_df))], index=new_df.index))
            elif field == 'name':
                new_df[field] = new_df[field].fillna(new_df['id'].apply(lambda x: f"Pairing_{x[:8]}"))
            elif field in ['created_at', 'updated_at']:
                new_df[field] = new_df[field].fillna(pd.Timestamp.now())
            elif field == 'deleted_at':
                new_df[field] = new_df[field].fillna(pd.Timestamp('1970-01-01'))
    
    # Set default values for other Pairing columns
    for col in pg_columns:
        if col not in new_df.columns:
            if col in ['trackId', 'created_by_id', 'created_by_name', 'status', 'complete_reason_sub_status',
                       'discharge_incomplete_sub_status', 'in_program_track_sub_status',
                       'incomplete_reason_sub_status', 'track_status', 'updated_by_id', 'updated_by_name']:
                new_df[col] = None
            elif col == 'status':
                new_df[col] = 'waiting_to_be_paired'  # Default status
            elif col == 'track_status':
                new_df[col] = 'in_program'  # Default track status
    
    # Ensure text columns are strings
    for col, dtype in pg_columns.items():
        if col in new_df.columns and dtype == 'text':
            new_df[col] = new_df[col].apply(
                lambda x: json.dumps(x) if isinstance(x, dict) else str(x) if x is not None else None
            )
    
    logger.info(f"Transformed DataFrame has {len(new_df.columns)} columns: {new_df.columns.tolist()}")
    return new_df

def load_to_postgres(df, target_table):
    """Load transformed data to PostgreSQL."""
    try:
        engine = create_engine(
            f"postgresql://{POSTGRES_CONFIG['user']}:{POSTGRES_CONFIG['password']}@{POSTGRES_CONFIG['host']}:{POSTGRES_CONFIG['port']}/{POSTGRES_CONFIG['dbname']}"
        )
        
        # Check required columns
        required_columns = ['id', 'created_at', 'updated_at', 'name', 'deleted_at']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Convert timestamp columns to appropriate format
        for col in ['created_at', 'updated_at', 'deleted_at']:
            if col in df.columns and df[col].dtype != 'datetime64[ns]':
                df[col] = pd.to_datetime(df[col], errors='coerce')
        
        # Load data to PostgreSQL
        df.to_sql(target_table, engine, schema='public', if_exists='append', index=False)
        logger.info(f"Loaded {len(df)} rows to PostgreSQL table public.{target_table}")
        return True
    except Exception as e:
        logger.error(f"Error loading data to {target_table}: {e}")
        raise

def main():
    """Execute ETL for ltp_assignments to Pairing."""
    try:
        # Extract
        assignments_df = extract_mysql_data()
        
        # Transform
        pairing_df = transform_data(assignments_df)
        
        # Print sample of transformed data
        logger.info(f"Transformed data sample:\n{pairing_df.head()}")
        
        # Load
        load_result = load_to_postgres(pairing_df, 'Pairing')
        
        if load_result:
            logger.info("ETL process completed successfully")
        
    except Exception as e:
        logger.error(f"ETL process failed: {e}")

if __name__ == "__main__":
    main()



# #!/usr/bin/env python3
# import pandas as pd
# import pymysql
# import psycopg2
# from sqlalchemy import create_engine
# import logging
# import uuid
# import json

# # Configure logging
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
# logger = logging.getLogger(__name__)

# # Database connection parameters
# MYSQL_CONFIG = {
#     'host': 'localhost',
#     'user': 'root',
#     'password': 'root',
#     'db': 'my_app_db',
#     'charset': 'utf8mb4',
#     'port': 3306
# }

# POSTGRES_CONFIG = {
#     'host': 'localhost',
#     'user': 'postgres',
#     'password': 'root',
#     'dbname': 'postgres',
#     'port': 5432
# }

# # Utility functions (from reference ETL)
# def int_to_uuid(int_id):
#     """Convert integer ID to UUID."""
#     int_id = int(int_id) # ensuring it should be in int.
#     namespace = uuid.NAMESPACE_DNS
#     return str(uuid.uuid5(namespace, str(int_id)))

# def safe_param_parse(param_value):
#     """Safely parse parameter value which might be in JSON format."""
#     if not param_value or not isinstance(param_value, str):
#         return param_value
#     try:
#         return json.loads(param_value)
#     except json.JSONDecodeError:
#         logger.info(f"Not valid JSON: {param_value}")
#         return param_value
#     except Exception:
#         logger.error(f"Error parsing JSON: {param_value}")
#         return param_value

# def connect_mysql():
#     """Connect to MySQL database."""
#     try:
#         conn = pymysql.connect(**MYSQL_CONFIG)
#         logger.info("Connected to MySQL database")
#         return conn
#     except Exception as e:
#         logger.error(f"Error connecting to MySQL: {e}")
#         raise

# def connect_postgres():
#     """Connect to PostgreSQL database."""
#     try:
#         conn = psycopg2.connect(**POSTGRES_CONFIG)
#         logger.info("Connected to PostgreSQL database")
#         return conn
#     except Exception as e:
#         logger.error(f"Error connecting to PostgreSQL: {e}")
#         raise

# def get_existing_ids():
#     """Retrieve existing IDs from the Pairing table."""
#     try:
#         conn = connect_postgres()
#         cursor = conn.cursor()
#         cursor.execute('SELECT id FROM public."Pairing"')
#         existing_ids = {row[0] for row in cursor.fetchall()}
#         cursor.close()
#         conn.close()
#         logger.info(f"Retrieved {len(existing_ids)} existing IDs from Pairing table")
#         return existing_ids
#     except Exception as e:
#         logger.error(f"Error retrieving existing IDs: {e}")
#         raise

# def extract_mysql_data():
#     """Extract data from MySQL ltp_assignments table."""
#     try:
#         engine = create_engine(
#             f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['db']}?charset={MYSQL_CONFIG['charset']}"
#         )
#         # Select only the columns we need
#         columns = ['id_assignment', 'date_add', 'date_mod', 'params', 'id_people', 'id_region_manager', 'date_removed']
#         query = f"SELECT {', '.join(columns)} FROM ltp_assignments"
#         assignments_df = pd.read_sql(query, engine)
#         logger.info(f"Extracted {len(assignments_df)} rows from ltp_assignments")

#         # Check for duplicate id_assignment values (unlikely due to AUTO_INCREMENT)
#         duplicates = assignments_df[assignments_df.duplicated(subset=['id_assignment'], keep=False)]
#         if not duplicates.empty:
#             logger.warning(f"Found {len(duplicates)} duplicate id_assignment values: {duplicates['id_assignment'].tolist()}")
#             assignments_df = assignments_df.drop_duplicates(subset=['id_assignment'], keep='first')
#             logger.info(f"After deduplication, {len(assignments_df)} rows remain")
        
#         return assignments_df
#     except Exception as e:
#         logger.error(f"Error extracting data: {e}")
#         raise

# def transform_data(assignments_df):
#     """Transform data from MySQL ltp_assignments to PostgreSQL Pairing schema."""
#     logger.info("Transforming data from ltp_assignments to Pairing")
    
#     # Get PostgreSQL table schema
#     postgres_conn = connect_postgres()
#     cursor = postgres_conn.cursor()
#     cursor.execute("""
#         SELECT column_name, data_type 
#         FROM information_schema.columns 
#         WHERE table_schema = 'public' 
#         AND table_name = %s
#         ORDER BY ordinal_position
#     """, ('Pairing',))
#     pg_columns = {row[0]: row[1] for row in cursor.fetchall()}
#     logger.info(f"Target PostgreSQL columns: {list(pg_columns.keys())}")
#     cursor.close()
#     postgres_conn.close()
    
#     # Get existing IDs to avoid duplicates
#     existing_ids = get_existing_ids()
    
#     # Create new DataFrame for transformed data
#     new_df = pd.DataFrame()
    
#     # Direct column mappings as provided
#     direct_mappings = {
#         'id_assignment': 'id',
#         'date_add': 'created_at',
#         'date_mod': 'updated_at',
#         'params': 'description',
#         'id_people': 'momId',
#         'id_region_manager': 'advocateUserId',
#         'date_removed': 'deleted_at'
#     }
    
#     # Apply direct mappings
#     for mysql_col, pg_col in direct_mappings.items():
#         if mysql_col in assignments_df.columns and pg_col in pg_columns:
#             logger.debug(f"Mapping {mysql_col} to {pg_col}")
#             try:
#                 if mysql_col == 'id_assignment':
#                     new_df[pg_col] = assignments_df[mysql_col].apply(
#                         lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else str(uuid.uuid4())
#                     )
#                 elif mysql_col in ['id_people', 'id_region_manager']:
#                     new_df[pg_col] = assignments_df[mysql_col].apply(
#                         lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else None
#                     )
#                 elif mysql_col in ['date_add', 'date_mod']:
#                     new_df[pg_col] = pd.to_datetime(assignments_df[mysql_col], unit='s', errors='coerce')
#                 elif mysql_col == 'date_removed':
#                     new_df[pg_col] = pd.to_datetime(assignments_df[mysql_col], unit='s', errors='coerce').fillna(pd.Timestamp('1970-01-01'))
#                 elif mysql_col == 'params':
#                     new_df[pg_col] = assignments_df[mysql_col].apply(
#                         lambda x: json.dumps(safe_param_parse(x)) if isinstance(safe_param_parse(x), dict) else safe_param_parse(x)
#                     )
#             except Exception as e:
#                 logger.error(f"Error mapping {mysql_col} to {pg_col}: {e}")
#                 raise
    
#     # Filter out rows with duplicate IDs
#     initial_row_count = len(new_df)
#     duplicate_mask = new_df['id'].isin(existing_ids)
#     if duplicate_mask.any():
#         duplicates = new_df[duplicate_mask][['id']].merge(
#             assignments_df[['id_assignment']], left_index=True, right_index=True
#         )
#         logger.warning(f"Skipping {duplicate_mask.sum()} rows with duplicate IDs: {duplicates[['id', 'id_assignment']].to_dict('records')}")
#         new_df = new_df[~duplicate_mask]
#     logger.info(f"After filtering duplicates, {len(new_df)} rows remain (skipped {initial_row_count - len(new_df)})")
    
#     # Generate required name field
#     if 'name' in pg_columns:
#         new_df['name'] = new_df['id'].apply(
#             lambda x: f"Pairing_{x[:8]}" if pd.notnull(x) else f"Pairing_{str(uuid.uuid4())[:8]}"
#         )
    
#     # Handle required fields
#     required_fields = ['id', 'created_at', 'updated_at', 'name', 'deleted_at']
#     for field in required_fields:
#         if field in pg_columns and field not in new_df.columns:
#             if field == 'id':
#                 new_df[field] = [str(uuid.uuid4()) for _ in range(len(new_df))]
#             elif field == 'name':
#                 new_df[field] = new_df['id'].apply(lambda x: f"Pairing_{x[:8]}")
#             elif field in ['created_at', 'updated_at']:
#                 new_df[field] = pd.Timestamp.now()
#             elif field == 'deleted_at':
#                 new_df[field] = pd.Timestamp('1970-01-01')
    
#     # Ensure required fields are not null
#     for field in required_fields:
#         if field in new_df.columns:
#             if field == 'id':
#                 new_df[field] = new_df[field].fillna(pd.Series([str(uuid.uuid4()) for _ in range(len(new_df))], index=new_df.index))
#             elif field == 'name':
#                 new_df[field] = new_df[field].fillna(new_df['id'].apply(lambda x: f"Pairing_{x[:8]}"))
#             elif field in ['created_at', 'updated_at']:
#                 new_df[field] = new_df[field].fillna(pd.Timestamp.now())
#             elif field == 'deleted_at':
#                 new_df[field] = new_df[field].fillna(pd.Timestamp('1970-01-01'))
    
#     # Set default values for other Pairing columns
#     for col in pg_columns:
#         if col not in new_df.columns:
#             if col in ['trackId', 'created_by_id', 'created_by_name', 'status', 'complete_reason_sub_status',
#                        'discharge_incomplete_sub_status', 'in_program_track_sub_status',
#                        'incomplete_reason_sub_status', 'track_status', 'updated_by_id', 'updated_by_name']:
#                 new_df[col] = None
#             elif col == 'status':
#                 new_df[col] = 'waiting_to_be_paired'  # Default status
#             elif col == 'track_status':
#                 new_df[col] = 'in_program'  # Default track status
    
#     # Ensure text columns are strings
#     for col, dtype in pg_columns.items():
#         if col in new_df.columns and dtype == 'text':
#             new_df[col] = new_df[col].apply(
#                 lambda x: json.dumps(x) if isinstance(x, dict) else str(x) if x is not None else None
#             )
    
#     logger.info(f"Transformed DataFrame has {len(new_df.columns)} columns: {new_df.columns.tolist()}")
#     return new_df

# def load_to_postgres(df, target_table):
#     """Load transformed data to PostgreSQL."""
#     try:
#         engine = create_engine(
#             f"postgresql://{POSTGRES_CONFIG['user']}:{POSTGRES_CONFIG['password']}@{POSTGRES_CONFIG['host']}:{POSTGRES_CONFIG['port']}/{POSTGRES_CONFIG['dbname']}"
#         )
        
#         # Check required columns
#         required_columns = ['id', 'created_at', 'updated_at', 'name', 'deleted_at']
#         missing_columns = [col for col in required_columns if col not in df.columns]
#         if missing_columns:
#             logger.error(f"Missing required columns: {missing_columns}")
#             raise ValueError(f"Missing required columns: {missing_columns}")
        
#         # Convert timestamp columns to appropriate format
#         for col in ['created_at', 'updated_at', 'deleted_at']:
#             if col in df.columns and df[col].dtype != 'datetime64[ns]':
#                 df[col] = pd.to_datetime(df[col], errors='coerce')
        
#         # Load data to PostgreSQL
#         df.to_sql(target_table, engine, schema='public', if_exists='append', index=False)
#         logger.info(f"Loaded {len(df)} rows to PostgreSQL table public.{target_table}")
#         return True
#     except Exception as e:
#         logger.error(f"Error loading data to {target_table}: {e}")
#         raise

# def main():
#     """Execute ETL for ltp_assignments to Pairing."""
#     try:
#         # Extract
#         assignments_df = extract_mysql_data()
        
#         # Transform
#         pairing_df = transform_data(assignments_df)
        
#         # Print sample of transformed data
#         logger.info(f"Transformed data sample:\n{pairing_df.head()}")
        
#         # Load
#         load_result = load_to_postgres(pairing_df, 'Pairing')
        
#         if load_result:
#             logger.info("ETL process completed successfully")
        
#     except Exception as e:
#         logger.error(f"ETL process failed: {e}")

# if __name__ == "__main__":
#     main()
