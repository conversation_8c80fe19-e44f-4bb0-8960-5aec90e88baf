#!/usr/bin/env python3
import pandas as pd
import pymysql
import psycopg2
from sqlalchemy import create_engine
import logging
import uuid
from datetime import datetime

def int_to_uuid(int_id):
    """Convert integer ID to UUID using a fixed namespace."""
    namespace = uuid.NAMESPACE_DNS
    return str(uuid.uuid5(namespace, str(int_id)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection parameters
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'db': 'my_app_db',
    'charset': 'utf8mb4',
    'port': 3306
}

POSTGRES_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',
    'password': 'root',
    'dbname': 'postgres',
    'port': '5432'
}

# Table mapping
TABLE_MAPPING = {
    'ltp_affiliates': 'AffiliateAgency'
}

def connect_mysql():
    """Connect to MySQL database."""
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        logger.info("Connected to MySQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to MySQL: {e}")
        raise

def connect_postgres():
    """Connect to PostgreSQL database."""
    try:
        conn = psycopg2.connect(**POSTGRES_CONFIG)
        logger.info("Connected to PostgreSQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to PostgreSQL: {e}")
        raise

def extract_mysql_table(conn, table_name):
    """Extract data from MySQL table."""
    try:
        engine = create_engine(
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['db']}?charset={MYSQL_CONFIG['charset']}"
        )
        query = f"SELECT * FROM {table_name}"
        df = pd.read_sql(query, engine)
        logger.info(f"Extracted {len(df)} rows from MySQL table {table_name}")

        # Ensure all needed columns are present
        needed_columns = [
            'id_affiliate', 'id_agency', 'date_add', 'date_mod', 'date_removed'
        ]
        for col in needed_columns:
            if col not in df.columns:
                df[col] = None

        return df
    except Exception as e:
        logger.error(f"Error extracting data from {table_name}: {e}")
        raise

def transform_data(df, source_table, target_table):
    """Transform data from MySQL ltp_affiliates to PostgreSQL AffiliateAgency."""
    logger.info(f"Transforming data from {source_table} to {target_table}")

    # Get PostgreSQL table schema
    postgres_conn = connect_postgres()
    cursor = postgres_conn.cursor()
    cursor.execute("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = %s
        ORDER BY ordinal_position
    """, (target_table,))
    pg_columns = [row[0] for row in cursor.fetchall()]
    logger.info(f"Target PostgreSQL columns: {pg_columns}")
    cursor.close()
    postgres_conn.close()

    # Create new DataFrame for transformed data
    new_df = pd.DataFrame()

    # Column mappings
    column_mapping = {
        'id_affiliate': 'affiliate_id',
        'id_agency': 'agency_id',
        'date_add': 'created_at',
        'date_mod': 'updated_at',
        'date_removed': 'deleted_at'
    }

    # Map columns
    for mysql_col, pg_col in column_mapping.items():
        if mysql_col in df.columns and pg_col in pg_columns:
            if mysql_col == 'id_affiliate':
                new_df[pg_col] = df[mysql_col].apply(lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else str(uuid.uuid4()))
            elif mysql_col == 'id_agency':
                new_df[pg_col] = df[mysql_col].apply(lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else str(uuid.uuid4()))
            elif mysql_col in ['date_add', 'date_mod', 'date_removed']:
                new_df[pg_col] = pd.to_datetime(df[mysql_col], unit='s', errors='coerce')

    # Generate UUID for id
    if 'id' in pg_columns:
        new_df['id'] = [str(uuid.uuid4()) for _ in range(len(df))]

    # Ensure NOT NULL columns have valid values
    if 'created_at' in pg_columns:
        new_df['created_at'] = new_df['created_at'].fillna(pd.Timestamp.now())

    if 'updated_at' in pg_columns:
        new_df['updated_at'] = new_df['updated_at'].fillna(pd.Timestamp.now())

    if 'deleted_at' in pg_columns:
        new_df['deleted_at'] = new_df['deleted_at'].fillna(pd.Timestamp('1970-01-01'))
        default_deleted_at = new_df[new_df['deleted_at'] == pd.Timestamp('1970-01-01')][['id', 'deleted_at']]
        if not default_deleted_at.empty:
            logger.warning(f"Set default deleted_at for {len(default_deleted_at)} rows: {default_deleted_at.to_dict('records')}")

    if 'affiliate_id' in pg_columns:
        new_df['affiliate_id'] = new_df['affiliate_id'].fillna(pd.Series([str(uuid.uuid4()) for _ in range(len(new_df))], index=new_df.index))

    if 'agency_id' in pg_columns:
        new_df['agency_id'] = new_df['agency_id'].fillna(pd.Series([str(uuid.uuid4()) for _ in range(len(new_df))], index=new_df.index))

    # Set default values for other AffiliateAgency columns
    for col in pg_columns:
        if col not in new_df.columns:
            if col in ['created_by_id', 'created_by_name']:
                new_df[col] = None

    logger.info(f"Transformed DataFrame has {len(new_df.columns)} columns: {new_df.columns.tolist()}")
    return new_df

def load_to_postgres(df, target_table):
    """Load transformed data to PostgreSQL."""
    try:
        engine = create_engine(
            f'postgresql://{POSTGRES_CONFIG["user"]}:{POSTGRES_CONFIG["password"]}@{POSTGRES_CONFIG["host"]}:{POSTGRES_CONFIG["port"]}/{POSTGRES_CONFIG["dbname"]}'
        )
        logger.info(f"DataFrame to load has {len(df)} rows and columns: {df.columns.tolist()}")

        # Check for required columns
        required_columns = ['id', 'deleted_at', 'created_at', 'updated_at', 'affiliate_id', 'agency_id']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            raise ValueError(f"Missing required columns: {missing_columns}")

        # Load data to PostgreSQL
        schema = 'public'
        df.to_sql(target_table, engine, schema=schema, if_exists='append', index=False)

        logger.info(f"Loaded {len(df)} rows to PostgreSQL table {schema}.{target_table}")
        return True
    except Exception as e:
        logger.error(f"Error loading data to {target_table}: {e}")
        raise

def main():
    """Run ETL for ltp_affiliates to AffiliateAgency."""
    mysql_conn = None
    try:
        mysql_conn = connect_mysql()
        source_table = 'ltp_affiliates'
        target_table = TABLE_MAPPING[source_table]

        # Extract
        df = extract_mysql_table(mysql_conn, source_table)

        # Transform
        df_transformed = transform_data(df, source_table, target_table)

        # Log sample
        logger.info(f"Transformed data sample:\n{df_transformed.head()}")

        # Load
        load_result = load_to_postgres(df_transformed, target_table)

        if load_result:
            logger.info("ETL process completed successfully")

    except Exception as e:
        logger.error(f"ETL process failed: {e}")
    finally:
        if mysql_conn:
            mysql_conn.close()

if __name__ == "__main__":
    main()
