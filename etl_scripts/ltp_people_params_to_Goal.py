#!/usr/bin/env python3
import logging
import uuid
import json
import pandas as pd
import pymysql
import psycopg2
from sqlalchemy import create_engine

# ——— CONFIG & LOGGING ——————————————————————————————————————————————
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s'
)
logger = logging.getLogger(__name__)

MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'db': 'my_app_db',
    'charset': 'utf8mb4',
    'port': 3306
}

POSTGRES_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',
    'password': 'root',
    'dbname': 'postgres',
    'port': '5432'
}

# ——— HELPERS ——————————————————————————————————————————————————————
def safe_json(val):
    if not isinstance(val, str):
        return val
    try:
        return json.loads(val)
    except json.JSONDecodeError:
        return val

def int_to_uuid(val):
    try:
        i = int(val)
        return str(uuid.uuid5(uuid.NAMESPACE_DNS, str(i)))
    except Exception:
        logger.warning(f"Invalid int for UUID5: {val!r}, using random UUID4")
        return str(uuid.uuid4())

def goal_uuid(src_id, key):
    return str(uuid.uuid5(uuid.NAMESPACE_DNS, f"{src_id}-{key}"))

def connect_mysql():
    return pymysql.connect(**MYSQL_CONFIG)

def connect_postgres():
    return psycopg2.connect(**POSTGRES_CONFIG)

def get_existing_goal_ids():
    conn = connect_postgres()
    with conn.cursor() as cur:
        cur.execute('SELECT id FROM public."Goal"')
        ids = {row[0] for row in cur.fetchall()}
    conn.close()
    logger.info(f"Found {len(ids)} existing Goal IDs")
    return ids

# ——— EXTRACT ——————————————————————————————————————————————————————
def extract():
    # 1) pull param rows
    mysql_url = (
        f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@"
        f"{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['db']}?charset={MYSQL_CONFIG['charset']}"
    )
    engine = create_engine(mysql_url)
    sql = """
    SELECT
      id_people_param,
      id_people,
      param_value,
      id_add,
      id_mod,
      date_add,
      date_mod
    FROM ltp_people_params
    WHERE param_type = 'ema_meta'
      AND param_name = 'ema_iap'
    """
    df = pd.read_sql(sql, engine)
    logger.info(f"Extracted {len(df)} ltp_people_params rows")

    # 2) lookup names for id_add/id_mod from ltp_people
    person_ids = set(df['id_add'].dropna().astype(int).tolist() +
                     df['id_mod'].dropna().astype(int).tolist())
    if person_ids:
        ids_csv = ",".join(str(i) for i in person_ids)
        people_sql = f"""
        SELECT id_people, name_first, name_last
        FROM ltp_people
        WHERE id_people IN ({ids_csv})
        """
        people_df = pd.read_sql(people_sql, engine)
        # build dict: int → "First Last"
        people_df['full_name'] = (
            people_df['name_first'].fillna('') + ' ' + people_df['name_last'].fillna('')
        ).str.strip()
        people_dict = people_df.set_index('id_people')['full_name'].to_dict()
        logger.info(f"Fetched {len(people_dict)} people names")
    else:
        people_dict = {}
        logger.info("No id_add/id_mod values to lookup names")

    return df, people_dict

# ——— TRANSFORM ————————————————————————————————————————————————————
def transform(df: pd.DataFrame, people_dict: dict) -> pd.DataFrame:
    logger.info("Transforming to Goal schema")
    existing_ids = get_existing_goal_ids()
    rows = []

    for _, src in df.iterrows():
        src_id     = src['id_people_param']
        mom_uuid   = int_to_uuid(src['id_people'])
        created_at = pd.to_datetime(src['date_add'], unit='s', errors='coerce')
        updated_at = pd.to_datetime(src['date_mod'], unit='s', errors='coerce')
        created_by_id = int_to_uuid(src['id_add'])   if pd.notnull(src['id_add']) else None
        updated_by_id = int_to_uuid(src['id_mod'])   if pd.notnull(src['id_mod']) else None
        created_by_name = people_dict.get(int(src['id_add']), None)
        updated_by_name = people_dict.get(int(src['id_mod']), None)

        payload = safe_json(src['param_value'])
        if not isinstance(payload, dict):
            continue

        for key in sorted(k for k in payload.keys() if k.isdigit()):
            entry = payload[key]
            goal_name = entry.get('goal', '').strip()
            if not goal_name:
                continue

            plan = entry.get('plan', '').strip() or None
            date_str = entry.get('date', '').strip()
            due = pd.to_datetime(date_str, format='%m/%d/%Y', errors='coerce')
            done = due if entry.get('complete') == '1' and pd.notna(due) else None

            gid = goal_uuid(src_id, key)
            if gid in existing_ids:
                logger.debug(f"Skipping existing Goal id={gid}")
                continue

            rows.append({
                'id':               gid,
                'created_at':       created_at,
                'updated_at':       updated_at,
                'name':             goal_name,
                'description':      plan,
                'dueDate':          due,
                'momId':            mom_uuid,
                'created_by_id':    created_by_id,
                'created_by_name':  created_by_name,
                'updated_by_id':    updated_by_id,
                'updated_by_name':  updated_by_name,
                'deleted_at':       0,
                'doneDate':         done
            })

    out = pd.DataFrame(rows)
    logger.info(f"Prepared {len(out)} new Goal rows")
    return out

# ——— LOAD ——————————————————————————————————————————————————————
def load(df: pd.DataFrame):
    pg_url = (
        f"postgresql://{POSTGRES_CONFIG['user']}:{POSTGRES_CONFIG['password']}@"
        f"{POSTGRES_CONFIG['host']}:{POSTGRES_CONFIG['port']}/{POSTGRES_CONFIG['dbname']}"
    )
    engine = create_engine(pg_url)
    df.to_sql('Goal', engine, schema='public', if_exists='append', index=False)
    logger.info(f"Inserted {len(df)} rows into Goal")

# ——— MAIN ——————————————————————————————————————————————————————
def main():
    try:
        raw_df, people_dict = extract()
        transformed = transform(raw_df, people_dict)
        if not transformed.empty:
            load(transformed)
            logger.info("Goal ETL completed successfully")
        else:
            logger.info("No new Goal rows to migrate.")
    except Exception:
        logger.exception("Goal ETL failed!")

if __name__ == "__main__":
    main()
