#!/usr/bin/env python3
import pandas as pd
import pymysql
import psycopg2
from sqlalchemy import create_engine
import logging
import uuid
import json
import re  # Add this import for regex

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define utility functions
def safe_get_param_value(param_value, is_numeric=False):
    """Safely get a parameter value, handling different formats."""
    # Handle None values
    if param_value is None:
        return None
    
    # Handle dictionary values
    if isinstance(param_value, dict):
        # For dictionaries, return the whole dict
        return param_value
    
    # Handle list values
    if isinstance(param_value, list):
        # For lists, return the whole list
        return param_value
    
    # Handle string values
    if isinstance(param_value, str):
        # Try to parse as JSON if it looks like JSON
        if (param_value.startswith('{') and param_value.endswith('}')) or \
           (param_value.startswith('[') and param_value.endswith(']')):
            try:
                return json.loads(param_value)
            except json.JSONDecodeError:
                pass
        
        # If numeric extraction is requested
        if is_numeric:
            return extract_number_from_string(param_value)
        
        # Otherwise return the string as is
        return param_value
    
    # For other types, return as is
    return param_value
def extract_number_from_string(value):
    """Extract the first number from a string, handling various formats."""
    if value is None or not str(value).strip():
        return None
    
    value_str = str(value).strip()
    
    # Try to extract the first number using regex
    numbers = re.findall(r'\d+', value_str)
    if numbers:
        return int(numbers[0])
    
    # If no numbers found, return None
    return None

# Database connection parameters
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'db': 'my_app_db',
    'charset': 'utf8mb4',
    'port': 3306
}

POSTGRES_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',
    'password': 'root',
    'dbname': 'postgres',
    'port': '5432'
}
def safe_json_parse(json_str):
    """Safely parse JSON string to list, handling errors gracefully"""
    if not json_str or not isinstance(json_str, str):
        return []
    
    try:
        return json.loads(json_str)
    except json.JSONDecodeError:
        # Try to handle common issues
        if ',' in json_str:
            # Might be a comma-separated list
            return [lang.strip() for lang in json_str.split(',') if lang.strip()]
        else:
            # Just return as a single item list
            return [json_str] if json_str.strip() else []
    except Exception:
        return []

def safe_get_first_language(json_str):
    """Safely get the first language from a JSON string"""
    languages = safe_json_parse(json_str)
    if languages and isinstance(languages, list) and len(languages) > 0:
        return languages[0]
    return 'english'  # Default value

def safe_param_parse(param_value):
    """Safely parse parameter value which might be in JSON format"""
    if not param_value or not isinstance(param_value, str):
        return param_value
    
    # Try to parse as JSON
    try:
        parsed_value = json.loads(param_value)
        return parsed_value
    except json.JSONDecodeError:
        logger.info(f"Not valid JSON: {param_value}")
        # Not valid JSON, return as is
        return param_value
    except Exception:
        logger.info(f"Error parsing JSON: {param_value}")
        # Any other error, return as is
        return param_value

def int_to_uuid(int_id):
    # Use a fixed namespace for deterministic UUID generation
    namespace = uuid.NAMESPACE_DNS
    # Convert integer to string for uuid5
    return str(uuid.uuid5(namespace, str(int_id)))

def connect_mysql():
    """Connect to MySQL database"""
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        logger.info("Connected to MySQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to MySQL: {e}")
        raise

def connect_postgres():
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(**POSTGRES_CONFIG)
        logger.info("Connected to PostgreSQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to PostgreSQL: {e}")
        raise

def extract_mysql_data():
    """Extract data from MySQL tables ltp_people, ltp_people_params, and ltp_assignments"""
    try:
        engine = create_engine(
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['db']}?charset={MYSQL_CONFIG['charset']}"
        )
        
        # First, extract mom IDs from ltp_assignments
        mom_query = """
        SELECT DISTINCT id_people 
        FROM ltp_assignments 
        WHERE id_role IN (2, 10, 16) 
        AND date_removed IS NULL OR date_removed = 0
        """
        mom_ids_df = pd.read_sql(mom_query, engine)
        logger.info(f"Extracted {len(mom_ids_df)} mom IDs from ltp_assignments")
        
        # Get the list of mom IDs
        mom_ids = mom_ids_df['id_people'].tolist()
        
        if not mom_ids:
            logger.warning("No mom IDs found in ltp_assignments table!")
            return pd.DataFrame(), pd.DataFrame()
        
        # Format the list for SQL IN clause
        mom_ids_str = ','.join(str(id) for id in mom_ids)
        
        # Extract ltp_people filtered by mom IDs
        people_query = f"""
        SELECT * FROM ltp_people
        WHERE id_people IN ({mom_ids_str})
        """
        people_df = pd.read_sql(people_query, engine)
        logger.info(f"Extracted {len(people_df)} rows from ltp_people for moms")
        
        # Extract ltp_people_params filtered by mom IDs
        params_query = f"""
        SELECT * FROM ltp_people_params
        WHERE id_people IN ({mom_ids_str})
        """
        params_df = pd.read_sql(params_query, engine)
        logger.info(f"Extracted {len(params_df)} rows from ltp_people_params for moms")
        
        return people_df, params_df
    except Exception as e:
        logger.error(f"Error extracting data: {e}")
        raise

def transform_data(people_df, params_df):
    """Transform data from MySQL schema to PostgreSQL schema"""
    logger.info("Transforming data from ltp_people to mom")
    
    # Get PostgreSQL table schema
    postgres_conn = connect_postgres()
    cursor = postgres_conn.cursor()
    cursor.execute("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = %s
        ORDER BY ordinal_position
    """, ('Mom',))
    
    pg_columns = [row[0] for row in cursor.fetchall()]
    logger.info(f"Target PostgreSQL columns: {pg_columns}")
    cursor.close()
    postgres_conn.close()
    
    # Create new DataFrame for transformed data
    logger.info("Creating new DataFrame for transformed data")
    new_df = pd.DataFrame()
    
    # Process params_df to create a dictionary of param values for each person
    logger.info("Processing params_df to create dictionary of param values")
    params_dict = {}
    for _, row in params_df.iterrows():
        person_id = row['id_people']
        param_name = row['param_name']
        param_value = row['param_value']
        
        if person_id not in params_dict:
            params_dict[person_id] = {}
        
        # Handle intake_form specially - it contains many nested parameters
        if param_name == 'intake_form':
            try:
                intake_data = json.loads(param_value)
                # Add each field from intake_form as a separate parameter
                for key, value in intake_data.items():
                    params_dict[person_id][key] = value
                logger.info(f"Parsed intake_form for person_id={person_id} with {len(intake_data)} fields")
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse intake_form JSON for person_id={person_id}")
                params_dict[person_id][param_name] = param_value
            except Exception as e:
                logger.error(f"Error processing intake_form for person_id={person_id}: {e}")
                params_dict[person_id][param_name] = param_value
        else:
            params_dict[person_id][param_name] = param_value
        
        # Log a sample of parameter values for debugging
        if _ < 10:  # Only log the first 10 rows
            logger.info(f"Sample param: person_id={person_id}, param_name={param_name}, param_value={param_value}")
    
    logger.info(f"Created params dictionary with {len(params_dict)} entries")
    
    # Apply direct column mappings first
    logger.info("Applying direct column mappings")
    # Direct column mappings
    direct_mappings = {
        'id_people': 'id',
        'name_first': 'first_name',
        'name_last': 'last_name',
        'people_phone_mobile': 'phone_other',
        'people_gender': 'gender_c',
        'people_email_primary': 'email1',
        'username': 'account_name',
        'id_affiliate': 'affiliate_id',
        'id_agency': 'agency_id',
        'date_birth': 'birthdate',
        'address_street_1': 'primary_address_street',
        'address_city': 'primary_address_city',
        'address_state': 'primary_address_state',
        'address_zip': 'primary_address_postalcode',
        'address_county': 'primary_address_county_c',
        'address_access_instructions': 'address_access_c',
        'date_add': 'created_at',
        'date_mod': 'updated_at',
        'id_add': 'created_by_id',
        'date_removed': 'deleted_at',
        'people_status_text': 'prospect_status',
        'state': 'status',
        'url_avatar': 'photo_url',
        'people_phone_home': 'phone_alternate_c',
        'address_street_2': 'primary_address_street_two_c',
        'contact_emergency': 'emergency_contact_name_c',
        'contact_emergency_phone': 'emergency_contact_number_c',
        'people_languages': 'languages'
    }
    
    # Apply direct mappings
    for mysql_col, pg_col in direct_mappings.items():
        if mysql_col in people_df.columns and pg_col in pg_columns:
            logger.debug(f"Mapping {mysql_col} to {pg_col}")
            try:
                if mysql_col == 'id_people':
                    new_df[pg_col] = people_df[mysql_col].apply(lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else str(uuid.uuid4()))
                elif mysql_col in ['date_add', 'date_mod', 'date_removed', 'date_birth']:
                    new_df[pg_col] = pd.to_datetime(people_df[mysql_col], unit='s', errors='coerce')
                elif mysql_col == 'state':
                    # Map state values to appropriate status
                    new_df[pg_col] = people_df[mysql_col].apply(
                        lambda x: 'active' if pd.notnull(x) and x in [1, 46, 42] else 
                                     ('inactive' if pd.notnull(x) and (20 <= x < 30) else
                                     ('inactive' if pd.notnull(x) and x < 0 else 'inactive'))
                    )
                    # Log the mapping for verification
                    state_mapping = people_df[['id_people', mysql_col]].copy()
                    state_mapping['mapped_status'] = new_df[pg_col]
                    logger.info(f"State mapping sample (first 5 rows):\n{state_mapping.head().to_string()}")
                elif mysql_col == 'people_languages':
                    # Handle languages as JSON array with better error handling
                    new_df[pg_col] = people_df[mysql_col].apply(
                        lambda x: safe_json_parse(x)
                    )
                    # Also set language_preference_c to first language
                    if 'language_preference_c' in pg_columns:
                        new_df['language_preference_c'] = people_df[mysql_col].apply(
                            lambda x: safe_get_first_language(x)
                        )
                    # Also set languages_c to all languages
                    if 'languages_c' in pg_columns:
                        new_df['languages_c'] = people_df[mysql_col].apply(
                            lambda x: safe_json_parse(x)
                        )
                elif mysql_col == 'url_avatar':
                    new_df[pg_col] = people_df[mysql_col]
                    # Extract filename for photo_s3_file_name
                    if 'photo_s3_file_name' in pg_columns:
                        new_df['photo_s3_file_name'] = people_df[mysql_col].apply(
                            lambda x: x.split('/')[-1] if pd.notnull(x) and x else None
                        )
                else:
                    new_df[pg_col] = people_df[mysql_col]
            except Exception as e:
                logger.error(f"Error mapping {mysql_col} to {pg_col}: {e}")
                raise
    
    # Apply param mappings
    logger.info("Applying param mappings to new DataFrame")
    param_mappings = {
        # Direct parameters
        'pregnant': 'currently_pregnant_c',
        'num_children': ['number_of_children_c', 'children_in_home', 'number_of_children_in_home_c'],
        'additional_needs': 'need_details_c',
        'interest_interm_caregiver': 'caregiver_type_c',
        'referring_for': 'referral_type_c',
        'client_consent': 'consent_obtained_c',
        'referrer_first_name': 'referring_contact_first_name_c',
        'referrer_last_name': 'referring_contact_last_name_c',
        'referrer_email': 'referring_contact_email_c',
        'referrer_phone': 'referring_contact_phone_c',
        'discharge_reason': 'discharge_reason_c',
        'programs_benevolance_prev': 'connected_benevolance_c',
        'programs_childcare_prev': 'connected_childcare_c',
        'programs_closet_visit_prev': 'connected_closet_c',
        'programs_education_prev': 'connected_education_c',
        'programs_health_prev': 'connected_health_c',
        'programs_housing_prev': 'connected_housing_c',
        'programs_legal_prev': 'connected_legal_c',
        'programs_mental_health_prev': 'connected_mental_health_c',
        'programs_substance_prev': 'connected_substance_c',
        'date_intake': 'date_entered',
        'emergency_contact_relationship': 'emergency_contact_relation_c',
        'marital_status': 'martial_status',
        'race': 'race_c',
        
        # Fields from intake_form (now directly accessible)
        'caregiver_type': 'caregiver_type_c',
        'housing_status': 'housing_status_c',
        'additional_comments': 'what_else_c',
        'employment_status': 'employment_status_c',
        'education_level': 'education_level_c',
        'income': 'income_c',
        'has_health_insurance': 'has_health_insurance_c',
        'health_insurance_provider': 'health_insurance_provider_c',
        'history_of_substance_abuse': 'history_of_substance_abuse_c',
        'substance_last_use': 'substance_last_use_c',
        'attending_recovery': 'attending_recovery_c',
        'receiving_substance_treatment': 'receiving_substance_treatment_c',
        'received_medical_treatment': 'received_medical_treatment_c',
        'taking_medications': 'taking_medications_c',
        'prescription_compliance': 'prescription_compliance_c',
        'previous_cps_past_involvement': 'previous_cps_past_involvement_c',
        'previous_cps_current_investigation': 'previous_cps_current_investigation_c',
        'previous_cps_current_plan': 'previous_cps_current_plan_c',
        'additional_comments_cps': 'additional_comments_cps_c',
        'transportation': 'transportation_c',
        'custody': 'custody_c',
        'school_status': 'school_status_c',
        'nationality': 'nationality_c',
        'ages_of_children': 'ages_of_children_c',
        
        # Fields from interest_form (with interest_ prefix)
        'interest_housing_status': 'housing_status_c',
        'interest_marital_status': 'martial_status',
        'interest_race': 'race_c',
        'interest_employment_status': 'employment_status_c',
        'interest_education_level': 'education_level_c',
        'interest_income': 'income_c',
        'interest_additional_comments': 'what_else_c'
    }
    
    # Log the number of people in the DataFrame
    logger.info(f"Number of people in DataFrame: {len(new_df)}")
    
    # Log the first few IDs to verify
    if not new_df.empty:
        logger.info(f"First few IDs in new_df: {new_df['id'].head().tolist()}")
    
    # Create a mapping from person_id to UUID for faster lookups
    id_to_uuid = {id_val: int_to_uuid(id_val) for id_val in people_df['id_people'].unique()}
    logger.info(f"Created ID to UUID mapping with {len(id_to_uuid)} entries")
    
    # Apply param mappings with improved error handling
    param_mapping_counts = {param: 0 for param in param_mappings.keys()}

    for person_id, person_params in params_dict.items():
        # Convert person_id to UUID for lookup
        uuid_id = id_to_uuid.get(person_id)
        if not uuid_id:
            logger.warning(f"Person ID {person_id} not found in ID to UUID mapping")
            continue
        
        # Find the row index for this person
        idx = new_df[new_df['id'] == uuid_id].index
        
        if len(idx) == 0:
            logger.warning(f"Person ID {person_id} (UUID: {uuid_id}) not found in transformed DataFrame")
            continue
        
        for param_name, pg_cols in param_mappings.items():
            if param_name in person_params:
                raw_param_value = person_params[param_name]
                
                # Handle multiple target columns for same param
                if isinstance(pg_cols, list):
                    for pg_col in pg_cols:
                        if pg_col in pg_columns:
                            try:
                                # Determine if this is a numeric field
                                is_numeric = pg_col in ['number_of_children_c', 'children_in_home', 'number_of_children_in_home_c']
                                
                                # Get the processed parameter value
                                param_value = safe_get_param_value(raw_param_value, is_numeric)
                                
                                # Use a safer approach for setting values
                                for i in idx:
                                    new_df.at[i, pg_col] = param_value
                                param_mapping_counts[param_name] += 1
                            except Exception as e:
                                logger.error(f"Error setting {pg_col} for person {person_id}: {e}")
                else:
                    pg_col = pg_cols
                    if pg_col in pg_columns:
                        try:
                            # Determine if this is a numeric field
                            is_numeric = pg_col in ['number_of_children_c', 'children_in_home', 'number_of_children_in_home_c']
                            
                            # Get the processed parameter value
                            param_value = safe_get_param_value(raw_param_value, is_numeric)
                            
                            # Use a safer approach for setting values
                            for i in idx:
                                new_df.at[i, pg_col] = param_value
                            param_mapping_counts[param_name] += 1
                        except Exception as e:
                            logger.error(f"Error setting {pg_col} for person {person_id}: {e}")
    
    # Log the number of mappings applied for each parameter
    logger.info("Parameter mapping counts:")
    for param, count in param_mapping_counts.items():
        logger.info(f"  {param}: {count}")
    
    # Handle special cases
    logger.info("Handling special cases")
    if 'referred_to_agency_id' in pg_columns:
        new_df['referred_to_agency_id'] = people_df.apply(
            lambda row: row.get('cert_mentor_agency_ids', '') or row.get('cert_ff_agency_ids', ''),
            axis=1
        )
    
    # Ensure required fields have values
    logger.info("Ensuring required fields have values")
    required_fields = ['id', 'first_name', 'last_name', 'created_at', 'updated_at']
    for field in required_fields:
        if field in pg_columns and field in new_df.columns:
            try:
                if field == 'id':
                    new_df[field] = new_df[field].fillna(pd.Series([str(uuid.uuid4()) for _ in range(len(new_df))], index=new_df.index))
                elif field in ['first_name', 'last_name']:
                    new_df[field] = new_df[field].fillna('')
                elif field in ['created_at', 'updated_at']:
                    new_df[field] = new_df[field].fillna(pd.Timestamp.now())
            except Exception as e:
                logger.error(f"Error ensuring required field {field} has value: {e}")
    
    # Set prospect_status to 'prospect' by default
    if 'prospect_status' in pg_columns:
        logger.info("Setting default prospect_status to 'prospect'")
        new_df['prospect_status'] = 'prospect'
    
    # Add default values for any missing columns
    logger.info("Adding default values for missing columns")
    for col in pg_columns:
        if col not in new_df.columns:
            if col in ['what_else_c', 'supports_court_order_c', 'service_selected_c', 'created_by_name',
                      'closed_date_c', 'language_notes_c', 'assigned_user_id', 'referral_sub_status',
                      'referred_to_agency_reason', 'sms_message_opt_in', 'preferred_contact_method_c',
                      'cultural_heritage_c', 'pregnant_due_date']:
                new_df[col] = None
    
    logger.info(f"Transformed DataFrame has {len(new_df.columns)} columns: {new_df.columns.tolist()}")
    return new_df

def load_to_postgres(df, target_table):
    """Load transformed data to PostgreSQL"""
    try:
        # First, check and alter column types if needed
        logger.info("Checking and updating column types in PostgreSQL table")
        postgres_conn = connect_postgres()
        cursor = postgres_conn.cursor()
        
        # Get column types from PostgreSQL
        cursor.execute("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = %s
        """, (target_table,))
        
        column_info = {row[0]: {'data_type': row[1], 'is_nullable': row[2]} for row in cursor.fetchall()}
        logger.info(f"Retrieved {len(column_info)} column types from PostgreSQL")
        
        # Process numeric columns
        numeric_columns = ['number_of_children_c', 'children_in_home', 'number_of_children_in_home_c']
        for col in numeric_columns:
            if col in df.columns:
                logger.info(f"Converting column {col} to integer")
                try:
                    # Convert to integer, handling various formats
                    df[col] = df[col].apply(lambda x: extract_number_from_string(x) if isinstance(x, str) else x)
                    # Convert to numeric, coercing errors to NaN
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                    
                    # Check if column is NOT NULL and set default value if needed
                    if col in column_info and column_info[col]['is_nullable'] == 'NO':
                        logger.info(f"Setting default value 0 for NOT NULL column {col}")
                        df[col] = df[col].fillna(0)
                except Exception as e:
                    logger.error(f"Error converting {col} to integer: {e}")
                    # Set to default value if conversion fails and column is NOT NULL
                    if col in column_info and column_info[col]['is_nullable'] == 'NO':
                        df[col] = 0
                    else:
                        df[col] = None
        
        # Process all columns based on PostgreSQL types
        for col, info in column_info.items():
            dtype = info['data_type']
            is_nullable = info['is_nullable']
            
            if col in df.columns:
                if dtype in ('integer', 'bigint', 'smallint'):
                    if col not in numeric_columns:  # Skip columns we already processed
                        logger.info(f"Converting column {col} to integer")
                        try:
                            # Convert to numeric, coercing errors to NaN
                            df[col] = pd.to_numeric(df[col], errors='coerce')
                            
                            # Set default value for NOT NULL columns
                            if is_nullable == 'NO':
                                logger.info(f"Setting default value 0 for NOT NULL column {col}")
                                df[col] = df[col].fillna(0)
                        except Exception as e:
                            logger.error(f"Error converting {col} to integer: {e}")
                            # Set to default value if conversion fails and column is NOT NULL
                            if is_nullable == 'NO':
                                df[col] = 0
                            else:
                                df[col] = None
                
                elif dtype in ('numeric', 'decimal', 'real', 'double precision'):
                    logger.info(f"Converting column {col} to float")
                    try:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                        
                        # Set default value for NOT NULL columns
                        if is_nullable == 'NO':
                            logger.info(f"Setting default value 0.0 for NOT NULL column {col}")
                            df[col] = df[col].fillna(0.0)
                    except Exception as e:
                        logger.error(f"Error converting {col} to float: {e}")
                        # Set to default value if conversion fails and column is NOT NULL
                        if is_nullable == 'NO':
                            df[col] = 0.0
                        else:
                            df[col] = None
        
        # Convert DataFrame columns to appropriate types before loading
        for col in df.columns:
            if col in ('created_at', 'updated_at', 'date_of_birth') and col in df.columns:
                # Ensure these columns are timestamp type in the DataFrame
                if df[col].dtype != 'datetime64[ns]':
                    logger.info(f"Converting DataFrame column {col} to timestamp")
                    df[col] = pd.to_datetime(df[col], errors='coerce')
            elif col == 'deleted_at' and col in df.columns:
                # For deleted_at, use the Unix epoch (1970-01-01) as default
                logger.info(f"Setting default value for DataFrame column {col}")
                df[col] = pd.to_datetime(df[col], errors='coerce').fillna(pd.Timestamp('1970-01-01'))
        
        # Now load the data
        engine = create_engine(
            f"postgresql://{POSTGRES_CONFIG['user']}:{POSTGRES_CONFIG['password']}@{POSTGRES_CONFIG['host']}:{POSTGRES_CONFIG['port']}/{POSTGRES_CONFIG['dbname']}"
        )
        
        # Check required columns
        required_columns = ['id', 'first_name', 'last_name', 'created_at', 'updated_at']
        missing_columns = [col for col in required_columns if col not in df.columns]
         
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Load data to PostgreSQL
        df.to_sql(target_table, engine, schema='public', if_exists='append', index=False)
        
        logger.info(f"Loaded {len(df)} rows to PostgreSQL table public.{target_table}")
        return True
    except Exception as e:
        logger.error(f"Error loading data to {target_table}: {e}")
        raise

def main():
    """Execute ETL for ltp_people to mom"""
    try:
        # Extract
        people_df, params_df = extract_mysql_data()
        
        # Transform
        mom_df = transform_data(people_df, params_df)
        
        # Print sample of transformed data
        logger.info(f"Transformed data sample:\n{mom_df.head()}")
        
        # Load
        load_result = load_to_postgres(mom_df, 'Mom')
        
        if load_result:
            logger.info("ETL process completed successfully")
        
    except Exception as e:
        logger.error(f"ETL process failed: {e}")

if __name__ == "__main__":
    main()


