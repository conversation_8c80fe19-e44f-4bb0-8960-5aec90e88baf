#!/usr/bin/env python3
import logging
import uuid
import json
import pandas as pd
import pymysql
import psycopg2
from sqlalchemy import create_engine

logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s %(message)s')
logger = logging.getLogger(__name__)

MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'db': 'my_app_db',
    'charset': 'utf8mb4',
    'port': 3306
}

POSTGRES_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',
    'password': 'root',
    'dbname': 'postgres',
    'port': '5432'
}

def safe_float(val):
    """Return float(val) or None if val is blank or non‐numeric."""
    try:
        if val is None or (isinstance(val, str) and val.strip() == ""):
            return None
        return float(val)
    except (ValueError, TypeError):
        return None

def int_to_uuid(int_id):
    """Deterministic UUID5 on int_namespace, or random UUID4 on bad input."""
    try:
        i = int(int_id)
        return str(uuid.uuid5(uuid.NAMESPACE_DNS, str(i)))
    except Exception:
        logger.warning(f"Invalid int for UUID5: {int_id!r}, falling back to random UUID4")
        return str(uuid.uuid4())

def safe_json(val):
    """Parse a JSON string; return dict or raw value."""
    if not isinstance(val, str):
        return val
    try:
        return json.loads(val)
    except json.JSONDecodeError:
        return val

def connect_mysql():
    return pymysql.connect(**MYSQL_CONFIG)

def connect_postgres():
    return psycopg2.connect(**POSTGRES_CONFIG)

def get_existing_bene_ids():
    """Fetch existing BenevolenceNeed.id values to skip duplicates."""
    conn = connect_postgres()
    with conn.cursor() as cur:
        cur.execute('SELECT id FROM public."BenevolenceNeed"')
        ids = {row[0] for row in cur.fetchall()}
    conn.close()
    logger.info(f"Found {len(ids)} existing BenevolenceNeed IDs")
    return ids

# ---------------------------
# EXTRACT
# ---------------------------
def extract():
    engine = create_engine(
        f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@"
        f"{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['db']}?charset={MYSQL_CONFIG['charset']}"
    )
    sql = """
    SELECT 
      id_people_param,
      id_people,
      param_value,
      state,
      param_name,
      param_link_type,
      id_add, id_mod,
      date_add, date_mod, date_removed
    FROM ltp_people_params
    WHERE param_type = 'benevolence_log'
      AND param_name = 'ema_benevolence_log'
    """
    df = pd.read_sql(sql, engine)
    logger.info(f"Extracted {len(df)} benevolence_log rows")
    return df

# ---------------------------
# TRANSFORM
# ---------------------------
def transform(df: pd.DataFrame) -> pd.DataFrame:
    logger.info("Transforming to BenevolenceNeed schema")

    # base DataFrame
    out = pd.DataFrame()

    # 1) id: new UUID
    out['id'] = df['id_people_param'].map(int_to_uuid)

    # 2) timestamps
    for src, tgt in [('date_add','created_at'), ('date_mod','updated_at'), ('date_removed','resolved_date_c')]:
        out[tgt] = pd.to_datetime(df[src], unit='s', errors='coerce')

    # 3) name
    out['name'] = df['param_name']

    # 4) momId: deterministic from id_people
    out['momId'] = df['id_people'].map(int_to_uuid)

    # 5) JSON unpack
    parsed = df['param_value'].apply(safe_json)
    # description
    out['description'] = parsed.map(lambda d: d.get('msg_body') or d.get('msg_bdy') if isinstance(d, dict) else None)
    # financial_requested & gifted
    out['financial_amount_requested_c'] = parsed.map(lambda d: float(d.get('amount_requested')) if isinstance(d, dict) and d.get('amount_requested') else None)
    out['financial_amount_gifted_c']    = parsed.map(lambda d: float(d.get('amount_gifted'))    if isinstance(d, dict) and d.get('amount_gifted')    else None)
    # prevention plan
    out['financial_prevention_plan_c']  = parsed.map(lambda d: d.get('request_plan') if isinstance(d, dict) else None)
    # provided date
    out['provided_date_c'] = parsed.map(lambda d: pd.to_datetime(d.get('date_entry'), unit='s') if isinstance(d, dict) and d.get('date_entry') else pd.NaT)
    # address flag: assume state=1 means still open → did_address=False
    out['did_address_need_c'] = df['state'].map(lambda s: False if s == 1 else True)
    # representative/notes
    out['notes_c'] = parsed.map(lambda d: d.get('ema_representative') or None)

    # 6) type (enum)
    def map_type(d):
        """
        Map the JSON benevolence_type into our 3‐value enum:
        - 'closet_visit'  → Physical
        - 'financial_gift' → Financial
        - 'careportal'     → Other
        Default to 'Other' if it’s missing or unrecognized.
        """
        if not isinstance(d, dict):
            return 'Other'

        t = d.get('benevolence_type', '').lower()
        if t == 'closet_visit':
            return 'Physical'
        elif t == 'financial_gift':
            return 'Financial'
        elif t == 'careportal':
            return 'Other'
        else:
            # catches anything else (e.g. typos, new types)
            return 'Other'
    out['type_c'] = parsed.map(map_type)

    # 7) flags & defaults
    out['is_urgent_c'] = False
    out['other_is_referral_needed_c'] = False
    out['non_addressal_comment_c'] = None
    out['pg_fulfillment_method_c'] = df['param_link_type']
    # out['physical_good_monetary_value_c'] = parsed.map(
    #     lambda d: float(d.get('amount_requested')) if isinstance(d, dict) and d.get('benevolence_type') != 'financial' else None
    # )
    out['physical_good_monetary_value_c'] = parsed.map(
        lambda d: safe_float(d.get('amount_requested'))
                  if isinstance(d, dict) and d.get('benevolence_type') != 'financial'
                  else None
    )
    out['deleted_at'] = 0
    out['resolvedByUserId'] = df['id_mod'].map(int_to_uuid)
    out['created_by_id']    = df['id_add'].map(int_to_uuid)
    out['updated_by_id']    = df['id_mod'].map(int_to_uuid)
    # leave created_by_name / updated_by_name NULL for now

    # 8) skip duplicates
    existing = get_existing_bene_ids()
    before = len(out)
    out = out[~out['id'].isin(existing)].reset_index(drop=True)
    logger.info(f"Filtered out {before - len(out)} already‐migrated rows")

    return out

# ---------------------------
# LOAD
# ---------------------------
def load(df: pd.DataFrame):
    engine = create_engine(
        f"postgresql://{POSTGRES_CONFIG['user']}:{POSTGRES_CONFIG['password']}@"
        f"{POSTGRES_CONFIG['host']}:{POSTGRES_CONFIG['port']}/{POSTGRES_CONFIG['dbname']}"
    )
    df.to_sql('BenevolenceNeed', engine, schema='public', if_exists='append', index=False)
    logger.info(f"Inserted {len(df)} rows into BenevolenceNeed")

# ---------------------------
# MAIN
# ---------------------------
def main():
    try:
        raw = extract()
        transformed = transform(raw)
        if not transformed.empty:
            load(transformed)
            logger.info("BenevolenceNeed ETL completed successfully")
        else:
            logger.info("No new rows to migrate.")
    except Exception as e:
        logger.exception("ETL failed!")

if __name__ == "__main__":
    main()
