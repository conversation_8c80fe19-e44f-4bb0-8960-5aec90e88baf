#!/usr/bin/env python3
import pandas as pd
import pymysql
import psycopg2
from sqlalchemy import create_engine
import logging
import uuid
import requests
from urllib.parse import urlparse, urljoin
import boto3
from botocore.exceptions import ClientError
import os
import mimetypes
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('etl_debug.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Database connection parameters
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'db': 'my_app_db',
    'charset': 'utf8mb4',
    'port': 3306
}

POSTGRES_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',
    'password': 'root',
    'dbname': 'postgres',
    'port': 5432
}

# S3 configuration
S3_BUCKET = "ema-document-uploads"
S3_REGION = "us-east-1"
S3_BASE_URL = "https://ema-document-uploads.s3.amazonaws.com/"
AWS_ACCESS_KEY_ID = "********************"
AWS_SECRET_ACCESS_KEY = "ed9fiwR8TH3BSQhOJR7D7IeOGsTbYGDpL3K0EvPl"
AUTH_CREDENTIALS = {
    'email': '<EMAIL>',
    'password': '123456!ema'
}
BASE_URL = 'https://ema.promiseserves.org/'
LOGIN_URL = 'https://ema.promiseserves.org/login'

# Utility functions
def int_to_uuid(int_id):
    """Convert integer ID to UUID."""
    try:
        int_id = int(int_id)
        namespace = uuid.NAMESPACE_DNS
        return str(uuid.uuid5(namespace, str(int_id)))
    except (ValueError, TypeError):
        logger.warning(f"Invalid ID for UUID conversion: {int_id}")
        return str(uuid.uuid4())

def connect_mysql():
    """Connect to MySQL database."""
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        logger.info("Connected to MySQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to MySQL: {e}")
        raise

def connect_postgres():
    """Connect to PostgreSQL database."""
    try:
        conn = psycopg2.connect(**POSTGRES_CONFIG)
        logger.info("Connected to PostgreSQL database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to PostgreSQL: {e}")
        raise

def get_existing_ids():
    """Retrieve existing IDs from the Document table."""
    try:
        conn = connect_postgres()
        cursor = conn.cursor()
        cursor.execute('SELECT id FROM public."Document"')
        existing_ids = {row[0] for row in cursor.fetchall()}
        cursor.close()
        conn.close()
        logger.info(f"Retrieved {len(existing_ids)} existing IDs from Document table")
        return existing_ids
    except Exception as e:
        logger.error(f"Error retrieving existing IDs: {e}")
        raise

def is_valid_file(local_path, expected_type):
    """Check if file is valid (PDF or image) based on header."""
    try:
        with open(local_path, 'rb') as f:
            header = f.read(8)
            if expected_type == 'pdf' and header.startswith(b'%PDF-'):
                return True
            elif expected_type in ['png', 'jpeg', 'jpg']:
                if header.startswith(b'\x89PNG') or header.startswith(b'\xFF\xD8'):
                    return True
            logger.warning(f"Invalid file header at {local_path}: {header}")
            return False
    except Exception as e:
        logger.error(f"Error validating file {local_path}: {e}")
        return False

def upload_to_s3(url, filename):
    """Download file from URL after logging in, upload to S3 if not present, return S3 URL."""
    try:
        # Initialize S3 client
        s3_client = boto3.client(
            's3',
            region_name=S3_REGION,
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY
        )
        s3_key = filename

        # Check if file already exists in S3
        try:
            s3_client.head_object(Bucket=S3_BUCKET, Key=s3_key)
            s3_url = f"{S3_BASE_URL}{s3_key}"
            logger.info(f"File {filename} already exists in S3, skipping upload: {s3_url}")
            return s3_url, filename
        except ClientError as e:
            if e.response['Error']['Code'] != '404':
                logger.error(f"Error checking S3 file {filename}: {e}")
                return None, None
            # File does not exist, proceed with download/upload

        # Handle [BASE] and [BASEURL] URLs
        if url.startswith('[BASE]') or url.startswith('[BASEURL]'):
            url = urljoin(BASE_URL, url.replace('[BASE]', '').replace('[BASEURL]', ''))

        # Initialize session for login
        session = requests.Session()
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/pdf,image/png,*/*',
            'Accept-Encoding': 'gzip, deflate, br'
        }

        # Attempt login
        login_data = {
            'email': AUTH_CREDENTIALS['email'],
            'password': AUTH_CREDENTIALS['password']
        }
        logger.info(f"Attempting login to {LOGIN_URL}")
        login_response = session.post(LOGIN_URL, data=login_data, headers=headers)
        logger.info(f"Login Status: {login_response.status_code}")
        if login_response.status_code != 200:
            logger.error(f"Login failed: Status {login_response.status_code}")
            return None, None

        # Download file
        logger.info(f"Downloading file from {url}")
        response = session.get(url, headers=headers, stream=True, timeout=30)
        logger.info(f"Status Code: {response.status_code}")
        logger.info(f"Headers: {response.headers}")

        if response.status_code != 200:
            logger.error(f"Failed to download {url}: Status {response.status_code}")
            return None, None

        content_type = response.headers.get('Content-Type', 'unknown')
        logger.info(f"Content-Type: {content_type}")
        if 'pdf' not in content_type.lower() and 'image' not in content_type.lower():
            logger.warning(f"Unexpected Content-Type: {content_type}, file may be invalid")

        # Save file locally
        download_dir = "./downloads"
        os.makedirs(download_dir, exist_ok=True)
        local_path = os.path.join(download_dir, filename)
        logger.info(f"Saving file to {local_path}")
        first_bytes = b""
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    if len(first_bytes) < 100:
                        first_bytes += chunk[:100 - len(first_bytes)]
                    f.write(chunk)
        logger.info(f"First 100 bytes: {first_bytes[:100]}")
        file_size = os.path.getsize(local_path)
        logger.info(f"Downloaded {filename} to {local_path} (Size: {file_size} bytes)")
        expected_size = int(response.headers.get('Content-Length', 0))
        if expected_size and file_size != expected_size:
            logger.warning(f"File size mismatch: Expected {expected_size} bytes, got {file_size} bytes")

        # Determine Content-Type for S3
        content_type, _ = mimetypes.guess_type(filename)
        if not content_type:
            content_type = response.headers.get('Content-Type', 'application/octet-stream')
            logger.warning(f"Could not determine Content-Type for {filename}, using {content_type}")

        # Upload to S3
        logger.info(f"Uploading {filename} to S3")
        with open(local_path, 'rb') as f:
            s3_client.upload_fileobj(
                f,
                S3_BUCKET,
                s3_key,
                ExtraArgs={'ContentType': content_type}
            )

        # Generate S3 URL
        s3_url = f"{S3_BASE_URL}{s3_key}"
        logger.info(f"Uploaded {filename} to S3: {s3_url}")
        return s3_url, filename

    except ClientError as e:
        logger.error(f"S3 upload error for {filename}: {e}")
        return None, None
    except Exception as e:
        logger.error(f"Error processing {url}: {e}")
        return None, None

def extract_mysql_data():
    """Extract data from MySQL ltp_resources and ltp_people tables."""
    try:
        engine = create_engine(
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['db']}?charset={MYSQL_CONFIG['charset']}"
        )
        # Extract ltp_resources
        resources_query = """
        SELECT id_resource, resource_name, resource_desc_short, resource_desc_long, resource_content,
               resource_filename, resource_format, date_add, date_mod, date_removed, id_add, id_mod, url_download
        FROM ltp_resources
        """
        resources_df = pd.read_sql(resources_query, engine)
        logger.info(f"Extracted {len(resources_df)} rows from ltp_resources")

        # Check for duplicate id_resource values
        duplicates = resources_df[resources_df.duplicated(subset=['id_resource'], keep=False)]
        if not duplicates.empty:
            logger.warning(f"Found {len(duplicates)} duplicate id_resource values: {duplicates['id_resource'].tolist()}")
            resources_df = resources_df.drop_duplicates(subset=['id_resource'], keep='first')
            logger.info(f"After deduplication, {len(resources_df)} rows remain")

        # Extract ltp_people for created_by_name (id_add)
        id_add_set = set(resources_df['id_add'].dropna().astype(int).tolist())
        if id_add_set:
            add_query = """
            SELECT id_add, name_first, name_last
            FROM ltp_people
            WHERE id_add IN (%s)
            """ % ','.join(str(id) for id in id_add_set)
            add_df = pd.read_sql(add_query, engine)
            logger.info(f"Extracted {len(add_df)} rows from ltp_people for id_add")
        else:
            add_df = pd.DataFrame(columns=['id_add', 'name_first', 'name_last'])
            logger.info("No id_add values found, skipping ltp_people extraction for id_add")

        # Extract ltp_people for updated_by_name (id_mod)
        id_mod_set = set(resources_df['id_mod'].dropna().astype(int).tolist())
        if id_mod_set:
            mod_query = """
            SELECT id_mod, name_first, name_last
            FROM ltp_people
            WHERE id_mod IN (%s)
            """ % ','.join(str(id) for id in id_mod_set)
            mod_df = pd.read_sql(mod_query, engine)
            logger.info(f"Extracted {len(mod_df)} rows from ltp_people for id_mod")
        else:
            mod_df = pd.DataFrame(columns=['id_mod', 'name_first', 'name_last'])
            logger.info("No id_mod values found, skipping ltp_people extraction for id_mod")

        return resources_df, add_df, mod_df
    except Exception as e:
        logger.error(f"Error extracting data: {e}")
        raise

def transform_data(resources_df, add_df, mod_df):
    """Transform data from MySQL ltp_resources to PostgreSQL Document schema."""
    logger.info("Transforming data from ltp_resources to Document")

    # Get PostgreSQL table schema
    postgres_conn = connect_postgres()
    cursor = postgres_conn.cursor()
    cursor.execute("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = %s
        ORDER BY ordinal_position
    """, ('Document',))
    pg_columns = {row[0]: row[1] for row in cursor.fetchall()}
    logger.info(f"Target PostgreSQL columns: {list(pg_columns.keys())}")
    cursor.close()
    postgres_conn.close()

    # Get existing IDs to avoid duplicates
    existing_ids = get_existing_ids()

    # Create new DataFrame for transformed data
    new_df = pd.DataFrame()

    # Direct column mappings
    direct_mappings = {
        'id_resource': 'id',
        'resource_name': 'document_name',
        'resource_content': 'filecontents',
        'resource_filename': 'filename',
        'resource_format': 'mimeType',
        'date_add': 'created_at',
        'date_mod': 'updated_at',
        'date_removed': 'deleted_at',
        'id_add': 'created_by_id',
        'id_mod': 'updated_by_id'
    }

    # Create dictionaries for people names
    add_df['full_name'] = add_df['name_first'].str.cat(add_df['name_last'], sep=' ', na_rep='').str.strip()
    add_dict = add_df.set_index('id_add')['full_name'].to_dict()
    mod_df['full_name'] = mod_df['name_first'].str.cat(mod_df['name_last'], sep=' ', na_rep='').str.strip()
    mod_dict = mod_df.set_index('id_mod')['full_name'].to_dict()

    # Apply direct mappings
    for mysql_col, pg_col in direct_mappings.items():
        if mysql_col in resources_df.columns and pg_col in pg_columns:
            logger.debug(f"Mapping {mysql_col} to {pg_col}")
            try:
                if mysql_col == 'id_resource':
                    new_df[pg_col] = resources_df[mysql_col].apply(
                        lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else str(uuid.uuid4())
                    )
                elif mysql_col in ['id_add', 'id_mod']:
                    new_df[pg_col] = resources_df[mysql_col].apply(
                        lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else None
                    )
                elif mysql_col in ['date_add', 'date_mod', 'date_removed']:
                    new_df[pg_col] = pd.to_datetime(resources_df[mysql_col], unit='s', errors='coerce')
                else:
                    new_df[pg_col] = resources_df[mysql_col]
            except Exception as e:
                logger.error(f"Error mapping {mysql_col} to {pg_col}: {e}")
                raise

    # Handle description (concatenate resource_desc_short and resource_desc_long)
    if 'description' in pg_columns:
        logger.debug("Concatenating resource_desc_short and resource_desc_long for description")
        new_df['description'] = resources_df['resource_desc_short'].str.cat(
            resources_df['resource_desc_long'], sep=' ', na_rep='').str.strip()

    # Handle created_by_name and updated_by_name
    if 'created_by_name' in pg_columns:
        new_df['created_by_name'] = resources_df['id_add'].map(add_dict).fillna('')
    if 'updated_by_name' in pg_columns:
        new_df['updated_by_name'] = resources_df['id_mod'].map(mod_dict).fillna('')

    # Handle url_download and s3_file_name
    if 'external_url_c' in pg_columns or 's3_file_name' in pg_columns:
        logger.debug("Processing url_download for S3 upload")
        new_df['external_url_c'] = None
        new_df['s3_file_name'] = None

        for idx, row in resources_df.iterrows():
            url = row['url_download']
            if pd.notnull(url) and ('ema.promiseserves.org' in url or url.startswith('[BASE]') or url.startswith('[BASEURL]')):
                # Extract filename
                parsed_url = urlparse(url)
                filename = os.path.basename(parsed_url.path)
                if filename:
                    # Upload to S3
                    s3_url, s3_filename = upload_to_s3(url, filename)
                    new_df.at[idx, 'external_url_c'] = s3_url if s3_url else url
                    new_df.at[idx, 's3_file_name'] = s3_filename
                    # Add delay to avoid rate-limiting
                    time.sleep(1)
                else:
                    logger.warning(f"No filename extracted from URL: {url}")
                    new_df.at[idx, 'external_url_c'] = url
            else:
                new_df.at[idx, 'external_url_c'] = url

    # Set null fields
    null_fields = ['subcategory_id', 'mom_id', 'advocate_id', 'coordinator_id', 'lesson_id', 'lesson_template_id']
    for field in null_fields:
        if field in pg_columns:
            new_df[field] = None

    # Set boolean field
    if 'is_primary_lesson_resource' in pg_columns:
        new_df['is_primary_lesson_resource'] = False

    # Filter out rows with duplicate IDs
    initial_row_count = len(new_df)
    duplicate_mask = new_df['id'].isin(existing_ids)
    if duplicate_mask.any():
        duplicates = new_df[duplicate_mask][['id']].merge(
            resources_df[['id_resource']], left_index=True, right_index=True
        )
        logger.warning(f"Skipping {duplicate_mask.sum()} rows with duplicate IDs: {duplicates[['id', 'id_resource']].to_dict('records')}")
        new_df = new_df[~duplicate_mask]
    logger.info(f"After filtering duplicates, {len(new_df)} rows remain (skipped {initial_row_count - len(new_df)})")

    # Handle required fields
    required_fields = ['id', 'created_at', 'updated_at', 'document_name', 'deleted_at']
    for field in required_fields:
        if field in pg_columns and field not in new_df.columns:
            if field == 'id':
                new_df[field] = [str(uuid.uuid4()) for _ in range(len(new_df))]
            elif field == 'document_name':
                new_df[field] = new_df['id'].apply(lambda x: f"Document_{x[:8]}")
            elif field in ['created_at', 'updated_at']:
                new_df[field] = pd.Timestamp.now()
            elif field == 'deleted_at':
                new_df[field] = pd.Timestamp('1970-01-01')
        elif field in new_df.columns:
            if field == 'id':
                new_df[field] = new_df[field].fillna(pd.Series([str(uuid.uuid4()) for _ in range(len(new_df))], index=new_df.index))
            elif field == 'document_name':
                new_df[field] = new_df['document_name'].fillna(new_df['id'].apply(lambda x: f"Document_{x[:8]}"))
            elif field in ['created_at', 'updated_at']:
                new_df[field] = new_df[field].fillna(pd.Timestamp.now())
            elif field == 'deleted_at':
                new_df['deleted_at'] = new_df['deleted_at'].fillna(pd.Timestamp('1970-01-01'))
                # Log rows with default deleted_at
                default_deleted_at = new_df[new_df['deleted_at'] == pd.Timestamp('1970-01-01')][['id', 'deleted_at']]
                if not default_deleted_at.empty:
                    logger.warning(f"Set default deleted_at for {len(default_deleted_at)} rows: {default_deleted_at.to_dict('records')}")

    # Ensure text columns are strings
    for col, dtype in pg_columns.items():
        if col in new_df.columns and dtype == 'text':
            new_df[col] = new_df[col].apply(
                lambda x: str(x) if x is not None else None
            )

    logger.info(f"Transformed DataFrame has {len(new_df.columns)} columns: {new_df.columns.tolist()}")
    return new_df

def load_to_postgres(df, target_table):
    """Load transformed data to PostgreSQL."""
    try:
        engine = create_engine(
            f"postgresql://{POSTGRES_CONFIG['user']}:{POSTGRES_CONFIG['password']}@{POSTGRES_CONFIG['host']}:{POSTGRES_CONFIG['port']}/{POSTGRES_CONFIG['dbname']}"
        )

        # Check required columns
        required_columns = ['id', 'created_at', 'updated_at', 'document_name', 'deleted_at']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            raise ValueError(f"Missing required columns: {missing_columns}")

        # Convert timestamp columns
        for col in ['created_at', 'updated_at', 'deleted_at']:
            if col in df.columns and df[col].dtype != 'datetime64[ns]':
                df[col] = pd.to_datetime(df[col], errors='coerce')

        # Load data to PostgreSQL
        df.to_sql(target_table, engine, schema='public', if_exists='append', index=False)
        logger.info(f"Loaded {len(df)} rows to PostgreSQL table public.{target_table}")
        return True
    except Exception as e:
        logger.error(f"Error loading data to {target_table}: {e}")
        raise

def main():
    """Execute ETL for ltp_resources to Document."""
    try:
        # Extract
        resources_df, add_df, mod_df = extract_mysql_data()

        # Transform
        document_df = transform_data(resources_df, add_df, mod_df)

        # Print sample of transformed data
        logger.info(f"Transformed data sample:\n{document_df.head()}")

        # Load
        load_result = load_to_postgres(document_df, 'Document')

        if load_result:
            logger.info("ETL process completed successfully")

    except Exception as e:
        logger.error(f"ETL process failed: {e}")

if __name__ == "__main__":
    main()



##############################################################################
##############################################################################
##############################################################################
##############################################################################
##############################################################################
##############################################################################


# #!/usr/bin/env python3
# import pandas as pd
# import pymysql
# import psycopg2
# from sqlalchemy import create_engine
# import logging
# import uuid
# import json
# import requests
# from urllib.parse import urlparse, urljoin
# import boto3
# from botocore.exceptions import ClientError
# import os

# # Configure logging
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
# logger = logging.getLogger(__name__)

# # Database connection parameters
# MYSQL_CONFIG = {
#     'host': 'localhost',
#     'user': 'root',
#     'password': 'root',
#     'db': 'my_app_db',
#     'charset': 'utf8mb4',
#     'port': 3306
# }

# POSTGRES_CONFIG = {
#     'host': 'localhost',
#     'user': 'postgres',
#     'password': 'root',
#     'dbname': 'postgres',
#     'port': 5432
# }

# # S3 configuration with provided credentials - TODO: I will add this into .env
# S3_BUCKET = "ema-document-uploads"
# S3_REGION = "us-east-1"
# S3_BASE_URL = "https://famresources.s3.amazonaws.com/"
# AWS_ACCESS_KEY_ID = "********************"
# AWS_SECRET_ACCESS_KEY = "ed9fiwR8TH3BSQhOJR7D7IeOGsTbYGDpL3K0EvPl"
# AUTH_CREDENTIALS = {
#     'email': '<EMAIL>',
#     'password': '123456!ema'
# }
# BASE_URL = 'https://ema.promiseserves.org/'

# # Utility functions
# def int_to_uuid(int_id):
#     """Convert integer ID to UUID."""
#     try:
#         int_id = int(int_id)
#         namespace = uuid.NAMESPACE_DNS
#         return str(uuid.uuid5(namespace, str(int_id)))
#     except (ValueError, TypeError):
#         logger.warning(f"Invalid ID for UUID conversion: {int_id}")
#         return str(uuid.uuid4())

# def connect_mysql():
#     """Connect to MySQL database."""
#     try:
#         conn = pymysql.connect(**MYSQL_CONFIG)
#         logger.info("Connected to MySQL database")
#         return conn
#     except Exception as e:
#         logger.error(f"Error connecting to MySQL: {e}")
#         raise

# def connect_postgres():
#     """Connect to PostgreSQL database."""
#     try:
#         conn = psycopg2.connect(**POSTGRES_CONFIG)
#         logger.info("Connected to PostgreSQL database")
#         return conn
#     except Exception as e:
#         logger.error(f"Error connecting to PostgreSQL: {e}")
#         raise

# def get_existing_ids():
#     """Retrieve existing IDs from the Document table."""
#     try:
#         conn = connect_postgres()
#         cursor = conn.cursor()
#         cursor.execute('SELECT id FROM public."Document"')
#         existing_ids = {row[0] for row in cursor.fetchall()}
#         cursor.close()
#         conn.close()
#         logger.info(f"Retrieved {len(existing_ids)} existing IDs from Document table")
#         return existing_ids
#     except Exception as e:
#         logger.error(f"Error retrieving existing IDs: {e}")
#         raise

# def upload_to_s3(url, filename):
#     """Download file from url_download and upload to S3 if not already present, return S3 URL."""
#     try:
#         # Initialize S3 client with provided credentials
#         s3_client = boto3.client(
#             's3',
#             region_name=S3_REGION,
#             aws_access_key_id=AWS_ACCESS_KEY_ID,
#             aws_secret_access_key=AWS_SECRET_ACCESS_KEY
#         )
#         s3_key = filename

#         # Check if file already exists in S3
#         try:
#             s3_client.head_object(Bucket=S3_BUCKET, Key=s3_key)
#             s3_url = f"{S3_BASE_URL}{s3_key}"
#             logger.info(f"File {filename} already exists in S3, skipping upload: {s3_url}")
#             return s3_url, filename
#         except ClientError as e:
#             if e.response['Error']['Code'] != '404':
#                 logger.error(f"Error checking S3 file {filename}: {e}")
#                 return None, None
#             # File does not exist, proceed with upload

#         # Handle [BASE] URLs
#         if url.startswith('[BASE]'):
#             url = urljoin(BASE_URL, url.replace('[BASE]', ''))

#         # Download file
#         logger.info(f"Downloading file from {url}")
#         response = requests.get(url, auth=(AUTH_CREDENTIALS['email'], AUTH_CREDENTIALS['password']), stream=True)
#         if response.status_code != 200:
#             logger.error(f"Failed to download file from {url}: Status {response.status_code}")
#             return None, None

#         # Upload to S3
#         logger.info(f"Uploading {filename} to S3")
#         s3_client.upload_fileobj(
#             response.raw,
#             S3_BUCKET,
#             s3_key,
#             ExtraArgs={'ContentType': response.headers.get('Content-Type', 'application/octet-stream')}
#         )

#         # Generate S3 URL
#         s3_url = f"{S3_BASE_URL}{s3_key}"
#         logger.info(f"Uploaded {filename} to S3: {s3_url}")
#         return s3_url, filename
#     except ClientError as e:
#         logger.error(f"S3 upload error for {filename}: {e}")
#         return None, None
#     except Exception as e:
#         logger.error(f"Error processing {url}: {e}")
#         return None, None

# def extract_mysql_data():
#     """Extract data from MySQL ltp_resources and ltp_people tables."""
#     try:
#         engine = create_engine(
#             f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['db']}?charset={MYSQL_CONFIG['charset']}"
#         )
#         # Extract ltp_resources
#         resources_query = """
#         SELECT id_resource, resource_name, resource_desc_short, resource_desc_long, resource_content,
#                resource_filename, resource_format, date_add, date_mod, date_removed, id_add, id_mod, url_download
#         FROM ltp_resources
#         """
#         resources_df = pd.read_sql(resources_query, engine)
#         logger.info(f"Extracted {len(resources_df)} rows from ltp_resources")

#         # Check for duplicate id_resource values
#         duplicates = resources_df[resources_df.duplicated(subset=['id_resource'], keep=False)]
#         if not duplicates.empty:
#             logger.warning(f"Found {len(duplicates)} duplicate id_resource values: {duplicates['id_resource'].tolist()}")
#             resources_df = resources_df.drop_duplicates(subset=['id_resource'], keep='first')
#             logger.info(f"After deduplication, {len(resources_df)} rows remain")

#         # Extract ltp_people for created_by_name and updated_by_name
#         people_ids = set(resources_df['id_add'].dropna().astype(int).tolist() + resources_df['id_mod'].dropna().astype(int).tolist())
#         if people_ids:
#             people_query = """
#             SELECT id_people, name_first, name_last
#             FROM ltp_people
#             WHERE id_people IN (%s)
#             """ % ','.join(str(id) for id in people_ids)
#             people_df = pd.read_sql(people_query, engine)
#             logger.info(f"Extracted {len(people_df)} rows from ltp_people for names")
#         else:
#             people_df = pd.DataFrame(columns=['id_people', 'name_first', 'name_last'])
#             logger.info("No id_add or id_mod values found, skipping ltp_people extraction")

#         return resources_df, people_df
#     except Exception as e:
#         logger.error(f"Error extracting data: {e}")
#         raise

# def transform_data(resources_df, people_df):
#     """Transform data from MySQL ltp_resources to PostgreSQL Document schema."""
#     logger.info("Transforming data from ltp_resources to Document")

#     # Get PostgreSQL table schema
#     postgres_conn = connect_postgres()
#     cursor = postgres_conn.cursor()
#     cursor.execute("""
#         SELECT column_name, data_type 
#         FROM information_schema.columns 
#         WHERE table_schema = 'public' 
#         AND table_name = %s
#         ORDER BY ordinal_position
#     """, ('Document',))
#     pg_columns = {row[0]: row[1] for row in cursor.fetchall()}
#     logger.info(f"Target PostgreSQL columns: {list(pg_columns.keys())}")
#     cursor.close()
#     postgres_conn.close()

#     # Get existing IDs to avoid duplicates
#     existing_ids = get_existing_ids()

#     # Create new DataFrame for transformed data
#     new_df = pd.DataFrame()

#     # Direct column mappings
#     direct_mappings = {
#         'id_resource': 'id',
#         'resource_name': 'document_name',
#         'resource_content': 'filecontents',
#         'resource_filename': 'filename',
#         'resource_format': 'mimeType',
#         'date_add': 'created_at',
#         'date_mod': 'updated_at',
#         'date_removed': 'deleted_at',
#         'id_add': 'created_by_id',
#         'id_mod': 'updated_by_id'
#     }

#     # Create a dictionary for people names (id_people -> full_name)
#     people_df['full_name'] = people_df['name_first'].str.cat(people_df['name_last'], sep=' ', na_rep='').str.strip()
#     people_dict = people_df.set_index('id_people')['full_name'].to_dict()

#     # Apply direct mappings
#     for mysql_col, pg_col in direct_mappings.items():
#         if mysql_col in resources_df.columns and pg_col in pg_columns:
#             logger.debug(f"Mapping {mysql_col} to {pg_col}")
#             try:
#                 if mysql_col == 'id_resource':
#                     new_df[pg_col] = resources_df[mysql_col].apply(
#                         lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else str(uuid.uuid4())
#                     )
#                 elif mysql_col in ['id_add', 'id_mod']:
#                     new_df[pg_col] = resources_df[mysql_col].apply(
#                         lambda x: int_to_uuid(x) if pd.notnull(x) and x != 0 else None
#                     )
#                 elif mysql_col in ['date_add', 'date_mod', 'date_removed']:
#                     new_df[pg_col] = pd.to_datetime(resources_df[mysql_col], unit='s', errors='coerce')
#                 else:
#                     new_df[pg_col] = resources_df[mysql_col]
#             except Exception as e:
#                 logger.error(f"Error mapping {mysql_col} to {pg_col}: {e}")
#                 raise

#     # Handle description (concatenate resource_desc_short and resource_desc_long)
#     if 'description' in pg_columns:
#         logger.debug("Concatenating resource_desc_short and resource_desc_long for description")
#         new_df['description'] = resources_df['resource_desc_short'].str.cat(
#             resources_df['resource_desc_long'], sep=' ', na_rep='').str.strip()

#     # Handle created_by_name and updated_by_name
#     if 'created_by_name' in pg_columns:
#         new_df['created_by_name'] = resources_df['id_add'].map(people_dict).fillna('')
#     if 'updated_by_name' in pg_columns:
#         new_df['updated_by_name'] = resources_df['id_mod'].map(people_dict).fillna('')

#     # Handle url_download and s3_file_name
#     if 'external_url_c' in pg_columns or 's3_file_name' in pg_columns:
#         logger.debug("Processing url_download for S3 upload")
#         new_df['external_url_c'] = None
#         new_df['s3_file_name'] = None
#         for idx, row in resources_df.iterrows():
#             url = row['url_download']
#             if pd.notnull(url) and ('ema.promiseserves.org' in url or url.startswith('[BASE]')):
#                 # Extract filename from URL
#                 parsed_url = urlparse(url)
#                 filename = os.path.basename(parsed_url.path)
#                 if filename:
#                     # Upload to S3
#                     s3_url, s3_filename = upload_to_s3(url, filename)
#                     new_df.at[idx, 'external_url_c'] = s3_url
#                     new_df.at[idx, 's3_file_name'] = s3_filename
#                 else:
#                     logger.warning(f"No filename extracted from URL: {url}")
#                     new_df.at[idx, 'external_url_c'] = url
#             else:
#                 new_df.at[idx, 'external_url_c'] = url

#     # Set null fields
#     null_fields = ['subcategory_id', 'mom_id', 'advocate_id', 'coordinator_id', 'lesson_id', 'lesson_template_id']
#     for field in null_fields:
#         if field in pg_columns:
#             new_df[field] = None

#     # Set boolean field
#     if 'is_primary_lesson_resource' in pg_columns:
#         new_df['is_primary_lesson_resource'] = False

#     # Filter out rows with duplicate IDs
#     initial_row_count = len(new_df)
#     duplicate_mask = new_df['id'].isin(existing_ids)
#     if duplicate_mask.any():
#         duplicates = new_df[duplicate_mask][['id']].merge(
#             resources_df[['id_resource']], left_index=True, right_index=True
#         )
#         logger.warning(f"Skipping {duplicate_mask.sum()} rows with duplicate IDs: {duplicates[['id', 'id_resource']].to_dict('records')}")
#         new_df = new_df[~duplicate_mask]
#     logger.info(f"After filtering duplicates, {len(new_df)} rows remain (skipped {initial_row_count - len(new_df)})")

#     # Handle required fields
#     required_fields = ['id', 'created_at', 'updated_at', 'document_name', 'deleted_at']
#     for field in required_fields:
#         if field in pg_columns and field not in new_df.columns:
#             if field == 'id':
#                 new_df[field] = [str(uuid.uuid4()) for _ in range(len(new_df))]
#             elif field == 'document_name':
#                 new_df[field] = new_df['id'].apply(lambda x: f"Document_{x[:8]}")
#             elif field in ['created_at', 'updated_at']:
#                 new_df[field] = pd.Timestamp.now()
#             elif field == 'deleted_at':
#                 new_df[field] = pd.Timestamp('1970-01-01')
#         elif field in new_df.columns:
#             if field == 'id':
#                 new_df[field] = new_df[field].fillna(pd.Series([str(uuid.uuid4()) for _ in range(len(new_df))], index=new_df.index))
#             elif field == 'document_name':
#                 new_df[field] = new_df[field].fillna(new_df['id'].apply(lambda x: f"Document_{x[:8]}"))
#             elif field in ['created_at', 'updated_at']:
#                 new_df[field] = new_df[field].fillna(pd.Timestamp.now())
#             elif field == 'deleted_at':
#                 new_df['deleted_at'] = new_df['deleted_at'].fillna(pd.Timestamp('1970-01-01'))
#                 # Log rows with default deleted_at
#                 default_deleted_at = new_df[new_df['deleted_at'] == pd.Timestamp('1970-01-01')][['id', 'deleted_at']]
#                 if not default_deleted_at.empty:
#                     logger.warning(f"Set default deleted_at for {len(default_deleted_at)} rows: {default_deleted_at.to_dict('records')}")

#     # Ensure text columns are strings
#     for col, dtype in pg_columns.items():
#         if col in new_df.columns and dtype == 'text':
#             new_df[col] = new_df[col].apply(
#                 lambda x: str(x) if x is not None else None
#             )

#     logger.info(f"Transformed DataFrame has {len(new_df.columns)} columns: {new_df.columns.tolist()}")
#     return new_df

# def load_to_postgres(df, target_table):
#     """Load transformed data to PostgreSQL."""
#     try:
#         engine = create_engine(
#             f"postgresql://{POSTGRES_CONFIG['user']}:{POSTGRES_CONFIG['password']}@{POSTGRES_CONFIG['host']}:{POSTGRES_CONFIG['port']}/{POSTGRES_CONFIG['dbname']}"
#         )

#         # Check required columns
#         required_columns = ['id', 'created_at', 'updated_at', 'document_name', 'deleted_at']
#         missing_columns = [col for col in required_columns if col not in df.columns]
#         if missing_columns:
#             logger.error(f"Missing required columns: {missing_columns}")
#             raise ValueError(f"Missing required columns: {missing_columns}")

#         # Convert timestamp columns
#         for col in ['created_at', 'updated_at', 'deleted_at']:
#             if col in df.columns and df[col].dtype != 'datetime64[ns]':
#                 df[col] = pd.to_datetime(df[col], errors='coerce')

#         # Load data to PostgreSQL
#         df.to_sql(target_table, engine, schema='public', if_exists='append', index=False)
#         logger.info(f"Loaded {len(df)} rows to PostgreSQL table public.{target_table}")
#         return True
#     except Exception as e:
#         logger.error(f"Error loading data to {target_table}: {e}")
#         raise

# def main():
#     """Execute ETL for ltp_resources to Document."""
#     try:
#         # Extract
#         resources_df, people_df = extract_mysql_data()

#         # Transform
#         document_df = transform_data(resources_df, people_df)

#         # Print sample of transformed data
#         logger.info(f"Transformed data sample:\n{document_df.head()}")

#         # Load
#         load_result = load_to_postgres(document_df, 'Document')

#         if load_result:
#             logger.info("ETL process completed successfully")

#     except Exception as e:
#         logger.error(f"ETL process failed: {e}")

# if __name__ == "__main__":
#     pass # remove this if you want to run the script
#     main()
