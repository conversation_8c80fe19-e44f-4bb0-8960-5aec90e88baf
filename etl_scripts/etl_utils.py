#!/usr/bin/env python3
"""
Utility functions for ETL scripts.
"""
import uuid
import logging
import json
from datetime import datetime

def int_to_uuid(int_id):
    """Convert integer ID to deterministic UUID using a fixed namespace."""
    int_id = int(int_id)
    namespace = uuid.NAMESPACE_DNS
    return str(uuid.uuid5(namespace, str(int_id)))

def get_id_mapping(pg_conn, old_id, entity_type):
    """
    Get new UUID for an old ID from the mapping table.
    
    Args:
        pg_conn: PostgreSQL connection
        old_id: Original ID from source system
        entity_type: Type of entity (e.g., 'Mom', 'User')
        
    Returns:
        UUID string or None if not found
    """
    if not old_id:
        return None
    
    cursor = pg_conn.cursor()
    try:
        cursor.execute(
            "SELECT new_id FROM id_mapping WHERE old_id = %s AND entity_type = %s",
            (str(old_id), entity_type)  # Convert old_id to string to ensure type compatibility
        )
        result = cursor.fetchone()
        cursor.close()
        return result[0] if result else None
    except Exception as e:
        logging.error(f"Error getting ID mapping for {old_id} ({entity_type}): {e}")
        cursor.close()
        return None

def create_id_mapping(pg_conn, old_id, new_id, entity_type):
    """
    Create a new ID mapping entry.
    
    Args:
        pg_conn: PostgreSQL connection
        old_id: Original ID from source system
        new_id: New UUID in target system
        entity_type: Type of entity (e.g., 'Mom', 'User')
        
    Returns:
        Boolean indicating success
    """
    if not old_id or not new_id:
        return False
    
    cursor = pg_conn.cursor()
    try:
        # Check if mapping already exists
        cursor.execute(
            "SELECT 1 FROM id_mapping WHERE old_id = %s AND entity_type = %s",
            (str(old_id), entity_type)
        )
        if cursor.fetchone():
            cursor.close()
            return True  # Mapping already exists
        
        # Insert new mapping
        cursor.execute(
            """
            INSERT INTO id_mapping (old_id, new_id, entity_type, created_at, updated_at)
            VALUES (%s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """,
            (str(old_id), str(new_id), entity_type)
        )
        pg_conn.commit()
        cursor.close()
        return True
    except Exception as e:
        logging.error(f"Error creating ID mapping for {old_id} ({entity_type}): {e}")
        pg_conn.rollback()
        cursor.close()
        return False

def ensure_id_mapping_table(pg_conn):
    """
    Ensure the ID mapping table exists.
    
    Args:
        pg_conn: PostgreSQL connection
        
    Returns:
        Boolean indicating success
    """
    cursor = pg_conn.cursor()
    try:
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS id_mapping (
                old_id VARCHAR(255) NOT NULL,
                new_id UUID NOT NULL,
                entity_type VARCHAR(50) NOT NULL,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (old_id, entity_type)
            )
        """)
        pg_conn.commit()
        cursor.close()
        logging.info("ID mapping table exists or was created")
        return True
    except Exception as e:
        logging.error(f"Error ensuring ID mapping table: {e}")
        pg_conn.rollback()
        cursor.close()
        return False

def safe_json_parse(json_str):
    """
    Safely parse JSON string.
    
    Args:
        json_str: JSON string to parse
        
    Returns:
        Parsed JSON object or empty dict if parsing fails
    """
    if not json_str:
        return {}
    
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError) as e:
        logging.warning(f"Error parsing JSON: {e}")
        return {}