
# '''
# Below is the script that will delete the promiserves files from s3.
# '''

# #!/usr/bin/env python3
# import logging
# import pymysql
# from urllib.parse import urlparse, urljoin
# import boto3
# from botocore.exceptions import ClientError
# import os

# # Configure logging to console and file
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(levelname)s - %(message)s',
#     handlers=[
#         logging.FileHandler('delete_s3_files.log'),
#         logging.StreamHandler()
#     ]
# )
# logger = logging.getLogger(__name__)

# # Database connection parameters
# MYSQL_CONFIG = {
#     'host': 'localhost',
#     'user': 'root',
#     'password': 'root',
#     'database': 'my_app_db',
#     'charset': 'utf8mb4',
#     'port': 3306
# }

# # S3 configuration
# S3_BUCKET = "ema-document-uploads"
# S3_REGION = "us-east-1"
# AWS_ACCESS_KEY_ID = "********************"
# AWS_SECRET_ACCESS_KEY = "ed9fiwR8TH3BSQhOJR7D7IeOGsTbYGDpL3K0EvPl"
# BASE_URL = 'https://ema.promiseserves.org/'

# def get_urls_from_mysql():
#     """Retrieve url_download values from ltp_resources table."""
#     try:
#         conn = pymysql.connect(**MYSQL_CONFIG)
#         cursor = conn.cursor()
#         query = "SELECT url_download FROM ltp_resources"
#         cursor.execute(query)
#         urls = [row[0] for row in cursor.fetchall()]
#         logger.info(f"Retrieved {len(urls)} URLs from ltp_resources")
#         cursor.close()
#         conn.close()
#         return urls
#     except pymysql.Error as e:
#         logger.error(f"MySQL error: {e}")
#         return []
#     except Exception as e:
#         logger.error(f"Error retrieving URLs: {e}")
#         return []

# def delete_s3_file(filename):
#     """Check if file exists in S3 and delete it, return True if deleted."""
#     try:
#         s3_client = boto3.client(
#             's3',
#             region_name=S3_REGION,
#             aws_access_key_id=AWS_ACCESS_KEY_ID,
#             aws_secret_access_key=AWS_SECRET_ACCESS_KEY
#         )
#         # Check if file exists
#         try:
#             s3_client.head_object(Bucket=S3_BUCKET, Key=filename)
#             # File exists, delete it
#             s3_client.delete_object(Bucket=S3_BUCKET, Key=filename)
#             logger.info(f"Deleted {filename} from S3 bucket {S3_BUCKET}")
#             return True
#         except ClientError as e:
#             if e.response['Error']['Code'] == '404':
#                 logger.info(f"File {filename} not found in S3 bucket {S3_BUCKET}")
#                 return False
#             else:
#                 logger.error(f"Error checking S3 file {filename}: {e}")
#                 return False
#     except Exception as e:
#         logger.error(f"Error deleting {filename} from S3: {e}")
#         return False

# def main():
#     """Process URLs from ltp_resources, check and delete corresponding S3 files."""
#     # Get URLs from MySQL
#     urls = get_urls_from_mysql()
#     if not urls:
#         logger.error("No URLs retrieved, exiting")
#         return

#     # Count promiseserves and [BASE] URLs
#     promiseserves_urls = [
#         url for url in urls
#         if url and (url.startswith('https://ema.promiseserves.org') or url.startswith('[BASE]') or url.startswith('[BASEURL]'))
#     ]
#     logger.info(f"Found {len(promiseserves_urls)} URLs for ema.promiseserves.org or [BASE]/[BASEURL] to check for S3 deletion")
#     print(f"Total promiseserves/[BASE] URLs to check: {len(promiseserves_urls)}")

#     # # Process each URL
    # test_urls = [
    #     'https://ema.promiseserves.org/uploads/45/c14cb083a761e3faaa8819d2b1fca0e5.pdf',
    #     'https://ema.promiseserves.org/uploads/45/9770668d4705e0c14a15c0b0ba0ecb6a.pdf',
    #     '[BASE]uploads/45/e474f0446ee9e66eac54140160053567.png',
    #     'https://ema.promiseserves.org/uploads/45/c14cb083a761e3faaa8819d2b1fca0e5.pdf',  # Duplicate
    #     '[BASE]uploads/45/e474f0446ee9e66eac54140160053567.png',  # Duplicate
    #     '[BASEURL]uploads/45/resources/domestic_violence/2020-LIR-Relationship-Spectrum-onepager.pdf'  # Example [BASEURL]
    # ]
#     count = 0
#     for url in test_urls:  # to delete all urls of data base replace test_urls with urls
#         count+=1
#         if not url:
#             logger.warning("Empty URL, skipping")
#             continue

#         # Filter for ema.promiseserves.org URLs
#         if not (url.startswith('https://ema.promiseserves.org') or url.startswith('[BASE]') or url.startswith('[BASEURL]')):
#             logger.info(f"Skipping non-promiseserves URL: {url}")
#             continue

#         # Handle [BASE] and [BASEURL] URLs
#         if url.startswith('[BASE]') or url.startswith('[BASEURL]'):
#             url = urljoin(BASE_URL, url.replace('[BASE]', '').replace('[BASEURL]', ''))

#         # Extract filename
#         parsed_url = urlparse(url)
#         filename = os.path.basename(parsed_url.path)
#         if not filename:
#             logger.warning(f"No filename extracted from URL: {url}")
#             continue

#         logger.info(f"Processing URL: {url}, Filename: {filename}, Count-{count}")
#         if delete_s3_file(filename):
#             count+=1
#             print(f"Deleted: Filename={filename} - Count-{count}")
#         else:
#             print(f"Not Found: Filename={filename}")

# if __name__ == "__main__":
#     main()

##############################################################################
##############################################################################
##############################################################################
##############################################################################
##############################################################################
##############################################################################


"""
Below script will download the correct file and uplaod that to s3 , i have tested this , and its working properly
"""

#!/usr/bin/env python3
import logging
import requests
from urllib.parse import urlparse, urljoin
import boto3
from botocore.exceptions import ClientError
import os
import mimetypes

# Configure logging to console and file
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('upload_debug.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configuration
S3_BUCKET = "ema-document-uploads"
S3_REGION = "us-east-1"
S3_BASE_URL = "https://ema-document-uploads.s3.amazonaws.com/"
AWS_ACCESS_KEY_ID = "********************"
AWS_SECRET_ACCESS_KEY = "ed9fiwR8TH3BSQhOJR7D7IeOGsTbYGDpL3K0EvPl"
AUTH_CREDENTIALS = {
    'email': '<EMAIL>',
    'password': '123456!ema'
}
BASE_URL = 'https://ema.promiseserves.org/'
LOGIN_URL = 'https://ema.promiseserves.org/login'

def upload_to_s3(url, filename):
    """Download file from URL after logging in, upload to S3 if not present, return S3 URL."""
    try:
        # Initialize S3 client
        s3_client = boto3.client(
            's3',
            region_name=S3_REGION,
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY
        )
        s3_key = filename

        # Check if file already exists in S3
        try:
            s3_client.head_object(Bucket=S3_BUCKET, Key=s3_key)
            s3_url = f"{S3_BASE_URL}{s3_key}"
            logger.info(f"File {filename} already exists in S3, skipping upload: {s3_url}")
            return s3_url, filename
        except ClientError as e:
            if e.response['Error']['Code'] != '404':
                logger.error(f"Error checking S3 file {filename}: {e}")
                return None, None
            # File does not exist, proceed with download/upload

        # Handle [BASE] and [BASEURL] URLs
        if url.startswith('[BASE]') or url.startswith('[BASEURL]'):
            url = urljoin(BASE_URL, url.replace('[BASE]', '').replace('[BASEURL]', ''))

        # Initialize session for login
        session = requests.Session()
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/pdf,image/png,*/*',
            'Accept-Encoding': 'gzip, deflate, br'
        }

        # Attempt login
        login_data = {
            'email': AUTH_CREDENTIALS['email'],
            'password': AUTH_CREDENTIALS['password']
        }
        logger.info(f"Attempting login to {LOGIN_URL}")
        login_response = session.post(LOGIN_URL, data=login_data, headers=headers)
        logger.info(f"Login Status: {login_response.status_code}")
        if login_response.status_code != 200:
            logger.error(f"Login failed: Status {login_response.status_code}")
            return None, None

        # Download file
        logger.info(f"Downloading file from {url}")
        response = session.get(url, headers=headers, stream=True, timeout=30)
        logger.info(f"Status Code: {response.status_code}")
        logger.info(f"Headers: {response.headers}")

        if response.status_code != 200:
            logger.error(f"Failed to download {url}: Status {response.status_code}")
            return None, None

        content_type = response.headers.get('Content-Type', 'unknown')
        logger.info(f"Content-Type: {content_type}")
        if 'pdf' not in content_type.lower() and 'image' not in content_type.lower():
            logger.warning(f"Unexpected Content-Type: {content_type}, file may be invalid")

        # Save file locally
        download_dir = "./downloads"
        os.makedirs(download_dir, exist_ok=True)
        local_path = os.path.join(download_dir, filename)
        logger.info(f"Saving file to {local_path}")
        first_bytes = b""
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    if len(first_bytes) < 100:
                        first_bytes += chunk[:100 - len(first_bytes)]
                    f.write(chunk)
        logger.info(f"First 100 bytes: {first_bytes[:100]}")
        file_size = os.path.getsize(local_path)
        logger.info(f"Downloaded {filename} to {local_path} (Size: {file_size} bytes)")
        expected_size = int(response.headers.get('Content-Length', 0))
        if expected_size and file_size != expected_size:
            logger.warning(f"File size mismatch: Expected {expected_size} bytes, got {file_size} bytes")

        # Determine Content-Type for S3
        content_type, _ = mimetypes.guess_type(filename)
        if not content_type:
            content_type = response.headers.get('Content-Type', 'application/octet-stream')
            logger.warning(f"Could not determine Content-Type for {filename}, using {content_type}")

        # Upload to S3
        logger.info(f"Uploading {filename} to S3")
        with open(local_path, 'rb') as f:
            s3_client.upload_fileobj(
                f,
                S3_BUCKET,
                s3_key,
                ExtraArgs={'ContentType': content_type}
            )

        # Generate S3 URL
        s3_url = f"{S3_BASE_URL}{s3_key}"
        logger.info(f"Uploaded {filename} to S3: {s3_url}")
        return s3_url, filename

    except ClientError as e:
        logger.error(f"S3 upload error for {filename}: {e}")
        return None, None
    except Exception as e:
        logger.error(f"Error processing {url}: {e}")
        return None, None

def main():
    """Test upload_to_s3 with a list of URLs, including duplicates."""
    test_urls = [
        'https://ema.promiseserves.org/uploads/45/c14cb083a761e3faaa8819d2b1fca0e5.pdf',
        'https://ema.promiseserves.org/uploads/45/9770668d4705e0c14a15c0b0ba0ecb6a.pdf',
        '[BASE]uploads/45/e474f0446ee9e66eac54140160053567.png',
        'https://ema.promiseserves.org/uploads/45/c14cb083a761e3faaa8819d2b1fca0e5.pdf',  # Duplicate
        '[BASE]uploads/45/e474f0446ee9e66eac54140160053567.png',  # Duplicate
        '[BASEURL]uploads/45/resources/domestic_violence/2020-LIR-Relationship-Spectrum-onepager.pdf'  # Example [BASEURL]
    ]

    # Process each URL
    for url in test_urls:
        parsed_url = urlparse(url)
        filename = os.path.basename(parsed_url.path)
        if not filename:
            logger.warning(f"No filename extracted from URL: {url}")
            continue

        logger.info(f"Processing URL: {url}")
        s3_url, s3_filename = upload_to_s3(url, filename)
        if s3_url and s3_filename:
            print(f"Success: URL={url}, S3_URL={s3_url}, Filename={s3_filename}")
        else:
            print(f"Failed: URL={url}, Error logged")

if __name__ == "__main__":
    main()


##############################################################################
##############################################################################
##############################################################################
##############################################################################
##############################################################################
##############################################################################


# '''
# below script successfully donwloaded the file and its not corrupted!
# '''

# #!/usr/bin/env python3
# import requests
# import os
# from urllib.parse import urlparse
# import logging

# # Configure logging
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(levelname)s - %(message)s',
#     handlers=[
#         logging.FileHandler('download_debug.log'),
#         logging.StreamHandler()
#     ]
# )
# logger = logging.getLogger(__name__)

# # Configuration
# AUTH_CREDENTIALS = {
#     'email': '<EMAIL>',
#     'password': '123456!ema'
# }
# BASE_URL = 'https://ema.promiseserves.org/'
# LOGIN_URL = 'https://ema.promiseserves.org/login'
# TEST_URL = 'https://ema.promiseserves.org/uploads/45/c14cb083a761e3faaa8819d2b1fca0e5.pdf'

# def download_file(url, save_dir="./downloads"):
#     """Download file from URL after logging in, save locally."""
#     try:
#         # Extract filename
#         parsed_url = urlparse(url)
#         filename = os.path.basename(parsed_url.path)
#         if not filename:
#             logger.error(f"No filename extracted from URL: {url}")
#             return

#         # Create save directory
#         os.makedirs(save_dir, exist_ok=True)
#         local_path = os.path.join(save_dir, filename)

#         # Initialize session
#         session = requests.Session()
#         headers = {
#             'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
#             'Accept': 'application/pdf,image/png,*/*',
#             'Accept-Encoding': 'gzip, deflate, br'
#         }

#         # Attempt login
#         login_data = {
#             'email': AUTH_CREDENTIALS['email'],
#             'password': AUTH_CREDENTIALS['password']
#         }
#         logger.info(f"Attempting login to {LOGIN_URL}")
#         login_response = session.post(LOGIN_URL, data=login_data, headers=headers)
#         logger.info(f"Login Status: {login_response.status_code}")
#         if login_response.status_code != 200:
#             logger.error(f"Login failed: Status {login_response.status_code}")
#             return

#         # Download file
#         logger.info(f"Downloading file from {url}")
#         response = session.get(url, headers=headers, stream=True, timeout=30)
#         logger.info(f"Status Code: {response.status_code}")
#         logger.info(f"Headers: {response.headers}")

#         if response.status_code != 200:
#             logger.error(f"Failed to download {url}: Status {response.status_code}")
#             return

#         content_type = response.headers.get('Content-Type', 'unknown')
#         logger.info(f"Content-Type: {content_type}")
#         if 'pdf' not in content_type.lower() and 'image' not in content_type.lower():
#             logger.warning(f"Unexpected Content-Type: {content_type}, file may be invalid")

#         # Save file and capture first 100 bytes
#         logger.info(f"Saving file to {local_path}")
#         first_bytes = b""
#         with open(local_path, 'wb') as f:
#             for chunk in response.iter_content(chunk_size=8192):
#                 if chunk:
#                     if len(first_bytes) < 100:
#                         first_bytes += chunk[:100 - len(first_bytes)]
#                     f.write(chunk)
#         logger.info(f"First 100 bytes: {first_bytes[:100]}")
#         file_size = os.path.getsize(local_path)
#         logger.info(f"Downloaded {filename} to {local_path} (Size: {file_size} bytes)")
#         expected_size = int(response.headers.get('Content-Length', 0))
#         if expected_size and file_size != expected_size:
#             logger.warning(f"File size mismatch: Expected {expected_size} bytes, got {file_size} bytes")

#     except Exception as e:
#         logger.error(f"Error downloading {url}: {e}")

# if __name__ == "__main__":
#     download_file(TEST_URL)



##############################################################################
##############################################################################
##############################################################################
##############################################################################
##############################################################################
##############################################################################



# '''
# Below script is to test whether the downloaded files are correct or not!
# '''

# #!/usr/bin/env python3
# import requests
# import os
# from urllib.parse import urlparse, urljoin

# # Configuration
# AUTH_CREDENTIALS = {
#     'email': '<EMAIL>',
#     'password': '123456!ema'
# }
# BASE_URL = 'https://ema.promiseserves.org/'

# def download_file(url, save_dir="./downloads"):
#     """Download file from URL and save locally."""
#     # Handle [BASE] URLs
#     if url.startswith('[BASE]'):
#         url = urljoin(BASE_URL, url.replace('[BASE]', ''))

#     # Extract filename
#     parsed_url = urlparse(url)
#     filename = os.path.basename(parsed_url.path)
#     if not filename:
#         print(f"No filename extracted from URL: {url}")
#         return

#     # Create save directory
#     os.makedirs(save_dir, exist_ok=True)
#     local_path = os.path.join(save_dir, filename)

#     # Download and save file
#     response = requests.get(url, auth=(AUTH_CREDENTIALS['email'], AUTH_CREDENTIALS['password']), stream=True)
#     if response.status_code == 200:
#         with open(local_path, 'wb') as f:
#             for chunk in response.iter_content(chunk_size=8192):
#                 if chunk:
#                     f.write(chunk)
#         print(f"Downloaded {filename} to {local_path}")
#     else:
#         print(f"Failed to download {url}: Status {response.status_code}")

# # Test URLs
# test_urls = [
#     'https://ema.promiseserves.org/uploads/45/c14cb083a761e3faaa8819d2b1fca0e5.pdf',
#     'https://ema.promiseserves.org/uploads/45/9770668d4705e0c14a15c0b0ba0ecb6a.pdf',
#     '[BASE]uploads/45/e474f0446ee9e66eac54140160053567.png',
#     'https://ema.promiseserves.org/uploads/45/c14cb083a761e3faaa8819d2b1fca0e5.pdf',  # Duplicate
#     '[BASE]Uploads/45/e474f0446ee9e66eac54140160053567.png'  # Duplicate
# ]

# # Download each file
# for url in test_urls:
#     download_file(url)






##############################################################################
##############################################################################
##############################################################################
##############################################################################
##############################################################################
##############################################################################

# '''
# This Script is for testing purpose. This will ensure the same url 
# should not upload to s3 bucket even the script runs multiple times, and else, upload the file.
# '''

# #!/usr/bin/env python3
# import logging
# import requests
# from urllib.parse import urlparse, urljoin
# import boto3
# from botocore.exceptions import ClientError
# import os

# # Configure logging to console and file
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(levelname)s - %(message)s',
#     handlers=[
#         logging.FileHandler('test_s3_upload.log'),
#         logging.StreamHandler()
#     ]
# )
# logger = logging.getLogger(__name__)

# # S3 configuration with provided credentials - TODO: I will add this into .env
# S3_BUCKET = "ema-document-uploads"
# S3_REGION = "us-east-1"
# S3_BASE_URL = "https://famresources.s3.amazonaws.com/"
# AWS_ACCESS_KEY_ID = "********************"
# AWS_SECRET_ACCESS_KEY = "ed9fiwR8TH3BSQhOJR7D7IeOGsTbYGDpL3K0EvPl"
# AUTH_CREDENTIALS = {
#     'email': '<EMAIL>',
#     'password': '123456!ema'
# }
# BASE_URL = 'https://ema.promiseserves.org/'

# def upload_to_s3(url, filename):
#     """Download file from url_download and upload to S3 if not already present, return S3 URL."""
#     try:
#         # Initialize S3 client with provided credentials
#         s3_client = boto3.client(
#             's3',
#             region_name=S3_REGION,
#             aws_access_key_id=AWS_ACCESS_KEY_ID,
#             aws_secret_access_key=AWS_SECRET_ACCESS_KEY
#         )
#         s3_key = filename

#         # Check if file already exists in S3
#         try:
#             s3_client.head_object(Bucket=S3_BUCKET, Key=s3_key)
#             s3_url = f"{S3_BASE_URL}{s3_key}"
#             logger.info(f"File {filename} already exists in S3, skipping upload: {s3_url}")
#             return s3_url, filename
#         except ClientError as e:
#             if e.response['Error']['Code'] != '404':
#                 logger.error(f"Error checking S3 file {filename}: {e}")
#                 return None, None
#             # File does not exist, proceed with upload

#         # Handle [BASE] URLs
#         if url.startswith('[BASE]'):
#             url = urljoin(BASE_URL, url.replace('[BASE]', ''))

#         # Download file
#         logger.info(f"Downloading file from {url}")
#         response = requests.get(url, auth=(AUTH_CREDENTIALS['email'], AUTH_CREDENTIALS['password']), stream=True)
#         if response.status_code != 200:
#             logger.error(f"Failed to download file from {url}: Status {response.status_code}")
#             return None, None

#         # Upload to S3
#         logger.info(f"Uploading {filename} to S3")
#         s3_client.upload_fileobj(
#             response.raw,
#             S3_BUCKET,
#             s3_key,
#             ExtraArgs={'ContentType': response.headers.get('Content-Type', 'application/octet-stream')}
#         )

#         # Generate S3 URL
#         s3_url = f"{S3_BASE_URL}{s3_key}"
#         logger.info(f"Uploaded {filename} to S3: {s3_url}")
#         return s3_url, filename
#     except ClientError as e:
#         logger.error(f"S3 upload error for {filename}: {e}")
#         return None, None
#     except Exception as e:
#         logger.error(f"Error processing {url}: {e}")
#         return None, None

# def main():
#     """Test upload_to_s3 with a list of URLs, including a duplicate."""
# # List of test URLs
#     test_urls = [
#         'https://ema.promiseserves.org/uploads/45/c14cb083a761e3faaa8819d2b1fca0e5.pdf',
#         'https://ema.promiseserves.org/uploads/45/9770668d4705e0c14a15c0b0ba0ecb6a.pdf',
#         '[BASE]uploads/45/e474f0446ee9e66eac54140160053567.png',
#         'https://ema.promiseserves.org/uploads/45/c14cb083a761e3faaa8819d2b1fca0e5.pdf',  # Duplicate
#         '[BASE]uploads/45/e474f0446ee9e66eac54140160053567.png'
#     ]

#     # Process each URL
#     for url in test_urls:
#         # Extract filename
#         parsed_url = urlparse(url)
#         filename = os.path.basename(parsed_url.path)
#         if not filename:
#             logger.warning(f"No filename extracted from URL: {url}")
#             continue

#         logger.info(f"Processing URL: {url}")
#         s3_url, s3_filename = upload_to_s3(url, filename)
#         if s3_url and s3_filename:
#             print(f"Success: URL={url}, S3_URL={s3_url}, Filename={s3_filename}")
#         else:
#             print(f"Failed: URL={url}, Error logged")

# if __name__ == "__main__":
#     main()
