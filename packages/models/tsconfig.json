{"compilerOptions": {"target": "es2020", "module": "es2020", "lib": ["es2017", "es2022", "DOM", "DOM.Iterable"], "declaration": true, "outDir": "./dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "rootDir": "src/", "baseUrl": ".", "importHelpers": true, "sourceMap": true, "moduleResolution": "bundler"}, "include": ["src/**/*.ts", "tests/**/*.ts", "**/*.ts", "**/*.d.ts"], "exclude": ["dist", "node_modules", ".turbo"]}