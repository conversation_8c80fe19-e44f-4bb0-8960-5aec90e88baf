/**
 * Password validation utilities
 * Provides comprehensive password validation and strength checking
 */

export interface PasswordValidationResult {
  isValid: boolean;
  errors: string[];
  strength: 'weak' | 'fair' | 'good' | 'strong';
  score: number; // 0-100
}

export interface PasswordRequirements {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  specialChars: string;
}

// Default password requirements
export const DEFAULT_PASSWORD_REQUIREMENTS: PasswordRequirements = {
  minLength: 8,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  specialChars: '!@#$%^&*()_+-=[]{};\':"|,.<>/?',
};

/**
 * Validates a password against the specified requirements
 */
export function validatePassword(
  password: string,
  requirements: PasswordRequirements = DEFAULT_PASSWORD_REQUIREMENTS
): PasswordValidationResult {
  const errors: string[] = [];
  let score = 0;

  // Check minimum length
  if (password.length < requirements.minLength) {
    errors.push(`Password must be at least ${requirements.minLength} characters long`);
  } else {
    score += 20;
    // Bonus points for longer passwords
    if (password.length >= 12) score += 10;
    if (password.length >= 16) score += 10;
  }

  // Check uppercase letters
  if (requirements.requireUppercase) {
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    } else {
      score += 15;
    }
  }

  // Check lowercase letters
  if (requirements.requireLowercase) {
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    } else {
      score += 15;
    }
  }

  // Check numbers
  if (requirements.requireNumbers) {
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    } else {
      score += 15;
    }
  }

  // Check special characters
  if (requirements.requireSpecialChars) {
    // Escape special regex characters and handle the dash properly
    const escapedChars = requirements.specialChars
      .replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
      .replace(/-/g, '\\-');
    const specialCharRegex = new RegExp(`[${escapedChars}]`);
    if (!specialCharRegex.test(password)) {
      errors.push(`Password must contain at least one special character (${requirements.specialChars})`);
    } else {
      score += 15;
    }
  }

  // Additional strength checks
  const uniqueChars = new Set(password).size;
  if (uniqueChars >= password.length * 0.7) {
    score += 10; // Bonus for character diversity
  }

  // Check for common patterns (reduce score)
  if (/(.)\1{2,}/.test(password)) {
    score -= 10; // Repeated characters
  }
  if (/123|abc|qwe|asd|zxc/i.test(password)) {
    score -= 15; // Common sequences
  }

  // Determine strength based on score
  let strength: 'weak' | 'fair' | 'good' | 'strong';
  if (score < 40) {
    strength = 'weak';
  } else if (score < 60) {
    strength = 'fair';
  } else if (score < 80) {
    strength = 'good';
  } else {
    strength = 'strong';
  }

  return {
    isValid: errors.length === 0,
    errors,
    strength,
    score: Math.min(100, Math.max(0, score)),
  };
}

/**
 * Gets password strength color for UI display
 */
export function getPasswordStrengthColor(strength: string): string {
  switch (strength) {
    case 'weak':
      return '#ef4444'; // red-500
    case 'fair':
      return '#f97316'; // orange-500
    case 'good':
      return '#eab308'; // yellow-500
    case 'strong':
      return '#22c55e'; // green-500
    default:
      return '#6b7280'; // gray-500
  }
}

/**
 * Gets password requirements as a human-readable list
 */
export function getPasswordRequirementsList(
  requirements: PasswordRequirements = DEFAULT_PASSWORD_REQUIREMENTS
): string[] {
  const list: string[] = [];

  list.push(`At least ${requirements.minLength} characters long`);
  
  if (requirements.requireUppercase) {
    list.push('At least one uppercase letter (A-Z)');
  }
  
  if (requirements.requireLowercase) {
    list.push('At least one lowercase letter (a-z)');
  }
  
  if (requirements.requireNumbers) {
    list.push('At least one number (0-9)');
  }
  
  if (requirements.requireSpecialChars) {
    list.push(`At least one special character (${requirements.specialChars})`);
  }

  return list;
}
