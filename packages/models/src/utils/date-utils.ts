import { format, parseISO, parse, differenceInMinutes } from 'date-fns';
import type { DateArray } from 'ics';

/**
 * Use this for either date or datetime strings
 * Function will detect if the string is an ISO datetime string or just a date string,
 * and format it accordingly.
 *
 * @param dateString - The date string to format
 * @param dateFormat - The format to use when formatting the date string (default: 'MM/dd/yy', but can be any valid date-fns format)
 */
export const formatDateFromString = (dateString: string, dateFormat: string = 'MM/dd/yy'): string => {
  let dateObj;

  // Check if the date string contains a time component (ISO format)
  if (dateString.includes('T')) {
    dateObj = parseISO(dateString); // For datetime strings (ISO format)
  } else {
    // For 'yyyy-mm-dd' date strings
    dateObj = parse(dateString, 'yyyy-MM-dd', new Date());
  }

  if (isNaN(dateObj.getTime())) {
    return 'Invalid Date';
  }

  return format(dateObj, dateFormat);
};

export const formatDateAndTime = (dateString: string) => {
  const date = new Date(dateString);
  // Format date and time as MM/DD/YY HH:MM:SS AM/PM
  return date
    .toLocaleString('en-US', {
      year: '2-digit',
      month: '2-digit',
      day: '2-digit',
      hour: 'numeric',
      minute: 'numeric',
      second: 'numeric',
      hour12: true,
    })
    .replace(',', ''); // removing the comma it automatically inputs
};

export const formatStartEndDateTime = (start: string, end: string) => {
  const startDate = new Date(start);
  const endDate = new Date(end);

  if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    return '';
  }

  // Assume start and end dates are the same day
  const dateFormatted = format(startDate, 'MMM. d, yyyy');
  const startTime = format(startDate, 'h:mm a');
  const endTime = format(endDate, 'h:mm a');

  return `${dateFormatted} ${startTime} - ${endTime}`;
};

interface Duration {
  duration_hours: string;
  duration_minutes: string;
}

export const calculateDuration = (start: Date, end: Date): Duration => {
  // Calculate the total duration in minutes
  const totalMinutes = differenceInMinutes(end, start);

  // Derive hours and minutes
  const duration_hours = Math.floor(totalMinutes / 60).toString();
  const duration_minutes = (totalMinutes % 60).toString();

  return {
    duration_hours,
    duration_minutes,
  };
};

/**
 * Check if the start time is before the end time
 * @param startTime - The start time string (HH:MM)
 * @param endTime - The end time string (HH:MM)
 * @returns boolean
 */
export const isStartTimeBeforeEndTime = (startTime: string, endTime: string) => {
  const [startHour, startMinute] = startTime.split(':').map(Number);
  const [endHour, endMinute] = endTime.split(':').map(Number);

  if (isNaN(startHour) || isNaN(startMinute) || isNaN(endHour) || isNaN(endMinute)) {
    return false;
  }

  const start = new Date(0, 0, 0, startHour, startMinute);
  const end = new Date(0, 0, 0, endHour, endMinute);

  return start < end;
};

/**
 * Convert a date object to an array of date and time values
 * Supports creation of a start or end date for an iCalendar (.ics) event
 * @param date - The date object to convert
 * @returns number[]
 * @example
 * dateToDateTimeArray(new Date(2000, 1, 5, 10, 0)) // [2000, 1, 5, 10, 0] (January 5, 2000, 10:00 AM)
 */
export const dateToDateTimeArray = (date: Date): DateArray => {
  return [
    date.getFullYear(),
    date.getMonth() + 1, // Convert zero-based month to 1-based
    date.getDate(),
    date.getHours(),
    date.getMinutes(),
  ];
};

/**
 * Create a Date object from a date string and a time string
 * @param dateString - The date string in ISO format
 * @param timeString - The time string in HH:MM format
 * @returns Date
 */
export const dateFromDateAndTimeFields = (dateString: string, timeString: string): Date => {
  const date = new Date(dateString);
  const [hours, minutes] = timeString.split(':').map(Number);

  date.setHours(hours);
  date.setMinutes(minutes);

  return date;
};

export const formatDateFromDateStartAndEndTime = (data: {
  date_start: string;
  start_time: string;
  end_time: string;
}) => {
  const { date_start, start_time, end_time } = data;

  // Parse the date and start/end times
  const date = new Date(date_start); // Ensure date_start is a valid string representation of the date
  const [startHour, startMinute] = start_time.split(':').map(Number);
  const [endHour, endMinute] = end_time.split(':').map(Number);

  // Adjust the date object with the start time
  date.setHours(startHour, startMinute);

  // Format the date
  const options: Intl.DateTimeFormatOptions = {
    timeZone: 'America/Chicago',
    month: 'long',
    day: 'numeric',
    year: 'numeric',
  };
  const formattedDate = date.toLocaleDateString('en-US', options);

  // Format the times with 12-hour format and meridiem
  const startTime = new Date(date);
  const endTime = new Date(date);
  endTime.setHours(endHour, endMinute);

  const formatTime = (time: Date) =>
    time.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
      timeZone: 'America/Chicago',
    });

  const formattedStartTime = formatTime(startTime);
  const formattedEndTime = formatTime(endTime);

  // Combine the date and time into the desired format
  return `${formattedDate}, ${formattedStartTime}-${formattedEndTime} CST`;
};

export const convertTo24Hour = (time12h: string) => {
  if (!time12h) return '';
  const [time, modifier] = time12h.split(' ');
  const [hours, minutes] = time.split(':');
  let hoursNum = parseInt(hours);
  if (modifier === 'PM' && hoursNum < 12) hoursNum = hoursNum + 12;
  if (modifier === 'AM' && hoursNum === 12) hoursNum = 0;
  return `${hoursNum.toString().padStart(2, '0')}:${minutes}`;
};

export const ninetyDaysAgo = () => {
  const date = new Date();
  date.setHours(0, 0, 0, 0); // Set to start of day
  date.setDate(date.getDate() - 90);
  return date;
};

/**
 * Format a date for display with a customizable format string
 * @param date The date to format
 * @param formatString The format string to use (defaults to MM/dd/yyyy)
 * @returns The formatted date string
 */
export const formatDateForDisplay = (date: Date, formatString: string = 'MM/dd/yyyy'): string => {
  return format(date, formatString);
};

/**
 * Formats a time string into a localized time format
 * @param time - Time string in "HH:mm" format
 * @returns Formatted time string in the format "H:MM AM/PM"
 * @example
 * formatTime("14:30") // "2:30 PM"
 */
export function formatTime(time: string): string {
  const [hours, minutes] = time.split(':');
  const date = new Date();
  date.setHours(parseInt(hours, 10));
  date.setMinutes(parseInt(minutes, 10));
  return date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' });
}
