import { validatePassword, DEFAULT_PASSWORD_REQUIREMENTS, getPasswordRequirementsList } from '../password-validation';

describe('Password Validation', () => {
  describe('validatePassword', () => {
    it('should validate a strong password', () => {
      const result = validatePassword('MyStr0ng!Pass');
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.strength).toBe('strong');
      expect(result.score).toBeGreaterThan(70);
    });

    it('should reject password that is too short', () => {
      const result = validatePassword('Abc1!');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must be at least 8 characters long');
    });

    it('should reject password without uppercase letter', () => {
      const result = validatePassword('mypassword123!');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one uppercase letter');
    });

    it('should reject password without lowercase letter', () => {
      const result = validatePassword('MYPASSWORD123!');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one lowercase letter');
    });

    it('should reject password without number', () => {
      const result = validatePassword('MyPassword!');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one number');
    });

    it('should reject password without special character', () => {
      const result = validatePassword('MyPassword123');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one special character (!@#$%^&*()_+-=[]{};\':"|,.<>/?)');
    });

    it('should handle multiple validation errors', () => {
      const result = validatePassword('abc');
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(1);
      expect(result.errors).toContain('Password must be at least 8 characters long');
      expect(result.errors).toContain('Password must contain at least one uppercase letter');
      expect(result.errors).toContain('Password must contain at least one number');
      expect(result.errors).toContain('Password must contain at least one special character (!@#$%^&*()_+-=[]{};\':"|,.<>/?)');
    });

    it('should assign appropriate strength levels', () => {
      const weak = validatePassword('password');
      const fair = validatePassword('Password1');
      const good = validatePassword('Password1!');
      const strong = validatePassword('MyVeryStr0ng!Password');

      expect(weak.strength).toBe('weak');
      expect(fair.strength).toBe('fair');
      expect(good.strength).toBe('good');
      expect(strong.strength).toBe('strong');
    });

    it('should give bonus points for longer passwords', () => {
      const short = validatePassword('MyStr0ng!');
      const medium = validatePassword('MyStr0ng!Pass');
      const long = validatePassword('MyVeryLongStr0ng!Password');

      expect(medium.score).toBeGreaterThan(short.score);
      expect(long.score).toBeGreaterThan(medium.score);
    });

    it('should penalize common patterns', () => {
      const normal = validatePassword('MyStr0ng!Pass');
      const repeated = validatePassword('MyStr000!Pass');
      const sequence = validatePassword('MyStr123!Pass');

      expect(repeated.score).toBeLessThan(normal.score);
      expect(sequence.score).toBeLessThan(normal.score);
    });
  });

  describe('getPasswordRequirementsList', () => {
    it('should return default requirements list', () => {
      const requirements = getPasswordRequirementsList();
      
      expect(requirements).toContain('At least 8 characters long');
      expect(requirements).toContain('At least one uppercase letter (A-Z)');
      expect(requirements).toContain('At least one lowercase letter (a-z)');
      expect(requirements).toContain('At least one number (0-9)');
      expect(requirements).toContain('At least one special character (!@#$%^&*()_+-=[]{};\':"|,.<>/?)');
    });

    it('should return custom requirements list', () => {
      const customRequirements = {
        ...DEFAULT_PASSWORD_REQUIREMENTS,
        minLength: 12,
        requireSpecialChars: false,
      };
      
      const requirements = getPasswordRequirementsList(customRequirements);
      
      expect(requirements).toContain('At least 12 characters long');
      expect(requirements).not.toContain('At least one special character');
    });
  });

  describe('Real-world password examples', () => {
    const validPasswords = [
      'MySecure123!',
      'P@ssw0rd2024',
      'Str0ng!Password',
      'C0mplex#Pass',
      'Secure$123',
    ];

    const invalidPasswords = [
      'password',
      '12345678',
      'PASSWORD',
      'Password',
      'Password123',
      'Pass!',
      'short',
    ];

    validPasswords.forEach((password) => {
      it(`should accept valid password: ${password}`, () => {
        const result = validatePassword(password);
        expect(result.isValid).toBe(true);
      });
    });

    invalidPasswords.forEach((password) => {
      it(`should reject invalid password: ${password}`, () => {
        const result = validatePassword(password);
        expect(result.isValid).toBe(false);
      });
    });
  });
});
