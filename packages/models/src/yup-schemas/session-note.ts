import * as yup from 'yup';

export enum SessionNoteStatus {
  SUBMITTED = 'submitted',
  NEW = 'new',
  REJECTED = 'rejected',
  APPROVED = 'approved',
}

export const sessionNoteSchema = yup.object().shape({
  id: yup.string().required(),
  name: yup.string().required(),
  description: yup.string().required(),
  note: yup.string().required(),
  attendance_and_promptness: yup.string().nullable(),
  status: yup.string().oneOf(Object.values(SessionNoteStatus)).required(),
  moms_engagement_c: yup.string().nullable(),
  new_attempt: yup.boolean().nullable(),
  new_attempt_example: yup.string().nullable(),
  date_submitted_c: yup.string().nullable(),
});

export type SessionNoteSchema = yup.InferType<typeof sessionNoteSchema>;

export const sessionReportWithPairingsSchema = yup.object({
  ...sessionNoteSchema.fields,
  created_at: yup.date().optional().nullable(),
  created_by_id: yup.string().nullable(),
  created_by_name: yup.string().nullable(),
  updated_at: yup.date().optional().nullable(),
  session_id: yup.string().required(),
  track_id: yup.string().nullable(),
  covered_lesson_template_id: yup.string().nullable(),
  session: yup.object({
    id: yup.string().required(),
    created_at: yup.date().optional().nullable(),
    created_by_id: yup.string().nullable(),
    created_by_name: yup.string().nullable(),
    updated_at: yup.date().optional().nullable(),
    date_start: yup.date().optional().nullable(),
    date_end: yup.date().optional().nullable(),
    description: yup.string().nullable(),
    name: yup.string().required().nullable(),
    location: yup.string().nullable(),
    join_url: yup.string().nullable(),
    status: yup.string().required().nullable(),
    session_type: yup.string().required().nullable(),
    is_group_session: yup.boolean().nullable(),
    pairing_id: yup.string().optional().nullable(),
    session_pairings: yup
      .array()
      .of(
        yup.object({
          id: yup.string().required(),
          created_at: yup.date().optional().nullable(),
          created_by_id: yup.string().nullable(),
          created_by_name: yup.string().nullable(),
          updated_at: yup.date().optional().nullable(),
        }),
      )
      .nullable(),
    pairing: yup
      .object()
      .shape({
        id: yup.string().required(),
        created_at: yup.date().optional().nullable(),
        created_by_id: yup.string().nullable(),
        created_by_name: yup.string().nullable(),
        updated_at: yup.date().optional().nullable(),
        name: yup.string().required(),
        description: yup.string().nullable(),
        momId: yup.string().required().nullable(),
        advocateUserId: yup.string().required().nullable(),
        trackId: yup.string().required().nullable(),
        status: yup.string().required().nullable(),
        track_status: yup.string().nullable(),
        in_program_track_sub_status: yup.string().nullable(),
        discharge_incomplete_sub_status: yup.string().nullable(),
        incomplete_reason_sub_status: yup.string().nullable(),
        complete_reason_sub_status: yup.string().nullable(),
        advocateUser: yup
          .object()
          .shape({
            id: yup.string().required(),
            firstName: yup.string().nullable(),
            lastName: yup.string().nullable(),
            email: yup.string().required(),
            advocate_status: yup.string().nullable(),
            description: yup.string().nullable(),
            status: yup.string().nullable(),
            created_at: yup.date().nullable(),
            created_by_id: yup.string().nullable(),
            created_by_name: yup.string().nullable(),
            refreshTokenHash: yup.string().nullable(),
          })
          .nullable(),
        mom: yup
          .object()
          .shape({
            id: yup.string().required(),
            first_name: yup.string().nullable(),
            last_name: yup.string().nullable(),
            email1: yup.string().nullable(),
            phone_other: yup.string().nullable(),
            prospect_status: yup.string().nullable(),
          })
          .nullable(),
      })
      .nullable(),
  }),
});

export type SessionReportWithPairings = yup.InferType<typeof sessionReportWithPairingsSchema>;
export enum AttendanceAndPromptnessType {
  ON_TIME = 'On_Time',
  LATE = 'Late',
  NO_SHOW = 'No_Show',
}

export enum MomEngagementType {
  FULL = 'Full',
  PARTIAL = 'Partial',
  NONE = 'None',
}
