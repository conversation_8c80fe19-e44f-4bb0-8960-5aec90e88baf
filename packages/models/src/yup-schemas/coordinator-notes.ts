import * as yup from 'yup';

export const coordinatorNotesApiFieldSchemas = {
  description: yup.string().required('Note details are required'),
  id: yup.string().optional(),
  name: yup.string().optional().nullable(),
  created_at: yup.date().optional().nullable(),
  type_c: yup.string().oneOf(['safety_or_concern_update', 'court_update', 'interview_advocate']),
};

export const coordinatorNoteTypeOptions = [
  { value: 'safety_or_concern_update', label: 'Safety or Concern Update' },
  { value: 'court_update', label: 'Court Update' },
];

export const coordinatorNotesSchema = yup.object().shape(coordinatorNotesApiFieldSchemas);
export type CoordinatorNotesSchema = yup.InferType<typeof coordinatorNotesSchema>;

export enum CoordinatorNoteTypeEnum {
  SafetyOrConcernUpdate = 'safety_or_concern_update',
  CourtUpdate = 'court_update',
  InterviewAdvocate = 'interview_advocate',
}
