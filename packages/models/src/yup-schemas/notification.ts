export enum NotificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  FAILED = 'failed',
}

export enum NotificationTemplate {
  SESSION_NOTE_SUBMITTED = 'session_note_submitted',
  SESSION_NOTE_REJECTED = 'session_note_rejected',
  MOM_ASSIGNED_TO_COORDINATOR = 'mom_assigned_to_coordinator',
  NEW_REFERRAL_SUPERVISOR = 'new_referral_supervisor',
  MOM_READY_FOR_ADVOCATE_ASSIGNMENT = 'mom_ready_for_advocate_assignment',
  BENEVOLENCE_NEED_SUBMITTED = 'benevolence_need_submitted',
  NEW_WELLNESS_ASSESSMENT_COORDINATOR = 'new_wellness_assessment_coordinator',
  FORGOT_PASSWORD = 'forgot_password',
  ADVOCATE_INVITE_TRAINING = 'advocate_invite_training',
  UPCOMING_SESSION_REMINDER = 'upcoming_session_reminder',
  NEW_MOM_ASSIGNED_ADVOCATE = 'new_mom_assigned_advocate',
  NEW_ADVOCATE_ASSIGNED_TO_MOM = 'new_advocate_assigned_to_mom',
  MOM_ACCEPTED_INTO_PROGRAM = 'mom_accepted_into_program',
}
