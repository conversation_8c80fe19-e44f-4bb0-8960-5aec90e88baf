import * as yup from 'yup';
import { documentSchema } from './document';

export const lessonTemplateSchema = yup.object().shape({
  id: yup.string().optional(), // Optional seems weird here but it needs to be optional b/c create requests (as one example) won't include an id.
  title: yup.string(),
  description: yup.string().optional(),
  priority: yup.number().optional(),
  order: yup.number().optional(),
  duration_days: yup.number().optional(),
  track_id: yup.string().optional(),
  documents: yup.array().of(documentSchema).optional(),
});

export type LessonTemplateSchema = yup.InferType<typeof lessonTemplateSchema>;
