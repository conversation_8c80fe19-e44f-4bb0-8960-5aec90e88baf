import * as yup from 'yup';
import { REQUIRED, SELECT_OPTION } from './common-validation';

export const connectionMethodOptions = [
  { value: 'Call', label: 'Call' },
  { value: 'SMS_Text', label: 'SMS/Text' },
  { value: 'Email', label: 'Email' },
  { value: 'In_Person', label: 'In Person' },
  { value: 'Video', label: 'Video' },
];

export const connectionLogUiFieldSchemas = {
  // workaround for client-side validation of field using datepicker component that returns a Date object,
  // while the SuiteCRM API expects a string in the format 'YYYY-MM-DD'
  date_created_c: yup.date().required(REQUIRED),
};

export const connectionLogApiFieldSchemas = {
  id: yup.string().optional(),
  created_by_name: yup.string().optional().nullable(), // for UI display only
  contact_method_c: yup
    .string()
    .required(SELECT_OPTION)
    .oneOf(connectionMethodOptions.map((option) => option.value)),
  summary_c: yup.string().required(REQUIRED),
  // SuiteCRM 'Checkbox' field type uses enum "1"/"0" instead of true/false
  is_visible_to_advocates_c: yup.boolean().required(SELECT_OPTION),
  date_created_c: yup.date().required(REQUIRED),
  // contact_id: yup.string().optional(), // TEMP: for UI filtering only
  name: yup.string().optional().nullable(),
  isDeleted: yup.boolean().optional(),
};

export const connectionLogSchema = yup.object().shape(connectionLogApiFieldSchemas);
export type ConnectionLogSchema = yup.InferType<typeof connectionLogSchema>;

const createConnectionLogApiFieldSchemas = {
  mom_id: yup.string().optional(),
  user_id: yup.string().optional(),
  ...connectionLogApiFieldSchemas,
};

export const createConnectionLogSchema = yup.object().shape(createConnectionLogApiFieldSchemas);
export type CreateConnectionLogSchema = yup.InferType<typeof createConnectionLogSchema>;

export const connectionLogWithMinimalMomSchema = yup.object().shape({
  ...connectionLogApiFieldSchemas,
  mom: yup
    .object()
    .shape({
      first_name: yup.string().nullable(),
      last_name: yup.string().nullable(),
    })
    .optional()
    .nullable(),
});
export type ConnectionLogWithMinimalMomSchema = yup.InferType<typeof connectionLogWithMinimalMomSchema>;
