import * as yup from 'yup';
import { REQUIRED } from './common-validation';

export enum ServiceType {
  BENEVOLENCE = 'benevolence',
  CARE_COMMUNITIES = 'care_communities',
  CHILDCARE = 'childcare',
  CLOSET_VISIT = 'closet_visit',
  CRISIS_RESOURCES = 'crisis_resources',
  EDUCATION = 'education',
  GROUP_PARENTING = 'group_parenting',
  HEALTH = 'health',
  HOUSING = 'housing',
  LEGAL = 'legal',
  MENTAL_HEALTH = 'mental_health',
  SUBSTANCE = 'substance',
  THERAPY = 'therapy'
}

export enum OutcomeType {
  SUCCESSFUL = 'successful',
  UNSUCCESSFUL = 'unsuccessful',
  UNKNOWN = 'unknown'
}

export const serviceReferralSchema = yup.object().shape({
  id: yup.string().optional(),
  service: yup.string().oneOf(Object.values(ServiceType)).required(REQUIRED),
  outcome: yup.string().oneOf(Object.values(OutcomeType)).required(REQUIRED),
  start_date: yup.date().required(REQUIRED),
  provider: yup.string().required(REQUIRED),
  mom_id: yup.string().required(REQUIRED),
});

export type ServiceReferralSchema = yup.InferType<typeof serviceReferralSchema>;
