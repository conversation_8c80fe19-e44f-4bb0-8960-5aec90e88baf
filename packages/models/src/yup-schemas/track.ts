import * as yup from 'yup';

export const trackSchema = yup.object().shape({
  id: yup.string().optional(),
  title: yup.string().required(),
  description: yup.string().optional(),
  track_summary: yup.string().optional(),
  mom_summary: yup.string().optional(),
  connection_description: yup.string().optional(),
  language_type: yup.string().oneOf(['english', 'spanish']).optional(),
  pairings: yup.array().of(yup.string()).optional(),
  lessonTemplates: yup.array().of(yup.string()).optional(),
  sessionNotes: yup.array().of(yup.string()).optional(),
});

export type TrackSchema = yup.InferType<typeof trackSchema>;
