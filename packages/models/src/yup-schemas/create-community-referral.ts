import * as yup from 'yup';
import { documentSchema } from './document';
import { communityReferralSchema } from './community-referral';

export const createCommunityReferralRequestSchema = yup.object().shape({
  cloudflare_turnstile_token: yup.string().required(), // This needs to be optional when submitting locally
  referral: communityReferralSchema,
  documents: yup.array().of(documentSchema).optional(),
});

export type CreateCommunityReferralRequest = yup.InferType<typeof createCommunityReferralRequestSchema>;
