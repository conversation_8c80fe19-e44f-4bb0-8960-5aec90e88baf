import * as yup from 'yup';

export const REFERRAL_STATUS_PROSPECT = 'prospect';

// Shape of static services configuration object
// Note: services are shown as a static info section/list on the referral form rather than a selction field
interface ReferralServiceItem {
  title?: string;
  description: string;
}
export interface ReferralServicesConfig {
  overviewText: string;
  servicesList: ReferralServiceItem[];
  conclusionText?: string;
}

export const caregiverOptions = [
  { label: 'Biological Mom', value: 'biological_mom' },
  { label: 'Relative/Guardian', value: 'relative_guardian' },
  { label: 'Non-Relative Guardian', value: 'nonrelative_guardian' },
  { label: 'Adoptive Mom', value: 'adoptive_mom' },
];

export const createReferralMeetingSchema = yup.object().shape({
  referral_id: yup.string().required(),
  full_name: yup.string().required(),
  session_type: yup.string().required(),
  meeting_type: yup.object().required(),
  location: yup.string().optional(),
  join_url: yup.string().optional(),
  start_time: yup.string().required(),
  date_start: yup.string().required(),
  date_end: yup.string().required(),
  end_time: yup.string().required(),
});

export type CreateReferralMeetingSchema = yup.InferType<typeof createReferralMeetingSchema>;

export type ReferralSessionDataType = {
  referral_id?: string;
  full_name: string;
  date_start: string;
  date_end: string;
  start_time: string;
  end_time: string;
  join_url?: string;
  location?: string;
  meeting_type: string;
  session_type: string;
};
