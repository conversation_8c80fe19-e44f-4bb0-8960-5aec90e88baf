import * as yup from 'yup';

export const documentSchema = yup.object().shape({
  id: yup.string().optional(), // Optional seems weird here but it needs to be optional b/c create requests (as one example) won't include an id.
  document_name: yup.string().optional(),
  filecontents: yup.string().optional(),
  filename: yup.string().optional().nullable(),
  mimeType: yup.string().optional().nullable(),
  description: yup.string().optional(),
  external_url_c: yup.string().optional(), // This is the URL for external resources.
  s3_file_name: yup.string().optional(), // This is the S3 file name for the document.
  subcategory_id: yup.string().optional(), // This is the subcategory for the document.
  mom_id: yup.string().optional(),
  coordinator_id: yup.string().optional(),
  advocate_id: yup.string().optional(),
  coordinator: yup.object().optional(),
  advocate: yup.object().optional(),
  created_at: yup.date().optional(),
  lesson_template_id: yup.string().optional(),
  lesson_id: yup.string().optional(),
  is_primary_lesson_resource: yup.boolean(),
  documentTags: yup
    .array()
    .of(
      yup.object().shape({
        id: yup.string().optional(),
        name: yup.string().optional(),
      }),
    )
    .optional(),
});

export type DocumentSchema = yup.InferType<typeof documentSchema>;
