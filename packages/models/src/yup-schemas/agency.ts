import * as yup from 'yup';

export const agencySchema = yup.object().shape({
  id: yup.string().optional(), // Optional seems weird here but it needs to be optional b/c create requests (as one example) won't include an id.
  name: yup.string().optional().nullable(),
  agency_name: yup.string().required(),
  agency_phone: yup.string().optional().nullable(),
  address: yup.string().optional().nullable(),
  address_city: yup.string().optional().nullable(),
  address_state: yup.string().optional().nullable(),
  address_postalcode: yup.string().optional().nullable(),
  contact_first_name: yup.string().required(),
  contact_last_name: yup.string().required(),
  contact_email: yup.string().optional().nullable(),
  contact_phone: yup.string().optional().nullable(),
});

export type Agency = yup.InferType<typeof agencySchema>;
