import * as yup from 'yup';

export enum MomStatus {
  INACTIVE = 'inactive',
  ACTIVE = 'active',
}

export enum ProspectStatus {
  PROSPECT = 'prospect',
  ENGAGED_IN_PROGRAM = 'engaged_in_program',
  DID_NOT_ENGAGE_IN_PROGRAM = 'did_not_engage_in_program',
  PROSPECT_INTAKE_SCHEDULED = 'prospect_intake_scheduled',
}

export type ProspectStatusType =
  | 'prospect'
  | 'prospect_intake_scheduled'
  | 'engaged_in_program'
  | 'did_not_engage_in_program'
  | null;

export const momStatusOptions = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
];

export type MomStatusType = (typeof momStatusOptions)[number]['value'];

export const LanguageArray = [
  'english',
  'spanish',
  'chinese_mandarin',
  'vietnamese',
  'tagalog',
  'arabic',
  'korean',
  'russian',
  'french',
  'hindi',
  'portuguese',
  'bengali',
  'urdu',
  'german',
  'haitian_creole',
  'polish',
  'italian',
  'japanese',
  'persian_farsi',
  'gujarati',
  'other',
] as const;

export const momFullSchema = yup.object().shape({
  id: yup.string().optional(), // This needs to be optional b/c the id field is not present yet when we're applying the yup validation. Plus, it wouldn't be provided in the case of creations.
  first_name: yup.string().optional().nullable(),
  last_name: yup.string().required().nullable(),
  name: yup.string().optional().nullable(), // Used to display full name in UI

  email1: yup.string().optional().nullable(),
  phone_other: yup.string().optional().nullable(),
  birthdate: yup
    .date()
    .transform((v, originalV) => (originalV === '' ? undefined : v))
    .optional()
    .nullable(),
  status: yup
    .string()
    .transform((v) => (v === '' ? undefined : v))
    .optional()
    .oneOf(momStatusOptions.map((status) => status.value))
    .nullable(),
  caregiver_type_c: yup
    .string()
    .nullable()
    .optional()
    .oneOf(['biological_mom', 'adoptive_mom', 'relative_guardian', 'nonrelative_guardian', null]),
  discharge_reason_c: yup.string().optional().nullable(),
  date_entered: yup.string().optional().nullable(),
  closed_date_c: yup
    .date()
    .transform((v, originalV) => (originalV === '' ? undefined : v))
    .nullable()
    .optional(),
  language_preference_c: yup.string().nullable().optional().oneOf(LanguageArray),
  language_notes_c: yup.string().optional().nullable(),
  languages_c: yup.array().of(yup.string().oneOf(LanguageArray)).nullable().optional(),
  primary_address_street: yup.string().optional().nullable(),
  primary_address_city: yup.string().optional().nullable(),
  primary_address_state: yup.string().optional().nullable(),
  primary_address_postalcode: yup.string().optional().nullable(),
  primary_address_county_c: yup.string().optional().nullable(),
  address_access_c: yup.string().optional().nullable(),
  connected_benevolance_c: yup.bool().optional().nullable(),
  connected_childcare_c: yup.bool().optional().nullable(),
  connected_closet_c: yup.bool().optional().nullable(),
  connected_education_c: yup.bool().optional().nullable(),
  connected_health_c: yup.bool().optional().nullable(),
  connected_housing_c: yup.bool().optional().nullable(),
  connected_legal_c: yup.bool().optional().nullable(),
  connected_mental_health_c: yup.bool().optional().nullable(),
  connected_substance_c: yup.bool().optional().nullable(),
  photo: yup.string().optional().nullable(),
  referral_type_c: yup.string().optional().nullable(),
  supports_court_order_c: yup.boolean().optional().nullable(),
  service_selected_c: yup.string().optional().nullable(),
  need_details_c: yup.string().optional().nullable(),
  created_by_name: yup.string().optional().nullable(),
  account_name: yup.string().optional().nullable(),
  currently_pregnant_c: yup.string().optional().nullable(),
  number_of_children_c: yup.number().optional().nullable(),
  preferred_contact_method_c: yup.string().optional().nullable(),
  assigned_user_id: yup.string().optional().nullable(),
  converted: yup.string().optional().nullable(),
  full_name: yup.string().optional().nullable(),
  prospect_status: yup
    .string()
    .nullable()
    .optional()
    .oneOf(['prospect', 'prospect_intake_scheduled', 'engaged_in_program', 'did_not_engage_in_program', null]),
  photo_url: yup.string().optional(),
  photo_s3_file_name: yup.string().optional(),
});

export type MomType = yup.InferType<typeof momFullSchema>;
