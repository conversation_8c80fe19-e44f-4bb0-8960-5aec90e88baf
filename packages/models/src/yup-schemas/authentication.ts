import * as yup from 'yup';
import { passwordSchema } from './common-fields';

export const forgotPasswordRequestResetSchema = yup.object().shape({
  username: yup.string().required(),
});

export const resetForgottenPasswordSchema = yup.object().shape({
  password: passwordSchema,
  confirmPassword: yup
    .string()
    .required('Confirm password is required')
    .oneOf([yup.ref('password')], 'Passwords must match'),
  token: yup.string().required(),
  username: yup.string().required(),
});

export type ForgotPasswordType = yup.InferType<typeof forgotPasswordRequestResetSchema>;
export type ResetForgottenPasswordType = yup.InferType<typeof resetForgottenPasswordSchema>;
