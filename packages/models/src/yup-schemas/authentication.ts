import * as yup from 'yup';

export const forgotPasswordRequestResetSchema = yup.object().shape({
  username: yup.string().required(),
});

export const resetForgottenPasswordSchema = yup.object().shape({
  password: yup.string().required(),
  confirmPassword: yup.string().required(),
  token: yup.string().required(),
  username: yup.string().required(),
});

export type ForgotPasswordType = yup.InferType<typeof forgotPasswordRequestResetSchema>;
export type ResetForgottenPasswordType = yup.InferType<typeof resetForgottenPasswordSchema>;
