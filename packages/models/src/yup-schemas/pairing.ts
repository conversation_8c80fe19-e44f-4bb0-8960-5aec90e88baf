import * as yup from 'yup';

export enum PairingStatusType {
  PAIRED = 'paired',
  WAITING_TO_BE_PAIRED = 'waiting_to_be_paired',
  PAIRING_COMPLETE = 'pairing_complete',
}

export const pairingSchema = yup.object().shape({
  id: yup.string().optional(), // Optional seems weird here but it needs to be optional b/c create requests (as one example) won't include an id.
  name: yup.string().optional(),
  description: yup.string().optional(),
  momId: yup.string().optional(),
  mom: yup.object().optional(),
  advocateUserId: yup.string().optional(),
  advocateUser: yup.object().optional(),
  trackId: yup.string().optional(),
  track: yup.object().optional(),
  status: yup.string().optional(),
  track_status: yup.string().optional(),
  in_program_track_sub_status: yup.string().optional(),
  discharge_incomplete_sub_status: yup.string().optional(),
  incomplete_reason_sub_status: yup.string().optional(),
  complete_reason_sub_status: yup.string().optional(),
  session_pairings: yup.array().optional(),
  lessons: yup.array().optional(),
});

export type PairingSchema = yup.InferType<typeof pairingSchema>;
