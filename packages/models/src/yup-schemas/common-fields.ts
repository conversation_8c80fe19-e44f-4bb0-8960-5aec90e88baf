import * as yup from 'yup';
import {
  INVALID_PHONE,
  INVALID_ZIP,
  INVALID_EMAIL,
  START_TIME_BEFORE_END_TIME,
  END_TIME_AFTER_START_TIME,
} from './common-validation';
import { isStartTimeBeforeEndTime } from '../utils/date-utils';

const PHONE_REGEX = /^([\\+]?|00)((([(]{0,1}\\s*[0-9]{1,4}\\s*[)]{0,1})\\s*)*|([\\-\\s\\./0-9])*)+$/; // SuiteCRM phone number validation regex
const US_ZIP_REGEX = /^\d{5}$/; // only accepts US 5-digit numerical postal code

export const phoneSchema = yup.string().matches(PHONE_REGEX, INVALID_PHONE);
export const zipCodeSchema = yup.string().matches(US_ZIP_REGEX, INVALID_ZIP);
export const emailSchema = yup.string().email(INVALID_EMAIL);

export const getStartTimeSchema = (endTimeField: string, errorMessage?: string) => {
  return yup.string().test('is-before-end-time', errorMessage || START_TIME_BEFORE_END_TIME, function (startTime) {
    const endTime = this.parent[endTimeField];
    if (!startTime || !endTime) return true; // Don't validate if start or end time is not set
    return isStartTimeBeforeEndTime(startTime, endTime);
  });
};

export const getEndTimeSchema = (startTimeField: string, errorMessage?: string) => {
  return yup.string().test('is-after-start-time', errorMessage || END_TIME_AFTER_START_TIME, function (endTime) {
    const startTime = this.parent[startTimeField];
    if (!startTime || !endTime) return true; // Don't validate if start or end time is not set
    return isStartTimeBeforeEndTime(startTime, endTime);
  });
};
