import * as yup from 'yup';
import {
  INVALID_PHONE,
  INVALID_ZIP,
  INVALID_EMAIL,
  START_TIME_BEFORE_END_TIME,
  END_TIME_AFTER_START_TIME,
} from './common-validation';
import { isStartTimeBeforeEndTime } from '../utils/date-utils';

const PHONE_REGEX = /^([\\+]?|00)((([(]{0,1}\\s*[0-9]{1,4}\\s*[)]{0,1})\\s*)*|([\\-\\s\\./0-9])*)+$/; // SuiteCRM phone number validation regex
const US_ZIP_REGEX = /^\d{5}$/; // only accepts US 5-digit numerical postal code

// Password validation regex patterns
const PASSWORD_MIN_LENGTH = 8;
const PASSWORD_UPPERCASE_REGEX = /[A-Z]/; // At least one uppercase letter
const PASSWORD_LOWERCASE_REGEX = /[a-z]/; // At least one lowercase letter
const PASSWORD_NUMBER_REGEX = /\d/; // At least one number
const PASSWORD_SPECIAL_CHAR_REGEX = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/; // At least one special character

export const phoneSchema = yup.string().matches(PHONE_REGEX, INVALID_PHONE);
export const zipCodeSchema = yup.string().matches(US_ZIP_REGEX, INVALID_ZIP);
export const emailSchema = yup.string().email(INVALID_EMAIL);

// Password validation schema with comprehensive requirements
export const passwordSchema = yup
  .string()
  .required('Password is required')
  .min(PASSWORD_MIN_LENGTH, `Password must be at least ${PASSWORD_MIN_LENGTH} characters long`)
  .matches(PASSWORD_UPPERCASE_REGEX, 'Password must contain at least one uppercase letter')
  .matches(PASSWORD_LOWERCASE_REGEX, 'Password must contain at least one lowercase letter')
  .matches(PASSWORD_NUMBER_REGEX, 'Password must contain at least one number')
  .matches(PASSWORD_SPECIAL_CHAR_REGEX, 'Password must contain at least one special character (!@#$%^&*()_+-=[]{};\':"|,.<>/?)');

export const getStartTimeSchema = (endTimeField: string, errorMessage?: string) => {
  return yup.string().test('is-before-end-time', errorMessage || START_TIME_BEFORE_END_TIME, function (startTime) {
    const endTime = this.parent[endTimeField];
    if (!startTime || !endTime) return true; // Don't validate if start or end time is not set
    return isStartTimeBeforeEndTime(startTime, endTime);
  });
};

export const getEndTimeSchema = (startTimeField: string, errorMessage?: string) => {
  return yup.string().test('is-after-start-time', errorMessage || END_TIME_AFTER_START_TIME, function (endTime) {
    const startTime = this.parent[startTimeField];
    if (!startTime || !endTime) return true; // Don't validate if start or end time is not set
    return isStartTimeBeforeEndTime(startTime, endTime);
  });
};
