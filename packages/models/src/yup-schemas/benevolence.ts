import * as yup from 'yup';

export const BenevolenceNeedsType = {
  Financial: { label: 'Financial', value: 'Financial' },
  PhysicalGoods: { label: 'Physical Goods', value: 'Physical' },
  OtherServices: { label: 'Other Services', value: 'Other' },
} as const;

export type BenevolenceNeeds = (typeof BenevolenceNeedsType)[keyof typeof BenevolenceNeedsType];
export type BenevolenceNeedsLabels = BenevolenceNeeds['label'];
export type BenevolenceNeedsValues = BenevolenceNeeds['value'];

export const benevolenceApiFieldSchemas = {
  created_at: yup.string().optional(),
  created_by_name: yup.string().nullable().optional(),
  id: yup.string().optional(),
  isDeleted: yup.boolean().optional(),
  name: yup.string().optional(),
  amount_c: yup
    .number()
    .transform((v, originalV) => (originalV === '' ? undefined : v))
    .optional()
    .nullable(),
  description: yup.string().optional().nullable(),
  did_address_need_c: yup.boolean().required('Please say if the need has been addressed'),
  financial_amount_gifted_c: yup
    .number()
    .transform((v, originalV) => (originalV === '' ? null : v))
    .nullable()
    .optional(),
  financial_amount_requested_c: yup
    .number()
    .transform((v, originalV) => (originalV === '' ? null : v))
    .nullable()
    .optional(),
  financial_mom_contribution_c: yup
    .number()
    .transform((v, originalV) => (originalV === '' ? null : v))
    .nullable()
    .optional(),
  financial_prevention_plan_c: yup.string().optional().nullable(),
  is_urgent_c: yup.boolean().optional().nullable(),
  non_addressal_comment_c: yup.string().optional().nullable(),
  notes_c: yup.string().nullable().default('').optional(),
  other_is_referral_needed_c: yup.boolean().required(),
  pg_fulfillment_method_c: yup.string().optional().nullable(),
  physical_good_monetary_value_c: yup.number().optional().nullable(),
  resolved_date_c: yup.date().optional().nullable(),
  provided_date_c: yup.date().optional().nullable(),
  type_c: yup
    .string()
    .required('Please select a need type')
    .oneOf(Object.values(BenevolenceNeedsType).map((item) => item.value)),
  ema_benevolence_contacts_name: yup.string().optional(),
  user_id_c: yup.string().optional(),
};

export const benevolenceSchema = yup.object().shape(benevolenceApiFieldSchemas);
export type BenevolenceSchema = yup.InferType<typeof benevolenceSchema>;

const createBenevolenceApiFieldSchemas = {
  client_id: yup.string().required(),
  mom_name: yup.string().required(),
  ...benevolenceApiFieldSchemas,
};

export const createBenevolenceSchema = yup.object().shape(createBenevolenceApiFieldSchemas);
export type CreateBenevolenceSchema = yup.InferType<typeof createBenevolenceSchema>;

// Add a function to generate the form validation schema
export const getBenevolenceFormSchema = (needType: string) => {
  const baseSchema = {
    did_address_need_c: yup.boolean().required('Please say if the need has been addressed'),
    notes_c: yup.string().nullable().optional(),
    provided_date_c: yup.string().nullable().optional(),
    non_addressal_comment_c: yup.string().nullable().optional(),
  };

  switch (needType) {
    case BenevolenceNeedsType.PhysicalGoods.value:
      return yup.object().shape({
        ...baseSchema,
        pg_fulfillment_method_c: yup.string().nullable().optional(),
        physical_good_monetary_value_c: yup
          .number()
          .transform((value) => (isNaN(value) ? undefined : Number(value)))
          .nullable()
          .optional(),
      });

    case BenevolenceNeedsType.Financial.value:
      return yup.object().shape({
        ...baseSchema,
        financial_amount_requested_c: yup
          .number()
          .transform((value) => (isNaN(value) ? undefined : Number(value)))
          .nullable()
          .optional(),
        financial_mom_contribution_c: yup
          .number()
          .transform((value) => (isNaN(value) ? undefined : Number(value)))
          .nullable()
          .optional(),
        financial_amount_gifted_c: yup
          .number()
          .transform((value) => (isNaN(value) ? undefined : Number(value)))
          .nullable()
          .optional(),
        financial_prevention_plan_c: yup.string().nullable().optional(), // financial plan
      });

    case BenevolenceNeedsType.OtherServices.value:
      return yup.object().shape({
        ...baseSchema,
        other_is_referral_needed_c: yup.boolean().required('Referral needed is required'),
        did_address_need_c: yup.boolean().required('Please say if the need has been addressed'),
      });

    default:
      return yup.object().shape(baseSchema);
  }
};
