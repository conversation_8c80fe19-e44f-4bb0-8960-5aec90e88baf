import * as yup from 'yup';
import { selfReferralApiFieldSchemas } from './self-referral';
import { emailSchema, phoneSchema } from './common-fields';
import { REQUIRED, MUST_AGREE } from './common-validation';

export const communityReferralApiFieldSchemas = {
  ...selfReferralApiFieldSchemas,
  consent_obtained_c: yup.boolean().oneOf([true], MUST_AGREE),
  supports_court_order_c: yup.boolean().optional(),
  referring_contact_first_name_c: yup.string().required(REQUIRED),
  referring_contact_last_name_c: yup.string().required(REQUIRED),
  referring_contact_email_c: emailSchema.required(REQUIRED),
  referring_contact_phone_c: phoneSchema.required(REQUIRED),
  agency_id: yup.string().optional(),
};

export const communityReferralSchema = yup.object().shape(communityReferralApiFieldSchemas);

export type CommunityReferralSchema = yup.InferType<typeof communityReferralSchema>;
