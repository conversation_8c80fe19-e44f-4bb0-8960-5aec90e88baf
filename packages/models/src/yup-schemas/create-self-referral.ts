import * as yup from 'yup';
import { documentSchema } from './document';
import { selfReferralSchema } from './self-referral';

export const createSelfReferralRequestSchema = yup.object().shape({
  cloudflare_turnstile_token: yup.string().required(), // This needs to be optional when submitting locally
  referral: selfReferralSchema,
  documents: yup.array().of(documentSchema).optional(),
});

export type CreateSelfReferralRequest = yup.InferType<typeof createSelfReferralRequestSchema>;
