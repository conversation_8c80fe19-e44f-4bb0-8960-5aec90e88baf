import * as yup from 'yup';
import { emailSchema, phoneSchema, zipCodeSchema } from './common-fields';
import { REQUIRED, INVALID_NUMBER } from './common-validation';
import { caregiverOptions, REFERRAL_STATUS_PROSPECT } from './referral';

export const selfReferralApiFieldSchemas = {
  id: yup.string().optional(), // Optional seems weird here but it needs to be optional b/c create requests (as one example) won't include an id.
  primary_address_postalcode: zipCodeSchema.required(REQUIRED),
  first_name: yup.string().required(REQUIRED),
  last_name: yup.string().required(REQUIRED),
  prospect_status: yup
    .string()
    .optional()
    .oneOf([REFERRAL_STATUS_PROSPECT, 'engaged_in_program', 'did_not_engage_in_program', 'prospect_intake_scheduled']),
  referral_sub_status: yup
    .string()
    .optional()
    .oneOf([
      'unable_to_contact',
      'client_declined_services',
      'ineligible_for_program',
      'duplicate_referral',
      'referred_to_alternate_program',
    ]), // if the user is of status 'did_not_engage_in_program', then the referral_sub_status is required
  phone_other: phoneSchema.required(REQUIRED),
  currently_pregnant_c: yup.string().oneOf(['yes', 'no', 'unknown']).required(REQUIRED),
  number_of_children_c: yup
    .number()
    .min(0, INVALID_NUMBER)
    .max(20, INVALID_NUMBER)
    .transform((v, originalV) => (originalV === '' ? undefined : v))
    .required(REQUIRED),
  need_details_c: yup.string().optional(),
  what_else_c: yup.string().optional(),
  caregiver_type_c: yup
    .string()
    .required(REQUIRED)
    .oneOf(caregiverOptions.map((item) => item.value)),
  gender_c: yup.string().oneOf(['male', 'female', 'no_response']).required(REQUIRED),
  preferred_contact_method_c: yup.string().oneOf(['email', 'both', 'text_message']).required(REQUIRED),
  email1: emailSchema.required(REQUIRED),
  referral_type_c: yup.string().optional().oneOf(['self', 'community']), // Note: Even though this is optional on the schema, it is always being set in the api.
  affiliate_id: yup.string().optional(),
  supports_court_order_c: yup.boolean().optional(),
  service_selected_c: yup.string().optional(),
  created_by_name: yup.string().optional(),
  account_name: yup.string().optional(),
  assigned_user_name: yup.string().optional(),
  converted: yup.string().optional().oneOf(['0', '1']),
};

export const selfReferralSchema = yup.object().shape(selfReferralApiFieldSchemas);

export type SelfReferralSchema = yup.InferType<typeof selfReferralSchema>;
