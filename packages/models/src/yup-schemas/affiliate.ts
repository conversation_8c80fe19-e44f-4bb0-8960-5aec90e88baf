import * as yup from 'yup';

export const affiliateSchema = yup.object().shape({
  id: yup.string().optional(),
  name: yup.string().required(),
  description: yup.string().optional(),
  billing_address_street: yup.string().optional(),
  billing_address_street_2: yup.string().optional(),
  billing_address_street_3: yup.string().optional(),
  billing_address_street_4: yup.string().optional(),
  billing_address_city: yup.string().optional(),
  billing_address_state: yup.string().optional(),
  billing_address_postalcode: yup.string().optional(),
  billing_address_country: yup.string().optional(),
  phone_office: yup.string().optional(),
  website: yup.string().optional(),
  email1: yup.string().optional(),
  affiliateAgencies: yup.array().of(
    yup.object().shape({
      id: yup.string().optional(),
      affiliate_id: yup.string().optional(),
      agency_id: yup.string().optional(),
      agency: yup
        .object()
        .shape({
          id: yup.string().optional(),
          name: yup.string().optional(),
          agency_name: yup.string().optional(),
        })
        .optional(),
    }),
  ),
  agency_id: yup
    .array()
    .of(
      yup.object().shape({
        label: yup.string().required(),
        value: yup.string().required(),
      }),
    )
    .nullable()
    .optional(),
});

export const affiliateMinimalSchema = affiliateSchema.pick([
  'id',
  'name',
  'billing_address_city',
  'billing_address_state',
]);

export type Affiliate = yup.InferType<typeof affiliateSchema>;
export type AffiliateMinimal = yup.InferType<typeof affiliateMinimalSchema>;
