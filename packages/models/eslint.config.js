import parserTs from '@typescript-eslint/parser';
import tseslint from 'typescript-eslint';
import tsPlugin from '@typescript-eslint/eslint-plugin';
import pluginJs from '@eslint/js';
// import prettier from 'eslint-config-prettier'; // Uncomment if you have the flat config version

export default [
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
  // prettier, // Uncomment if you have the flat config version
  {
    files: ['src/**/*.ts'],
    languageOptions: {
      parser: parserTs,
      parserOptions: {
        project: './tsconfig.json',
        tsconfigRootDir: process.cwd(),
        sourceType: 'module',
      },
    },
    plugins: { '@typescript-eslint': tsPlugin },
    rules: {
      '@typescript-eslint/interface-name-prefix': 'off',
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-explicit-any': 'error',
      '@typescript-eslint/no-unused-vars': [
        'warn',
        {
          ignoreRestSiblings: true,
          varsIgnorePattern: '^_',
          argsIgnorePattern: '^_',
          destructuredArrayIgnorePattern: '^_',
          caughtErrorsIgnorePattern: '^_?error$',
        },
      ],
    },
  },
  {
    ignores: ['.eslintrc.js', '**/dist/**', '**/node_modules/**', '.turbo/**'],
  },
];
