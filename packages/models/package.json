{"name": "models", "version": "1.0.0", "description": "", "source": "src/index.ts", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "directories": {"dist": "dist"}, "files": ["dist"], "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts --clean", "build:watch": "npm run build -- --watch src", "lint": "eslint --fix .", "lint-ci": "eslint .", "prettier": "prettier --check src/**/*.ts", "prettier:fix": "prettier --write src/**/*.ts", "test": "echo \"Error: no test specified\" && exit 1", "clean": "rm -rf dist && rm -rf .turbo", "nuke": "pnpm run clean && rm -rf node_modules"}, "keywords": [], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "ics": "^3.8.1", "yup": "^1.4.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^26.0.3", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^11.1.6", "@stylistic/eslint-plugin": "^2.10.1", "@types/node": "^22.9.0", "@typescript-eslint/eslint-plugin": "^8.14.0", "@typescript-eslint/parser": "^8.14.0", "eslint-config-prettier": "^9.1.0", "prettier": "^3.3.3", "tsup": "^8.3.6", "tslib": "^2.8.1", "typescript": "~5.0.4", "typescript-eslint": "^8.14.0"}}