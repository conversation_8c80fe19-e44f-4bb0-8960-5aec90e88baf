import { Logger, LogLevel } from './logger';

export type HttpClientOptions = { baseUrl?: URL | string; parseResponse?: boolean } & Partial<RequestInit>;
export type HttpClientInit = Partial<Pick<HttpClientOptions, 'headers' | 'baseUrl'>>;

export class HttpClient {
  private _baseUrl: string;

  private _headers: HeadersInit = {} as HeadersInit;

  constructor(options: HttpClientInit = {}) {
    this._baseUrl = options.baseUrl?.toString() || '';
    this._headers = options.headers || {};
  }

  protected _getHeaders(): HttpClientOptions {
    return {};
  }

  protected _combineURLSegments(segment1: string, segment2: string): string {
    const separator = '/';
    const trimmedSegment1 = segment1.trim().replace(new RegExp(`${separator}$`), '');
    const trimmedSegment2 = segment2.trim().replace(new RegExp(`^${separator}`), '');
    return trimmedSegment1 + separator + trimmedSegment2;
  }

  protected async _fetchJSON<T>(endpoint: string, options?: HttpClientOptions): Promise<T> {
    const url = this._combineURLSegments(this._baseUrl, endpoint);
    options = options ?? this._getHeaders();
    Logger.log(LogLevel.DEBUG, `fetching ${url}`, options);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { baseUrl, parseResponse, headers, ...otherOptions } = options;
    const init = {
      ...otherOptions,
      headers: { ...this._headers, ...headers },
      cache: 'no-store',
    };
    let res: Response | null = null;
    try {
      res = await fetch(url, init as RequestInit);
      if (!res.ok) {
        throw new Error(res.statusText);
      }

      if (parseResponse !== false && res.status !== 204) {
        const text = await res.text();
        try {
          const returnVal = JSON.parse(text) as T;
          Logger.log(LogLevel.DEBUG, returnVal);
          return returnVal;
        } catch (error) {
          Logger.log(LogLevel.WARN, error, { failedToParse: true, body: text });
          throw error;
        }
      }

      return res as T;
    } catch (error) {
      Logger.log(LogLevel.ERROR, error);
      if (!!res && !res.bodyUsed) {
        Logger.log(LogLevel.ERROR, error, { status: res.status, statusText: res.statusText, body: await res.text() });
      }
      throw error;
    }
  }

  public setHeader(key: keyof HeadersInit, value: string) {
    this._headers[key] = value;
    return this;
  }

  public async get<TRes>(endpoint: string, options: HttpClientOptions = this._getHeaders()) {
    return this._fetchJSON<TRes>(endpoint, {
      ...options,
      method: 'GET',
    });
  }

  public async post<T, TRes>(endpoint: string, body: T, options: HttpClientOptions = this._getHeaders()) {
    return this._fetchJSON<TRes>(endpoint, {
      ...options,
      body: body instanceof URLSearchParams ? body : JSON.stringify(body),
      method: 'POST',
    });
  }

  public async patch<T, TRes>(endpoint: string, body: T, options: HttpClientOptions = this._getHeaders()) {
    return this._fetchJSON<TRes>(endpoint, {
      ...options,
      body: JSON.stringify(body),
      method: 'PATCH',
    });
  }

  public async put<T, TRes>(endpoint: string, body: T, options: HttpClientOptions = this._getHeaders()) {
    return this._fetchJSON<TRes>(endpoint, {
      ...options,
      body: JSON.stringify(body),
      method: 'PUT',
    });
  }

  public async delete<TRes>(endpoint: string, options: HttpClientOptions = this._getHeaders()) {
    return this._fetchJSON<TRes>(endpoint, {
      parseResponse: false,
      ...options,
      method: 'DELETE',
    });
  }
}
