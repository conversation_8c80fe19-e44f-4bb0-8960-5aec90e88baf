export enum LogLevel {
  FATAL = 0x1,
  ERROR = 0x2,
  WARN = 0x4,
  INFO = 0x8,
  DEBUG = 0x10,
}

export class Logger {
  public static log(level: LogLevel, ...args: unknown[]) {
    if (parseInt(process.env.LOG_LEVEL || '0') >= level) {
      switch (level) {
        case LogLevel.FATAL:
          console.error(`[${LogLevel[level]}]`, ...args);
          break;
        case LogLevel.ERROR:
          console.error(`[${LogLevel[level]}]`, ...args);
          break;
        case LogLevel.DEBUG:
          console.debug(`[${LogLevel[level]}]`, ...args);
          break;
        case LogLevel.INFO:
          console.info(`[${LogLevel[level]}]`, ...args);
          break;
        case LogLevel.WARN:
          console.warn(`[${LogLevel[level]}]`, ...args);
          break;
      }
    }
  }
}
