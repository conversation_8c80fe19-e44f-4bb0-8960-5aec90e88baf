{"compilerOptions": {"target": "es2020", "module": "esnext", "moduleDetection": "auto", "moduleResolution": "bundler", "lib": ["es2017", "es2022", "DOM", "DOM.Iterable"], "declaration": true, "outDir": "./dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "rootDir": ".", "baseUrl": ".", "importHelpers": true, "sourceMap": true, "types": ["node"]}, "include": ["src/**/*.ts", "tests/**/*.ts", "**/*.ts", "**/*.d.ts", "tests/test-api.js"], "exclude": ["dist", "node_modules"]}