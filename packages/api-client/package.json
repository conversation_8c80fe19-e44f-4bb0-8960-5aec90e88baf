{"name": "api-client", "version": "1.0.0", "description": "", "type": "module", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "types": "dist/src/index.d.ts", "directories": {"dist": "dist"}, "files": ["dist"], "scripts": {"watch": "rollup -c -w", "build": "rollup -c", "lint-ci": "eslint .", "lint": "eslint . --fix", "clean": "rm -rf dist && rm -rf .turbo", "nuke": "pnpm run clean && rm -rf node_modules"}, "keywords": [], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@suiteapi/models": "workspace:models@^", "ts-md5": "^1.3.1"}, "devDependencies": {"@eslint/js": "^9.14.0", "@rollup/plugin-commonjs": "^26.0.3", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^11.1.6", "@stylistic/eslint-plugin-ts": "^2.10.1", "@types/eslint__js": "^8.42.3", "@types/jest": "^29.5.14", "@types/node": "^22.9.0", "@typescript-eslint/eslint-plugin": "^8.14.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "jest": "^29.7.0", "prettier": "^3.3.3", "rollup": "^4.25.0", "ts-jest": "^29.2.5", "tsconfig-paths": "^4.2.0", "tslib": "^2.8.1", "typescript": "^5.6.3", "typescript-eslint": "^8.14.0"}}