import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import typescript from '@rollup/plugin-typescript';
import terser from '@rollup/plugin-terser';
import json from '@rollup/plugin-json';
import { readFileSync } from 'fs';

const pkg = JSON.parse(readFileSync('./package.json', 'utf-8'));
export default {
  input: 'src/index.ts',
  output: [
    {
      file: pkg.main, // CommonJS output file (from package.json)
      format: 'cjs',
      sourcemap: true,
    },
    {
      file: pkg.module, // ES module output file (from package.json)
      format: 'esm',
      sourcemap: true,
    },
  ],
  external: Object.keys(pkg.dependencies || {}),
  plugins: [resolve({ preferBuiltins: true }), commonjs(), typescript(), terser(), json()],
};
