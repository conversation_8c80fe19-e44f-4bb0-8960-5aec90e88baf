import stylisticTs from '@stylistic/eslint-plugin-ts';
import parserTs from '@typescript-eslint/parser';
import { FlatCompat } from '@eslint/eslintrc';
import path from 'path';
import { fileURLToPath } from 'url';

// mimic CommonJS variables -- not needed if using CommonJS
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: {},
});

const config = [
  ...compat.extends(
    '../../.eslintrc',
    'eslint:recommended',
    'plugin:@typescript-eslint/eslint-recommended',
    'plugin:@typescript-eslint/recommended',
    'prettier',
  ),
  //stylisticTs.configs["disable-legacy"],
  {
    plugins: {
      '@stylistic/ts': stylisticTs,
    },
    languageOptions: {
      parser: parserTs,
    },
    rules: {
      '@stylistic/ts/lines-between-class-members': 'error',
    },
  },
  { files: ['src/**/*.{js,mjs,cjs,ts}'] },
  { ignores: ['dist/*', 'node_modules/*'] },
];

export default config;
