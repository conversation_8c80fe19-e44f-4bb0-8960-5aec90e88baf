#!/bin/bash

if [ -z "$1" ]; then
  echo "Error: Environment name is required."
  exit 1
fi

if [[ ! "$1" =~ ^[a-z0-9-]+$ ]]; then
  echo "Error: Environment name must contain only lowercase letters, numbers, and hyphens."
  exit 1
fi

# Define the .env file path
env_file="apps/api/.env"

# Link to the Railway environment
railway link -t Servant -p EMA -e development -s ema-postgres

# Create the new environment
railway env new -d development $1

# Find the new value for DATABASE_URL
new_db_url=$(railway variables -e $1 -s ema-postgres --json | jq -r '.DATABASE_PUBLIC_URL')

# Link to the new Railway environment
railway link -t Servant -p EMA -e $1 -s ema-postgres

# Use sed to replace the DATABASE_URL value
sed -i '' -E "s|^DATABASE_URL=.*|DATABASE_URL=\"$new_db_url\"|" "$env_file"

# Print confirmation
echo "DATABASE_URL has been updated!"
