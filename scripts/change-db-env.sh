#!/bin/bash

# Define the .env file path
env_file="apps/api/.env"
notification_processor_env_file="apps/notification-processor/.env"

# Link to the Railway environment
railway link -t Servant -p EMA -e development -s ema-postgres

# Find the new value for DATABASE_URL
new_db_url=$(railway variables -e $1 -s ema-postgres --json | jq -r '.DATABASE_PUBLIC_URL')

# Use sed to replace the DATABASE_URL value
sed -i '' -E "s|^DATABASE_URL=.*|DATABASE_URL=\"$new_db_url\"|" "$env_file"

# Use sed to replace the DATABASE_URL value
sed -i '' -E "s|^DATABASE_URL=.*|DATABASE_URL=\"$new_db_url\"|" "$notification_processor_env_file"

# Print confirmation
echo "DATABASE_URLs have been updated!"
