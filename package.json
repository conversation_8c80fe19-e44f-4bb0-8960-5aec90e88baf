{"name": "ema", "version": "1.0.0", "description": "", "type": "module", "main": "index.js", "scripts": {"build": "turbo build", "start:api:remote": "pnpm run --filter api start:prod", "start:portal:remote": "pnpm run --filter portal start", "build:api": "turbo build --filter api", "build:portal": "turbo build --filter portal", "build:models": "turbo build --filter models", "create:prisma:migration": "pnpm run --filter api create:prisma:migration ", "deploy:prisma:migrations": "pnpm run --filter api deploy:prisma:migrations", "zenstack:generate": "pnpm run --filter api zenstack:generate", "zenstack:format": "pnpm run --filter api zenstack:format", "prisma:db:push": "pnpm run --filter api prisma:db:push", "dev": "turbo dev", "test": "echo \"Error: no test specified\" && exit 1", "clean": "turbo clean && rm -rf .turbo", "nuke": "turbo nuke && rm -rf node_modules && rm -rf .turbo", "lint": "eslint \"packages/**/src/**/*.{ts,tsx}\" \"apps/**/src/**/*.{ts,tsx}\" --fix", "lint-ci": "eslint \"packages/**/src/**/*.{ts,tsx}\" \"apps/**/src/**/*.{ts,tsx}\"", "rw:login": "railway login", "rw:link:env": "railway link -t Servant -p EMA -s ema-postgres -e ", "rw:logs:api": "railway logs -s ema-api --build", "rw:logs:portal": "railway logs -s ema-portal --build", "rw:logs:db": "railway logs -s ema-postgres --build", "rw:logs:api:deploy": "railway logs -s ema-api", "rw:logs:portal:deploy": "railway logs -s ema-portal", "rw:logs:db:deploy": "railway logs -s ema-postgres", "rw:redeploy:db": "railway redeploy -s ema-postgres", "rw:redeploy:api": "railway redeploy -s ema-api", "rw:redeploy:portal": "railway redeploy -s ema-portal", "rw:new-env": "bash scripts/new-infra-env.sh ", "rw:change-db-env": "bash scripts/change-db-env.sh "}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@9.10.0", "devDependencies": {"eslint": "^8.41.0", "prettier": "^3.3.3", "turbo": "^2.1.2", "typescript": "^5.5.4"}}