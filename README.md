# Every Mother's Advocate (EMA)

Welcome to the Every Mother's Advocate (EMA) project! This project aims to provide a customized portal experience connecting advocates, coordinators and moms to the resources and people they need to support children well.

## Table of Contents

- [Getting Started](#getting-started)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Running the Project](#running-the-project)
- [Contributing](#contributing)

## Getting Started

These instructions will help you set up the project on your local machine for development and testing purposes.

## Prerequisites

Before you begin, ensure you have the following installed on your machine:

- [Node.js](https://nodejs.org/) version 20.x
- [pnpm](https://pnpm.io/) (version 6.x or later)
- [Git](https://git-scm.com/)

Tip: If using `nvm` to manage your Node.js version, this project has an `.nvmrc` file, which sets the Node.js version at 20.x when running the following command in the project directory:

```sh
nvm use
```

## Installation

1. **Clone the repository:**

```sh
git clone https://github.com/servant-io/ema.git
cd ema
```

2. **Install dependencies:**

Install [NVM](https://github.com/nvm-sh/nvm) if you don't have it, then switch to use the project specific verson.

```sh
nvm use
```

You may have to install the projects version if it's not already included. Once you've done that you can move on to project specific installs:

```sh
npm install -g turbo@latest
npm install -g pnpm
npm install -g @nestjs/cli
npm install -g @railway/cli
pnpm install
pnpm run build
```

This is not required but we recommend using the following VSCode/Cursor extensions:

- [ZenStack](https://marketplace.visualstudio.com/items?itemName=zenstack.zenstack)
- [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)

3. **Setup Environment Variables**

- From the EMA Vault in 1Password, open the '.env for api project' note, copy the contents and create a new `.env` file in the `apps/api` directory.
- From the EMA Vault in 1Password, open the '.env.local for portal project' note, copy the contents and create a new `.env.local` file in the `apps/portal` directory.

4. **Running the Project**

To start the development server, run:

```sh
pnpm dev
```

This will start the project in development mode. Open your browser and navigate to http://localhost:3000 to see the application running.

## Running Tests

To run the tests, use the following command:

```sh
pnpm test
```

## Linting and Formatting

To lint the codebase, run:

```sh
pnpm lint
```

# Onboarding

Please feel free to watch these onboarding videos for an overview of the project:

https://www.loom.com/share/573a7028e5f447f48a524b27c969d533?sid=1188e2ce-e1ec-4ed2-8fc0-b5d82ccb6412

https://www.loom.com/share/ee2ab0cbe5d8409e8fae1b010928bf7f?sid=29af6127-81b4-46b4-b5e0-2207d3a12c25

# Contributing

We welcome contributions to the EMA project! To contribute, follow these steps:

https://miro.com/app/board/uXjVLnsIxP4=/?share_link_id=418982215194

Please ensure your code adheres to the project's coding standards and passes all tests.

# Noteable Links

- [Notion Project Page](https://www.notion.so/servant-io/MA-Home-bf4c084634704e61a9d0bdfcc29ad080?pvs=4)
- [Recommended Developer Workflow](https://miro.com/app/board/uXjVLnsIxP4=/?share_link_id=418982215194)
- [Slightly Outdated Architecture Diagram](https://miro.com/app/board/uXjVLqfCUFk=/?share_link_id=969908816326)
- [Legacy End-to-End Experience Diagrams](https://miro.com/app/board/uXjVLDLD2e0=/?share_link_id=719669490214)
- [Legacy Data Map](https://miro.com/app/board/uXjVKk1-bnk=/?share_link_id=************)
- [Figma Designs](https://www.figma.com/design/MnCxmDknmPl08NDxK1MwED/Current-Designs-%2F-Wireframes?node-id=157-9751&m=dev)

- [ZenStack](https://zenstack.dev/docs/welcome)
- [NestJS Docs](https://docs.nestjs.com/)
- [NextJS Docs](https://nextjs.org/docs/14)
- [NestJS CLI Docs](https://docs.nestjs.com/cli/overview)

  - **Please use the NestJS CLI to generate new controllers, services, etc. when working in the `apps/api` directory.**

- [Railway CLI Docs](https://docs.railway.com/guides/cli)
- [Infrastructure - Railway](https://railway.com/)

  - Be sure to sign up with the SAME GitHub account as the one that you use to access this repo.

- [Ema Portal Dev](https://ema-portal-development.up.railway.app/)
- [Ema Portal Staging](https://ema-portal-staging.up.railway.app/)

# Projects

## Api

The `api` application is the NestJS project which hosts the API.

### Accessing `PrismaClient`s in the `api` project.

There are 4 ways to obtain a `PrismaClient` within the api project....in order of preference they are:

1. Inject an `ENHANCED_PRISMA` decorated version of the `PrismaClient`. Can be seen here: https://github.com/servant-io/ema/blob/main/apps/api/src/search/search.service.ts This gives you a PrismaClient with all of the Zenstack authorization, automatic delete filtering, etc. goodness plus our custom extensions to the `PrismaClient`.
1. Inject the `ExtendedPrismaClientFactoryService` and use its `createExtendedPrismaClient` method. Example can be found here: https://github.com/servant-io/ema/blob/main/apps/api/src/users/users.service.ts This gives you a `PrismaClient` that bypasses the Zenstack authorization but still contains our custom extensions to the `PrismaClient` (like filling the created_by fields automagically and doing soft deletes instead of hard deletes).
1. Inject an undecorated version of the `PrismaClient`. I don't have an example of this. Generally speaking you shouldn't need to do it. I'm not saying we'll never need to, but it should be pretty uncommon and commented on specifically if necessary.
1. `new`-ing up a `PrismaClient` on the fly. While technically an option, you almost never actually want/need to do this. You're way out in edge-case land if you do.

Note: Seed files are actually an exception to this rule. b/c of when and the way that they're executed your best/easiest option is to just new up a `PrismaClient` per seed file.

## Portal

The `portal` application is the NextJS project which hosts the portal experience.

### Components

We'll be using (shadcn)[https://ui.shadcn.com/docs] as the source for components with tailwind for styling.

To add a new component from shadcn, navigate to the `apps/portal` directory and run

```
pnpm dlx shadcn-ui@latest add button
```

There is a `FormFactory` component for form generation that is built on top of the shadcn `Form` component and the react-hook-form library. See `docs/form-factory.md` for detailed documentation and examples.

# Shared Packages

## Models

The `models` library houses the data models and type definitions that are shared between the API and the portal.

# Deployment

## Deployment Process

The deployment process follows a simple workflow from development to production through staging.

### Staging Deployment

1. Create a Pull Request from `main` to `staging`. Like [this](https://github.com/servant-io/ema/pull/442) one.
2. Get the PR reviewed and approved
3. Merge the PR using a regular merge (do not squash)
4. Verify the `staging` environment deployment was successful [here](https://railway.com/project/************************************?environmentId=49d424e2-2f93-4c66-b520-a8738d82d45a)
5. Perform smoke testing on the Staging environment [here](https://ema-portal-staging.up.railway.app)

### Production Deployment

1. Create a Pull Request from `staging` to `prod`. Like [this](https://github.com/servant-io/ema/pull/444) one.
2. Get the PR reviewed and approved
3. Merge the PR using a regular merge (do not squash)
4. Verify the `production` environment deployment was successful [here](https://railway.com/project/************************************?environmentId=19f6574b-a7c6-41f2-8caf-b05d82b70474)
5. Perform smoke testing on the Production environment [here](https://ema-portal-production.up.railway.app/)

Note: Always use regular merges (not squash merges) to maintain the commit history, make it easier to track changes between environments and enable reverse-integrations if ever needed.

# Noteworthy Commands

`pnpm run zenstack:generate`
Generates the prisma schema, client, openapi schema and client React Query hooks for api access.

`pnpm run create:prisma:migration [MigrationNameNoBracketsNoSpacesPascalCase]`
Creates a new prisma migration file without applying it to the database.
Migration is based on the database that you're currently targeting in your /apps/api/.env file.

`pnpm run deploy:prisma:migrations`
Applies migrations to the database that you're currently targeting in your /apps/api/.env file.
DO NOT EVER RUN THIS COMMAND WHEN POINTING TO DEVELOPMENT, STAGING OR PRODUCTION.

`pnpm run rw:login --browserless`
Allows you to login to Railway. In order to do so, run this command and follow the instructions.

`pnpm run rw:link:env [environment-name]`
Links your Railway CLI to the Railway environment that you specify.

`pnpm run rw:logs:api`
Access the api build logs for your currently linked Railway environment.

`pnpm run rw:logs:portal`
Access the portal build logs for your currently linked Railway environment.

`pnpm run rw:logs:db`
Access the database build logs for your currently linked Railway environment.

`pnpm run rw:logs:api:deploy`
Access the api deployment logs for your currently linked Railway environment.

`pnpm run rw:logs:portal:deploy`
Access the portal deployment logs for your currently linked Railway environment.

`pnpm run rw:logs:db:deploy`
Access the database deployment logs for your currently linked Railway environment.

`pnpm run rw:redeploy:db`
Redeploy the database service for your currently linked Railway environment.

`pnpm run rw:redeploy:api`
Redeploy the api service for your currently linked Railway environment.

`pnpm run rw:redeploy:portal`
Redeploy the portal service for your currently linked Railway environment.

`pnpm run rw:new-env [environment-name]`
Create a new Railway environment, link your Railway CLI to it and update your api .env file's DATABASE_URL to point to the new environment's database..

- Note: This command changes your linked environment to the new environment that you specify.
- Note: Environment names must be all lowercase lettters, numbers and hyphens

`pnpm run rw:change-db-env [environment-name]`
Update your api .env file's DATABASE_URL to point to the environment's database.

- Note: This command changes your linked environment to the environment that you specify.
