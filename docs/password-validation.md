# Password Validation System

This document describes the comprehensive password validation system implemented across the EMA application.

## Password Requirements

All passwords in the system must meet the following security requirements:

- **Minimum Length**: At least 8 characters
- **Uppercase Letters**: At least one uppercase letter (A-Z)
- **Lowercase Letters**: At least one lowercase letter (a-z)
- **Numbers**: At least one number (0-9)
- **Special Characters**: At least one special character from: `!@#$%^&*()_+-=[]{};\':"|,.<>/?`

## Implementation

### Frontend Validation

#### Yup Schema
The password validation is implemented using Yup schemas that can be imported and used in forms:

```typescript
import { YupSchemas } from '@suiteapi/models';

const formSchema = yup.object().shape({
  password: YupSchemas.passwordSchema,
  confirmPassword: yup
    .string()
    .required('Confirm password is required')
    .oneOf([yup.ref('password')], 'Passwords must match'),
});
```

#### Password Strength Indicator Component
A React component is available to show real-time password strength feedback:

```typescript
import PasswordStrengthIndicator from '@/components/ui/password-strength-indicator';

function MyForm() {
  const [password, setPassword] = useState('');
  
  return (
    <div>
      <input 
        type="password" 
        value={password} 
        onChange={(e) => setPassword(e.target.value)} 
      />
      <PasswordStrengthIndicator 
        password={password} 
        showRequirements={true} 
      />
    </div>
  );
}
```

#### Validation Utilities
For custom validation logic, you can use the validation utilities directly:

```typescript
import { validatePassword } from '@suiteapi/models';

const result = validatePassword('MyPassword123!');
console.log(result.isValid); // true
console.log(result.strength); // 'strong'
console.log(result.score); // 85
console.log(result.errors); // []
```

### Backend Validation

#### Password Validation Service
The backend includes a dedicated service for password validation:

```typescript
import { PasswordValidationService } from './password-validation.service';

@Injectable()
export class MyService {
  constructor(private passwordValidationService: PasswordValidationService) {}
  
  async createUser(userData: any) {
    // This will throw BadRequestException if password is invalid
    this.passwordValidationService.validatePasswordOrThrow(userData.password);
    
    // Continue with user creation...
  }
}
```

#### Auth Service Integration
Password validation is automatically applied in:

- User registration (`AuthService.createUser()`)
- Password changes (`AuthService.changePassword()`)
- Password resets (via Yup schema validation)

## Updated Forms

The following forms have been updated to use the new password validation:

1. **Change Password Form** (`apps/portal/src/app/login/_lib/change-password-form.config.tsx`)
2. **Reset Password Form** (`apps/portal/src/app/login/_lib/reset-password-form-fields.tsx`)
3. **Authentication Schemas** (`packages/models/src/yup-schemas/authentication.ts`)

## Error Handling

### Frontend
When validation fails, the Yup schema will provide specific error messages for each requirement that isn't met:

```typescript
// Example validation errors:
[
  "Password must be at least 8 characters long",
  "Password must contain at least one uppercase letter",
  "Password must contain at least one number"
]
```

### Backend
The backend service throws `BadRequestException` with detailed error information:

```typescript
{
  "message": "Password does not meet security requirements",
  "errors": [
    "Password must be at least 8 characters long",
    "Password must contain at least one special character"
  ],
  "requirements": [
    "At least 8 characters long",
    "At least one uppercase letter (A-Z)",
    "At least one lowercase letter (a-z)",
    "At least one number (0-9)",
    "At least one special character (!@#$%^&*()_+-=[]{};\':\"\\|,.<>/?)"
  ]
}
```

## Testing

Comprehensive tests are available in `packages/models/src/utils/__tests__/password-validation.test.ts`.

Run tests with:
```bash
npm test password-validation
```

## Security Considerations

1. **Client-side validation is for UX only** - All critical validation happens on the server
2. **Password hashing** - Passwords are hashed using bcrypt before storage
3. **Strength scoring** - The system provides a 0-100 strength score for password quality feedback
4. **Pattern detection** - Common weak patterns (repeated characters, sequences) are penalized

## Customization

To modify password requirements, update the constants in `packages/models/src/yup-schemas/common-fields.ts`:

```typescript
const PASSWORD_MIN_LENGTH = 8; // Change minimum length
const PASSWORD_SPECIAL_CHAR_REGEX = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/; // Modify allowed special characters
```

## Migration Notes

- Existing users are not forced to update their passwords immediately
- New password requirements only apply to new passwords and password changes
- The `mustChangePassword` flag can be used to force password updates for specific users
