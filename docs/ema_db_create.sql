-- This script was generated by the ERD tool in pgAdmin 4.
-- Please log an issue at https://github.com/pgadmin-org/pgadmin4/issues/new/choose if you find any bugs, including reproduction steps.
-- Should we ever need a manual create script, this one is a good start.
-- This script is generated by pgAdmin 4. Please do not modify it directly.
-- As of 4.3.25 this is up to date
BEGIN;


CREATE TABLE IF NOT EXISTS public."AAPIScore"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    updated_at timestamp(3) without time zone NOT NULL,
    mom_id text COLLATE pg_catalog."default" NOT NULL,
    "constructAPreAssessment" double precision,
    "constructAPostAssessment" double precision,
    "constructBPreAssessment" double precision,
    "constructBPostAssessment" double precision,
    "constructCPreAssessment" double precision,
    "constructCPostAssessment" double precision,
    "constructDPreAssessment" double precision,
    "constructDPostAssessment" double precision,
    "constructEPreAssessment" double precision,
    "constructEPostAssessment" double precision,
    deleted_at bigint NOT NULL DEFAULT 0,
    CONSTRAINT "AAPIScore_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."ActionItem"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    name text COLLATE pg_catalog."default" NOT NULL,
    description text COLLATE pg_catalog."default",
    "dueDate" timestamp(3) without time zone,
    "goalId" text COLLATE pg_catalog."default" NOT NULL,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    "doneDate" timestamp(3) without time zone,
    deleted_at bigint NOT NULL DEFAULT 0,
    CONSTRAINT "ActionItem_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."Affiliate"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    name text COLLATE pg_catalog."default" NOT NULL,
    description text COLLATE pg_catalog."default",
    billing_address_street text COLLATE pg_catalog."default",
    billing_address_street_2 text COLLATE pg_catalog."default",
    billing_address_street_3 text COLLATE pg_catalog."default",
    billing_address_street_4 text COLLATE pg_catalog."default",
    billing_address_city text COLLATE pg_catalog."default",
    billing_address_state text COLLATE pg_catalog."default",
    billing_address_postalcode text COLLATE pg_catalog."default",
    billing_address_country text COLLATE pg_catalog."default",
    phone_office text COLLATE pg_catalog."default",
    website text COLLATE pg_catalog."default",
    email1 text COLLATE pg_catalog."default",
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    deleted_at bigint NOT NULL DEFAULT 0,
    agency_nickname text COLLATE pg_catalog."default",
    contact_name text COLLATE pg_catalog."default",
    status "AffiliateStatusType",
    CONSTRAINT "Affiliate_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."AffiliateAgency"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    deleted_at bigint NOT NULL DEFAULT 0,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    updated_at timestamp(3) without time zone NOT NULL,
    affiliate_id text COLLATE pg_catalog."default" NOT NULL,
    agency_id text COLLATE pg_catalog."default" NOT NULL,
    CONSTRAINT "AffiliateAgency_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."Agency"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    name text COLLATE pg_catalog."default",
    agency_name text COLLATE pg_catalog."default" NOT NULL,
    agency_phone text COLLATE pg_catalog."default",
    address text COLLATE pg_catalog."default",
    address_city text COLLATE pg_catalog."default",
    address_state text COLLATE pg_catalog."default",
    address_postalcode text COLLATE pg_catalog."default",
    contact_first_name text COLLATE pg_catalog."default" NOT NULL,
    contact_last_name text COLLATE pg_catalog."default" NOT NULL,
    contact_email text COLLATE pg_catalog."default",
    contact_phone text COLLATE pg_catalog."default",
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    deleted_at bigint NOT NULL DEFAULT 0,
    CONSTRAINT "Agency_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."BenevolenceNeed"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    name text COLLATE pg_catalog."default" NOT NULL,
    description text COLLATE pg_catalog."default",
    "momId" text COLLATE pg_catalog."default" NOT NULL,
    amount_c double precision,
    provided_date_c timestamp(3) without time zone,
    did_address_need_c boolean NOT NULL DEFAULT false,
    financial_amount_gifted_c double precision,
    financial_amount_requested_c double precision,
    financial_mom_contribution_c double precision,
    financial_prevention_plan_c text COLLATE pg_catalog."default",
    is_urgent_c boolean NOT NULL DEFAULT false,
    non_addressal_comment_c text COLLATE pg_catalog."default",
    notes_c text COLLATE pg_catalog."default",
    other_is_referral_needed_c boolean NOT NULL DEFAULT false,
    pg_fulfillment_method_c text COLLATE pg_catalog."default",
    physical_good_monetary_value_c double precision,
    resolved_date_c timestamp(3) without time zone,
    "resolvedByUserId" text COLLATE pg_catalog."default",
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    type_c "BenevolenceNeedType" NOT NULL DEFAULT 'Financial'::"BenevolenceNeedType",
    deleted_at bigint NOT NULL DEFAULT 0,
    CONSTRAINT "BenevolenceNeed_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."ConnectionLog"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    summary_c text COLLATE pg_catalog."default" NOT NULL,
    is_visible_to_advocates_c boolean NOT NULL,
    date_created_c timestamp(3) without time zone NOT NULL,
    name text COLLATE pg_catalog."default",
    mom_id text COLLATE pg_catalog."default",
    user_id text COLLATE pg_catalog."default",
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    contact_method_c "ContactMethodType" NOT NULL,
    deleted_at bigint NOT NULL DEFAULT 0,
    CONSTRAINT "ConnectionLog_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."CoordinatorNote"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    name text COLLATE pg_catalog."default",
    description text COLLATE pg_catalog."default" NOT NULL,
    mom_id text COLLATE pg_catalog."default",
    "isVisibleToAdvocates" boolean NOT NULL DEFAULT true,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    type_c "CoordinatorNoteType" NOT NULL,
    advocate_id text COLLATE pg_catalog."default",
    coordinator_id text COLLATE pg_catalog."default",
    deleted_at bigint NOT NULL DEFAULT 0,
    CONSTRAINT "CoordinatorNote_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."Document"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    document_name text COLLATE pg_catalog."default",
    filecontents text COLLATE pg_catalog."default",
    filename text COLLATE pg_catalog."default",
    "mimeType" text COLLATE pg_catalog."default",
    description text COLLATE pg_catalog."default",
    external_url_c text COLLATE pg_catalog."default",
    subcategory_id text COLLATE pg_catalog."default",
    mom_id text COLLATE pg_catalog."default",
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    advocate_id text COLLATE pg_catalog."default",
    coordinator_id text COLLATE pg_catalog."default",
    lesson_id text COLLATE pg_catalog."default",
    lesson_template_id text COLLATE pg_catalog."default",
    deleted_at bigint NOT NULL DEFAULT 0,
    is_primary_lesson_resource boolean NOT NULL DEFAULT false,
    s3_file_name text COLLATE pg_catalog."default",
    CONSTRAINT "Document_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."DocumentTag"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    deleted_at bigint NOT NULL DEFAULT 0,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    updated_at timestamp(3) without time zone NOT NULL,
    document_id text COLLATE pg_catalog."default" NOT NULL,
    tag_id text COLLATE pg_catalog."default" NOT NULL,
    CONSTRAINT "DocumentTag_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."Event"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    updated_at timestamp(3) without time zone NOT NULL,
    event_title text COLLATE pg_catalog."default" NOT NULL,
    description text COLLATE pg_catalog."default",
    start_date timestamp(3) without time zone,
    end_date timestamp(3) without time zone,
    location text COLLATE pg_catalog."default",
    join_url text COLLATE pg_catalog."default",
    deleted_at bigint NOT NULL DEFAULT 0,
    max_attendees integer,
    CONSTRAINT "Event_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."EventRespondent"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    updated_at timestamp(3) without time zone NOT NULL,
    event_id text COLLATE pg_catalog."default" NOT NULL,
    user_id text COLLATE pg_catalog."default" NOT NULL,
    "hasBeenInvited" boolean NOT NULL DEFAULT false,
    "didRsvp" boolean NOT NULL DEFAULT false,
    "didCheckin" boolean NOT NULL DEFAULT false,
    deleted_at bigint NOT NULL DEFAULT 0,
    address text COLLATE pg_catalog."default",
    "childrenCount" integer NOT NULL DEFAULT 0,
    email text COLLATE pg_catalog."default",
    "needsTransport" boolean NOT NULL DEFAULT false,
    phone_number text COLLATE pg_catalog."default",
    CONSTRAINT "EventRespondent_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."Goal"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    name text COLLATE pg_catalog."default" NOT NULL,
    description text COLLATE pg_catalog."default",
    "dueDate" timestamp(3) without time zone,
    "momId" text COLLATE pg_catalog."default" NOT NULL,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    deleted_at bigint NOT NULL DEFAULT 0,
    "doneDate" timestamp(3) without time zone,
    CONSTRAINT "Goal_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."Lesson"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    description text COLLATE pg_catalog."default",
    duration_days integer,
    "order" integer,
    priority integer,
    status "LessonStatusType",
    title text COLLATE pg_catalog."default" NOT NULL,
    deleted_at bigint NOT NULL DEFAULT 0,
    source_lesson_template_id text COLLATE pg_catalog."default",
    pairing_id text COLLATE pg_catalog."default",
    CONSTRAINT "Lesson_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."LessonTemplate"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    description text COLLATE pg_catalog."default",
    duration_days integer,
    "order" integer,
    priority integer,
    title text COLLATE pg_catalog."default" NOT NULL,
    track_id text COLLATE pg_catalog."default" NOT NULL,
    deleted_at bigint NOT NULL DEFAULT 0,
    CONSTRAINT "LessonTemplate_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."Mom"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    first_name text COLLATE pg_catalog."default",
    last_name text COLLATE pg_catalog."default",
    phone_other text COLLATE pg_catalog."default",
    currently_pregnant_c text COLLATE pg_catalog."default",
    number_of_children_c integer,
    need_details_c text COLLATE pg_catalog."default",
    what_else_c text COLLATE pg_catalog."default",
    caregiver_type_c text COLLATE pg_catalog."default",
    gender_c text COLLATE pg_catalog."default",
    email1 text COLLATE pg_catalog."default",
    referral_type_c text COLLATE pg_catalog."default",
    supports_court_order_c boolean,
    service_selected_c text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    account_name text COLLATE pg_catalog."default",
    affiliate_id text COLLATE pg_catalog."default",
    agency_id text COLLATE pg_catalog."default",
    consent_obtained_c boolean,
    referring_contact_first_name_c text COLLATE pg_catalog."default",
    referring_contact_last_name_c text COLLATE pg_catalog."default",
    referring_contact_email_c text COLLATE pg_catalog."default",
    referring_contact_phone_c text COLLATE pg_catalog."default",
    birthdate timestamp(3) without time zone,
    discharge_reason_c text COLLATE pg_catalog."default",
    closed_date_c timestamp(3) without time zone,
    language_notes_c text COLLATE pg_catalog."default",
    primary_address_street text COLLATE pg_catalog."default",
    primary_address_city text COLLATE pg_catalog."default",
    primary_address_state text COLLATE pg_catalog."default",
    primary_address_postalcode text COLLATE pg_catalog."default",
    primary_address_county_c text COLLATE pg_catalog."default",
    connected_benevolance_c boolean,
    connected_childcare_c boolean,
    connected_closet_c boolean,
    connected_education_c boolean,
    connected_health_c boolean,
    connected_housing_c boolean,
    connected_legal_c boolean,
    connected_mental_health_c boolean,
    connected_substance_c boolean,
    photo text COLLATE pg_catalog."default",
    date_entered text COLLATE pg_catalog."default",
    address_access_c text COLLATE pg_catalog."default",
    assigned_user_id text COLLATE pg_catalog."default",
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    created_by_id text COLLATE pg_catalog."default",
    deleted_at bigint NOT NULL DEFAULT 0,
    prospect_status "ProspectStatus",
    referral_sub_status "ReferralSubStatus",
    referred_to_agency_id text COLLATE pg_catalog."default",
    referred_to_agency_reason text COLLATE pg_catalog."default",
    status "MomStatus",
    sms_message_opt_in boolean,
    preferred_contact_method_c "CommunicationPreferenceType",
    language_preference_c "Language",
    languages_c "Language"[],
    CONSTRAINT "Mom_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."Notification"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    updated_at timestamp(3) without time zone NOT NULL,
    subject text COLLATE pg_catalog."default" NOT NULL,
    content text COLLATE pg_catalog."default",
    type "NotificationType" NOT NULL,
    mom_id text COLLATE pg_catalog."default",
    recipient_user_id text COLLATE pg_catalog."default",
    sending_user_id text COLLATE pg_catalog."default",
    event_id text COLLATE pg_catalog."default",
    status text COLLATE pg_catalog."default",
    date_sent timestamp(3) without time zone,
    date_received timestamp(3) without time zone,
    date_read timestamp(3) without time zone,
    date_replied timestamp(3) without time zone,
    deleted_at bigint NOT NULL DEFAULT 0,
    CONSTRAINT "Notification_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."Pairing"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    name text COLLATE pg_catalog."default" NOT NULL,
    description text COLLATE pg_catalog."default",
    "momId" text COLLATE pg_catalog."default",
    "advocateUserId" text COLLATE pg_catalog."default",
    "trackId" text COLLATE pg_catalog."default",
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    status "PairingStatusType",
    deleted_at bigint NOT NULL DEFAULT 0,
    complete_reason_sub_status "CompleteReasonSubStatusType",
    discharge_incomplete_sub_status "DischargeIncompleteSubStatusType",
    in_program_track_sub_status "InProgramTrackSubStatusType",
    incomplete_reason_sub_status "IncompleteReasonSubStatusType",
    track_status "TrackStatusType",
    CONSTRAINT "Pairing_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."Role"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    name text COLLATE pg_catalog."default" NOT NULL,
    description text COLLATE pg_catalog."default",
    key text COLLATE pg_catalog."default" NOT NULL,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    deleted_at bigint NOT NULL DEFAULT 0,
    CONSTRAINT "Role_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."Session"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    date_start timestamp(3) without time zone,
    date_end timestamp(3) without time zone,
    description text COLLATE pg_catalog."default",
    name text COLLATE pg_catalog."default",
    location text COLLATE pg_catalog."default",
    join_url text COLLATE pg_catalog."default",
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    session_type "SessionType",
    status "SessionStatusType",
    deleted_at bigint NOT NULL DEFAULT 0,
    pairing_id text COLLATE pg_catalog."default",
    session_group_id text COLLATE pg_catalog."default",
    CONSTRAINT "Session_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."SessionGroup"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    deleted_at bigint NOT NULL DEFAULT 0,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    updated_at timestamp(3) without time zone NOT NULL,
    CONSTRAINT "SessionGroup_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."SessionNote"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    attendance_and_promptness "AttendanceAndPromptnessType",
    date_submitted_c timestamp(3) without time zone,
    description text COLLATE pg_catalog."default",
    moms_engagement_c "MomEngagementType",
    name text COLLATE pg_catalog."default" NOT NULL,
    new_attempt boolean,
    new_attempt_example text COLLATE pg_catalog."default",
    note text COLLATE pg_catalog."default",
    session_id text COLLATE pg_catalog."default" NOT NULL,
    status "SessionNoteStatusType",
    deleted_at bigint NOT NULL DEFAULT 0,
    covered_lesson_id text COLLATE pg_catalog."default",
    CONSTRAINT "SessionNote_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."Tag"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    deleted_at bigint NOT NULL DEFAULT 0,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    updated_at timestamp(3) without time zone NOT NULL,
    name text COLLATE pg_catalog."default" NOT NULL,
    description text COLLATE pg_catalog."default",
    CONSTRAINT "Tag_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."Track"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    description text COLLATE pg_catalog."default",
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    connection_description text COLLATE pg_catalog."default",
    language_type "LanguageType",
    mom_summary text COLLATE pg_catalog."default",
    title text COLLATE pg_catalog."default" NOT NULL,
    track_summary text COLLATE pg_catalog."default",
    deleted_at bigint NOT NULL DEFAULT 0,
    CONSTRAINT "Track_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."User"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    email text COLLATE pg_catalog."default" NOT NULL,
    "passwordHash" text COLLATE pg_catalog."default" NOT NULL,
    "refreshTokenHash" text COLLATE pg_catalog."default",
    username text COLLATE pg_catalog."default" NOT NULL,
    "firstName" text COLLATE pg_catalog."default",
    "lastName" text COLLATE pg_catalog."default",
    "affiliateId" text COLLATE pg_catalog."default",
    advocate_capacity_for_moms integer,
    date_of_birth timestamp(3) without time zone,
    home_church text COLLATE pg_catalog."default",
    phone text COLLATE pg_catalog."default",
    secondary_email text COLLATE pg_catalog."default",
    secondary_phone text COLLATE pg_catalog."default",
    sms_message_opt_in boolean,
    timezone text COLLATE pg_catalog."default",
    "profilePicExternalUrl" text COLLATE pg_catalog."default",
    "profilePicMimeType" text COLLATE pg_catalog."default",
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    address_city text COLLATE pg_catalog."default",
    address_postalcode text COLLATE pg_catalog."default",
    address_state text COLLATE pg_catalog."default",
    address_street text COLLATE pg_catalog."default",
    description text COLLATE pg_catalog."default",
    phone_home text COLLATE pg_catalog."default",
    phone_mobile text COLLATE pg_catalog."default",
    phone_other text COLLATE pg_catalog."default",
    phone_work text COLLATE pg_catalog."default",
    communication_preference "CommunicationPreferenceType",
    status "UserStatusType",
    advocate_status "AdvocateStatusType",
    deleted_at bigint NOT NULL DEFAULT 0,
    availability text COLLATE pg_catalog."default",
    language_notes_c text COLLATE pg_catalog."default",
    language_preference_c "Language",
    languages_c "Language"[],
    CONSTRAINT "User_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."UserRole"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    user_id text COLLATE pg_catalog."default" NOT NULL,
    role_id text COLLATE pg_catalog."default" NOT NULL,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    deleted_at bigint NOT NULL DEFAULT 0,
    CONSTRAINT "UserRole_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."WellnessAssessment"
(
    id text COLLATE pg_catalog."default" NOT NULL,
    cc_affordability text COLLATE pg_catalog."default",
    cc_backup_care text COLLATE pg_catalog."default",
    cc_childcare_safety boolean,
    cc_ed_concerns boolean,
    cc_health_ins text COLLATE pg_catalog."default",
    cc_med_access text COLLATE pg_catalog."default",
    cc_notes text COLLATE pg_catalog."default",
    cc_reliable_care text COLLATE pg_catalog."default",
    cc_school text COLLATE pg_catalog."default",
    cc_school_enrollment text COLLATE pg_catalog."default",
    cc_score text COLLATE pg_catalog."default",
    cc_special_ed boolean,
    cc_special_med text COLLATE pg_catalog."default",
    completed_ahead boolean,
    completed_date timestamp(3) without time zone,
    cw_active_involvement text COLLATE pg_catalog."default",
    cw_allegations text COLLATE pg_catalog."default",
    cw_date_of_involvement timestamp(3) without time zone,
    cw_fp_goal text COLLATE pg_catalog."default",
    cw_fp_impact text COLLATE pg_catalog."default",
    cw_home_status text COLLATE pg_catalog."default",
    cw_involvement_as_child text COLLATE pg_catalog."default",
    cw_involvement_as_mom boolean,
    cw_maltreatment_type text COLLATE pg_catalog."default",
    cw_notes text COLLATE pg_catalog."default",
    cw_score text COLLATE pg_catalog."default",
    date_entered timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_modified timestamp(3) without time zone NOT NULL,
    deleted boolean,
    description text COLLATE pg_catalog."default",
    emp_afford_food text COLLATE pg_catalog."default",
    emp_challenges text COLLATE pg_catalog."default",
    emp_challenges_score text COLLATE pg_catalog."default",
    emp_concrete_pfs_total text COLLATE pg_catalog."default",
    emp_diff_manage_bills text COLLATE pg_catalog."default",
    emp_difficulty text COLLATE pg_catalog."default",
    emp_duration_current text COLLATE pg_catalog."default",
    emp_emergency_funds text COLLATE pg_catalog."default",
    emp_fin_notes text COLLATE pg_catalog."default",
    emp_fin_struggles text COLLATE pg_catalog."default",
    emp_fin_struggles_score text COLLATE pg_catalog."default",
    emp_highest_ed text COLLATE pg_catalog."default",
    emp_notes text COLLATE pg_catalog."default",
    emp_score text COLLATE pg_catalog."default",
    emp_status text COLLATE pg_catalog."default",
    emp_support_needed text COLLATE pg_catalog."default",
    emp_support_received text COLLATE pg_catalog."default",
    emp_work_eligibility text COLLATE pg_catalog."default",
    fah_child_behavior text COLLATE pg_catalog."default",
    fah_communication text COLLATE pg_catalog."default",
    fah_discipline text COLLATE pg_catalog."default",
    fah_emotions text COLLATE pg_catalog."default",
    fah_fr_pfs_summary text COLLATE pg_catalog."default",
    fah_notes text COLLATE pg_catalog."default",
    fah_nurture_pfs_summary text COLLATE pg_catalog."default",
    fah_positive_outlook text COLLATE pg_catalog."default",
    fah_power_struggles text COLLATE pg_catalog."default",
    fah_score text COLLATE pg_catalog."default",
    fah_traditions text COLLATE pg_catalog."default",
    faith_attendance text COLLATE pg_catalog."default",
    faith_belief_self text COLLATE pg_catalog."default",
    faith_fulfillment text COLLATE pg_catalog."default",
    faith_goodness text COLLATE pg_catalog."default",
    faith_happiness text COLLATE pg_catalog."default",
    faith_id text COLLATE pg_catalog."default",
    faith_notes text COLLATE pg_catalog."default",
    faith_overall_sat text COLLATE pg_catalog."default",
    faith_purpose text COLLATE pg_catalog."default",
    faith_purpose_summary text COLLATE pg_catalog."default",
    faith_purpose_summary_score text COLLATE pg_catalog."default",
    faith_sacrifice text COLLATE pg_catalog."default",
    faith_sat_summary text COLLATE pg_catalog."default",
    faith_sat_summary_score text COLLATE pg_catalog."default",
    faith_support boolean,
    faith_virtue_summary text COLLATE pg_catalog."default",
    faith_virtue_summary_score text COLLATE pg_catalog."default",
    home_category text COLLATE pg_catalog."default",
    home_name_on_lease text COLLATE pg_catalog."default",
    home_notes text COLLATE pg_catalog."default",
    home_perc_toward text COLLATE pg_catalog."default",
    home_recent_homeless text COLLATE pg_catalog."default",
    home_risk_in_home boolean,
    home_safe boolean,
    home_score text COLLATE pg_catalog."default",
    home_security_concerns text COLLATE pg_catalog."default",
    home_type text COLLATE pg_catalog."default",
    home_voucher text COLLATE pg_catalog."default",
    legal_current text COLLATE pg_catalog."default",
    legal_notes text COLLATE pg_catalog."default",
    legal_plan_childcare text COLLATE pg_catalog."default",
    legal_rep text COLLATE pg_catalog."default",
    legal_rep_access text COLLATE pg_catalog."default",
    meeting_method text COLLATE pg_catalog."default",
    name text COLLATE pg_catalog."default",
    notes_care_of_children text COLLATE pg_catalog."default",
    rel_dynamics text COLLATE pg_catalog."default",
    rel_length text COLLATE pg_catalog."default",
    rel_notes text COLLATE pg_catalog."default",
    rel_resolve_arguments text COLLATE pg_catalog."default",
    rel_score text COLLATE pg_catalog."default",
    rel_status text COLLATE pg_catalog."default",
    rel_tension text COLLATE pg_catalog."default",
    rel_wast_sf text COLLATE pg_catalog."default",
    soc_frs_content_with_rels text COLLATE pg_catalog."default",
    soc_frs_rel_sat text COLLATE pg_catalog."default",
    soc_frs_summary text COLLATE pg_catalog."default",
    soc_frs_summary_score text COLLATE pg_catalog."default",
    soc_notes text COLLATE pg_catalog."default",
    soc_pfs_emergency_contact text COLLATE pg_catalog."default",
    soc_pfs_summary_score text COLLATE pg_catalog."default",
    soc_pfs_support_goals text COLLATE pg_catalog."default",
    soc_pfs_supportive_advice text COLLATE pg_catalog."default",
    soc_pfs_supportive_rels text COLLATE pg_catalog."default",
    soc_pfs_trusted_network_score text COLLATE pg_catalog."default",
    soc_score text COLLATE pg_catalog."default",
    soc_trusted_network text COLLATE pg_catalog."default",
    staff_name_c text COLLATE pg_catalog."default",
    start_date_c timestamp(3) without time zone,
    subs_notes text COLLATE pg_catalog."default",
    subs_recency text COLLATE pg_catalog."default",
    subs_support_needed text COLLATE pg_catalog."default",
    subs_treatment_history boolean,
    trnprt_access text COLLATE pg_catalog."default",
    trnprt_affordable text COLLATE pg_catalog."default",
    trnprt_license text COLLATE pg_catalog."default",
    trnprt_notes text COLLATE pg_catalog."default",
    trnprt_private_safety text COLLATE pg_catalog."default",
    trnprt_score text COLLATE pg_catalog."default",
    trnprt_seat_access boolean,
    well_counseling_interest text COLLATE pg_catalog."default",
    well_counseling_past text COLLATE pg_catalog."default",
    well_health_summary text COLLATE pg_catalog."default",
    well_health_summary_score text COLLATE pg_catalog."default",
    well_mental_health text COLLATE pg_catalog."default",
    well_notes text COLLATE pg_catalog."default",
    well_phq_q1 text COLLATE pg_catalog."default",
    well_phq_q2 text COLLATE pg_catalog."default",
    well_phq_q3 text COLLATE pg_catalog."default",
    well_phq_q4 text COLLATE pg_catalog."default",
    well_phq_q5 text COLLATE pg_catalog."default",
    well_phq_q6 text COLLATE pg_catalog."default",
    well_phq_q7 text COLLATE pg_catalog."default",
    well_phq_q8 text COLLATE pg_catalog."default",
    well_phq_q9 text COLLATE pg_catalog."default",
    well_phq_total text COLLATE pg_catalog."default",
    well_phq_total_score text COLLATE pg_catalog."default",
    well_phys_health text COLLATE pg_catalog."default",
    well_score text COLLATE pg_catalog."default",
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    created_by_id text COLLATE pg_catalog."default",
    created_by_name text COLLATE pg_catalog."default",
    deleted_at bigint NOT NULL DEFAULT 0,
    CONSTRAINT "WellnessAssessment_pkey" PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public."_AdvocateToCoordinator"
(
    "A" text COLLATE pg_catalog."default" NOT NULL,
    "B" text COLLATE pg_catalog."default" NOT NULL,
    CONSTRAINT "_AdvocateToCoordinator_AB_pkey" PRIMARY KEY ("A", "B")
);

CREATE TABLE IF NOT EXISTS public._prisma_migrations
(
    id character varying(36) COLLATE pg_catalog."default" NOT NULL,
    checksum character varying(64) COLLATE pg_catalog."default" NOT NULL,
    finished_at timestamp with time zone,
    migration_name character varying(255) COLLATE pg_catalog."default" NOT NULL,
    logs text COLLATE pg_catalog."default",
    rolled_back_at timestamp with time zone,
    started_at timestamp with time zone NOT NULL DEFAULT now(),
    applied_steps_count integer NOT NULL DEFAULT 0,
    CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id)
);

ALTER TABLE IF EXISTS public."AAPIScore"
    ADD CONSTRAINT "AAPIScore_mom_id_fkey" FOREIGN KEY (mom_id)
    REFERENCES public."Mom" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE RESTRICT;
CREATE INDEX IF NOT EXISTS "AAPIScore_mom_id_key"
    ON public."AAPIScore"(mom_id);


ALTER TABLE IF EXISTS public."ActionItem"
    ADD CONSTRAINT "ActionItem_goalId_fkey" FOREIGN KEY ("goalId")
    REFERENCES public."Goal" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE RESTRICT;


ALTER TABLE IF EXISTS public."AffiliateAgency"
    ADD CONSTRAINT "AffiliateAgency_affiliate_id_fkey" FOREIGN KEY (affiliate_id)
    REFERENCES public."Affiliate" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE RESTRICT;


ALTER TABLE IF EXISTS public."AffiliateAgency"
    ADD CONSTRAINT "AffiliateAgency_agency_id_fkey" FOREIGN KEY (agency_id)
    REFERENCES public."Agency" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE RESTRICT;


ALTER TABLE IF EXISTS public."BenevolenceNeed"
    ADD CONSTRAINT "BenevolenceNeed_momId_fkey" FOREIGN KEY ("momId")
    REFERENCES public."Mom" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE RESTRICT;


ALTER TABLE IF EXISTS public."BenevolenceNeed"
    ADD CONSTRAINT "BenevolenceNeed_resolvedByUserId_fkey" FOREIGN KEY ("resolvedByUserId")
    REFERENCES public."User" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."ConnectionLog"
    ADD CONSTRAINT "ConnectionLog_mom_id_fkey" FOREIGN KEY (mom_id)
    REFERENCES public."Mom" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."ConnectionLog"
    ADD CONSTRAINT "ConnectionLog_user_id_fkey" FOREIGN KEY (user_id)
    REFERENCES public."User" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."CoordinatorNote"
    ADD CONSTRAINT "CoordinatorNote_advocate_id_fkey" FOREIGN KEY (advocate_id)
    REFERENCES public."User" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."CoordinatorNote"
    ADD CONSTRAINT "CoordinatorNote_coordinator_id_fkey" FOREIGN KEY (coordinator_id)
    REFERENCES public."User" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."CoordinatorNote"
    ADD CONSTRAINT "CoordinatorNote_mom_id_fkey" FOREIGN KEY (mom_id)
    REFERENCES public."Mom" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."Document"
    ADD CONSTRAINT "Document_advocate_id_fkey" FOREIGN KEY (advocate_id)
    REFERENCES public."User" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."Document"
    ADD CONSTRAINT "Document_coordinator_id_fkey" FOREIGN KEY (coordinator_id)
    REFERENCES public."User" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."Document"
    ADD CONSTRAINT "Document_lesson_id_fkey" FOREIGN KEY (lesson_id)
    REFERENCES public."Lesson" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."Document"
    ADD CONSTRAINT "Document_lesson_template_id_fkey" FOREIGN KEY (lesson_template_id)
    REFERENCES public."LessonTemplate" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."Document"
    ADD CONSTRAINT "Document_mom_id_fkey" FOREIGN KEY (mom_id)
    REFERENCES public."Mom" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."DocumentTag"
    ADD CONSTRAINT "DocumentTag_document_id_fkey" FOREIGN KEY (document_id)
    REFERENCES public."Document" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE RESTRICT;


ALTER TABLE IF EXISTS public."DocumentTag"
    ADD CONSTRAINT "DocumentTag_tag_id_fkey" FOREIGN KEY (tag_id)
    REFERENCES public."Tag" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE RESTRICT;


ALTER TABLE IF EXISTS public."EventRespondent"
    ADD CONSTRAINT "EventRespondent_event_id_fkey" FOREIGN KEY (event_id)
    REFERENCES public."Event" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE RESTRICT;


ALTER TABLE IF EXISTS public."EventRespondent"
    ADD CONSTRAINT "EventRespondent_user_id_fkey" FOREIGN KEY (user_id)
    REFERENCES public."User" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE RESTRICT;


ALTER TABLE IF EXISTS public."Goal"
    ADD CONSTRAINT "Goal_momId_fkey" FOREIGN KEY ("momId")
    REFERENCES public."Mom" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE RESTRICT;


ALTER TABLE IF EXISTS public."Lesson"
    ADD CONSTRAINT "Lesson_pairing_id_fkey" FOREIGN KEY (pairing_id)
    REFERENCES public."Pairing" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."Lesson"
    ADD CONSTRAINT "Lesson_source_lesson_template_id_fkey" FOREIGN KEY (source_lesson_template_id)
    REFERENCES public."LessonTemplate" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."LessonTemplate"
    ADD CONSTRAINT "LessonTemplate_track_id_fkey" FOREIGN KEY (track_id)
    REFERENCES public."Track" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE RESTRICT;


ALTER TABLE IF EXISTS public."Mom"
    ADD CONSTRAINT "Mom_affiliate_id_fkey" FOREIGN KEY (affiliate_id)
    REFERENCES public."Affiliate" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."Mom"
    ADD CONSTRAINT "Mom_agency_id_fkey" FOREIGN KEY (agency_id)
    REFERENCES public."Agency" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."Mom"
    ADD CONSTRAINT "Mom_referred_to_agency_id_fkey" FOREIGN KEY (referred_to_agency_id)
    REFERENCES public."Agency" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."Notification"
    ADD CONSTRAINT "Notification_event_id_fkey" FOREIGN KEY (event_id)
    REFERENCES public."Event" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."Notification"
    ADD CONSTRAINT "Notification_mom_id_fkey" FOREIGN KEY (mom_id)
    REFERENCES public."Mom" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."Notification"
    ADD CONSTRAINT "Notification_recipient_user_id_fkey" FOREIGN KEY (recipient_user_id)
    REFERENCES public."User" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."Notification"
    ADD CONSTRAINT "Notification_sending_user_id_fkey" FOREIGN KEY (sending_user_id)
    REFERENCES public."User" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."Pairing"
    ADD CONSTRAINT "Pairing_advocateUserId_fkey" FOREIGN KEY ("advocateUserId")
    REFERENCES public."User" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."Pairing"
    ADD CONSTRAINT "Pairing_momId_fkey" FOREIGN KEY ("momId")
    REFERENCES public."Mom" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."Pairing"
    ADD CONSTRAINT "Pairing_trackId_fkey" FOREIGN KEY ("trackId")
    REFERENCES public."Track" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."Session"
    ADD CONSTRAINT "Session_pairing_id_fkey" FOREIGN KEY (pairing_id)
    REFERENCES public."Pairing" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."Session"
    ADD CONSTRAINT "Session_session_group_id_fkey" FOREIGN KEY (session_group_id)
    REFERENCES public."SessionGroup" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."SessionNote"
    ADD CONSTRAINT "SessionNote_covered_lesson_id_fkey" FOREIGN KEY (covered_lesson_id)
    REFERENCES public."Lesson" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."SessionNote"
    ADD CONSTRAINT "SessionNote_session_id_fkey" FOREIGN KEY (session_id)
    REFERENCES public."Session" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE RESTRICT;
CREATE INDEX IF NOT EXISTS "SessionNote_session_id_key"
    ON public."SessionNote"(session_id);


ALTER TABLE IF EXISTS public."User"
    ADD CONSTRAINT "User_affiliateId_fkey" FOREIGN KEY ("affiliateId")
    REFERENCES public."Affiliate" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE SET NULL;


ALTER TABLE IF EXISTS public."UserRole"
    ADD CONSTRAINT "UserRole_role_id_fkey" FOREIGN KEY (role_id)
    REFERENCES public."Role" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE RESTRICT;


ALTER TABLE IF EXISTS public."UserRole"
    ADD CONSTRAINT "UserRole_user_id_fkey" FOREIGN KEY (user_id)
    REFERENCES public."User" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE RESTRICT;


ALTER TABLE IF EXISTS public."_AdvocateToCoordinator"
    ADD CONSTRAINT "_AdvocateToCoordinator_A_fkey" FOREIGN KEY ("A")
    REFERENCES public."User" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE CASCADE;


ALTER TABLE IF EXISTS public."_AdvocateToCoordinator"
    ADD CONSTRAINT "_AdvocateToCoordinator_B_fkey" FOREIGN KEY ("B")
    REFERENCES public."User" (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE CASCADE;
CREATE INDEX IF NOT EXISTS "_AdvocateToCoordinator_B_index"
    ON public."_AdvocateToCoordinator"("B");

END;