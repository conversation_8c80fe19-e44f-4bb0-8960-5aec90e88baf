CREATE TABLE `ltp_affiliate_params`  (
  `id_affiliate_param` int(11) NOT NULL AUTO_INCREMENT,
  `id_affiliate` int(11) NOT NULL,
  `id_param_link` int(11) NULL DEFAULT NULL,
  `param_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `param_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `param_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `state` tinyint(4) NULL DEFAULT NULL,
  `id_add` tinyint(4) NULL DEFAULT NULL,
  `id_mod` tinyint(4) NULL DEFAULT NULL,
  `id_removed` tinyint(4) NULL DEFAULT NULL,
  `date_add` int(11) NULL DEFAULT NULL,
  `date_mod` int(11) NULL DEFAULT NULL,
  `date_removed` int(11) NULL DEFAULT NULL,
  `ordering` int(11) NULL DEFAULT NULL,
  `param_link_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id_affiliate_param`) USING BTREE,
  UNIQUE INDEX `id_affiliate_param`(`id_affiliate_param`) USING BTREE,
  INDEX `param_type`(`param_type`) USING BTREE,
  INDEX `param_name`(`param_name`) USING BTREE,
  INDEX `id_param_link`(`id_param_link`) USING BTREE,
  INDEX `id_affiliate`(`id_affiliate`) USING BTREE,
  INDEX `param_link_type`(`param_link_type`) USING BTREE,
  INDEX `state`(`state`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 510 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `ltp_affiliates`  (
  `id_affiliate` int(11) NOT NULL AUTO_INCREMENT,
  `affiliate_name` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `affiliate_desc` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL,
  `affiliate_contact_name` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `affiliate_contact_email` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `afflliate_contact_phone` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `affiliate_address_street` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `affiliate_address_city` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `affiliate_address_state` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `affiliate_address_zip` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `affiliate_address_country` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `url_avatar` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `state` tinyint(4) NULL DEFAULT NULL,
  `date_add` int(11) NULL DEFAULT NULL,
  `date_mod` int(11) NULL DEFAULT NULL,
  `date_removed` int(11) NULL DEFAULT NULL,
  `date_start` int(11) NULL DEFAULT NULL,
  `date_sync` int(11) NULL DEFAULT NULL,
  `id_add` int(11) NULL DEFAULT NULL,
  `id_mod` int(11) NULL DEFAULT NULL,
  `id_removed` int(11) NULL DEFAULT NULL,
  `id_salesforce` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `affiliate_phone` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `affiliate_subdomain` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `affiliate_style_settings` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL,
  `affiliate_agree_volunteer` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL,
  `affiliate_agree_family` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL,
  `affiliate_agree_advocate` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL,
  `affiliate_agree_staff` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL,
  `allow_interest_care_portal` tinyint(4) NULL DEFAULT NULL,
  `restrict_cross_church_volunteers` tinyint(4) NULL DEFAULT NULL,
  `default_search_radius` int(11) NULL DEFAULT NULL,
  `allow_foster_family_home_types` tinyint(4) NULL DEFAULT NULL,
  `allow_volunteer_compliance_fields` tinyint(4) NULL DEFAULT NULL,
  `params` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL,
  `allow_community_name_edit_on_new` tinyint(4) NULL DEFAULT NULL,
  `allow_family_name_edit_on_new` tinyint(4) NULL DEFAULT NULL,
  `enable_regions` tinyint(4) NULL DEFAULT NULL,
  `id_agency` tinyint(4) NULL DEFAULT NULL,
  `id_place` int(11) NULL DEFAULT NULL,
  `is_anchor` tinyint(4) NULL DEFAULT NULL,
  `affiliate_website` varchar(230) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `affiliate_name_short` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_promise_network_partner` tinyint(4) NULL DEFAULT NULL,
  `affiliate_time_zone` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `affiliate_css` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL,
  `disable_resources` tinyint(4) NULL DEFAULT NULL,
  `is_network_recruiter` tinyint(4) NULL DEFAULT NULL,
  `allowed_apps` varchar(150) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id_affiliate`) USING BTREE,
  UNIQUE INDEX `id_affiliate`(`id_affiliate`) USING BTREE,
  UNIQUE INDEX `id_salesforce`(`id_salesforce`) USING BTREE,
  INDEX `id_place`(`id_place`) USING BTREE,
  INDEX `affiliate_website`(`affiliate_website`) USING BTREE,
  INDEX `affiliate_subdomain`(`affiliate_subdomain`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 99 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;


CREATE TABLE `ltp_agreement_signatures`  (
  `id_signature` int(11) NOT NULL AUTO_INCREMENT,
  `id_agreement` int(11) NULL DEFAULT NULL,
  `id_affiliate` int(11) NULL DEFAULT NULL,
  `id_church` int(11) NULL DEFAULT NULL,
  `id_people` int(11) NULL DEFAULT NULL,
  `id_family` int(11) NULL DEFAULT NULL,
  `sig_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `sig_date` int(11) NULL DEFAULT NULL,
  `sig_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `sig_other_1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `sig_other_2` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `sig_other_3` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `sig_other_4` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `sign_other_5` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_add` int(11) NULL DEFAULT NULL,
  `id_mod` int(11) NULL DEFAULT NULL,
  `id_removed` int(11) NULL DEFAULT NULL,
  `state` tinyint(4) NULL DEFAULT NULL,
  `date_add` int(11) NULL DEFAULT NULL,
  `date_mod` int(11) NULL DEFAULT NULL,
  `date_removed` int(11) NULL DEFAULT NULL,
  `id_resource` int(11) NULL DEFAULT NULL,
  `params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  PRIMARY KEY (`id_signature`) USING BTREE,
  UNIQUE INDEX `id_signature`(`id_signature`) USING BTREE,
  INDEX `id_people`(`id_people`) USING BTREE,
  INDEX `id_affilaite`(`id_affiliate`) USING BTREE,
  INDEX `id_church`(`id_church`) USING BTREE,
  INDEX `id_family`(`id_family`) USING BTREE,
  INDEX `id_resource`(`id_resource`) USING BTREE,
  INDEX `id_agreement`(`id_agreement`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4791 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `ltp_agreements`  (
  `id_agreement` int(11) NOT NULL AUTO_INCREMENT,
  `id_affiliate` int(11) NULL DEFAULT NULL,
  `id_role` int(11) NULL DEFAULT NULL,
  `agreement_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `agreement_slug` varchar(230) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `agreement_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `agreement_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `agreement_scope` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `state` tinyint(4) NULL DEFAULT NULL,
  `id_add` int(11) NULL DEFAULT NULL,
  `id_mod` int(11) NULL DEFAULT NULL,
  `id_removed` int(11) NULL DEFAULT NULL,
  `date_add` int(11) NULL DEFAULT NULL,
  `date_mod` int(11) NULL DEFAULT NULL,
  `date_removed` int(11) NULL DEFAULT NULL,
  `group` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `group_plural` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `field_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `region_states` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `region_zips` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `applicable_roles` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id_agreement`) USING BTREE,
  UNIQUE INDEX `id_agreement`(`id_agreement`) USING BTREE,
  INDEX `id_affiliate`(`id_affiliate`) USING BTREE,
  INDEX `id_role`(`id_role`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 60 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `ltp_assignments`  (
  `id_assignment` int(11) NOT NULL AUTO_INCREMENT,
  `id_people` int(11) NULL DEFAULT NULL,
  `id_community` int(11) NULL DEFAULT NULL,
  `id_church` int(11) NULL DEFAULT NULL,
  `id_role` int(11) NULL DEFAULT NULL,
  `id_family` int(11) NULL DEFAULT NULL,
  `id_agency` int(11) NULL DEFAULT NULL,
  `id_partner` int(11) NULL DEFAULT NULL,
  `id_msg` int(11) NULL DEFAULT NULL,
  `id_event` int(11) NULL DEFAULT NULL,
  `id_affiliate` int(11) NULL DEFAULT NULL,
  `id_region` int(11) NULL DEFAULT NULL,
  `id_region_manager` int(11) NULL DEFAULT NULL,
  `date_add` int(11) NULL DEFAULT NULL,
  `date_mod` int(11) NULL DEFAULT NULL,
  `date_removed` int(11) NULL DEFAULT NULL,
  `date_reminder` int(11) NULL DEFAULT NULL,
  `date_start` int(11) NULL DEFAULT NULL,
  `date_end` int(11) NULL DEFAULT NULL,
  `state` tinyint(4) NULL DEFAULT NULL,
  `id_add` int(11) NULL DEFAULT NULL,
  `id_mod` int(11) NULL DEFAULT NULL,
  `id_removed` int(11) NULL DEFAULT NULL,
  `assignment_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `meal_week` int(11) NULL DEFAULT NULL,
  `meal_day` int(11) NULL DEFAULT NULL,
  `id_batch` int(11) NULL DEFAULT NULL,
  `id_need` int(11) NULL DEFAULT NULL,
  `id_group` int(11) NULL DEFAULT NULL,
  `id_notify` int(11) NULL DEFAULT NULL,
  `params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `id_lms_course` int(11) NULL DEFAULT NULL,
  INDEX `id_lms_course`(`id_lms_course`) USING BTREE,
  INDEX `assignments_church_type_state`(`id_church`, `assignment_type`, `state`) USING BTREE,
  INDEX `ix_community_assignment`(`id_community`, `assignment_type`, `state`) USING BTREE,
  INDEX `id_people`(`id_people`, `assignment_type`, `state`) USING BTREE,
  INDEX `id_community`(`id_community`, `assignment_type`, `state`) USING BTREE,
  INDEX `id_church`(`id_church`, `assignment_type`, `state`) USING BTREE,
  INDEX `id_role`(`id_role`, `assignment_type`, `state`) USING BTREE,
  INDEX `id_family`(`id_family`, `assignment_type`, `state`) USING BTREE,
  INDEX `assignment_type`(`assignment_type`, `state`) USING BTREE,
  INDEX `id_agency`(`id_agency`, `assignment_type`, `state`) USING BTREE,
  INDEX `id_partner`(`id_partner`, `assignment_type`, `state`) USING BTREE,
  INDEX `id_msg`(`id_msg`, `assignment_type`, `state`) USING BTREE,
  INDEX `id_region`(`id_region`, `assignment_type`, `state`) USING BTREE,
  INDEX `id_need`(`id_need`, `assignment_type`, `state`) USING BTREE,
  INDEX `id_group`(`id_group`, `assignment_type`, `state`) USING BTREE,
  INDEX `id_notify`(`id_notify`, `assignment_type`, `state`) USING BTREE,
  INDEX `id_event`(`id_event`, `assignment_type`, `state`) USING BTREE,
  INDEX `id_affiliate`(`id_affiliate`, `assignment_type`, `state`) USING BTREE,
  INDEX `id_add`(`id_add`) USING BTREE,
  INDEX `id_church_role`(`id_church`, `assignment_type`, `id_role`, `state`) USING BTREE,
  INDEX `id_assignment`(`id_assignment`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1196257 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `ltp_certs`  (
  `id_cert` int(11) NOT NULL AUTO_INCREMENT,
  `cert_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `cert_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `cert_roles` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `state` tinyint(4) NULL DEFAULT NULL,
  `date_add` int(11) NULL DEFAULT NULL,
  `date_mod` int(11) NULL DEFAULT NULL,
  `date_removed` int(11) NULL DEFAULT NULL,
  `id_add` int(11) NULL DEFAULT NULL,
  `id_mod` int(11) NULL DEFAULT NULL,
  `id_removed` int(11) NULL DEFAULT NULL,
  `params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `role_eligibility` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `ceu_hours` decimal(5, 2) NULL DEFAULT NULL,
  PRIMARY KEY (`id_cert`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 348 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


CREATE TABLE `ltp_church_params`  (
  `id_church_param` int(11) NOT NULL AUTO_INCREMENT,
  `id_church` int(11) NOT NULL,
  `id_param_link` int(11) NULL DEFAULT NULL,
  `param_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `param_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `param_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `state` tinyint(4) NULL DEFAULT NULL,
  `id_add` int(4) NULL DEFAULT NULL,
  `id_mod` int(4) NULL DEFAULT NULL,
  `id_removed` int(4) NULL DEFAULT NULL,
  `date_add` int(11) NULL DEFAULT NULL,
  `date_mod` int(11) NULL DEFAULT NULL,
  `date_removed` int(11) NULL DEFAULT NULL,
  `ordering` int(11) NULL DEFAULT NULL,
  `param_link_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id_church_param`) USING BTREE,
  UNIQUE INDEX `id_church_param`(`id_church_param`) USING BTREE,
  INDEX `param_type`(`param_type`) USING BTREE,
  INDEX `id_church`(`id_church`) USING BTREE,
  INDEX `param_name`(`param_name`) USING BTREE,
  INDEX `id_param_link`(`id_param_link`) USING BTREE,
  INDEX `param_link_type`(`param_link_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 571407 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


CREATE TABLE `ltp_churches`  (
  `id_church` int(11) NOT NULL AUTO_INCREMENT,
  `church_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `church_nick` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `campus_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `address_street_1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `address_street_2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `address_city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `address_state` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `address_zip` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `address_county` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `address_country` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `church_geocode` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `church_geo_lat` decimal(11, 8) NULL DEFAULT NULL,
  `church_geo_lng` decimal(11, 8) NULL DEFAULT NULL,
  `church_website` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `church_contact_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `church_contact_email` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `is_campus` tinyint(4) NULL DEFAULT NULL,
  `id_church_parent` int(4) NULL DEFAULT NULL,
  `is_anchor_church` tinyint(4) NULL DEFAULT NULL,
  `is_satelite_church` tinyint(4) NULL DEFAULT NULL,
  `state` tinyint(4) NULL DEFAULT NULL,
  `date_add` int(11) NULL DEFAULT NULL,
  `date_mod` int(11) NULL DEFAULT NULL,
  `date_removed` int(11) NULL DEFAULT NULL,
  `date_start` int(11) NULL DEFAULT NULL,
  `date_end` int(11) NULL DEFAULT NULL,
  `date_sync` int(11) NULL DEFAULT NULL,
  `date_admin` int(11) NULL DEFAULT NULL,
  `id_add` int(11) NULL DEFAULT NULL,
  `id_mod` int(11) NULL DEFAULT NULL,
  `id_removed` int(11) NULL DEFAULT NULL,
  `id_admin` int(11) NULL DEFAULT NULL,
  `id_salesforce` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_salesforce_record_type` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_import_batch` int(11) NULL DEFAULT NULL,
  `partner_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `church_denomination` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `geo_address_street` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `geo_address_city` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `geo_address_state` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `geo_address_zip` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `geo_address_country` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `church_county` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `is_affiliate_church` tinyint(4) NULL DEFAULT NULL,
  `id_batch` int(11) NULL DEFAULT NULL,
  `id_org_internal` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `church_name_alts` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `disable_volunteer_sharing` tinyint(4) NULL DEFAULT NULL,
  `church_requires_training` tinyint(4) NULL DEFAULT NULL,
  `church_type_ltp` tinyint(4) NULL DEFAULT NULL,
  `church_type_resource` tinyint(4) NULL DEFAULT NULL,
  `church_type_careportal` tinyint(4) NULL DEFAULT NULL,
  `edit_key` varchar(235) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `church_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_place` int(11) NULL DEFAULT NULL,
  `church_map_eligible` tinyint(4) NULL DEFAULT NULL,
  `church_hash` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `fam_name` varchar(230) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `index_church_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `index_campus_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `index_church_denom` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `index_church_index` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `omit_from_church_count` tinyint(4) NULL DEFAULT NULL,
  `min_access_level` int(11) NULL DEFAULT NULL,
  `entry_point` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_signature` int(11) NULL DEFAULT NULL,
  `church_slug` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_careportal` int(11) NULL DEFAULT NULL,
  `activity_count` int(11) NULL DEFAULT NULL,
  `church_demographic` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `mailing_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_usachurch` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id_church`) USING BTREE,
  UNIQUE INDEX `id_church`(`id_church`) USING BTREE,
  INDEX `id_church_parent`(`id_church_parent`) USING BTREE,
  INDEX `id_place`(`id_place`) USING BTREE,
  INDEX `id_signature`(`id_signature`) USING BTREE,
  INDEX `church_slug`(`church_slug`) USING BTREE,
  INDEX `id_careportal`(`id_careportal`) USING BTREE,
  INDEX `address_country`(`address_country`) USING BTREE,
  INDEX `address_county`(`address_county`) USING BTREE,
  INDEX `address_state`(`address_state`) USING BTREE,
  INDEX `address_zip`(`address_zip`) USING BTREE,
  INDEX `church_hash_idx`(`church_hash`, `state`) USING BTREE,
  INDEX `state`(`state`) USING BTREE,
  INDEX `address_country_state`(`address_country`, `state`) USING BTREE,
  INDEX `ltp_churches_geo_lat`(`church_geo_lat`) USING BTREE,
  INDEX `ltp_churches_geo_lng`(`church_geo_lng`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 252222 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


CREATE TABLE `ltp_email_queue`  (
  `id_email_queue` int(11) NOT NULL AUTO_INCREMENT,
  `id_msg` int(11) NULL DEFAULT NULL,
  `id_people` int(11) NULL DEFAULT NULL,
  `email_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `state` tinyint(4) NULL DEFAULT NULL,
  `date_add` int(11) NULL DEFAULT NULL,
  `date_mod` int(11) NULL DEFAULT NULL,
  `date_sent` int(11) NULL DEFAULT NULL,
  `date_sendable` int(11) NULL DEFAULT NULL,
  `id_add` int(11) NULL DEFAULT NULL,
  `id_mod` int(11) NULL DEFAULT NULL,
  `id_email_layout` int(11) NULL DEFAULT NULL,
  `id_msg_template` int(11) NULL DEFAULT NULL,
  `hash` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `is_system_notification` tinyint(4) NULL DEFAULT NULL,
  `system_notify` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `msg_error` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_affiliate_send` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id_email_queue`) USING BTREE,
  UNIQUE INDEX `id_email_queue`(`id_email_queue`) USING BTREE,
  INDEX `id_msg`(`id_msg`) USING BTREE,
  INDEX `id_people`(`id_people`) USING BTREE,
  INDEX `id_email_layout`(`id_email_layout`) USING BTREE,
  INDEX `hash`(`hash`) USING BTREE,
  INDEX `email_address`(`email_address`) USING BTREE,
  INDEX `state`(`state`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 336169 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


CREATE TABLE `ltp_events`  (
  `id_event` int(11) NOT NULL AUTO_INCREMENT,
  `event_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_church` int(11) NULL DEFAULT NULL,
  `id_church_add` int(11) NULL DEFAULT NULL,
  `id_event_contact` int(11) NULL DEFAULT NULL,
  `id_place` int(11) NULL DEFAULT NULL,
  `id_event_cat` int(11) NULL DEFAULT NULL,
  `event_type` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `event_desc` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL,
  `event_location_name` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `event_location_detail` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `event_place_name` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `event_address_street` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `event_address_city` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `event_address_state` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `event_address_zip` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `event_address_county` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `event_address_country` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `event_geocode` varchar(150) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `event_geo_lat` decimal(11, 8) NULL DEFAULT NULL,
  `event_geo_lng` decimal(11, 8) NULL DEFAULT NULL,
  `event_contact_name` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `event_contact_phone` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `event_date_start` int(11) NULL DEFAULT NULL,
  `event_date_end` int(11) NULL DEFAULT NULL,
  `event_time_zone` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `event_slug` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `event_max_size` int(11) NULL DEFAULT NULL,
  `event_position` int(11) NULL DEFAULT NULL,
  `state` tinyint(4) NULL DEFAULT NULL,
  `date_add` int(11) NULL DEFAULT NULL,
  `date_mod` int(11) NULL DEFAULT NULL,
  `date_removed` int(11) NULL DEFAULT NULL,
  `id_add` int(11) NULL DEFAULT NULL,
  `id_mod` int(11) NULL DEFAULT NULL,
  `id_removed` int(11) NULL DEFAULT NULL,
  `has_childcare` tinyint(4) NULL DEFAULT NULL,
  `childcare_fee` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `show_count_adults` tinyint(4) NULL DEFAULT NULL,
  `show_count_kids` tinyint(4) NULL DEFAULT NULL,
  `show_count_meals_adults` tinyint(4) NULL DEFAULT NULL,
  `show_count_meals_kids` tinyint(4) NULL DEFAULT NULL,
  `show_ages_kids` tinyint(4) NULL DEFAULT NULL,
  `show_care_portal_option` tinyint(4) NULL DEFAULT NULL,
  `id_org_internal` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `hide_change_event` tinyint(4) NULL DEFAULT NULL,
  `hide_event_from_list` tinyint(4) NULL DEFAULT NULL,
  `show_simple_form` tinyint(4) NULL DEFAULT NULL,
  `show_tshirt_sizes` tinyint(4) NULL DEFAULT NULL,
  `rsvp_show_address` tinyint(11) NULL DEFAULT NULL,
  `orientation_event_complete_status` int(11) NULL DEFAULT 30,
  `url_short` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `url_church` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `required_address` tinyint(4) NULL DEFAULT NULL,
  `event_key` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `send_event_reminder` tinyint(4) NULL DEFAULT NULL,
  `send_event_followup` tinyint(4) NULL DEFAULT NULL,
  `send_rsvp_confirmation` tinyint(4) NULL DEFAULT NULL,
  `meal_position` int(4) NULL DEFAULT NULL,
  `params` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL,
  `is_virtual` tinyint(4) NULL DEFAULT NULL,
  `virtual_early_start` int(255) NULL DEFAULT 0,
  `virtual_event_type` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `virtual_event_moderators` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `virtual_event_password` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `virtual_event_url_external` varchar(235) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `virtual_event_lockout_time` int(11) NULL DEFAULT NULL,
  `virtual_event_live_stream_url` varchar(235) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `email_body_response` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL,
  `email_subject_response` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `repeat_freq` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `repeat_int` tinyint(11) NULL DEFAULT NULL,
  `repeat_date_end` int(11) NOT NULL,
  `repeat_id_event` int(11) NULL DEFAULT NULL,
  `event_all_day` tinyint(4) NULL DEFAULT NULL,
  `email_body_rsvp` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL,
  `email_subject_rsvp` varchar(250) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `is_affiliate_hosted` tinyint(4) NULL DEFAULT NULL,
  `is_confirmed` tinyint(4) NULL DEFAULT NULL,
  `not_claimable` tinyint(4) NULL DEFAULT NULL,
  `email_body_notattend` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL,
  `email_subject_notattend` varchar(250) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `tags` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `event_date_rsvp_end` int(11) NULL DEFAULT NULL,
  `access_level_min` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id_event`, `repeat_date_end`) USING BTREE,
  UNIQUE INDEX `id_event`(`id_event`) USING BTREE,
  INDEX `id_event_contact`(`id_event_contact`) USING BTREE,
  INDEX `event_key`(`event_key`) USING BTREE,
  INDEX `id_place`(`id_place`) USING BTREE,
  INDEX `repeat_id_event`(`repeat_id_event`) USING BTREE,
  INDEX `id_church`(`id_church`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 105110 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;


CREATE TABLE `ltp_families`  (
  `id_family` int(11) NOT NULL AUTO_INCREMENT,
  `family_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `family_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `family_structure` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_salesforce` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `state` tinyint(4) NULL DEFAULT NULL,
  `date_add` int(11) NULL DEFAULT NULL,
  `date_mod` int(11) NULL DEFAULT NULL,
  `date_removed` int(11) NULL DEFAULT NULL,
  `date_sync` int(11) NULL DEFAULT NULL,
  `id_add` int(11) NULL DEFAULT NULL,
  `id_mod` int(11) NULL DEFAULT NULL,
  `id_church_recruited` int(11) NULL DEFAULT NULL,
  `id_church_home` int(11) NULL DEFAULT NULL,
  `id_church_add` int(11) NULL DEFAULT NULL,
  `id_church_claim` int(11) NULL DEFAULT NULL,
  `id_church_serving` int(11) NULL DEFAULT NULL,
  `id_church_refer` int(11) NULL DEFAULT NULL,
  `id_people_primary` int(11) NULL DEFAULT NULL,
  `status_foster` int(11) NULL DEFAULT NULL,
  `status_adoption` int(11) NULL DEFAULT NULL,
  `status_bio` int(11) NULL DEFAULT NULL,
  `previously_served_on_cc` tinyint(4) NULL DEFAULT NULL,
  `previously_fostered` tinyint(4) NULL DEFAULT NULL,
  `recruited_by_church` tinyint(4) NULL DEFAULT NULL,
  `recruited_by_affiliate` tinyint(4) NULL DEFAULT NULL,
  `url_avatar` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_org_internal` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_import_batch` int(11) NULL DEFAULT NULL,
  `requestor_name_first` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `requestor_name_last` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `requestor_phone` int(20) NULL DEFAULT NULL,
  `requestor_email` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `requestor_relationship` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `requestor_id_church` int(11) NULL DEFAULT NULL,
  `family_geo_lat` decimal(11, 8) NULL DEFAULT NULL,
  `family_geo_lng` decimal(11, 8) NULL DEFAULT NULL,
  `has_past_adopted` tinyint(4) NULL DEFAULT NULL,
  `has_past_fostered` tinyint(4) NULL DEFAULT NULL,
  `has_past_kinship_placement` tinyint(4) NULL DEFAULT NULL,
  `has_current_kinship_placement` tinyint(4) NULL DEFAULT NULL,
  `has_current_foster_placement` tinyint(4) NULL DEFAULT NULL,
  `has_desire_adopt` tinyint(4) NULL DEFAULT NULL,
  `has_desire_foster` tinyint(4) NULL DEFAULT NULL,
  `has_desire_kinship` tinyint(4) NULL DEFAULT NULL,
  `params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `agree_family_sign_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `agree_family_sign_date` int(11) NULL DEFAULT NULL,
  `edit_key` varchar(235) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `edit_key_date_expire` int(11) NULL DEFAULT NULL,
  `id_place` int(11) NULL DEFAULT NULL,
  `id_churches_nearby` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `entry_point` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `family_languages` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `family_languages_other` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name_parent0_first` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name_parent0_last` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name_parent1_first` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name_parent1_last` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name_parent2_first` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name_parent2_last` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name_parent3_last` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name_parent3_first` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `access_instructions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `id_community_active` int(11) NULL DEFAULT NULL,
  `id_community_last` int(11) NULL DEFAULT NULL,
  `date_foster_start` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id_family`) USING BTREE,
  UNIQUE INDEX `id_family`(`id_family`) USING BTREE,
  INDEX `id_place`(`id_place`) USING BTREE,
  INDEX `id_church_home`(`id_church_home`) USING BTREE,
  INDEX `id_people_primary`(`id_people_primary`) USING BTREE,
  INDEX `id_church_claim`(`id_church_claim`) USING BTREE,
  INDEX `id_church_add`(`id_church_add`) USING BTREE,
  INDEX `id_church_refer`(`id_church_refer`) USING BTREE,
  INDEX `id_church_serving`(`id_church_serving`) USING BTREE,
  INDEX `id_community`(`id_community_active`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8696 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


CREATE TABLE `ltp_lms_coursework`  (
  `id_lms_coursework` int(11) NOT NULL AUTO_INCREMENT,
  `id_lms_course` int(11) NULL DEFAULT NULL,
  `id_people` int(11) NULL DEFAULT NULL,
  `progress` decimal(4, 0) NULL DEFAULT NULL,
  `state` tinyint(4) NULL DEFAULT NULL,
  `params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `date_add` int(11) NULL DEFAULT NULL,
  `date_mod` int(11) NULL DEFAULT NULL,
  `date_removed` int(11) NULL DEFAULT NULL,
  `date_complete` int(11) NULL DEFAULT NULL,
  `id_add` int(11) NULL DEFAULT NULL,
  `id_mod` int(11) NULL DEFAULT NULL,
  `id_removed` int(11) NULL DEFAULT NULL,
  `id_event` int(11) NULL DEFAULT NULL,
  `id_role` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id_lms_coursework`) USING BTREE,
  UNIQUE INDEX `id_lms_coursework`(`id_lms_coursework`) USING BTREE,
  INDEX `id_people`(`id_people`) USING BTREE,
  INDEX `id_lms_course`(`id_lms_course`) USING BTREE,
  INDEX `id_event`(`id_event`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 146715 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


CREATE TABLE `ltp_messages`  (
  `id_msg` int(11) NOT NULL AUTO_INCREMENT,
  `msg_title` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `msg_body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `msg_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `state` tinyint(4) NULL DEFAULT NULL,
  `date_add` int(11) NULL DEFAULT NULL,
  `date_mod` int(11) NULL DEFAULT NULL,
  `date_removed` int(11) NULL DEFAULT NULL,
  `id_add` int(11) NULL DEFAULT NULL,
  `id_mod` int(11) NULL DEFAULT NULL,
  `id_sender` int(11) NULL DEFAULT NULL,
  `id_removed` int(11) NULL DEFAULT NULL,
  `email_from_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `email_from_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `id_msg_thread` int(11) NULL DEFAULT NULL,
  `permissions_view_role` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `permissions_view_id` int(11) NULL DEFAULT NULL,
  `id_email_layout` int(11) NULL DEFAULT NULL,
  `email_layout_header` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `email_layout_footer` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `msg_one_to_many` tinyint(4) NULL DEFAULT NULL,
  `id_msg_template` int(11) NULL DEFAULT NULL,
  `date_entry` int(11) NULL DEFAULT NULL,
  `hash` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `attachments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `tags` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `convo_hash` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id_msg`) USING BTREE,
  UNIQUE INDEX `id_msg`(`id_msg`) USING BTREE,
  UNIQUE INDEX `hash`(`hash`) USING BTREE,
  INDEX `id_msg_thread`(`id_msg_thread`) USING BTREE,
  INDEX `id_msg_template`(`id_msg_template`) USING BTREE,
  INDEX `idx_convo_hash`(`convo_hash`) USING BTREE,
  INDEX `state`(`state`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 72739 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;


CREATE TABLE `ltp_messaging_templates`  (
  `id_msg_template` int(11) NOT NULL AUTO_INCREMENT,
  `id_affiliate` int(11) NULL DEFAULT NULL,
  `id_church` int(11) NULL DEFAULT NULL,
  `messaging_alias` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `messaging_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `messaging_desc` varchar(220) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `messaging_subject` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `messaging_body` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `messaging_from` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `messaging_from_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `messaging_type` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `messaging_controller` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `messaging_view` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `messaging_view_section` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `messaging_scopes` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `state` tinyint(4) NULL DEFAULT NULL,
  `date_mod` int(11) NULL DEFAULT NULL,
  `date_add` int(11) NULL DEFAULT NULL,
  `date_removed` int(11) NULL DEFAULT NULL,
  `id_add` int(11) NULL DEFAULT NULL,
  `id_mod` int(11) NULL DEFAULT NULL,
  `id_removed` int(11) NULL DEFAULT NULL,
  `id_email_series` int(11) NULL DEFAULT NULL,
  `series_order` int(11) NULL DEFAULT NULL,
  `meta_title` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `meta_desc` varchar(225) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `meta_url` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `meta_img` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `id_email_layout` int(11) NULL DEFAULT NULL,
  `email_layout_header` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `email_layout_footer` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `messaging_params` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `is_system_msg` tinyint(4) NULL DEFAULT NULL,
  `key` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `password` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id_msg_template`) USING BTREE,
  UNIQUE INDEX `id_msg_template`(`id_msg_template`) USING BTREE,
  INDEX `id_email_series`(`id_email_series`) USING BTREE,
  INDEX `id_affiliate`(`id_affiliate`) USING BTREE,
  INDEX `messaging_alias`(`messaging_alias`) USING BTREE,
  INDEX `id_email_layout`(`id_email_layout`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 463 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;


CREATE TABLE `ltp_people`  (
  `id_people` int(11) NOT NULL AUTO_INCREMENT,
  `name_first` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name_last` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `people_email_primary` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name_middle` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name_preferred` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name_spouse_first` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name_spouse_last` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `address_street_1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `address_street_2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `address_city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `address_state` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `address_zip` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `address_country` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `address_county` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `address_access_instructions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `people_geocode` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `people_geo_lat` decimal(11, 8) NULL DEFAULT NULL,
  `people_geo_lng` decimal(11, 8) NULL DEFAULT NULL,
  `people_email_secondary` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `people_phone_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `people_phone_office` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `people_phone_home` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `people_gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `people_dob` int(11) NULL DEFAULT NULL,
  `home_church` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_home_church` int(11) NULL DEFAULT NULL,
  `id_church_add` int(11) NULL DEFAULT NULL,
  `url_avatar` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `url_avatar_one_pager` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `is_affiliate_admin` tinyint(4) NULL DEFAULT NULL,
  `state` tinyint(4) NULL DEFAULT NULL,
  `state_last` tinyint(4) NULL DEFAULT NULL,
  `date_add` int(11) NULL DEFAULT NULL,
  `date_mod` int(11) NULL DEFAULT NULL,
  `date_sync` int(11) NULL DEFAULT NULL,
  `date_removed` int(11) NULL DEFAULT NULL,
  `password` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_add` int(11) NULL DEFAULT NULL,
  `id_mod` int(11) NULL DEFAULT NULL,
  `id_removed` int(11) NULL DEFAULT NULL,
  `date_self_update` int(11) NULL DEFAULT NULL,
  `date_last_login` int(11) NULL DEFAULT NULL,
  `pass_reset` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `pass_reset_force` tinyint(4) NULL DEFAULT NULL,
  `interest_advocate` tinyint(4) NULL DEFAULT NULL,
  `interest_foster_family_respite` tinyint(4) NULL DEFAULT NULL,
  `interest_foster_family` tinyint(4) NULL DEFAULT NULL,
  `interest_child_mentor` tinyint(4) NULL DEFAULT NULL,
  `interest_family_helper` tinyint(4) NULL DEFAULT NULL,
  `interest_team_leader` tinyint(4) NULL DEFAULT NULL,
  `interest_care_community` tinyint(4) NULL DEFAULT NULL,
  `interest_interim_caregiver` tinyint(4) NULL DEFAULT NULL,
  `interest_prayer` tinyint(4) NULL DEFAULT NULL,
  `interest_adoption` tinyint(4) NULL DEFAULT NULL,
  `interest_care_portal` tinyint(4) NULL DEFAULT NULL,
  `interest_fam_custom_1` tinyint(4) NULL DEFAULT NULL,
  `interest_fam_custom_2` tinyint(4) NULL DEFAULT NULL,
  `interest_fam_custom_3` tinyint(4) NULL DEFAULT NULL,
  `interest_fam_custom_4` tinyint(4) NULL DEFAULT NULL,
  `interest_fam_custom_5` tinyint(4) NULL DEFAULT NULL,
  `interest_fam_custom_6` tinyint(4) NULL DEFAULT NULL,
  `interest_fam_custom_7` tinyint(4) NULL DEFAULT NULL,
  `interest_fam_custom_8` tinyint(4) NULL DEFAULT NULL,
  `interest_fam_custom_9` tinyint(4) NULL DEFAULT NULL,
  `interest_fam_custom_0` tinyint(4) NULL DEFAULT NULL,
  `age` int(11) NULL DEFAULT NULL,
  `date_birth` int(11) NULL DEFAULT NULL,
  `date_anniversary` int(11) NULL DEFAULT NULL,
  `has_cert_mentor` tinyint(4) NULL DEFAULT NULL,
  `cert_mentor_agency_ids` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `has_cert_cpr` tinyint(4) NULL DEFAULT NULL,
  `cert_cpr_date_expire` int(11) NULL DEFAULT NULL,
  `vol_agree_sign_date` int(11) NULL DEFAULT NULL,
  `vol_agree_sign_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_salesforce` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_salesforce_household` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_org_internal` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_import_batch` int(11) NULL DEFAULT NULL,
  `geo_address_street` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `geo_address_city` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `geo_address_state` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `geo_address_zip` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `geo_address_country` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_churches_assigned` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `agree_advocate_sign_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `agree_advocate_sign_date` int(11) NULL DEFAULT NULL,
  `agree_staff_sign_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `agree_staff_sign_date` int(11) NULL DEFAULT NULL,
  `agree_family_sign_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `agree_family_sign_date` int(11) NULL DEFAULT NULL,
  `params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `cert_bg_check_agency` int(11) NULL DEFAULT NULL,
  `cert_bg_check_church` int(11) NULL DEFAULT NULL,
  `cert_fingerprints` int(11) NULL DEFAULT NULL,
  `cert_training_church` int(11) NULL DEFAULT NULL,
  `cert_training_agency` int(11) NULL DEFAULT NULL,
  `cert_training_affiliate` int(11) NULL DEFAULT NULL,
  `cert_church_approval` int(11) NULL DEFAULT NULL,
  `contact_emergency` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `force_logout` tinyint(4) NULL DEFAULT NULL,
  `currently_logged_in` tinyint(4) NULL DEFAULT NULL,
  `can_login` tinyint(4) NULL DEFAULT NULL,
  `login_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `has_portal_access` tinyint(4) NULL DEFAULT NULL,
  `edit_key` varchar(235) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `edit_key_date_expire` int(11) NULL DEFAULT NULL,
  `auth_send_behalf` tinyint(4) NULL DEFAULT NULL,
  `time_zone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_place` int(11) NULL DEFAULT NULL,
  `entry_point` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `occupation` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `affiliate_auth` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `obscure_name` tinyint(4) NULL DEFAULT NULL,
  `people_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `people_status_text` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `session_time` int(11) NULL DEFAULT NULL,
  `index_name_first` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `notifications` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `has_cert_ff` tinyint(4) NULL DEFAULT NULL,
  `cert_ff_agency_ids` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `username` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'NULL',
  `cert_tb_screen_date` int(11) NULL DEFAULT NULL,
  `contact_emergency_phone` int(12) NULL DEFAULT NULL,
  `cert_interview_date` int(12) NULL DEFAULT NULL,
  `people_languages` varchar(75) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `people_languages_other` varchar(75) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `use_assigned_status` tinyint(4) NULL DEFAULT NULL,
  `conv_migrate_status` tinyint(4) NULL DEFAULT NULL,
  PRIMARY KEY (`id_people`) USING BTREE,
  UNIQUE INDEX `login_key`(`login_key`) USING BTREE,
  INDEX `id_people`(`id_people`) USING BTREE,
  INDEX `id_place`(`id_place`) USING BTREE,
  INDEX `people_phone_mobile`(`people_phone_mobile`) USING BTREE,
  INDEX `username`(`username`) USING BTREE,
  INDEX `people_email_primary`(`people_email_primary`) USING BTREE,
  INDEX `state`(`state`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 74245 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `ltp_people_params`  (
  `id_people_param` int(11) NOT NULL AUTO_INCREMENT,
  `id_people` int(11) NOT NULL,
  `id_param_link` int(11) NULL DEFAULT NULL,
  `param_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `param_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `param_value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `state` tinyint(4) NULL DEFAULT NULL,
  `id_add` int(4) NULL DEFAULT NULL,
  `id_mod` int(4) NULL DEFAULT NULL,
  `id_removed` int(4) NULL DEFAULT NULL,
  `date_add` int(11) NULL DEFAULT NULL,
  `date_mod` int(11) NULL DEFAULT NULL,
  `date_removed` int(11) NULL DEFAULT NULL,
  `param_link_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id_people_param`) USING BTREE,
  UNIQUE INDEX `id_people_param`(`id_people_param`) USING BTREE,
  INDEX `param_type`(`param_type`) USING BTREE,
  INDEX `param_name`(`param_name`) USING BTREE,
  INDEX `id_people`(`id_people`) USING BTREE,
  INDEX `id_param_link`(`id_param_link`) USING BTREE,
  INDEX `param_link_type`(`param_link_type`) USING BTREE,
  INDEX `id_people_param_type`(`id_people`, `param_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 140492 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

CREATE TABLE `ltp_places`  (
  `id_place` int(11) NOT NULL AUTO_INCREMENT,
  `place_id_provider` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `place_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `place_street_number` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `place_street` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `place_city` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `place_county` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `place_state` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `place_zip` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `place_country` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `place_lat` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `place_lng` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `place_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `place_time_zone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `state` tinyint(4) NULL DEFAULT NULL,
  `date_add` int(11) NULL DEFAULT NULL,
  `date_mod` int(11) NULL DEFAULT NULL,
  `date_removed` int(11) NULL DEFAULT NULL,
  `id_add` int(11) NULL DEFAULT NULL,
  `id_mod` int(11) NULL DEFAULT NULL,
  `id_removed` int(11) NULL DEFAULT NULL,
  `place_src` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `is_generic` tinyint(4) NULL DEFAULT NULL,
  `place_access_instructions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `id_place_parent` int(11) NULL DEFAULT NULL,
  `place_geo_hash` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `place_unit` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `place_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `place_website` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id_place`) USING BTREE,
  UNIQUE INDEX `id_place`(`id_place`) USING BTREE,
  INDEX `place_lat`(`place_lat`) USING BTREE,
  INDEX `place_lng`(`place_lng`) USING BTREE,
  INDEX `place_id_provider`(`place_id_provider`) USING BTREE,
  INDEX `place_county`(`place_county`) USING BTREE,
  INDEX `place_state`(`place_state`) USING BTREE,
  INDEX `state`(`state`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 58618 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `ltp_places_zipcode_data`  (
  `zip` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `type` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `primary_city` varchar(27) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `acceptable_cities` varchar(282) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `unacceptable_cities` varchar(2208) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `state` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `county` varchar(39) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `timezone` varchar(28) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `area_codes` varchar(39) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `latitude` decimal(6, 2) NOT NULL,
  `longitude` decimal(7, 2) NOT NULL,
  `world_region` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `country` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `decommissioned` bit(1) NOT NULL,
  `estimated_population` int(11) NOT NULL,
  `notes` varchar(124) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `min_latitude` decimal(6, 4) NULL DEFAULT NULL,
  `max_latitude` decimal(6, 4) NULL DEFAULT NULL,
  `min_longitude` decimal(7, 4) NULL DEFAULT NULL,
  `max_longitude` decimal(7, 4) NULL DEFAULT NULL,
  PRIMARY KEY (`zip`) USING BTREE,
  UNIQUE INDEX `zip`(`zip`) USING BTREE,
  INDEX `state`(`state`) USING BTREE,
  INDEX `latitude`(`latitude`) USING BTREE,
  INDEX `longitude`(`longitude`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `ltp_resources`  (
  `id_resource` int(11) NOT NULL AUTO_INCREMENT,
  `resource_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `resource_desc_short` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `resource_desc_long` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `resource_type` varchar(245) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `resource_tags` varchar(245) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `resource_keywords` varchar(245) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `resource_searchable` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `resource_cats` varchar(245) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `resource_format` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `resource_slug` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `resource_slug_msg_template` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `resource_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `state` tinyint(4) NULL DEFAULT NULL,
  `resource_version` int(11) NULL DEFAULT NULL,
  `date_add` int(11) NULL DEFAULT NULL,
  `date_mod` int(11) NULL DEFAULT NULL,
  `date_removed` int(11) NULL DEFAULT NULL,
  `id_add` int(11) NULL DEFAULT NULL,
  `id_mod` int(11) NULL DEFAULT NULL,
  `id_removed` int(11) NULL DEFAULT NULL,
  `id_resource_parent` int(11) NULL DEFAULT NULL,
  `id_resource_version_parent` int(11) NULL DEFAULT NULL,
  `access_level_min` int(11) NULL DEFAULT NULL,
  `access_roles` varchar(245) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `url_download` varchar(245) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `url_preview` varchar(245) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `url_thumbnail` varchar(245) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `url_short` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `url_shareable` varchar(245) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_affiliates` varchar(245) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `count_views` int(11) NULL DEFAULT NULL,
  `count_downloads` int(11) NULL DEFAULT NULL,
  `btn_action_title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `order_cat_view` int(11) NULL DEFAULT NULL,
  `order_master` int(11) NULL DEFAULT NULL,
  `access_level_min_download` int(11) NULL DEFAULT NULL,
  `access_level_affiliate_state` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_creator_affiliate` int(11) NULL DEFAULT NULL,
  `id_creator_people` int(11) NULL DEFAULT NULL,
  `id_creator_church` int(11) NULL DEFAULT NULL,
  `is_curated` tinyint(4) NULL DEFAULT NULL,
  `rights_can_share` tinyint(4) NULL DEFAULT NULL,
  `rights_can_edit` tinyint(4) NULL DEFAULT NULL,
  `id_overridden` int(11) NULL DEFAULT NULL,
  `params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `review_status` tinyint(4) NULL DEFAULT NULL,
  `resource_filename` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `primary_language` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id_resource`) USING BTREE,
  UNIQUE INDEX `id_resource`(`id_resource`) USING BTREE,
  INDEX `resource_keywords`(`resource_keywords`(191)) USING BTREE,
  INDEX `resource_type`(`resource_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6141 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `ltp_roles`  (
  `id_role` int(11) NOT NULL AUTO_INCREMENT,
  `role_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `role_desc` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `role_scope` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `role_scope_segment` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `role_scope_limiters` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `role_permissions` int(11) NULL DEFAULT NULL,
  `state` int(11) NULL DEFAULT NULL,
  `is_serving_role` tinyint(4) NULL DEFAULT NULL,
  `is_affiliate_specific` tinyint(4) NULL DEFAULT NULL,
  `is_interest` tinyint(4) NULL DEFAULT NULL,
  `id_onboarded_role` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id_role`) USING BTREE,
  INDEX `id_role`(`id_role`) USING BTREE,
  INDEX `role_scope`(`role_scope`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1016 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


CREATE TABLE `ltp_rsvps`  (
  `id_rsvp` int(11) NOT NULL AUTO_INCREMENT,
  `id_event` int(11) NULL DEFAULT NULL,
  `id_people` int(11) NULL DEFAULT NULL,
  `count_adults` int(11) NULL DEFAULT NULL,
  `count_kids` int(11) NULL DEFAULT NULL,
  `count_meals_adults` int(11) NULL DEFAULT NULL,
  `count_meals_kids` int(11) NULL DEFAULT NULL,
  `ages_kids` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `date_add` int(11) NULL DEFAULT NULL,
  `date_mod` int(11) NULL DEFAULT NULL,
  `date_remove` int(11) NULL DEFAULT NULL,
  `id_add` int(11) NULL DEFAULT NULL,
  `id_mod` int(11) NULL DEFAULT NULL,
  `id_remove` int(11) NULL DEFAULT NULL,
  `state` tinyint(4) NULL DEFAULT NULL,
  `date_check_in` int(11) NULL DEFAULT NULL,
  `date_response` int(11) NULL DEFAULT NULL,
  `rsvp_at_event` tinyint(4) NULL DEFAULT NULL,
  `id_role` int(11) NULL DEFAULT NULL,
  `role_name_custom` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `id_people_parent` int(11) NULL DEFAULT NULL,
  `date_attend_start` int(11) NULL DEFAULT NULL,
  `date_attend_end` int(11) NULL DEFAULT NULL,
  `date_attend_last_check_in` int(11) NULL DEFAULT NULL,
  `date_attend_start_session` int(11) NULL DEFAULT NULL,
  `attend_time_total` int(11) NULL DEFAULT NULL,
  `count_chat_entries` int(11) NULL DEFAULT NULL,
  `params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  PRIMARY KEY (`id_rsvp`) USING BTREE,
  UNIQUE INDEX `id_rsvp`(`id_rsvp`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22098 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
