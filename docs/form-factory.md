# FormFactory Documentation

## Overview

The `FormFactory` is a powerful form generation component built on top of shadcn/ui's Form component. It provides a declarative way to create forms by defining a configuration object, handling common form requirements like validation, layout, and dynamic behavior.

Key features:

- Built on shadcn/ui Form (which uses react-hook-form under the hood)
- Client-side validation using Yup schemas
- Support for dynamic field visibility and options
- Flexible layout with support for field groups and custom content
- Built-in support for common form patterns

Further reading:

- [shadcn/ui form component docs](https://ui.shadcn.com/docs/components/form)
- [react-hook-form docs](https://www.react-hook-form.com)
- [yup schema docs](https://github.com/jquense/yup)

## Supported Form Input Components

A variety of common and specialized input components are supported. The configuration for the form is defined using an array of `FieldConfig` objects, which is passed to the `FormFactory` component.

When creating a `FieldConfig` object, the `type` property is used to determine which input component to render.

Some basic input types, which generally utilize out-of-the-box shadcn/ui components, include:

- `text`, `number`, `password` - standard inputs with the input `type` attribute matching the name of the config `type` property
- `textarea` - a standard textarea input
- `select` - a standard select dropdown
- `checkbox` - a standard checkbox

Some more specialized input types include:

- `button-radio-group` - This is a group of buttons that function like a radio button group. It is very commonly used in various designs developed for EMA - although there is a "normal" radio button group component available, the designs almost always use the button-style group instead of traditional radio buttons. The component is designed to respond to small screens by wrapping the buttons onto multiple lines.
- `tel` - an input with phone icon embedded in the text field, type `tel`, and text formatting for US-style phone numbers
- `email` - an input with email icon embedded in the text field, and type `email`
- `files` - a file upload input using `react-dropzone`. See the [react-dropzone docs](https://react-dropzone.js.org/) for more information on the available options.
- `date` - date picker input with options for setting min and max selectable dates. Configured to return a string in the format of `YYYY-MM-DDTHH:mm:ss.SSSZ` (ISO 8601 format).
- `time` - input allowing typing time (in 12 hour AM/PM format) or selecting from a time picker. Returns a string in the format of `HH:mm` (24 hour format).

The type `row-group` is not for an input component, but rather a layout component that allows for grouping fields into a row. This is useful for creating horizontal input groupings, and is designed to implement designs such as for first and last name fields that appear side-by-side on desktop devices. A configuration object of this type expects a `subFields` property, which is an array of `FieldConfig` objects that will be rendered as a row.

> Tip: the TS types for form configuration are defined in `apps/portal/src/types/form-field.ts`. This is a good starting point to reference all the available options for configuring `FieldConfig` objects (and determine if you need to add functionality/config options). If you add or change something, please consider updating the docs! If you change something, test thoroughly... it could break previously implemented forms!

## Basic Usage

Below is a simple example of creating a form. This example is a simplified version of the EMA referral form to demonstrate basic usage.

First, a Yup schema is needed if one does not already exist for the form you're creating.

Yup schemas are commonly defined in `apps/portal/src/types/schemas/`. Since these schemas are used by APIs as well as for client-side form validation, there are sometimes mismatches between fields that are used by the form vs. by the API. You can always extract the individual fields from an overall schema into a custom schema for use by the UI.

For this example, start with a Yup schema - add a file `apps/portal/src/types/schemas/example.ts` with the following code:

```ts
import * as yup from 'yup';
import { emailSchema } from './common-fields'; // 'common-fields' contains common reusable field schemas (email, phone, zip, etc.)
import { REQUIRED } from './common-validation'; // 'common-validation' contains configuration for common client-side validation messages

const statusOptions = ['status1', 'status2', 'status3'] as const;

export const exampleReferralFields = {
  first_name: yup.string().required(REQUIRED),
  last_name: yup.string().required(REQUIRED),
  status: yup.string().required().oneOf(statusOptions),
  email: emailSchema.required(REQUIRED),
  details: yup.string().optional(),
};

export const exampleReferralSchema = yup.object().shape(exampleReferralFields);
```

Next, create an example directory, `apps/portal/src/app/form-factory-example` and add two files, `page.tsx` and `example-referral-form.config.ts`.

In `example-referral-form.config.ts`, configure the form fields, which will be passed to the `FormFactory` instance and mapped to input components:

```ts
import { type FieldConfig } from '@/types/form-field';

export const exampleReferralFormFields: FieldConfig[] = [
  {
    name: 'first_name',
    label: 'First Name',
    type: 'text',
    placeholder: 'Enter first name',
  },
  {
    name: 'last_name',
    label: 'Last Name',
    type: 'text',
    placeholder: 'Enter last name',
  },
  {
    name: 'status',
    label: 'Status',
    type: 'select',
    placeholder: 'Select a status',
    options: [
      { value: 'status1', label: 'Status 1' },
      { value: 'status2', label: 'Status 2' },
      { value: 'status3', label: 'Status 3' },
    ],
  },
  {
    name: 'email',
    label: 'Email',
    type: 'email',
    placeholder: 'Enter email',
  },
  {
    name: 'details',
    label: 'Details',
    type: 'textarea',
    placeholder: 'Enter details',
  },
];
```

Tip: take a look at `apps/portal/src/types/form-field.ts` to see all the options available for configuring the `FieldConfig` options.

In `page.tsx`:

```tsx
'use client';

import FormFactory from '@/components/custom/form-factory';
import { exampleReferralSchema } from '@/types/schemas/example';
import { exampleReferralFormFields } from './example-referral-form.config';

const Page = (): JSX.Element => {
  return (
    <div className="w-full max-w-2xl mx-auto p-4">
      <h1 className="text-3xl font-bold">Example simplified referral form</h1>

      <FormFactory
        fields={exampleReferralFormFields} // maps field config objects to form input components
        schema={exampleReferralSchema} // configures yup schema for client-side validation
        onSubmit={(values) => console.info('submit', values)} // callback to handle form submission, returns object with form values
      />
    </div>
  );
};
export default Page;
```

This creates a complete, self-contained form using the configured fields. A submit button is automatically included if no configuration is provided for action buttons. Clicking the submit button in the example above logs the data in the following format:

```ts
{first_name: 'Mary', last_name: 'Beth', status: 'status1', email: '<EMAIL>', details: 'This is a test'}
```

## Configuring Default values

If there are potential default values when the FormFactory component is initialized, it's possible to pass these values as defaults.

Example of a form with some fields pre-filled from pre-existing data

```tsx
<FormFactory
  fields={exampleReferralFormFields}
  schema={exampleReferralSchema}
  defaultValues={{
    first_name: profile.firstName,
    last_name: profile.lastName,
  }}
  // ... other props
/>
```

## Configuring Custom Submit Button and Other Action Buttons

It is possible to override the default submit button component and add a custom component, which gives control over styling, and also allows adding other action buttons that are able to handle custom callbacks.

All that is required for the "Submit" button to validate the form and call the submit callback with the form values, is setting the button `type` as "submit."

Example of a `FormFactory` instance used in a shadcn/ui `Dialog` component, with dialog components used in a custom action buttons group:

```tsx
<FormFactory
  fields={exampleReferralFormFields}
  schema={exampleReferralSchema}
  defaultValues={exampleDefaultValues}
  onSubmit={submitCallback}
  actionButtonsComponent={
    <DialogFooter className="sm:justify-end mt-4">
      <DialogClose asChild>
        <Button type="button" variant="secondary">
          Cancel
        </Button>
      </DialogClose>
      <Button type="submit" variant="default" disabled={isSubmitting}>
        {isSubmitting ? 'Submitting...' : 'Submit'}
      </Button>
    </DialogFooter>
  }
/>
```

## Configuring a Form Config with Dynamic values

Some forms may need to be configured dynamically. An example is a form with a select or button group input whose options are loaded from an API response. In this case, it's possible to create a function that takes dynamic data as arguments and returns a `FormFactory` config.

Example:

```tsx
export const getExampleReferralFormFields = (exampleDynamicOptions: FieldConfig['options']): FieldConfig[] => [
// ... other fields
  {
    name: 'status',
    label: 'Status',
    type: 'select',
    placeholder: 'Select a status',
    options: exampleDynamicOptions,
  },
// ... other fields
  },
];
```

Usage:

```tsx
<FormFactory
  fields={getExampleReferralFormFields(exampleOptions)} // pass in dynamic data as needed to configure form fields
  // ... other props
/>
```

## Showing/Hiding Fields Dynamically Based on Values in Other Fields

Fields can have optional `disabled` or `hidden` props that accept callback functions that can be used to make comparisons based on the current state of all form values.

For example, if you wanted to disable or hide a field if the value of a select field in the same form was set to `status1`:

```tsx
export const exampleReferralFormFields: FieldConfig[] = [
  // ... other fields
  {
    name: 'status',
    label: 'Status',
    type: 'select',
    placeholder: 'Select a status',
    options: [
      { value: 'status1', label: 'Status 1' },
      { value: 'status2', label: 'Status 2' },
      { value: 'status3', label: 'Status 3' },
    ],
  },
  {
    name: 'email',
    label: 'Email',
    type: 'email',
    placeholder: 'Enter email',
    // the current value of another field can be used to determine if this field should be hidden
    hidden: (values) => values?.status === 'status1',
  },
  {
    name: 'details',
    label: 'Details',
    type: 'textarea',
    placeholder: 'Enter details',
    defaultValue: 'Some details',
    // the current value of another field can be used to determine if this field should be disabled
    disabled: (values) => values?.status === 'status2',
  },
];
```

## Triggering a callback in the parent component when a field value changes

It is possible to trigger a callback in the parent component when a field value changes by passing a function to the `onChange` prop. This function will be called with the new value whenever any field value changes.

In the example below, the callback function is triggered when the value of the `category` field changes, allowing the parent to update some state that is outside of the form.

```tsx
export const exampleReferralFormFields: FieldConfig[] = [
  // ... other fields
  {
    name: 'category',
    label: 'Category',
    type: 'button-radio-group',
    options: [
      { value: 'option1', label: 'Option 1' },
      { value: 'option2', label: 'Option 2' },
    ],
    onChange: (values?: FormValues) => {
      if (values && values.category) {
        onCategoryChangeCallback(values.category === 'option1');
      }
    },
  },
  // ... other fields
];
```

## Grouping Fields in a Horizontal Row

Fields can be grouped into a horizontal row using the `row-group` type. This is useful for creating horizontal input groupings, and is designed to implement designs such as first and last name fields that appear side-by-side on desktop devices. On smaller screens, the fields will stack vertically. A configuration object of this type expects a `subFields` property, which is an array of `FieldConfig` objects that will be rendered as a row.

Example:

```tsx
const exampleReferralFormFields: FieldConfig[] = [
  // ... other fields
  {
    name: 'name_group',
    type: 'row-group',
    label: 'Your Name',
    subFields: [
      {
        name: 'first_name',
        label: '',
        type: 'text',
        placeholder: 'First',
      },
      {
        name: 'last_name',
        label: '',
        type: 'text',
        placeholder: 'Last',
      },
    ],
  },
  // ... other fields
];
```

## Adding Custom Content between Fields

It is possible to add custom content between fields by adding a `bottomContent` property to a field config object. This can be useful for adding explanatory text, additional instructions, or any other custom content.

If the content is conditional on the current form values, it's possible to add a `bottomContentHidden` property to the field config object, which accepts a callback function that returns a boolean value. If the function returns `true`, the content will be hidden.

> Tip: when setting config values as JSX elements in a config file, make sure the file extension is `.tsx` and not `.ts`!

Example:

```tsx
import CustomContent from './custom-content';

const getExampleReferralFormFields = (exampleProp: string): FieldConfig[] => {
  return [
    // ... other fields
    {
      name: 'options',
      label: 'Options',
      placeholder: 'Select an option',
      type: 'select',
      options: [
        { value: 'option1', label: 'Option 1' },
        { value: 'option2', label: 'Option 2' },
      ],
      // if building the form config dynamically via a function, it's possible to pass any props needed to the custom content component
      bottomContent: <CustomContent exampleProp={exampleProp} />,
      // an optional callback function can be added to hide the content based on the current form values, or some other condition
      bottomContentHidden: (values?: FormValues) => values?.options === 'option1',
    },
    // ... other fields
  ];
};
```
