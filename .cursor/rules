Always reference existing implementations before suggesting solutions.

Always include links to reference implementations for any suggestions.

Always write a detailed plan for code changes before execution.

Always ask for permission before implementing code changes.

Never make changes I have not explicitly asked you to make.

Suggest additional improvements you identify separately from my requested changes.

Always use functional React components, not class components.

Use Tailwind CSS for all styling.

Prefer async/await over promise chains.

Do not use any lodash methods — use native JS instead.

Always follow all rules I have given you.