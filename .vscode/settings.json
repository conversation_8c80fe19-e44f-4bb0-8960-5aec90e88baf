{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "prettier.printWidth": 120, "[powershell]": {"editor.defaultFormatter": "ms-vscode.powershell"}, "files.eol": "auto", "prettier.endOfLine": "auto", "cSpell.words": ["clsx", "datetime", "datetimecombo", "dynamicenum", "emailbody", "fullname", "longtext", "multienum", "parentenum", "postalcode", "suiteapi", "TLDR", "zipcode", "zipcodes"], "cSpell.ignorePaths": ["package-lock.json", "package.json", "pnpm-lock.yaml", "node_modules", ".git/", ".vscode", ".eslintrc.js", "apps/portal/data/mockdata"], "cSpell.ignoreRegExpList": ["/from\\s+(['\"]).*\\1/"]}