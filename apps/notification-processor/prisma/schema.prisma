generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model AAPIScore {
  id                       String   @id
  created_at               DateTime @default(now())
  created_by_id            String?
  created_by_name          String?
  updated_at               DateTime
  mom_id                   String   @unique
  constructAPreAssessment  Float?
  constructAPostAssessment Float?
  constructBPreAssessment  Float?
  constructBPostAssessment Float?
  constructCPreAssessment  Float?
  constructCPostAssessment Float?
  constructDPreAssessment  Float?
  constructDPostAssessment Float?
  constructEPreAssessment  Float?
  constructEPostAssessment Float?
  deleted_at               BigInt   @default(0)
  updated_by_id            String?
  updated_by_name          String?
  Mom                      Mom      @relation(fields: [mom_id], references: [id])
}

model ActionItem {
  id              String    @id
  created_at      DateTime  @default(now())
  updated_at      DateTime
  name            String
  description     String?
  dueDate         DateTime?
  goalId          String
  created_by_id   String?
  created_by_name String?
  doneDate        DateTime?
  deleted_at      BigInt    @default(0)
  updated_by_id   String?
  updated_by_name String?
  Goal            Goal      @relation(fields: [goalId], references: [id])
}

model Affiliate {
  id                         String               @id
  name                       String
  description                String?
  billing_address_street     String?
  billing_address_street_2   String?
  billing_address_street_3   String?
  billing_address_street_4   String?
  billing_address_city       String?
  billing_address_state      String?
  billing_address_postalcode String?
  billing_address_country    String?
  phone_office               String?
  website                    String?
  email1                     String?
  created_at                 DateTime             @default(now())
  updated_at                 DateTime
  created_by_id              String?
  created_by_name            String?
  deleted_at                 BigInt               @default(0)
  agency_nickname            String?
  contact_name               String?
  status                     AffiliateStatusType?
  updated_by_id              String?
  updated_by_name            String?
  AffiliateAgency            AffiliateAgency[]
  Mom                        Mom[]
  User                       User[]
  Track                      Track[]              @relation("AffiliateToTrack")
}

model AffiliateAgency {
  id              String    @id
  deleted_at      BigInt    @default(0)
  created_at      DateTime  @default(now())
  created_by_id   String?
  created_by_name String?
  updated_at      DateTime
  affiliate_id    String
  agency_id       String
  updated_by_id   String?
  updated_by_name String?
  Affiliate       Affiliate @relation(fields: [affiliate_id], references: [id])
  Agency          Agency    @relation(fields: [agency_id], references: [id])

  @@unique([affiliate_id, agency_id, deleted_at])
}

model Agency {
  id                                    String            @id
  name                                  String?
  agency_name                           String
  agency_phone                          String?
  address                               String?
  address_city                          String?
  address_state                         String?
  address_postalcode                    String?
  contact_first_name                    String
  contact_last_name                     String
  contact_email                         String?
  contact_phone                         String?
  created_at                            DateTime          @default(now())
  updated_at                            DateTime
  created_by_id                         String?
  created_by_name                       String?
  deleted_at                            BigInt            @default(0)
  updated_by_id                         String?
  updated_by_name                       String?
  AffiliateAgency                       AffiliateAgency[]
  Mom_Mom_agency_idToAgency             Mom[]             @relation("Mom_agency_idToAgency")
  Mom_Mom_referred_to_agency_idToAgency Mom[]             @relation("Mom_referred_to_agency_idToAgency")
}

model BenevolenceNeed {
  id                             String              @id
  created_at                     DateTime            @default(now())
  updated_at                     DateTime
  name                           String
  description                    String?
  momId                          String
  amount_c                       Float?
  provided_date_c                DateTime?
  did_address_need_c             Boolean             @default(false)
  financial_amount_gifted_c      Float?
  financial_amount_requested_c   Float?
  financial_mom_contribution_c   Float?
  financial_prevention_plan_c    String?
  is_urgent_c                    Boolean             @default(false)
  non_addressal_comment_c        String?
  notes_c                        String?
  other_is_referral_needed_c     Boolean             @default(false)
  pg_fulfillment_method_c        String?
  physical_good_monetary_value_c Float?
  resolved_date_c                DateTime?
  resolvedByUserId               String?
  created_by_id                  String?
  created_by_name                String?
  type_c                         BenevolenceNeedType @default(Financial)
  deleted_at                     BigInt              @default(0)
  updated_by_id                  String?
  updated_by_name                String?
  Mom                            Mom                 @relation(fields: [momId], references: [id])
  User                           User?               @relation(fields: [resolvedByUserId], references: [id])
  ServiceReferral                ServiceReferral[]
}

model ConnectionLog {
  id                        String            @id
  created_at                DateTime          @default(now())
  updated_at                DateTime
  summary_c                 String
  is_visible_to_advocates_c Boolean
  date_created_c            DateTime
  name                      String?
  mom_id                    String?
  user_id                   String?
  created_by_id             String?
  created_by_name           String?
  contact_method_c          ContactMethodType
  deleted_at                BigInt            @default(0)
  updated_by_id             String?
  updated_by_name           String?
  Mom                       Mom?              @relation(fields: [mom_id], references: [id])
  User                      User?             @relation(fields: [user_id], references: [id])
}

model CoordinatorNote {
  id                                        String              @id
  created_at                                DateTime            @default(now())
  updated_at                                DateTime
  name                                      String?
  description                               String
  mom_id                                    String?
  isVisibleToAdvocates                      Boolean             @default(true)
  created_by_id                             String?
  created_by_name                           String?
  type_c                                    CoordinatorNoteType
  advocate_id                               String?
  coordinator_id                            String?
  deleted_at                                BigInt              @default(0)
  updated_by_id                             String?
  updated_by_name                           String?
  User_CoordinatorNote_advocate_idToUser    User?               @relation("CoordinatorNote_advocate_idToUser", fields: [advocate_id], references: [id])
  User_CoordinatorNote_coordinator_idToUser User?               @relation("CoordinatorNote_coordinator_idToUser", fields: [coordinator_id], references: [id])
  Mom                                       Mom?                @relation(fields: [mom_id], references: [id])
}

model Document {
  id                                 String          @id
  document_name                      String?
  filecontents                       String?
  filename                           String?
  mimeType                           String?
  description                        String?
  external_url_c                     String?
  subcategory_id                     String?
  mom_id                             String?
  created_at                         DateTime        @default(now())
  updated_at                         DateTime
  created_by_id                      String?
  created_by_name                    String?
  advocate_id                        String?
  coordinator_id                     String?
  lesson_id                          String?
  lesson_template_id                 String?
  deleted_at                         BigInt          @default(0)
  is_primary_lesson_resource         Boolean         @default(false)
  s3_file_name                       String?
  updated_by_id                      String?
  updated_by_name                    String?
  User_Document_advocate_idToUser    User?           @relation("Document_advocate_idToUser", fields: [advocate_id], references: [id])
  User_Document_coordinator_idToUser User?           @relation("Document_coordinator_idToUser", fields: [coordinator_id], references: [id])
  Lesson                             Lesson?         @relation(fields: [lesson_id], references: [id])
  LessonTemplate                     LessonTemplate? @relation(fields: [lesson_template_id], references: [id])
  Mom                                Mom?            @relation(fields: [mom_id], references: [id])
  DocumentTag                        DocumentTag[]
}

model DocumentTag {
  id              String   @id
  deleted_at      BigInt   @default(0)
  created_at      DateTime @default(now())
  created_by_id   String?
  created_by_name String?
  updated_at      DateTime
  document_id     String
  tag_id          String
  updated_by_id   String?
  updated_by_name String?
  Document        Document @relation(fields: [document_id], references: [id])
  Tag             Tag      @relation(fields: [tag_id], references: [id])

  @@unique([document_id, tag_id, deleted_at])
}

model Event {
  id              String            @id
  created_at      DateTime          @default(now())
  created_by_id   String?
  created_by_name String?
  updated_at      DateTime
  event_title     String
  description     String?
  start_date      DateTime?
  end_date        DateTime?
  location        String?
  join_url        String?
  deleted_at      BigInt            @default(0)
  max_attendees   Int?
  updated_by_id   String?
  updated_by_name String?
  EventRespondent EventRespondent[]
}

model EventRespondent {
  id              String         @id
  created_at      DateTime       @default(now())
  created_by_id   String?
  created_by_name String?
  updated_at      DateTime
  event_id        String
  user_id         String?
  hasBeenInvited  Boolean        @default(false)
  didRsvp         Boolean        @default(false)
  didCheckin      Boolean        @default(false)
  deleted_at      BigInt         @default(0)
  address         String?
  childrenCount   Int            @default(0)
  email           String?
  needsTransport  Boolean        @default(false)
  phone_number    String?
  mom_id          String?
  name            String?
  updated_by_id   String?
  updated_by_name String?
  Event           Event          @relation(fields: [event_id], references: [id])
  Mom             Mom?           @relation(fields: [mom_id], references: [id])
  User            User?          @relation(fields: [user_id], references: [id])
  Notification    Notification[]
}

model Goal {
  id              String       @id
  created_at      DateTime     @default(now())
  updated_at      DateTime
  name            String
  description     String?
  dueDate         DateTime?
  momId           String
  created_by_id   String?
  created_by_name String?
  deleted_at      BigInt       @default(0)
  doneDate        DateTime?
  updated_by_id   String?
  updated_by_name String?
  ActionItem      ActionItem[]
  Mom             Mom          @relation(fields: [momId], references: [id])
}

model Lesson {
  id                        String            @id
  created_at                DateTime          @default(now())
  updated_at                DateTime
  created_by_id             String?
  created_by_name           String?
  description               String?
  duration_days             Int?
  order                     Int?
  priority                  Int?
  status                    LessonStatusType?
  title                     String
  deleted_at                BigInt            @default(0)
  source_lesson_template_id String?
  pairing_id                String?
  updated_by_id             String?
  updated_by_name           String?
  Document                  Document[]
  Pairing                   Pairing?          @relation(fields: [pairing_id], references: [id])
  LessonTemplate            LessonTemplate?   @relation(fields: [source_lesson_template_id], references: [id])
  SessionNote               SessionNote[]
}

model LessonTemplate {
  id              String     @id
  created_at      DateTime   @default(now())
  updated_at      DateTime
  created_by_id   String?
  created_by_name String?
  description     String?
  duration_days   Int?
  order           Int?
  priority        Int?
  title           String
  track_id        String
  deleted_at      BigInt     @default(0)
  updated_by_id   String?
  updated_by_name String?
  Document        Document[]
  Lesson          Lesson[]
  Track           Track      @relation(fields: [track_id], references: [id])
}

model Mom {
  id                                       String                   @id
  first_name                               String?
  last_name                                String?
  phone_other                              String?
  currently_pregnant_c                     String?
  number_of_children_c                     Int                      @default(0)
  need_details_c                           String?
  what_else_c                              String?
  caregiver_type_c                         String?
  gender_c                                 String?
  email1                                   String?
  referral_type_c                          String?
  supports_court_order_c                   Boolean?
  service_selected_c                       String?
  created_by_name                          String?
  account_name                             String?
  affiliate_id                             String?
  agency_id                                String?
  consent_obtained_c                       Boolean?
  referring_contact_first_name_c           String?
  referring_contact_last_name_c            String?
  referring_contact_email_c                String?
  referring_contact_phone_c                String?
  birthdate                                DateTime?
  discharge_reason_c                       String?
  closed_date_c                            DateTime?
  language_notes_c                         String?
  primary_address_street                   String?
  primary_address_city                     String?
  primary_address_state                    String?
  primary_address_postalcode               String?
  primary_address_county_c                 String?
  connected_benevolance_c                  Boolean?
  connected_childcare_c                    Boolean?
  connected_closet_c                       Boolean?
  connected_education_c                    Boolean?
  connected_health_c                       Boolean?
  connected_housing_c                      Boolean?
  connected_legal_c                        Boolean?
  connected_mental_health_c                Boolean?
  connected_substance_c                    Boolean?
  date_entered                             String?
  address_access_c                         String?
  assigned_user_id                         String?
  created_at                               DateTime                 @default(now())
  updated_at                               DateTime
  created_by_id                            String?
  deleted_at                               BigInt                   @default(0)
  prospect_status                          ProspectStatus?
  referral_sub_status                      ReferralSubStatus?
  referred_to_agency_id                    String?
  referred_to_agency_reason                String?
  status                                   MomStatus?
  sms_message_opt_in                       Boolean?
  language_preference_c                    Language?
  languages_c                              Language[]
  children_in_home                         Int                      @default(0)
  cultural_heritage_c                      String?
  emergency_contact_name_c                 String?
  emergency_contact_number_c               String?
  emergency_contact_relation_c             String?
  languages                                String?
  martial_status                           String?
  number_of_children_in_home_c             Int                      @default(0)
  phone_alternate_c                        String?
  pregnant_due_date                        DateTime?
  primary_address_street_two_c             String?
  race_c                                   String?
  updated_by_id                            String?
  updated_by_name                          String?
  photoUrl                                 String?
  photoS3FileName                          String?
  thumbnailUrl                             String?
  thumbnailS3FileName                      String?
  iconUrl                                  String?
  iconS3FileName                           String?
  preferred_contact_method_c               CommunicationPreference?
  status_description                       String?
  AAPIScore                                AAPIScore?
  AssessmentResult                         AssessmentResult[]
  BenevolenceNeed                          BenevolenceNeed[]
  Child                                    Child[]
  ConnectionLog                            ConnectionLog[]
  CoordinatorNote                          CoordinatorNote[]
  Document                                 Document[]
  EventRespondent                          EventRespondent[]
  Goal                                     Goal[]
  Affiliate                                Affiliate?               @relation(fields: [affiliate_id], references: [id])
  Agency_Mom_agency_idToAgency             Agency?                  @relation("Mom_agency_idToAgency", fields: [agency_id], references: [id])
  User                                     User?                    @relation(fields: [assigned_user_id], references: [id])
  Agency_Mom_referred_to_agency_idToAgency Agency?                  @relation("Mom_referred_to_agency_idToAgency", fields: [referred_to_agency_id], references: [id])
  Notification                             Notification[]
  Pairing                                  Pairing[]
  ServiceReferral                          ServiceReferral[]
  Session                                  Session[]
  WellnessAssessment                       WellnessAssessment[]
}

model Notification {
  id                                        String               @id
  created_at                                DateTime             @default(now())
  created_by_id                             String?
  created_by_name                           String?
  updated_at                                DateTime
  mom_id                                    String?
  recipient_user_id                         String?
  sending_user_id                           String?
  date_sent                                 DateTime?
  date_received                             DateTime?
  date_read                                 DateTime?
  date_replied                              DateTime?
  deleted_at                                BigInt               @default(0)
  failure_reason                            String?
  template                                  NotificationTemplate
  status                                    NotificationStatus
  template_params                           Json?
  updated_by_id                             String?
  updated_by_name                           String?
  event_respondent_id                       String?
  EventRespondent                           EventRespondent?     @relation(fields: [event_respondent_id], references: [id])
  Mom                                       Mom?                 @relation(fields: [mom_id], references: [id])
  User_Notification_recipient_user_idToUser User?                @relation("Notification_recipient_user_idToUser", fields: [recipient_user_id], references: [id])
  User_Notification_sending_user_idToUser   User?                @relation("Notification_sending_user_idToUser", fields: [sending_user_id], references: [id])
}

model Pairing {
  id                              String                            @id
  created_at                      DateTime                          @default(now())
  updated_at                      DateTime
  name                            String
  description                     String?
  momId                           String?
  advocateUserId                  String?
  trackId                         String?
  created_by_id                   String?
  created_by_name                 String?
  status                          PairingStatusType?
  deleted_at                      BigInt                            @default(0)
  complete_reason_sub_status      CompleteReasonSubStatusType?
  discharge_incomplete_sub_status DischargeIncompleteSubStatusType?
  in_program_track_sub_status     InProgramTrackSubStatusType?
  incomplete_reason_sub_status    IncompleteReasonSubStatusType?
  track_status                    TrackStatusType?
  updated_by_id                   String?
  updated_by_name                 String?
  Lesson                          Lesson[]
  User                            User?                             @relation(fields: [advocateUserId], references: [id])
  Mom                             Mom?                              @relation(fields: [momId], references: [id])
  Track                           Track?                            @relation(fields: [trackId], references: [id])
  Session                         Session[]
}

model Role {
  id              String     @id
  created_at      DateTime   @default(now())
  updated_at      DateTime
  name            String     @unique
  description     String?
  key             String     @unique
  created_by_id   String?
  created_by_name String?
  deleted_at      BigInt     @default(0)
  updated_by_id   String?
  updated_by_name String?
  UserRole        UserRole[]
}

model Session {
  id                     String             @id
  created_at             DateTime           @default(now())
  updated_at             DateTime
  date_start             DateTime?
  date_end               DateTime?
  description            String?
  name                   String?
  location               String?
  join_url               String?
  created_by_id          String?
  created_by_name        String?
  session_type           SessionType?
  status                 SessionStatusType?
  deleted_at             BigInt             @default(0)
  pairing_id             String?
  session_group_id       String?
  updated_by_id          String?
  updated_by_name        String?
  has_reminder_been_sent Boolean            @default(false)
  assigned_user_id       String?
  mom_id                 String?
  User                   User?              @relation(fields: [assigned_user_id], references: [id])
  Mom                    Mom?               @relation(fields: [mom_id], references: [id])
  Pairing                Pairing?           @relation(fields: [pairing_id], references: [id])
  SessionGroup           SessionGroup?      @relation(fields: [session_group_id], references: [id])
  SessionNote            SessionNote?
}

model SessionGroup {
  id              String    @id
  deleted_at      BigInt    @default(0)
  created_at      DateTime  @default(now())
  created_by_id   String?
  created_by_name String?
  updated_at      DateTime
  updated_by_id   String?
  updated_by_name String?
  Session         Session[]
}

model SessionNote {
  id                        String                       @id
  created_at                DateTime                     @default(now())
  updated_at                DateTime
  created_by_id             String?
  created_by_name           String?
  attendance_and_promptness AttendanceAndPromptnessType?
  date_submitted_c          DateTime?
  description               String?
  moms_engagement_c         MomEngagementType?
  name                      String
  new_attempt               Boolean?
  new_attempt_example       String?
  note                      String?
  session_id                String                       @unique
  status                    SessionNoteStatusType?
  deleted_at                BigInt                       @default(0)
  covered_lesson_id         String?
  updated_by_id             String?
  updated_by_name           String?
  Lesson                    Lesson?                      @relation(fields: [covered_lesson_id], references: [id])
  Session                   Session                      @relation(fields: [session_id], references: [id])
}

model Tag {
  id              String        @id
  deleted_at      BigInt        @default(0)
  created_at      DateTime      @default(now())
  created_by_id   String?
  created_by_name String?
  updated_at      DateTime
  name            String
  description     String?
  updated_by_id   String?
  updated_by_name String?
  DocumentTag     DocumentTag[]

  @@unique([name, deleted_at])
}

model Track {
  id                     String           @id
  created_at             DateTime         @default(now())
  updated_at             DateTime
  description            String?
  created_by_id          String?
  created_by_name        String?
  connection_description String?
  language_type          LanguageType?
  mom_summary            String?
  title                  String
  track_summary          String?
  deleted_at             BigInt           @default(0)
  updated_by_id          String?
  updated_by_name        String?
  LessonTemplate         LessonTemplate[]
  Pairing                Pairing[]
  Affiliate              Affiliate[]      @relation("AffiliateToTrack")
}

model User {
  id                                                   String                   @id
  created_at                                           DateTime                 @default(now())
  updated_at                                           DateTime
  email                                                String
  passwordHash                                         String
  refreshTokenHash                                     String?
  username                                             String
  firstName                                            String?
  lastName                                             String?
  affiliateId                                          String?
  advocate_capacity_for_moms                           Int?
  date_of_birth                                        DateTime?
  home_church                                          String?
  phone                                                String?
  secondary_email                                      String?
  secondary_phone                                      String?
  sms_message_opt_in                                   Boolean?
  timezone                                             String?
  created_by_id                                        String?
  created_by_name                                      String?
  address_city                                         String?
  address_postalcode                                   String?
  address_state                                        String?
  address_street                                       String?
  description                                          String?
  phone_home                                           String?
  phone_mobile                                         String?
  phone_other                                          String?
  phone_work                                           String?
  deleted_at                                           BigInt                   @default(0)
  availability                                         String?
  language_notes_c                                     String?
  language_preference_c                                Language?
  languages_c                                          Language[]
  mustChangePassword                                   Boolean                  @default(false)
  updated_by_id                                        String?
  updated_by_name                                      String?
  photoUrl                                             String?
  photoS3FileName                                      String?
  thumbnailUrl                                         String?
  thumbnailS3FileName                                  String?
  iconUrl                                              String?
  iconS3FileName                                       String?
  communication_preference                             CommunicationPreference?
  status                                               UserStatus?
  advocate_status                                      AdvocateStatus?
  hasMoodleAccount                                     Boolean                  @default(false)
  moodleUserId                                         String?
  moodlePassword                                       String?
  AdvocateOnboarding                                   AdvocateOnboarding?
  BenevolenceNeed                                      BenevolenceNeed[]
  ConnectionLog                                        ConnectionLog[]
  CoordinatorNote_CoordinatorNote_advocate_idToUser    CoordinatorNote[]        @relation("CoordinatorNote_advocate_idToUser")
  CoordinatorNote_CoordinatorNote_coordinator_idToUser CoordinatorNote[]        @relation("CoordinatorNote_coordinator_idToUser")
  Document_Document_advocate_idToUser                  Document[]               @relation("Document_advocate_idToUser")
  Document_Document_coordinator_idToUser               Document[]               @relation("Document_coordinator_idToUser")
  EventRespondent                                      EventRespondent[]
  MagicLink                                            MagicLink[]
  Mom                                                  Mom[]
  Notification_Notification_recipient_user_idToUser    Notification[]           @relation("Notification_recipient_user_idToUser")
  Notification_Notification_sending_user_idToUser      Notification[]           @relation("Notification_sending_user_idToUser")
  Pairing                                              Pairing[]
  Session                                              Session[]
  Affiliate                                            Affiliate?               @relation(fields: [affiliateId], references: [id])
  UserRole                                             UserRole[]
  User_A                                               User[]                   @relation("AdvocateToCoordinator")
  User_B                                               User[]                   @relation("AdvocateToCoordinator")

  @@unique([username, deleted_at])
}

model UserRole {
  id              String   @id
  created_at      DateTime @default(now())
  updated_at      DateTime
  user_id         String
  role_id         String
  created_by_id   String?
  created_by_name String?
  deleted_at      BigInt   @default(0)
  updated_by_id   String?
  updated_by_name String?
  Role            Role     @relation(fields: [role_id], references: [id])
  User            User     @relation(fields: [user_id], references: [id])
}

model WellnessAssessment {
  id                          String    @id
  cc_affordability            String?
  cc_backup_care              String?
  cc_childcare_safety         Boolean?
  cc_ed_concerns              Boolean?
  cc_health_ins               String?
  cc_med_access               String?
  cc_notes                    String?
  cc_reliable_care            String?
  cc_school_enrollment        String?
  cc_special_ed               Boolean?
  cc_special_med              String?
  completed_ahead             Boolean?
  completed_date              DateTime?
  cw_active_involvement       String?
  cw_date_of_involvement      DateTime?
  cw_fp_goal                  String?
  cw_fp_impact                String?
  cw_home_status              String?
  cw_involvement_as_child     String?
  cw_involvement_as_mom       Boolean?
  cw_maltreatment_type        String?
  cw_notes                    String?
  emp_afford_food             String?
  emp_challenges              String?
  emp_concrete_pfs_total      String?
  emp_diff_manage_bills       String?
  emp_difficulty              String?
  emp_duration_current        String?
  emp_emergency_funds         String?
  emp_fin_notes               String?
  emp_fin_struggles           String?
  emp_highest_ed              String?
  emp_notes                   String?
  emp_status                  String?
  emp_support_needed          String?
  emp_support_received        String?
  emp_work_eligibility        String?
  naa_child_behavior          String?
  res_communication           String?
  naa_discipline              String?
  naa_emotions                String?
  res_fr_pfs_summary          String?
  naa_notes                   String?
  naa_nurture_pfs_summary     String?
  res_positive_outlook        String?
  naa_power_struggles         String?
  res_traditions              String?
  res_attendance              String?
  res_belief_self             String?
  res_fulfillment             String?
  res_goodness                String?
  res_happiness               String?
  soc_faith_id                String?
  res_notes                   String?
  res_overall_sat             String?
  res_purpose                 String?
  res_purpose_summary         String?
  res_sacrifice               String?
  res_sat_summary             String?
  res_support                 Boolean?
  res_virtue_summary          String?
  home_category               String?
  home_name_on_lease          String?
  home_notes                  String?
  home_perc_toward            String?
  home_recent_homeless        String?
  home_risk_in_home           Boolean?
  home_safe                   Boolean?
  home_security_concerns      String?
  home_type                   String?
  home_voucher                String?
  legal_current               String?
  legal_notes                 String?
  legal_plan_childcare        String?
  legal_rep                   String?
  legal_rep_access            String?
  meeting_method              String?
  soc_dynamics                String?
  soc_length                  String?
  soc_rel_notes               String?
  soc_resolve_arguments       String?
  soc_status                  String?
  soc_tension                 String?
  soc_wast_sf                 String?
  soc_frs_content_with_rels   String?
  soc_frs_rel_sat             String?
  soc_frs_summary             String?
  soc_notes                   String?
  soc_pfs_emergency_contact   String?
  soc_pfs_summary_score       String?
  soc_pfs_support_goals       String?
  soc_pfs_supportive_advice   String?
  soc_pfs_supportive_rels     String?
  soc_trusted_network         String?
  staff_name_c                String?
  start_date_c                DateTime?
  subs_notes                  String?
  subs_recency                String?
  subs_support_needed         String?
  subs_treatment_history      Boolean?
  trnprt_access               String?
  trnprt_affordable           String?
  trnprt_license              String?
  trnprt_notes                String?
  trnprt_private_safety       String?
  trnprt_seat_access          Boolean?
  well_counseling_interest    String?
  well_counseling_past        String?
  well_health_summary         String?
  well_mental_health          String?
  well_notes                  String?
  well_phq_q1                 String?
  well_phq_q2                 String?
  well_phq_q3                 String?
  well_phq_q4                 String?
  well_phq_q5                 String?
  well_phq_q6                 String?
  well_phq_q7                 String?
  well_phq_q8                 String?
  well_phq_q9                 String?
  well_phq_total              String?
  well_phys_health            String?
  created_at                  DateTime  @default(now())
  updated_at                  DateTime
  created_by_id               String?
  created_by_name             String?
  deleted_at                  BigInt    @default(0)
  res_score                   Int?
  legal_score                 Int?
  subs_score                  Int?
  cc_score                    Int?
  cw_score                    Int?
  emp_score                   Int?
  naa_score                   Int?
  home_score                  Int?
  soc_score                   Int?
  trnprt_score                Int?
  well_score                  Int?
  mom_id                      String
  cw_allegations              Boolean?
  updated_by_id               String?
  updated_by_name             String?
  res_reflections             String?
  soc_rel_reflections         String?
  soc_reflections             String?
  naa_reflections             String?
  well_counseling             String?
  well_counseling_summary     String?
  well_discouragement         String?
  well_discouragement_summary String?
  well_gad_q1                 String?
  well_gad_q2                 String?
  well_gad_q3                 String?
  well_gad_q4                 String?
  well_gad_q5                 String?
  well_gad_q6                 String?
  well_gad_q7                 String?
  well_gad_total              String?
  well_health_insurance       Boolean?
  well_medical_care           String?
  well_reflections            String?
  well_strategies             String?
  well_strategies_summary     String?
  Mom                         Mom       @relation(fields: [mom_id], references: [id])
}

model Child {
  id                                String    @id
  deleted_at                        BigInt    @default(0)
  created_at                        DateTime  @default(now())
  created_by_id                     String?
  created_by_name                   String?
  updated_at                        DateTime
  first_name                        String
  birthdate                         DateTime?
  gender                            String?
  lives_with                        String?
  legal_custody_status              String?
  father_involved                   String[]
  father_involvement                String?
  additional_info                   String?
  mom_id                            String
  active_child_welfare_involvement  String?
  date_of_child_welfare_involvement DateTime?
  family_preservation_goal          String?
  family_preservation_impact        String?
  updated_by_id                     String?
  updated_by_name                   String?
  Mom                               Mom       @relation(fields: [mom_id], references: [id])
}

model ServiceReferral {
  id                  String           @id
  created_at          DateTime         @default(now())
  created_by_id       String?
  created_by_name     String?
  updated_at          DateTime
  start_date          DateTime
  provider            String
  mom_id              String
  deleted_at          BigInt           @default(0)
  service             ServiceType
  outcome             OutcomeType
  updated_by_id       String?
  updated_by_name     String?
  benevolence_need_id String?
  BenevolenceNeed     BenevolenceNeed? @relation(fields: [benevolence_need_id], references: [id])
  Mom                 Mom              @relation(fields: [mom_id], references: [id])

  @@index([benevolence_need_id])
  @@index([mom_id])
}

model AuditLog {
  id              String      @id
  deleted_at      BigInt      @default(0)
  created_at      DateTime    @default(now())
  created_by_id   String?
  created_by_name String?
  updated_at      DateTime
  table           String
  action          AuditAction
  data            Json
  updated_by_id   String?
  updated_by_name String?
}

model AdvocateOnboarding {
  id                       String       @id
  deleted_at               BigInt       @default(0)
  created_at               DateTime     @default(now())
  created_by_id            String?
  created_by_name          String?
  updated_at               DateTime
  updated_by_id            String?
  updated_by_name          String?
  referredBy               String?
  canCompleteBackground    Boolean      @default(true)
  interests                Interest[]
  hasMultiLang             Boolean      @default(false)
  languages                Language[]
  hasExpCrisis             Boolean      @default(false)
  hasExpFoster             Boolean      @default(false)
  hasExpVictims            Boolean      @default(false)
  hasExpWelfare            Boolean      @default(false)
  hasExpChildren           String?
  personalNote             String?
  groupPref                String?
  parentingNote            String?
  availability             String?
  interviewDate            DateTime?
  interviewStartTime       String?
  interviewEndTime         String?
  interviewMeetingType     MeetingType?
  interviewLocation        String?
  reviewNote               String?
  reviewedBy               String?
  reviewedAt               DateTime?
  completedAt              DateTime?
  userId                   String?      @unique
  trackingToken            String?      @unique
  backgroundCheckCompleted Boolean      @default(false)
  User                     User?        @relation(fields: [userId], references: [id])
}

model MagicLink {
  id              String    @id
  deleted_at      BigInt    @default(0)
  created_at      DateTime  @default(now())
  created_by_id   String?
  created_by_name String?
  updated_at      DateTime
  updated_by_id   String?
  updated_by_name String?
  user_id         String
  token           String    @unique
  type            String    @default("forgot-password")
  expired         Boolean   @default(false)
  expires_at      DateTime
  used_at         DateTime?
  User            User      @relation(fields: [user_id], references: [id])
}

model Assessment {
  id                 String               @id
  deleted_at         BigInt               @default(0)
  created_at         DateTime             @default(now())
  created_by_id      String?
  created_by_name    String?
  updated_at         DateTime
  updated_by_id      String?
  updated_by_name    String?
  name               String
  order              Int
  AssessmentQuestion AssessmentQuestion[]
  AssessmentResult   AssessmentResult[]
}

model AssessmentConstruct {
  id                 String               @id
  deleted_at         BigInt               @default(0)
  created_at         DateTime             @default(now())
  created_by_id      String?
  created_by_name    String?
  updated_at         DateTime
  updated_by_id      String?
  updated_by_name    String?
  name               String
  order              Int
  AssessmentQuestion AssessmentQuestion[]
}

model AssessmentQuestion {
  id                               String                             @id
  deleted_at                       BigInt                             @default(0)
  created_at                       DateTime                           @default(now())
  created_by_id                    String?
  created_by_name                  String?
  updated_at                       DateTime
  updated_by_id                    String?
  updated_by_name                  String?
  question                         String
  order                            Int
  responseType                     ResponseType
  assessmentId                     String
  assessmentConstructId            String?
  AssessmentConstruct              AssessmentConstruct?               @relation(fields: [assessmentConstructId], references: [id])
  Assessment                       Assessment                         @relation(fields: [assessmentId], references: [id])
  AssessmentResultQuestionResponse AssessmentResultQuestionResponse[]
}

model AssessmentResult {
  id                               String                             @id
  deleted_at                       BigInt                             @default(0)
  created_at                       DateTime                           @default(now())
  created_by_id                    String?
  created_by_name                  String?
  updated_at                       DateTime
  updated_by_id                    String?
  updated_by_name                  String?
  assessmentId                     String
  momId                            String
  completedAt                      DateTime?
  Assessment                       Assessment                         @relation(fields: [assessmentId], references: [id])
  Mom                              Mom                                @relation(fields: [momId], references: [id])
  AssessmentResultQuestionResponse AssessmentResultQuestionResponse[]
}

model AssessmentResultQuestionResponse {
  id                   String             @id
  deleted_at           BigInt             @default(0)
  created_at           DateTime           @default(now())
  created_by_id        String?
  created_by_name      String?
  updated_at           DateTime
  updated_by_id        String?
  updated_by_name      String?
  assessmentResultId   String
  assessmentQuestionId String
  intResponse          Int?
  stringResponse       String?
  AssessmentQuestion   AssessmentQuestion @relation(fields: [assessmentQuestionId], references: [id])
  AssessmentResult     AssessmentResult   @relation(fields: [assessmentResultId], references: [id])
}

enum AffiliateStatusType {
  Active
  Inactive
}

enum AttendanceAndPromptnessType {
  On_Time
  Late
  No_Show
}

enum BenevolenceNeedType {
  Financial
  Physical
  Other
}

enum CompleteReasonSubStatusType {
  completed_full_track
  completed_without_support_sessions
  completed_without_post_assessment
}

enum ContactMethodType {
  Call
  SMS_Text
  Email
  In_Person
  Video
}

enum CoordinatorNoteType {
  safety_or_concern_update
  court_update
  interview_advocate
}

enum DischargeIncompleteSubStatusType {
  track_requirements_unmet
  client_choice
  relocated
  other
}

enum InProgramTrackSubStatusType {
  waiting_to_begin
  session_in_progress
}

enum IncompleteReasonSubStatusType {
  no_advocate
  extended_wait
  priorities_shifted
  achieved_outcomes
}

enum Language {
  english
  spanish
  chinese_mandarin
  vietnamese
  tagalog
  arabic
  korean
  russian
  french
  hindi
  portuguese
  bengali
  urdu
  german
  haitian_creole
  polish
  italian
  japanese
  persian_farsi
  gujarati
  other
}

enum LanguageType {
  english
  spanish
}

enum LessonStatusType {
  not_started
  in_progress
  completed
}

enum MomEngagementType {
  Full
  Partial
  None
}

enum MomStatus {
  inactive
  active
}

enum PairingStatusType {
  waiting_to_be_paired
  paired
  pairing_complete
}

enum ProspectStatus {
  prospect
  engaged_in_program
  did_not_engage_in_program
  prospect_intake_scheduled
}

enum ReferralSubStatus {
  unable_to_contact
  client_declined_services
  ineligible_for_program
  duplicate_referral
  referred_to_alternate_program
}

enum SessionNoteStatusType {
  submitted
  new
  rejected
  approved
}

enum SessionStatusType {
  Planned
  Held
  NotHeld
}

enum SessionType {
  Support_Session
  Track_Session
  Referral_Session
}

enum TrackStatusType {
  in_program
  discharged_incomplete
  complete
}

enum NotificationStatus {
  pending
  processing
  sent
  failed
  draft
}

enum NotificationTemplate {
  mom_assigned_to_coordinator
  new_referral_supervisor
  new_wellness_assessment_coordinator
  session_note_rejected
  mom_ready_for_advocate_assignment
  benevolence_need_submitted
  advocate_invite_training
  session_note_submitted
  forgot_password
  event_registration
  upcoming_session_reminder
  wellness_assessment_self_intake_mom
  new_mom_assigned_advocate
  new_advocate_assigned_to_mom
  mom_accepted_into_program
}

enum Interest {
  Advocate
  Volunteer
}

enum OutcomeType {
  successful
  unsuccessful
  unknown
}

enum ServiceType {
  benevolence
  care_communities
  childcare
  closet_visit
  crisis_resources
  education
  group_parenting
  health
  housing
  legal
  mental_health
  substance
  therapy
}

enum AuditAction {
  Create
  Update
  Delete
}

enum AdvocateStatus {
  Applied
  Interested
  Rejected
  Inactive
  Active
  In_Training
  Awaiting_Pairing
  Awaiting_Application
}

enum CommunicationPreference {
  text_message
  email
  both
}

enum MeetingType {
  Virtual
  InPerson
}

enum UserStatus {
  Active
  Inactive
}

enum ResponseType {
  int
  string
}
