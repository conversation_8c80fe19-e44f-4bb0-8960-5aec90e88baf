import { afterEach, beforeEach, describe, expect, it, jest } from '@jest/globals';

jest.mock('./_lib/NotificationToAwsCommandInputAdapter');
jest.mock('./_lib/senders/AwsNotificationSender');
jest.mock('./_lib/NotificationRepository');
jest.mock('./_lib/NotificationServiceFactory');

import { NotificationToAwsCommandInputAdapter } from './_lib/NotificationToAwsCommandInputAdapter';
import { AwsNotificationSender } from './_lib/senders/AwsNotificationSender';
import { NotificationRepository } from './_lib/NotificationRepository';
import { NotificationServiceFactory } from './_lib/NotificationServiceFactory';
import { PrismaClient } from '@prisma/client';

describe('processNotifications', () => {
  let consoleInfoSpy: jest.SpiedFunction<typeof console.info>;

  beforeEach(() => {
    // Mock console.info before each test
    consoleInfoSpy = jest.spyOn(console, 'info').mockImplementation(() => {});

    // Mock Date to return a fixed timestamp for consistent testing
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2024-01-01'));
  });

  afterEach(() => {
    // Clean up mocks after each test
    consoleInfoSpy.mockRestore();
    jest.resetAllMocks();
    jest.useRealTimers();
  });

  it('should log the current timestamp when processing notifications', async () => {
    // Import the function that runs the processor
    const { processNotifications } = await import('./process-notifications');

    const mockNotificationRepository = new NotificationRepository(
      {} as PrismaClient,
    ) as jest.Mocked<NotificationRepository>;

    mockNotificationRepository.fetchPendingNotifications.mockResolvedValue([]);

    // Run the processor
    await processNotifications(
      new NotificationToAwsCommandInputAdapter(),
      new AwsNotificationSender('', '', ''),
      mockNotificationRepository,
      new NotificationServiceFactory({} as PrismaClient),
    );

    // Verify the console output
    expect(consoleInfoSpy).toHaveBeenCalledWith('Notification processor ran at 2024-01-01T00:00:00.000Z');
  });
});

// Awaited<ReturnType<NotificationRepository['fetchPendingNotifications']>>[number]
