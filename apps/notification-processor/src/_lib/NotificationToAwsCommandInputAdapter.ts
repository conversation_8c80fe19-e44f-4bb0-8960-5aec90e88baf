import { SendEmailCommandInput } from '@aws-sdk/client-ses';
import templates from './NotificationTemplates';
import { compile as compileHandleBars } from 'handlebars';
import { isValidJsObject } from '@suiteapi/models';
import { Notification } from '@prisma/client';
import { NotificationRecipient } from './recipients/NotificationRecipient';
import { PublishCommandInput } from '@aws-sdk/client-sns';
import { promises as fs } from 'fs';

export class NotificationToAwsCommandInputAdapter {
  async toSendEmailCommandInput(
    notification: Notification,
    recipient: NotificationRecipient,
  ): Promise<SendEmailCommandInput | null> {
    const emailTemplate = templates[notification.template]?.email;
    if (!emailTemplate) {
      return null;
    }

    let body: string | undefined;
    if (emailTemplate.bodyFile) {
      const filePath = `${__dirname}/template-bodies/${emailTemplate.bodyFile}`;
      body = await fs.readFile(filePath, 'utf-8');
    } else {
      body = emailTemplate.body;
    }

    if (!body) {
      throw new Error(`No email body or bodyFile found for template: ${notification.template}`);
    }

    const subject = this.buildMessageFromTemplate(emailTemplate.subject, notification, recipient);
    const msg = this.buildMessageFromTemplate(body, notification, recipient);

    return {
      Destination: {
        ToAddresses: [recipient.email],
      },
      Source: process.env.NOTIFICATION_SOURCE_EMAIL,
      Message: {
        Subject: {
          Data: subject,
        },
        Body: {
          Html: {
            Data: msg,
          },
        },
      },
    };
  }

  toSendSmsCommandInput(notification: Notification, recipient: NotificationRecipient): PublishCommandInput | null {
    const smsTemplate = templates[notification.template]?.sms;
    if (!smsTemplate) {
      return null;
    }

    const smsMsg = this.buildMessageFromTemplate(smsTemplate, notification, recipient);

    return {
      Message: smsMsg,
      PhoneNumber: recipient.phone,
    };
  }

  private buildMessageFromTemplate(
    handlbarsTemplate: string,
    notification: Notification,
    recipient: NotificationRecipient,
  ): string {
    const compiledTemplate = compileHandleBars(handlbarsTemplate);
    const msgParams = {
      websiteUrlBase: process.env.WEBSITE_URL_BASE,
      moodleUrlBase: process.env.MOODLE_URL_BASE,
      ...(isValidJsObject(notification.template_params) ? (notification.template_params as object) : {}),
      ...recipient.templateParams,
    };

    const msg = compiledTemplate(msgParams);
    if (!msg) {
      throw new Error(`Failed to compile template ${notification.template}`);
    }

    return msg;
  }
}
