import { PrismaClient } from '@prisma/client';

export class SessionRepository {
  constructor(private readonly prisma: PrismaClient) {}

  async fetchSessionsToRemind() {
    return this.prisma.session.findMany({
      where: {
        has_reminder_been_sent: false,
        date_start: {
          lt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
        },
      },
      include: {
        Pairing: {
          include: {
            Mom: true,
            User: true,
          },
        },
      },
    });
  }

  async updateSessionReminderSent(sessionId: string) {
    return this.prisma.session.update({
      where: { id: sessionId },
      data: { has_reminder_been_sent: true },
    });
  }
}
