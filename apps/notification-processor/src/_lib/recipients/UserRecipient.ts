import { NotificationRecipient, NotificationRecipientTemplateParams } from './NotificationRecipient';
import { User } from '@prisma/client';

export class UserRecipient extends NotificationRecipient {
  constructor(private readonly recipient: User) {
    super();
  }

  get shouldSendEmail(): boolean {
    return (
      (this.recipient.communication_preference == 'email' || this.recipient.communication_preference == 'both') &&
      !!this.email
    );
  }

  get shouldSendSms(): boolean {
    return (
      (this.recipient.communication_preference == 'text_message' ||
        this.recipient.communication_preference == 'both') &&
      this.recipient.sms_message_opt_in == true &&
      !!this.phone
    );
  }

  get email(): string {
    return this.recipient.email ?? '';
  }

  get phone(): string {
    return this.formatPhoneNumber(this.recipient.phone_mobile);
  }

  get templateParams(): NotificationRecipientTemplateParams {
    return {
      recipientEmail: this.email,
      recipientPhone: this.recipient.phone_mobile ?? '',
      recipientFirstName: this.recipient.firstName ?? '',
      recipientLastName: this.recipient.lastName ?? '',
    };
  }
}
