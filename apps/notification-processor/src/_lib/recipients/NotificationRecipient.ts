export interface NotificationRecipientTemplateParams {
  recipientEmail: string;
  recipientPhone: string;
  recipientFirstName: string;
  recipientLastName: string;
}

export abstract class NotificationRecipient {
  abstract get shouldSendEmail(): boolean;
  abstract get shouldSendSms(): boolean;
  abstract get email(): string;
  abstract get phone(): string;
  abstract get templateParams(): NotificationRecipientTemplateParams;

  protected formatPhoneNumber(phone: string | null | undefined): string {
    if (!phone) return '';

    // Check if the phone number is already in E.164 format (+[11-13 digits])
    const e164Regex = /^\+\d{11,13}$/;
    if (e164Regex.test(phone)) {
      return phone;
    }

    // Remove all non-digit characters except plus sign
    const cleaned = phone.replace(/[^\d+]/g, '');

    // Make sure we have a 10-digit number
    if (cleaned.length !== 10) return '';

    // AWS SNS requires E.164 format (+1XXXXXXXXXX for US numbers)
    return '+1' + cleaned;
  }
}
