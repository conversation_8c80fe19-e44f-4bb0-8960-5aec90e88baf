import { NotificationRecipientTemplateParams } from './NotificationRecipient';

import { Mom } from '@prisma/client';
import { NotificationRecipient } from './NotificationRecipient';

export class MomRecipient extends NotificationRecipient {
  constructor(private readonly recipient: Mom) {
    super();
  }

  get shouldSendEmail(): boolean {
    return (
      (this.recipient.preferred_contact_method_c == 'email' || this.recipient.preferred_contact_method_c == 'both') &&
      !!this.email
    );
  }

  get shouldSendSms(): boolean {
    return (
      (this.recipient.preferred_contact_method_c == 'text_message' ||
        this.recipient.preferred_contact_method_c == 'both') &&
      !!this.phone
    );
  }

  get email(): string {
    return this.recipient.email1 ?? '';
  }

  get phone(): string {
    return this.formatPhoneNumber(this.recipient.phone_other);
  }

  get templateParams(): NotificationRecipientTemplateParams {
    return {
      recipientEmail: this.email,
      recipientPhone: this.phone,
      recipientFirstName: this.recipient.first_name ?? '',
      recipientLastName: this.recipient.last_name ?? '',
    };
  }
}
