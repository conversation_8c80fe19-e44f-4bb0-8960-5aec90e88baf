import { NotificationRecipientTemplateParams } from './NotificationRecipient';

import { EventRespondent, User, Mom } from '@prisma/client';
import { NotificationRecipient } from './NotificationRecipient';
import { UserRecipient } from './UserRecipient';
import { MomRecipient } from './MomRecipient';

export class EventRespondentRecipient extends NotificationRecipient {
  private readonly userRecipient?: UserRecipient;
  private readonly momRecipient?: MomRecipient;

  constructor(private readonly recipient: EventRespondent & { User?: User | null; Mom?: Mom | null }) {
    super();
    this.userRecipient = this.recipient.User ? new UserRecipient(this.recipient.User) : undefined;
    this.momRecipient = this.recipient.Mom ? new MomRecipient(this.recipient.Mom) : undefined;
  }

  get shouldSendEmail(): boolean {
    return !!this.userRecipient || !!this.momRecipient || !!this.recipient.email;
  }

  get shouldSendSms(): boolean {
    return !!this.userRecipient || !!this.momRecipient || !!this.recipient.phone_number;
  }

  get email(): string {
    if (this.userRecipient) {
      return this.userRecipient.email;
    }

    if (this.momRecipient) {
      return this.momRecipient.email;
    }

    return this.recipient.email ?? '';
  }
  get phone(): string {
    if (this.userRecipient) {
      return this.userRecipient.phone;
    }

    if (this.momRecipient) {
      return this.momRecipient.phone;
    }

    return this.formatPhoneNumber(this.recipient.phone_number);
  }

  get templateParams(): NotificationRecipientTemplateParams {
    if (this.userRecipient) {
      return this.userRecipient.templateParams;
    }

    if (this.momRecipient) {
      return this.momRecipient.templateParams;
    }

    const firstName = this.recipient.name?.split(' ')[0] ?? '';
    const lastName = this.recipient.name?.replace(firstName + ' ', '') ?? '';

    return {
      recipientEmail: this.email,
      recipientPhone: this.phone,
      recipientFirstName: firstName,
      recipientLastName: lastName,
    };
  }
}
