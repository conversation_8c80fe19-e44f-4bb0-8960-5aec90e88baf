import { Prisma, PrismaClient } from '@prisma/client';

export class NotificationRepository {
  constructor(private readonly prisma: PrismaClient) {}

  async fetchPendingNotifications() {
    return this.prisma.notification.findMany({
      where: {
        status: 'pending',
        deleted_at: 0,
      },
      take: 25,
      include: {
        Mom: true,
        User_Notification_recipient_user_idToUser: true,
        EventRespondent: {
          include: {
            User: true,
            Mom: true,
          },
        },
      },
    });
  }

  async createNotification(notification: Prisma.NotificationCreateInput) {
    return this.prisma.notification.create({
      data: notification,
    });
  }
}
