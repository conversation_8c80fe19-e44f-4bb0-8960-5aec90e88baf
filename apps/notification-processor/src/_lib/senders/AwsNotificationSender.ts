import { SESClient, SendEmailCommandInput, SendEmailCommand } from '@aws-sdk/client-ses';
import { PublishCommandInput, SNSClient, PublishCommand } from '@aws-sdk/client-sns';
import { NotificationSender } from './NotificationSender';

/**
 * Concrete implementation of NotificationSender using AWS SES and SNS services
 */

export class AwsNotificationSender extends NotificationSender<SendEmailCommandInput, PublishCommandInput> {
  private sesClient: SESClient;
  private snsClient: SNSClient;

  constructor(awsRegion: string, awsAccessKeyId: string, awsSecretAccessKey: string) {
    super();
    this.sesClient = new SESClient({
      region: awsRegion,
      credentials: {
        accessKeyId: awsAccessKeyId,
        secretAccessKey: awsSecretAccessKey,
      },
    });
    this.snsClient = new SNSClient({
      region: awsRegion,
      credentials: {
        accessKeyId: awsAccessKeyId,
        secretAccessKey: awsSecretAccessKey,
      },
    });
  }

  /**
   * Sends an email using AWS SES
   * @param email The email command input
   * @returns True if sent successfully, false otherwise
   */
  async sendEmailAsync(email: SendEmailCommandInput): Promise<boolean> {
    try {
      const command = new SendEmailCommand(email);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const response = await this.sesClient.send(command);

      return true;
    } catch (error) {
      console.error('Error sending email:', error);
      console.error('Email input:', email);
      return false;
    }
  }

  /**
   * Sends an SMS using AWS SNS
   * @param sms The SMS command input
   * @returns True if sent successfully, false otherwise
   */
  async sendSmsAsync(sms: PublishCommandInput): Promise<boolean> {
    try {
      sms.MessageDeduplicationId = Date.now().toString();
      const command = new PublishCommand(sms);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const response = await this.snsClient.send(command);

      return true;
    } catch (error) {
      console.error('Error sending sms:', error);
      console.error('SMS input:', sms);
      return false;
    }
  }
}
