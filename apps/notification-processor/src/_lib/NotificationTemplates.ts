export type NotificationTemplates = Record<
  string,
  {
    sms: string;
    email: {
      subject: string;
      body?: string;
      bodyFile?: string;
    };
  }
>;

/*
  You get the following template parameters 'for free'. Any other necessary parameters will need to be passed via the templateParams
  websiteUrlBase
  moodleUrlBase
  recipientFirstName
  recipientLastName
  recipientPhone
  recipientEmail
*/

const templates: NotificationTemplates = {
  new_referral_supervisor: {
    sms: 'A new mom referral has been submitted. Review info here: {{websiteUrlBase}}/portal/supervisor/dashboard',
    email: {
      subject: 'New Mom Referral Received',
      bodyFile: 'new-referral-supervisor.html',
    },
  },
  new_wellness_assessment_coordinator: {
    sms: 'A new mom intake form has been submitted. Review her information here: {{websiteUrlBase}}/portal/coordinator/dashboard',
    email: {
      subject: 'New Mom Wellbeing Assessment Received',
      body: `<p>Hi {{recipientFirstName}},</p><p>{{momName}} has submitted a wellbeing assessment, and it’s been added to your dashboard.</p><p>Please <a href="{{websiteUrlBase}}/portal/coordinator/dashboard">review it here</a></p>`,
    },
  },
  wellness_assessment_self_intake_mom: {
    sms: '',
    email: {
      subject: 'Complete Your Wellbeing Assessment to Get Started',
      body: `<p>Hi {{momName}},</p><p>Welcome to the ÉMA family! We’re thrilled to have you with us. To officially begin your journey, we’d love for you to complete the wellbeing assessment. This is an important first step that helps us understand your unique needs so we can provide the best support possible.</p><p><b>Here’s How It Works</b>: The wellbeing assessment is part of our intake process, and it's quick and easy to fill out. Sometimes it’s completed in person, but you can also do it at your own pace if that works better for you. Your Advocate will be available if you need any help—whether you choose to complete it with them or on your own.</p><p>This assessment is designed to help us create a plan that’s tailored to you, making sure that the support you receive is just what you need.</p><p>Thank you for being here—we’re excited to walk alongside you on this journey!</p><p>CTA: <a href="{{websiteUrlBase}}/referral/self-intake/{{token}}">Complete the Intake Form</a></p>`,
    },
  },
  mom_assigned_to_coordinator: {
    sms: 'Hi {{recipientFirstName}}, You have been assigned a new mom. Please login to the portal to view the details.',
    email: {
      subject: '{{recipientFirstName}} - New Mom Assigned',
      body: 'Hi {{recipientFirstName}}, You have been assigned a new mom. Please login to the portal to view the details.',
    },
  },
  forgot_password: {
    sms: 'Hi {{recipientFirstName}}, You have requested a password reset. Please click the link to reset your password: {{websiteUrlBase}}/reset-password/{{token}}',
    email: {
      subject: "Here's your password reset link",
      bodyFile: 'forgot-password.html',
    },
  },
  session_note_submitted: {
    sms: '',
    email: {
      subject: 'Session Notes Submitted',
      bodyFile: 'session-note-submitted.html',
    },
  },
  event_registration: {
    sms: 'Thank you for registering for {{eventTitle}}. Event details: {{eventDate}} at {{eventTime}}. Location: {{eventLocation}}',
    email: {
      subject: 'New Event - {{eventTitle}}',
      bodyFile: 'event-registration.html',
    },
  },
  session_note_rejected: {
    sms: '',
    email: {
      subject: 'Session Notes Rejected',
      bodyFile: 'session-note-rejected.html',
    },
  },
  mom_ready_for_advocate_assignment: {
    sms: '',
    email: {
      subject: 'Assign an Advocate to {{momName}}',
      bodyFile: 'mom-ready-for-advocate-assignment.html',
    },
  },
  benevolence_need_submitted: {
    sms: 'A {{needUrgency}} vital support need for {{momName}} has been flagged: {{needType}}. Review here: {{websiteUrlBase}}/portal/coordinator/dashboard',
    email: {
      subject: '{{needType}} - Support Need',
      bodyFile: 'benevolence-need-submitted.html',
    },
  },
  advocate_invite_training: {
    sms: '',
    email: {
      subject: 'Your Advocate Training Videos Are Ready',
      bodyFile: 'advocate-invite-training.html',
    },
  },
  upcoming_session_reminder: {
    sms: 'Your session with {{momName}} is almost here. View details: {{websiteUrlBase}}/portal/advocate/dashboard',
    email: {
      subject: 'Just a heads up—your next session is coming up!',
      bodyFile: 'upcoming-session-reminder.html',
    },
  },
  new_mom_assigned_advocate: {
    sms: `You’ve been assigned to {{momName}}. Review her profile here: {{websiteUrlBase}}/portal/advocate/dashboard`,
    email: {
      subject: 'New Mom Pairing: {{momName}}',
      bodyFile: 'new-mom-assigned-advocate.html',
    },
  },
  new_advocate_assigned_to_mom: {
    sms: `Hi {{recipientFirstName}}! Great news—you’ve been paired with your ĒMA Advocate, {{advocateName}}! They’ll reach out soon to connect and start supporting you. If you need anything, reply to this message or email <NAME_EMAIL>. -Team ÉMA`,
    email: {
      subject: `Exciting News — You’ve Been Paired with Your Advocate!`,
      bodyFile: 'new-advocate-assigned-to-mom.html',
    },
  },
  mom_accepted_into_program: {
    sms: `Hi {{recipientFirstName}}! Welcome to ĒMA! We’re here to support you with resources, advocacy, & a caring community to help you thrive as a mom. Keep an eye out for more messages from us with next steps!  - ĒMA Team`,
    email: {
      subject: `Welcome to ÉMA!`,
      bodyFile: 'mom-accepted-into-program.html',
    },
  },
  welcome_to_email: {
    sms: `Hi {{recipientFirstName}}! Your Trellis account is ready. Log in with:
Username: {{username}}
Temp Password: {{password}}
Login here: {{websiteUrlBase}}/login
Please update your password after logging in.`,
    email: {
      subject: 'Welcome to ÉMA!',
      bodyFile: 'welcome-to-ema.html',
    },
  },
};

export default templates;
