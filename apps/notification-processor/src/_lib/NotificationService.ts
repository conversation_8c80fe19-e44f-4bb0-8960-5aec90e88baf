import { MomR<PERSON><PERSON>ient } from './recipients/MomRecipient';
import { NotificationRecipient } from './recipients/NotificationRecipient';
import { NotificationRepository } from './NotificationRepository';
import { NotificationStatus, PrismaClient } from '@prisma/client';
import { UserRecipient } from './recipients/UserRecipient';
import { EventRespondentRecipient } from './recipients/EventRespondentRecipient';

export class NotificationService {
  constructor(
    private readonly notification: Awaited<ReturnType<NotificationRepository['fetchPendingNotifications']>>[number],
    private readonly prisma: PrismaClient,
  ) {}

  public async updateStatusAsync(status: NotificationStatus, failureReason?: string): Promise<void> {
    await this.prisma.notification.update({
      where: { id: this.notification.id },
      data: { status, ...(failureReason ? { failure_reason: failureReason } : {}) },
    });
  }

  public getRecipient(): NotificationRecipient {
    if (this.notification.EventRespondent) {
      return new EventRespondentRecipient(this.notification.EventRespondent);
    }

    if (this.notification.Mom) {
      return new MomRecipient(this.notification.Mom);
    }

    if (this.notification.User_Notification_recipient_user_idToUser) {
      return new UserRecipient(this.notification.User_Notification_recipient_user_idToUser);
    }

    throw new Error('Notification recipient not found');
  }
}
