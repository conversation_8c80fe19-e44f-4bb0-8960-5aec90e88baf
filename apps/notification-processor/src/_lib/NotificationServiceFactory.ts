import { PrismaClient } from '@prisma/client';
import { NotificationService } from './NotificationService';
import { NotificationRepository } from './NotificationRepository';

export class NotificationServiceFactory {
  constructor(private readonly prisma: PrismaClient) {}

  getNotificationService(
    notification: Awaited<ReturnType<NotificationRepository['fetchPendingNotifications']>>[number],
  ): NotificationService {
    return new NotificationService(notification, this.prisma);
  }
}
