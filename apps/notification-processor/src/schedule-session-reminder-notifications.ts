import { NotificationRepository } from './_lib/NotificationRepository';
import { SessionRepository } from './_lib/SessionRepository';
import { v4 as uuidv4 } from 'uuid';

export async function scheduleSessionReminderNotifications(
  sessionRepository: SessionRepository,
  notificationRepository: NotificationRepository,
) {
  const sessions = await sessionRepository.fetchSessionsToRemind();
  console.info(`Found ${sessions.length} sessions to remind`);

  for (const session of sessions) {
    console.info(
      `Reminding user ${session.Pairing?.User?.firstName} ${session.Pairing?.User?.lastName} about session ${session.id}`,
    );

    // Note: Need to convert date_start to date/time object based on recipient_user's timezone setting
    await notificationRepository.createNotification({
      id: uuidv4(),
      status: 'pending',
      template: 'upcoming_session_reminder',
      template_params: {
        momName: `${session.Pairing?.Mom?.first_name} ${session.Pairing?.Mom?.last_name}`,
        sessionDate: session.date_start?.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        }),
        sessionTime: session.date_start?.toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true,
        }),
        sessionLocation: session.location,
      },
      updated_at: new Date(),
      User_Notification_recipient_user_idToUser: {
        connect: {
          id: session.Pairing?.User?.id ?? '',
        },
      },
    });

    await sessionRepository.updateSessionReminderSent(session.id);
  }

  console.info(`Schedule session reminder notifications ran at ${new Date().toISOString()}`);
}
