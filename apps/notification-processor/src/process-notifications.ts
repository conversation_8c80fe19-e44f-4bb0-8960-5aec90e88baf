import { AwsNotificationSender } from './_lib/senders/AwsNotificationSender';
import { NotificationRepository } from './_lib/NotificationRepository';
import { NotificationToAwsCommandInputAdapter } from './_lib/NotificationToAwsCommandInputAdapter';
import { waitAsync } from '@suiteapi/models';
import { NotificationServiceFactory } from './_lib/NotificationServiceFactory';

export async function processNotifications(
  commandInputAdapter: NotificationToAwsCommandInputAdapter,
  sender: AwsNotificationSender,
  notificationRepository: NotificationRepository,
  notificationServiceFactory: NotificationServiceFactory,
) {
  const notifications = await notificationRepository.fetchPendingNotifications();

  notifications.forEach(async (notification) => {
    const notificationService = notificationServiceFactory.getNotificationService(notification);

    try {
      await notificationService.updateStatusAsync('processing');

      const recipient = notificationService.getRecipient();

      if (recipient.shouldSendEmail) {
        const commandInput = await commandInputAdapter.toSendEmailCommandInput(notification, recipient);
        if (commandInput) {
          await sender.sendEmailAsync(commandInput);
        }
      }

      if (recipient.shouldSendSms) {
        const commandInput = commandInputAdapter.toSendSmsCommandInput(notification, recipient);
        if (commandInput) {
          await sender.sendSmsAsync(commandInput);
        }
      }

      await notificationService.updateStatusAsync('sent');
    } catch (error) {
      console.error(`Error processing notification ${notification.id}: ${error}`);
      await notificationService.updateStatusAsync('failed', error instanceof Error ? error.message : 'Unknown error');
    }

    // If we are in sandbox mode, we need to wait for 1 second between each recipient
    if (process.env.AWS_SNSSES_SANDBOX == 'true') {
      await waitAsync(1000);
    }
  });

  console.info(`Notification processor ran at ${new Date().toISOString()}`);
}
