import { PrismaClient } from '@prisma/client';
import { NotificationToAwsCommandInputAdapter } from './_lib/NotificationToAwsCommandInputAdapter';
import { AwsNotificationSender } from './_lib/senders/AwsNotificationSender';
import { processNotifications } from './process-notifications';
import { NotificationRepository } from './_lib/NotificationRepository';
import { NotificationServiceFactory } from './_lib/NotificationServiceFactory';
import { SessionRepository } from './_lib/SessionRepository';
import { scheduleSessionReminderNotifications } from './schedule-session-reminder-notifications';

const commandInputAdapter = new NotificationToAwsCommandInputAdapter();
const sender = new AwsNotificationSender(
  process.env.AWS_NOTIFICATIONS_REGION ?? '',
  process.env.AWS_ACCESS_KEY_ID ?? '',
  process.env.AWS_SECRET_ACCESS_KEY ?? '',
);

const prisma = new PrismaClient();
const notificationRepository = new NotificationRepository(prisma);
const notificationServiceFactory = new NotificationServiceFactory(prisma);
const sessionRepository = new SessionRepository(prisma);

scheduleSessionReminderNotifications(sessionRepository, notificationRepository)
  .then(() => {
    processNotifications(commandInputAdapter, sender, notificationRepository, notificationServiceFactory);
  })
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
