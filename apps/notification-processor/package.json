{"name": "notification-processor", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "pnpm run prisma:generate && tsup src/main.ts --clean && mkdir -p dist/template-bodies && cp -r src/_lib/template-bodies/* dist/template-bodies/", "build:ci": "pnpm run prisma:pull && pnpm run prisma:generate && tsup src/main.ts --clean && mkdir -p dist/template-bodies && cp -r src/_lib/template-bodies/* dist/template-bodies/", "start": "node dist/main.js", "start:local": "pnpm run build && dotenv -e .env -- node dist/main.js", "format": "prettier --write \"src/**/*.ts\"", "lint": "eslint \"src/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "prisma:generate": "prisma generate", "prisma:pull": "prisma db pull", "clean": "rm -rf dist && rm -rf .turbo", "nuke": "pnpm run clean && rm -rf node_modules"}, "dependencies": {"@aws-sdk/client-ses": "^3.777.0", "@aws-sdk/client-sns": "^3.777.0", "@prisma/client": "~6.6.0", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "handlebars": "^4.7.8", "@suiteapi/api": "workspace:api@*", "@suiteapi/models": "workspace:models@*", "uuid": "^11.0.5"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/jest": "^29.5.14", "@types/node": "^20.3.1", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "dotenv-cli": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.7.0", "prettier": "^3.0.0", "prisma": "~6.6.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "tsup": "^8.3.6", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": ["ts-jest", {"useESM": true, "tsconfig": {"moduleResolution": "NodeNext"}}]}, "extensionsToTreatAsEsm": [".ts"], "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}