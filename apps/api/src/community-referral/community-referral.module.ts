import { Module } from '@nestjs/common';
import { CommunityReferralService } from './community-referral.service';
import { CommunityReferralController } from './community-referral.controller';
import { DocumentService } from 'src/document/document.service';
import { S3Service } from 'src/s3/s3.service';
import { UuidService } from 'src/uuid/uuid.service';
import { ConfigService } from '@nestjs/config';
import { ExtendedPrismaClientFactoryService } from 'src/extended-prisma-client-factory/extended-prisma-client-factory.service';

@Module({
  controllers: [CommunityReferralController],
  providers: [
    ConfigService,
    CommunityReferralService,
    DocumentService,
    S3Service,
    UuidService,
    ExtendedPrismaClientFactoryService,
  ],
})
export class CommunityReferralModule {}
