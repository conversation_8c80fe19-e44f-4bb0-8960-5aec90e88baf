import { Test, TestingModule } from '@nestjs/testing';
import { CommunityReferralController } from './community-referral.controller';
import { CommunityReferralService } from './community-referral.service';

describe('CommunityReferralController', () => {
  let controller: CommunityReferralController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CommunityReferralController],
      providers: [CommunityReferralService],
    }).compile();

    controller = module.get<CommunityReferralController>(
      CommunityReferralController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
