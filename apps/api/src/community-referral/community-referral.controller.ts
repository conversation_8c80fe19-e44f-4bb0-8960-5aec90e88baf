import { Controller, Post, Body, BadRequestException } from '@nestjs/common';
import { CommunityReferralService } from './community-referral.service';
import { DocumentService } from 'src/document/document.service';
import { SkipJwtAuth } from 'src/auth/skip-jwt-auth.decorator';
import { YupSchemas } from '@suiteapi/models';
import { isTurnstileTokenValidAsync } from 'utils/cloudflare-turnstile';

@SkipJwtAuth()
@Controller('community-referral')
export class CommunityReferralController {
  constructor(
    private readonly communityReferralService: CommunityReferralService,
    private readonly documentService: DocumentService,
  ) {}

  @Post()
  async create(
    @Body()
    createCommunityReferralDto: YupSchemas.CreateCommunityReferralRequest,
  ) {
    try {
      createCommunityReferralDto =
        await YupSchemas.createCommunityReferralRequestSchema.validate(
          createCommunityReferralDto,
          {
            stripUnknown: true,
          },
        );
    } catch (error) {
      console.error('Error validating request body', error);
      throw new BadRequestException('Invalid request body');
    }

    // validate turnstile token
    const isTurnstileTokenValid = await isTurnstileTokenValidAsync(
      createCommunityReferralDto.cloudflare_turnstile_token,
    );

    if (!isTurnstileTokenValid)
      throw new BadRequestException('Invalid turnstile token');

    createCommunityReferralDto.referral.prospect_status = 'prospect';
    const mom = await this.communityReferralService.create(
      createCommunityReferralDto.referral,
    );

    if (createCommunityReferralDto.documents) {
      for (const document of createCommunityReferralDto.documents) {
        await this.documentService.create({
          ...document,
          document_name: document.document_name ?? document.filename,
          mom_id: mom.id,
        });
      }
    }

    return mom;
  }
}
