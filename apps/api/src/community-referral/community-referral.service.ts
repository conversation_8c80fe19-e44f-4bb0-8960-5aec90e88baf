import { Injectable } from '@nestjs/common';
import { YupSchemas } from '@suiteapi/models';
import { ExtendedPrismaClientFactoryService } from 'src/extended-prisma-client-factory/extended-prisma-client-factory.service';
import { BaseReferralService } from 'src/common/base-referral.service';

@Injectable()
export class CommunityReferralService extends BaseReferralService<YupSchemas.CommunityReferralSchema> {
  constructor(prismaFactory: ExtendedPrismaClientFactoryService) {
    super(prismaFactory);
  }

  async create(createCommunityReferralDto: YupSchemas.CommunityReferralSchema) {
    // TODO: validate against yup schema

    const { affiliate_id, agency_id, id, ...reducedCommunityReferralData } =
      createCommunityReferralDto;

    const mom = await this.prisma.mom.create({
      data: {
        ...reducedCommunityReferralData,
        referral_sub_status:
          reducedCommunityReferralData.referral_sub_status || undefined,
        affiliate: {
          connect: {
            id: affiliate_id,
          },
        },
        agency: {
          connect: {
            id: agency_id,
          },
        },
        updated_at: new Date(),
      },
    });

    await this.createReferralNotifications(mom);

    return mom;
  }
}
