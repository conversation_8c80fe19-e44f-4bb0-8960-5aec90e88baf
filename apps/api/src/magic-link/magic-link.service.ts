import { Injectable } from '@nestjs/common';
import { ExtendedPrismaClientFactoryService } from 'src/extended-prisma-client-factory/extended-prisma-client-factory.service';
import { addDays, addHours } from 'date-fns';

@Injectable()
export class MagicLinkService {
  private prisma: ReturnType<
    ExtendedPrismaClientFactoryService['createExtendedPrismaClient']
  >;

  constructor(
    private readonly prismaFactory: ExtendedPrismaClientFactoryService,
  ) {
    this.prisma = this.prismaFactory.createExtendedPrismaClient();
  }

  async findByToken(token: string) {
    const ml = await this.prisma.magicLink.findFirst({
      where: {
        token,
        expired: false,
      },
    });

    // ensure the magic link exists
    if (!ml) {
      return null;
    }

    const now = new Date();

    const minutesToExpireIn = 15;

    // Max timeout is 15 minutes
    const maxAmountOfTimePassedInMiliseconds = minutesToExpireIn * 60 * 1000; // x minutes in milliseconds
    // Convert expiredAt to date object
    const expiredDate = new Date(ml.expires_at);
    // Subtract the current date from the expired date
    const expiresAtDiffNow = now.getTime() - expiredDate.getTime();
    // Check if the difference is greater than the max amount of time
    const hasMaxTimePassed =
      expiresAtDiffNow > maxAmountOfTimePassedInMiliseconds;

    const used = ml.used_at !== null;

    if (hasMaxTimePassed || used) {
      return null;
    }

    return ml;
  }

  async expireByToken(token: string) {
    const now = new Date(Date.now());
    return this.prisma.magicLink.update({
      where: { token },
      data: { expires_at: now, used_at: now, expired: true },
    });
  }

  async createForgotPassword({
    userId,
    token,
  }: {
    userId: string;
    token: string;
  }) {
    const expiresIn = addHours(new Date(), 1);
    return this.prisma.magicLink.create({
      data: {
        user: { connect: { id: userId } },
        token,
        type: 'forgot_password',
        expires_at: expiresIn,
        used_at: null,
      },
    });
  }

  async createSelfIntake({ userId, token }: { userId: string; token: string }) {
    const expiresIn = addDays(new Date(), 30);
    return this.prisma.magicLink.create({
      data: {
        user: { connect: { id: userId } },
        token,
        type: 'self_intake',
        expires_at: expiresIn,
        used_at: null,
      },
    });
  }
}
