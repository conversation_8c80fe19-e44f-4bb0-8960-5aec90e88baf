import { Test, TestingModule } from '@nestjs/testing';
import { MagicLinkService } from './magic-link.service';
import { ExtendedPrismaClientFactoryService } from 'src/extended-prisma-client-factory/extended-prisma-client-factory.service';

describe('MagicLinkService', () => {
  let service: MagicLinkService;

  const mockPrisma = {
    magic_link: {
      findFirst: jest.fn(),
      update: jest.fn(),
      create: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MagicLinkService,
        {
          provide: ExtendedPrismaClientFactoryService,
          useValue: {
            createExtendedPrismaClient: jest.fn(() => mockPrisma),
          },
        },
      ],
    }).compile();

    service = module.get<MagicLinkService>(MagicLinkService);
    module.get<ExtendedPrismaClientFactoryService>(
      ExtendedPrismaClientFactoryService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findByTokenAndUserId', () => {
    it('should throw an error if no magic link is found', async () => {
      mockPrisma.magic_link.findFirst.mockResolvedValue(null);

      await expect(
        service.findByTokenAndUserId('token', 'userId'),
      ).rejects.toThrow('No magic link found');
    });

    it('should throw an error if the magic link is expired, used, or invalid', async () => {
      mockPrisma.magic_link.findFirst.mockResolvedValue({
        expiresAt: new Date(Date.now() - 1000),
        used_at: null,
        expired: false,
      });

      await expect(
        service.findByTokenAndUserId('token', 'userId'),
      ).rejects.toThrow('Magic link not valid');
    });

    it('should return the magic link if valid', async () => {
      const magicLink = {
        expiresAt: new Date(Date.now() + 1000),
        used_at: null,
        expired: false,
      };
      mockPrisma.magic_link.findFirst.mockResolvedValue(magicLink);

      const result = await service.findByTokenAndUserId('token', 'userId');
      expect(result).toEqual(magicLink);
    });
  });

  describe('update', () => {
    it('should update the magic link for the user', async () => {
      const now = new Date();
      mockPrisma.magic_link.update.mockResolvedValue({
        userId: 'userId',
        expires_at: now,
        used_at: now,
        expired: true,
      });

      const result = await service.update('userId');
      expect(result).toEqual({
        userId: 'userId',
        expires_at: now,
        used_at: now,
        expired: true,
      });
      expect(mockPrisma.magic_link.update).toHaveBeenCalledWith({
        where: { userId: 'userId' },
        data: { expires_at: now, used_at: now, expired: true },
      });
    });
  });

  describe('create', () => {
    it('should create a new magic link', async () => {
      const now = new Date();
      const magicLink = {
        userId: 'userId',
        token: 'token',
        type: 'forgot_password',
        expiresAt: new Date(now.getTime() + 60 * 60 * 1000),
        used_at: null,
      };
      mockPrisma.magic_link.create.mockResolvedValue(magicLink);

      const result = await service.create('userId', 'token');
      expect(result).toEqual(magicLink);
      expect(mockPrisma.magic_link.create).toHaveBeenCalledWith({
        data: {
          userId: 'userId',
          token: 'token',
          type: 'forgot_password',
          expiresAt: expect.any(Date),
          used_at: null,
        },
      });
    });
  });
});
