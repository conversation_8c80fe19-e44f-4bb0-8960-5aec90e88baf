import { Logger } from '@nestjs/common';
import { Mom, NotificationStatus, NotificationTemplate } from '@prisma/client';
import { ExtendedPrismaClientFactoryService } from 'src/extended-prisma-client-factory/extended-prisma-client-factory.service';

export type ReferralBase = {
  affiliate_id?: string;
};

export abstract class BaseReferralService<T extends ReferralBase> {
  private readonly logger = new Logger(BaseReferralService.name);

  protected prisma: ReturnType<
    ExtendedPrismaClientFactoryService['createExtendedPrismaClient']
  >;

  constructor(
    protected readonly prismaFactory: ExtendedPrismaClientFactoryService,
  ) {
    this.prisma = this.prismaFactory.createExtendedPrismaClient();
  }

  abstract create(dto: T): Promise<Mom>;

  async createReferralNotifications(mom: Mom) {
    try {
      const supervisors = await this.prisma.user.findMany({
        where: {
          affiliateId: mom.affiliate_id,
          userRoles: { some: { role: { key: 'supervisor' } } }, // TODO: Create or use an enum here.
        },
      });

      for (const supervisor of supervisors) {
        await this.prisma.notification.create({
          data: {
            recipient_user_id: supervisor.id,
            status: NotificationStatus.pending,
            template: NotificationTemplate.new_referral_supervisor,
            template_params: {},
          },
        });
      }
    } catch (error) {
      this.logger.error(
        `Error creating referral notifications for mom ${mom.id}`,
        error,
      );
    }
  }
}
