import { Module } from '@nestjs/common';
import { SelfReferralService } from './self-referral.service';
import { SelfReferralController } from './self-referral.controller';
import { ExtendedPrismaClientFactoryService } from 'src/extended-prisma-client-factory/extended-prisma-client-factory.service';
@Module({
  controllers: [SelfReferralController],
  providers: [SelfReferralService, ExtendedPrismaClientFactoryService],
})
export class SelfReferralModule {}
