import { Injectable } from '@nestjs/common';
import { YupSchemas } from '@suiteapi/models';
import { ExtendedPrismaClientFactoryService } from 'src/extended-prisma-client-factory/extended-prisma-client-factory.service';
import { BaseReferralService } from 'src/common/base-referral.service';

@Injectable()
export class SelfReferralService extends BaseReferralService<YupSchemas.SelfReferralSchema> {
  constructor(prismaFactory: ExtendedPrismaClientFactoryService) {
    super(prismaFactory);
  }

  async create(createSelfReferralDto: YupSchemas.SelfReferralSchema) {
    // TODO: validate against yup schema

    const { affiliate_id, id, ...reducedSelfReferralData } =
      createSelfReferralDto;

    const mom = await this.prisma.mom.create({
      data: {
        ...reducedSelfReferralData,
        referral_sub_status:
          reducedSelfReferralData.referral_sub_status || undefined,
        affiliate: {
          connect: {
            id: affiliate_id,
          },
        },
        updated_at: new Date(),
      },
    });

    await this.createReferralNotifications(mom);

    return mom;
  }
}
