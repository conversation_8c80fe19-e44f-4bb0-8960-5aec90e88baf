import { Controller, Post, Body, BadRequestException } from '@nestjs/common';
import { SelfReferralService } from './self-referral.service';
import { SkipJwtAuth } from 'src/auth/skip-jwt-auth.decorator';
import { isTurnstileTokenValidAsync } from 'utils/cloudflare-turnstile';
import { YupSchemas } from '@suiteapi/models';

@Controller('self-referral')
export class SelfReferralController {
  constructor(private readonly selfReferralService: SelfReferralService) {}

  @SkipJwtAuth()
  @Post()
  async create(
    @Body() createSelfReferralDto: YupSchemas.CreateSelfReferralRequest,
  ) {
    try {
      createSelfReferralDto =
        await YupSchemas.createSelfReferralRequestSchema.validate(
          createSelfReferralDto,
          {
            stripUnknown: true,
          },
        );
    } catch (error) {
      console.error('Error validating request body', error);
      throw new BadRequestException('Invalid request body');
    }

    // validate turnstile token
    const isTurnstileTokenValid = await isTurnstileTokenValidAsync(
      createSelfReferralDto.cloudflare_turnstile_token,
    );

    if (!isTurnstileTokenValid)
      throw new BadRequestException('Invalid turnstile token');

    createSelfReferralDto.referral.prospect_status = 'prospect';
    return await this.selfReferralService.create(
      createSelfReferralDto.referral,
    );
  }
}
