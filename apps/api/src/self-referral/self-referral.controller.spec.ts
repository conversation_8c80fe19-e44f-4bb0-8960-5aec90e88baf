import { Test, TestingModule } from '@nestjs/testing';
import { SelfReferralController } from './self-referral.controller';
import { SelfReferralService } from './self-referral.service';

describe('SelfReferralController', () => {
  let controller: SelfReferralController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SelfReferralController],
      providers: [SelfReferralService],
    }).compile();

    controller = module.get<SelfReferralController>(SelfReferralController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
