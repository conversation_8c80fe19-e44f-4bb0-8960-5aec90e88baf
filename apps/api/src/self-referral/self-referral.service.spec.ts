import { Test, TestingModule } from '@nestjs/testing';
import { SelfReferralService } from './self-referral.service';

describe('SelfReferralService', () => {
  let service: SelfReferralService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SelfReferralService],
    }).compile();

    service = module.get<SelfReferralService>(SelfReferralService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
