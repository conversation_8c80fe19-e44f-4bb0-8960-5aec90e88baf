import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { UsersService } from '../users/users.service';
import * as bcrypt from 'bcrypt';
import { User } from '@prisma/client';

class MoodleSyncTest {
  private readonly logger = new Logger(MoodleSyncTest.name);
  private readonly usersService: UsersService;
  private readonly createdUsers: Array<{ user: User; password: string }> = [];
  private readonly shouldDeleteUsers: boolean;
  private readonly numUsers: number;

  constructor(
    usersService: UsersService,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _options: { shouldDeleteUsers?: boolean; numUsers?: number } = {},
  ) {
    const args = process.argv.slice(2);
    const helpRequested = args.includes('--help') || args.includes('-h');
    const keepUsers = args.includes('--keep-users');
    const numUsersArg = args.find((arg) => arg.startsWith('--num-users='));
    let parsedNumUsers = 10; // Default value

    if (numUsersArg) {
      const match = numUsersArg.match(/--num-users=(\d+)/);
      if (match) {
        parsedNumUsers = parseInt(match[1], 10);
      }
    }

    if (helpRequested) {
      this.printHelp();
      process.exit(0);
    }

    this.shouldDeleteUsers = !keepUsers;
    this.numUsers = parsedNumUsers;
    this.usersService = usersService;
  }

  private printHelp(): void {
    this.logger.log('Test Moodle User Sync Script\n');
    this.logger.log('Usage: npm run test:moodle-sync [options]\n');
    this.logger.log('Options:');
    this.logger.log(
      '  --num-users=N     Number of test users to create (default: 10)',
    );
    this.logger.log(
      '  --keep-users      Keep test users after running (skip deletion)',
    );
    this.logger.log('  -h, --help        Show this help message\n');
    this.logger.log('Example:');
    this.logger.log('  npm run test:moodle-sync --num-users=5 --keep-users\n');
  }

  private async createTestUser(
    index: number,
  ): Promise<{ user: User; password: string }> {
    const timestamp = Date.now();
    const email = `test.user.${timestamp}.${index}@example.com`;
    const password = `Test${index}Pass123!`;
    const username = `test.user.${timestamp}.${index}`;
    const passwordHash = await bcrypt.hash(password, 10);

    const userData = {
      email,
      username,
      passwordHash,
      firstName: `Test${index}`,
      lastName: `User${index}`,
      status: 'Active',
      advocate_status: 'Active',
      hasMoodleAccount: false, // Will be automatically set by the service
      address_city: 'Test City',
      address_state: 'CA',
      address_postalcode: '12345',
    };

    const createdUser = await this.usersService.create(userData);
    return { user: createdUser, password };
  }

  public async run(): Promise<void> {
    try {
      this.logger.log(`Creating ${this.numUsers} test users...`);

      // Create test users
      for (let i = 1; i <= this.numUsers; i++) {
        const { user, password } = await this.createTestUser(i);
        this.createdUsers.push({ user, password });
        this.logger.log(`Created test user ${i}/${this.numUsers}:`);
        this.logger.log(`  Email: ${user.email}`);
        this.logger.log(`  Password: ${password}`);
        this.logger.log(
          `  Moodle ID: ${user.moodleUserId || 'Not yet synced'}`,
        );
      }

      this.logger.log('\nAll test users created successfully!');

      if (this.shouldDeleteUsers) {
        this.logger.log('\nCleaning up test users...');
        for (const { user } of this.createdUsers) {
          await this.usersService.delete(user.id);
          this.logger.log(`Deleted user: ${user.email}`);
        }
        this.logger.log('All test users cleaned up successfully!');
      } else {
        this.logger.log('\nTest users have been kept for inspection.');
        this.logger.log('Remember to clean them up manually when done!');
        this.logger.log('\nCreated test users:');
        for (const { user, password } of this.createdUsers) {
          this.logger.log(`\nUser: ${user.email}`);
          this.logger.log(`Password: ${password}`);
          this.logger.log(`Moodle ID: ${user.moodleUserId || 'Not synced'}`);
        }
      }
    } catch (error) {
      this.logger.error('Error during test:', error);
      throw error;
    }
  }
}

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule);

  // Get the UsersService instance from the app context
  const usersService = app.get(UsersService);

  const test = new MoodleSyncTest(usersService);
  await test.run();
  await app.close();
}

bootstrap();
