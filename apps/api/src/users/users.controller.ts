import { Controller, Get, Param, Post, Body, Delete } from '@nestjs/common';
import { UsersService } from './users.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Users')
@Controller('user')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get(':id')
  @ApiOperation({ summary: 'Get a user by ID' })
  @ApiResponse({ status: 200, description: 'User retrieved successfully' })
  async findById(@Param('id') id: string) {
    return this.usersService.findById(id);
  }

  @Post(':id/photo')
  @ApiOperation({ summary: 'Upload a user photo' })
  @ApiResponse({ status: 200, description: 'User photo uploaded successfully' })
  async uploadUserPhoto(
    @Param('id') id: string,
    @Body() body: { photo: string; contentType: string },
  ) {
    return this.usersService.uploadUserPhoto(id, body.photo, body.contentType);
  }

  @Delete(':id/photo')
  @ApiOperation({ summary: 'Remove a user photo' })
  @ApiResponse({ status: 200, description: 'User photo removed successfully' })
  async removeUserPhoto(@Param('id') id: string) {
    return this.usersService.removeUserPhoto(id);
  }
}
