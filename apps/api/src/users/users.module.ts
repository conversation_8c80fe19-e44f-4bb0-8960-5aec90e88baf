import { Module, forwardRef } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { ExtendedPrismaClientFactoryService } from 'src/extended-prisma-client-factory/extended-prisma-client-factory.service';
import { PhotoModule } from '../photo/photo.module';
import { MoodleModule } from '../moodle/moodle.module';

@Module({
  imports: [PhotoModule, forwardRef(() => MoodleModule)],
  controllers: [UsersController],
  providers: [UsersService, ExtendedPrismaClientFactoryService],
  exports: [UsersService],
})
export class UsersModule {}
