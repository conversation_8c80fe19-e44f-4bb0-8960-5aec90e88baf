import {
  Injectable,
  Logger,
  NotFoundException,
  InternalServerErrorException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { ExtendedPrismaClientFactoryService } from '../extended-prisma-client-factory/extended-prisma-client-factory.service';
import { PhotoService } from '../photo/photo.service';
import { MoodleService } from '../moodle/moodle.service';
import * as crypto from 'crypto';

@Injectable()
export class UsersService {
  private prisma: ReturnType<
    ExtendedPrismaClientFactoryService['createExtendedPrismaClient']
  >;
  private readonly logger = new Logger(UsersService.name);

  constructor(
    private readonly prismaFactory: ExtendedPrismaClientFactoryService,
    private readonly photoService: PhotoService,
    @Inject(forwardRef(() => MoodleService))
    private readonly moodleService: MoodleService,
  ) {
    this.prisma = this.prismaFactory.createExtendedPrismaClient();
  }

  async findOne(username: string) {
    return await this.prisma.user.findUnique({
      where: {
        username_deleted_at: {
          username,
          deleted_at: 0,
        },
      },
    });
  }

  async findOneWithRoles(whereClause: any) {
    return await this.prisma.user.findUnique({
      where: whereClause,
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });
  }

  /**
   * Create a user and synchronize with Moodle
   * @param user User data
   * @returns Created user with Moodle password if generated
   */
  async create(user: any): Promise<any> {
    const createdUser = await this.prisma.user.create({
      data: user,
    });

    let moodlePassword: string | undefined;

    // Sync with Moodle
    try {
      const syncResult = await this.syncUserToMoodle(createdUser);
      moodlePassword = syncResult?.moodlePassword;
    } catch (error) {
      this.logger.error(`Failed to sync user with Moodle: ${error.message}`);
      // Continue even if Moodle sync fails
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { passwordHash, refreshTokenHash, ...remainingUser } = createdUser;

    return {
      ...remainingUser,
      moodlePassword, // Include the generated Moodle password for notifications
    };
  }

  /**
   * Update a user by ID and synchronize with Moodle
   * @param id User ID
   * @param data User data to update
   * @returns Updated user
   */
  async update(id: string, data: any): Promise<any> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id },
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      const updatedUser = await this.prisma.user.update({
        where: { id },
        data,
      });

      // Sync with Moodle
      try {
        // Moodle account exists
        if (!updatedUser.hasMoodleAccount || !updatedUser.moodleUserId) {
          await this.syncUserToMoodle(updatedUser);
        }
        // Otherwise update existing Moodle account if it exists
        else {
          await this.updateExistingMoodleUser(updatedUser);
        }
      } catch (error) {
        this.logger.error(
          `Failed to sync updated user with Moodle: ${error.message}`,
        );
        // Continue even if Moodle sync fails
      }

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { passwordHash, refreshTokenHash, ...userData } = updatedUser;
      return userData;
    } catch (error) {
      this.logger.error(`Error updating user: ${error.message}`);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to update user: ${error.message}`,
      );
    }
  }

  /**
   * Delete a user by ID and remove from Moodle
   * @param id User ID
   * @returns Deleted user
   */
  async delete(id: string): Promise<any> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id },
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      // Soft delete the user in our system (set deleted_at to current timestamp)
      const deletedUser = await this.prisma.user.update({
        where: { id },
        data: { deleted_at: Math.floor(Date.now() / 1000) },
      });

      // Remove from Moodle
      if (user.moodleUserId) {
        try {
          await this.removeUserFromMoodle(user.moodleUserId);
        } catch (error) {
          this.logger.error(
            `Failed to remove user from Moodle: ${error.message}`,
          );
          // Continue even if Moodle removal fails
        }
      }

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { passwordHash, refreshTokenHash, ...userData } = deletedUser;
      return userData;
    } catch (error) {
      this.logger.error(`Error deleting user: ${error.message}`);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to delete user: ${error.message}`,
      );
    }
  }

  async updatePassword(username: string, passwordHash: string) {
    try {
      const resultUser = await this.prisma.user.update({
        where: { username_deleted_at: { username, deleted_at: 0 } },
        data: { passwordHash, mustChangePassword: false },
      });

      return !!resultUser;
    } catch (error) {
      this.logger.error(error);
      return false;
    }
  }

  async updateRefreshToken(userId: string, refreshTokenHash: string) {
    return await this.prisma.user.update({
      where: { id: userId },
      data: { refreshTokenHash },
    });
  }

  async deleteRefreshToken(userId: string) {
    return await this.prisma.user.update({
      where: { id: userId },
      data: { refreshTokenHash: null },
    });
  }

  async findById(id: string) {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id },
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { passwordHash, refreshTokenHash, ...userData } = user;

      return userData;
    } catch (error) {
      this.logger.error(`Error finding user by ID: ${error.message}`);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to find user: ${error.message}`,
      );
    }
  }

  async uploadUserPhoto(id: string, photo: string, contentType: string) {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id },
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      const {
        s3FileName,
        url,
        thumbnailS3FileName,
        thumbnailUrl,
        iconS3FileName,
        iconUrl,
      } = await this.photoService.uploadPhoto(photo, contentType);

      return this.prisma.user.update({
        where: { id },
        data: {
          photoUrl: url,
          photoS3FileName: s3FileName,
          thumbnailUrl: thumbnailUrl,
          thumbnailS3FileName: thumbnailS3FileName,
          iconUrl: iconUrl,
          iconS3FileName: iconS3FileName,
        },
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to upload user photo: ${error.message}`,
      );
    }
  }

  async removeUserPhoto(id: string) {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id },
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      if (user?.photoS3FileName) {
        await this.photoService.removePhoto(user.photoS3FileName);
      }

      return this.prisma.user.update({
        where: { id },
        data: {
          photoUrl: null,
          photoS3FileName: null,
          thumbnailUrl: null,
          thumbnailS3FileName: null,
          iconUrl: null,
          iconS3FileName: null,
        },
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to remove user photo: ${error.message}`,
      );
    }
  }

  /**
   * Generate a unique username
   * first.last: jonh.smith, john.smith2, john.smith3, etc
   * @param firstName First name
   * @param lastName Last name
   * @returns Unique username
   */
  async generateUniqueUsername(firstName: string, lastName: string) {
    // Strip spaces and special characters, keep only alphanumeric characters
    // This handles cases like "Mary Jane" -> "MaryJane" and "O'Connor" -> "OConnor"
    const cleanFirstName = firstName.replace(/[^a-zA-Z0-9]/g, '');

    // For last name, if it contains a hyphen, use only the first part
    // This handles cases like "Smith-Jones" -> "Smith"
    const lastNamePart = lastName.split('-')[0];
    const cleanLastName = lastNamePart.replace(/[^a-zA-Z0-9]/g, '');

    let username = `${cleanFirstName}.${cleanLastName}`.toLowerCase();
    let counter = 1;

    // eslint-disable-next-line no-constant-condition
    while (true) {
      const existingUser = await this.prisma.user.findUnique({
        where: {
          username_deleted_at: {
            username,
            deleted_at: 0,
          },
        },
      });

      if (!existingUser) {
        return username;
      }

      username = `${cleanFirstName}.${cleanLastName}${counter}`.toLowerCase();
      counter++;
    }
  }

  /**
   * Synchronize a user with Moodle
   * @param user User data
   * @returns Object containing user data and generated password
   */
  private async syncUserToMoodle(
    user: any,
  ): Promise<{ moodlePassword?: string }> {
    if (!user.email) {
      this.logger.warn(`Cannot sync user to Moodle: email is missing`);
      return {};
    }

    try {
      // Check if user already exists in Moodle
      const moodleUser = await this.moodleService.getMoodleUserById(
        user.moodleUserId,
      );

      if (!moodleUser) {
        // Generate password for Moodle user
        const moodlePassword = this.generateRandomPassword();

        // Create new user in Moodle
        const moodleUserData = await this.moodleService.createUser({
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          email: user.email,
          username: user.username,
          phone: user.phone || user.phone_mobile || user.phone_work || '',
          city: user.address_city || '',
          state: user.address_state || '',
          zip: user.address_postalcode || '',
          password: moodlePassword,
        });

        if (moodleUserData && moodleUserData.id) {
          // Update our user with the Moodle user ID and store the password
          await this.prisma.user.update({
            where: { id: user.id },
            data: {
              hasMoodleAccount: true,
              moodleUserId: moodleUserData.id.toString(),
              moodlePassword: moodlePassword,
            },
          });

          this.logger.log(
            `Created Moodle user for ${user.email} with ID: ${moodleUserData.id}`,
          );

          return { moodlePassword };
        }
      } else {
        this.moodleService.updateUser({
          userId: moodleUser.id,
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          email: user.email,
          username: user.username,
          phone: user.phone || user.phone_mobile || user.phone_work || '',
          city: user.address_city || '',
          state: user.address_state || '',
          zip: user.address_postalcode || '',
        });

        // User already exists in Moodle, link to our system
        await this.prisma.user.update({
          where: { id: user.id },
          data: {
            hasMoodleAccount: true,
            moodleUserId: moodleUser.id.toString(),
          },
        });

        this.logger.log(
          `Linked existing Moodle user for ${user.email} with ID: ${moodleUser.id}`,
        );
      }
    } catch (error) {
      this.logger.error(`Moodle synchronization error: ${error.message}`);
      throw error;
    }

    return {};
  }

  /**
   * Remove a user from Moodle by moodle id
   * @param id User id
   */
  private async removeUserFromMoodle(id: string): Promise<void> {
    try {
      // Find the user in Moodle by id
      const moodleUser = await this.moodleService.getMoodleUserById(id);

      if (moodleUser && moodleUser.id) {
        // Call the Moodle API to delete the user
        await this.moodleService.deleteUser(id);
        this.logger.log(`Successfully removed Moodle user with id ${id}`);
      } else {
        this.logger.warn(`No Moodle user found with id ${id}`);
      }
    } catch (error) {
      this.logger.error(`Failed to remove user from Moodle: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate a cryptographically secure random password
   * @returns Random password (10 characters with guaranteed character diversity)
   */
  generateRandomPassword(): string {
    // Define character sets for guaranteed diversity
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

    // Ensure at least one character from each set
    let password = '';
    password += uppercase[crypto.randomInt(0, uppercase.length)];
    password += lowercase[crypto.randomInt(0, lowercase.length)];
    password += numbers[crypto.randomInt(0, numbers.length)];
    password += symbols[crypto.randomInt(0, symbols.length)];

    // Fill remaining 6 characters from all character sets
    const allChars = uppercase + lowercase + numbers + symbols;
    for (let i = 4; i < 10; i++) {
      password += allChars[crypto.randomInt(0, allChars.length)];
    }

    // Shuffle the password to avoid predictable patterns
    const passwordArray = password.split('');
    for (let i = passwordArray.length - 1; i > 0; i--) {
      const j = crypto.randomInt(0, i + 1);
      [passwordArray[i], passwordArray[j]] = [
        passwordArray[j],
        passwordArray[i],
      ];
    }

    return passwordArray.join('');
  }

  /**
   * Update an existing Moodle user with our system data
   * @param user User data from our system
   */
  private async updateExistingMoodleUser(user: any): Promise<void> {
    if (!user.moodleUserId) {
      this.logger.warn(`Cannot update Moodle user: moodleUserId is missing`);
      return;
    }

    try {
      await this.moodleService.updateUser({
        userId: user.moodleUserId,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone || user.phone_mobile || user.phone_work,
        city: user.address_city,
        state: user.address_state,
        zip: user.address_postalcode,
      });

      this.logger.log(`Updated Moodle user with ID: ${user.moodleUserId}`);
    } catch (error) {
      this.logger.error(`Failed to update Moodle user: ${error.message}`);
      throw error;
    }
  }
}
