import {
  Controller,
  Post,
  Delete,
  Param,
  Body,
  Get,
  Res,
} from '@nestjs/common';
import { PhotoService } from './photo.service';
import { Response } from 'express';
import { UploadPhotoDto } from './dto/upload-photo.dto';
import {
  PhotoResponseDto,
  PhotoDeleteResponseDto,
} from './dto/photo-response.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';

@ApiTags('Photos')
@Controller('photo')
export class PhotoController {
  constructor(private readonly photoService: PhotoService) {}

  @Post()
  @ApiOperation({ summary: 'Upload a photo' })
  @ApiResponse({
    status: 201,
    description: 'Photo uploaded successfully',
    type: PhotoResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async uploadPhoto(@Body() body: UploadPhotoDto): Promise<PhotoResponseDto> {
    return this.photoService.uploadPhoto(body.photo, body.contentType);
  }

  @Get(':filename')
  @ApiOperation({ summary: 'Get a photo by filename' })
  @ApiParam({
    name: 'filename',
    description: 'The filename of the photo to retrieve',
  })
  @ApiResponse({ status: 200, description: 'Photo retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Photo not found' })
  async getPhoto(@Res() res: Response, @Param('filename') filename: string) {
    try {
      const file = await this.photoService.downloadPhoto(filename);
      res.setHeader('Content-Type', 'image/jpeg');
      return res.send(file);
    } catch {
      return res.status(404).json({ message: 'Photo not found' });
    }
  }

  @Delete(':filename')
  @ApiOperation({ summary: 'Delete a photo by filename' })
  @ApiParam({
    name: 'filename',
    description: 'The filename of the photo to delete',
  })
  @ApiResponse({
    status: 200,
    description: 'Photo deleted successfully',
    type: PhotoDeleteResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Photo not found' })
  async removePhoto(
    @Param('filename') filename: string,
  ): Promise<PhotoDeleteResponseDto> {
    return this.photoService.removePhoto(filename);
  }
}
