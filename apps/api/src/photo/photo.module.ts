import { Module } from '@nestjs/common';
import { PhotoController } from './photo.controller';
import { PhotoService } from './photo.service';
import { S3Module } from '../s3/s3.module';
import { ConfigModule } from '@nestjs/config';
import { UuidModule } from '../uuid/uuid.module';

@Module({
  imports: [S3Module, ConfigModule, UuidModule],
  controllers: [PhotoController],
  providers: [PhotoService],
  exports: [PhotoService],
})
export class PhotoModule {}
