import {
  Injectable,
  BadRequestException,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { S3Service } from '../s3/s3.service';
import { UuidService } from '../uuid/uuid.service';
import * as sharp from 'sharp';

@Injectable()
export class PhotoService {
  private readonly MAX_PHOTO_SIZE = 5 * 1024 * 1024; // 5MB in bytes (increased from 1MB)
  private readonly THUMBNAIL_SIZE = 200; // Thumbnail size in pixels (width and height)
  private readonly ICON_SIZE = 40; // Icon size for navbar and small displays

  constructor(
    private readonly s3Service: S3Service,
    private readonly configService: ConfigService,
    private readonly uuidService: UuidService,
  ) {}

  /**
   * Creates a thumbnail from a base64 image
   * @param base64Image Base64 encoded image
   * @param size Size of the thumbnail (width/height)
   * @returns Buffer containing the thumbnail image
   */
  private async createThumbnail(
    base64Image: string,
    size: number,
  ): Promise<Buffer> {
    try {
      // Remove base64 header if present
      const base64Data = base64Image.replace(/^data:image\/\w+;base64,/, '');
      const buffer = Buffer.from(base64Data, 'base64');

      // Create thumbnail using sharp
      return await sharp(buffer)
        .resize({
          width: size,
          height: size,
          fit: 'cover',
          position: 'centre',
        })
        .toBuffer();
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to create thumbnail: ${error.message}`,
      );
    }
  }

  async uploadPhoto(base64Photo: string, contentType: string) {
    try {
      if (!base64Photo) {
        throw new BadRequestException('Photo data is required');
      }

      if (!contentType) {
        throw new BadRequestException('Content type is required');
      }

      // Calculate the size of the base64 string
      const base64Data = base64Photo.replace(/^data:image\/\w+;base64,/, '');
      const buffer = Buffer.from(base64Data, 'base64');
      const fileSize = buffer.length;

      if (fileSize > this.MAX_PHOTO_SIZE) {
        throw new BadRequestException('Photo size must be less than 5MB');
      }

      // Default to jpeg if we can't determine the extension
      const extension = contentType.split('/')[1] || 'jpeg';
      const uniqueId = this.uuidService.generateUuid();
      const filename = `mom-${uniqueId}.${extension}`;
      const thumbnailFilename = `mom-${uniqueId}-thumb.${extension}`;
      const iconFilename = `mom-${uniqueId}-icon.${extension}`;

      // Generate thumbnails
      const thumbnailBuffer = await this.createThumbnail(
        base64Photo,
        this.THUMBNAIL_SIZE,
      );
      const iconBuffer = await this.createThumbnail(
        base64Photo,
        this.ICON_SIZE,
      );

      const thumbnailBase64 = `data:${contentType};base64,${thumbnailBuffer.toString('base64')}`;
      const iconBase64 = `data:${contentType};base64,${iconBuffer.toString('base64')}`;

      // Upload original photo and both thumbnails to S3
      await Promise.all([
        this.s3Service.uploadFile(base64Photo, filename, contentType),
        this.s3Service.uploadFile(
          thumbnailBase64,
          thumbnailFilename,
          contentType,
        ),
        this.s3Service.uploadFile(iconBase64, iconFilename, contentType),
      ]);

      const region = this.configService.get('AWS_REGION');
      const bucketUrl = `https://${this.configService.get('AWS_S3_BUCKET_NAME')}.s3.${region}.amazonaws.com`;

      return {
        s3FileName: filename,
        url: `${bucketUrl}/${filename}`,
        thumbnailS3FileName: thumbnailFilename,
        thumbnailUrl: `${bucketUrl}/${thumbnailFilename}`,
        iconS3FileName: iconFilename,
        iconUrl: `${bucketUrl}/${iconFilename}`,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to upload photo: ${error.message}`,
      );
    }
  }

  async downloadPhoto(filename: string) {
    try {
      if (!filename) {
        throw new BadRequestException('Filename is required');
      }

      const file = await this.s3Service.downloadFile(filename);

      if (!file) {
        throw new NotFoundException(
          `Photo with filename ${filename} not found`,
        );
      }

      return file;
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to download photo: ${error.message}`,
      );
    }
  }

  async removePhoto(filename: string) {
    try {
      if (!filename) {
        throw new BadRequestException('Filename is required');
      }

      // Delete the original photo and all its thumbnails
      const thumbnailFilename = filename.replace(/(\.\w+)$/, '-thumb$1');
      const iconFilename = filename.replace(/(\.\w+)$/, '-icon$1');

      await Promise.all([
        this.s3Service.deleteFile(filename),
        this.s3Service.deleteFile(thumbnailFilename).catch(() => {
          // Ignore errors if thumbnail doesn't exist
        }),
        this.s3Service.deleteFile(iconFilename).catch(() => {
          // Ignore errors if icon doesn't exist
        }),
      ]);

      return {
        success: true,
        message: `Photo ${filename} successfully deleted`,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to remove photo: ${error.message}`,
      );
    }
  }
}
