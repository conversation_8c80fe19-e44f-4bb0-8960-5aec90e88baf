import { IsString, <PERSON>NotEmpty, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UploadPhotoDto {
  @ApiProperty({
    description: 'Base64 encoded photo data',
  })
  @IsString()
  @IsNotEmpty()
  photo: string;

  @ApiProperty({
    description: 'Content type of the photo',
    example: 'image/jpeg',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^image\/(jpeg|png|gif)$/, { message: 'Invalid content type' })
  contentType: string;
}
