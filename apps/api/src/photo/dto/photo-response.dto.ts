import { ApiProperty } from '@nestjs/swagger';

export class PhotoResponseDto {
  @ApiProperty({
    description: 'The S3 filename of the uploaded photo',
    example: 'mom-123e4567-e89b-12d3-a456-426614174000.jpeg',
  })
  s3FileName: string;

  @ApiProperty({
    description: 'The URL where the photo can be accessed',
    example:
      'https://my-bucket.s3.amazonaws.com/mom-123e4567-e89b-12d3-a456-426614174000.jpeg',
  })
  url: string;
}

export class PhotoDeleteResponseDto {
  @ApiProperty({
    description: 'Whether the deletion was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Message describing the result of the deletion',
    example:
      'Photo mom-123e4567-e89b-12d3-a456-426614174000.jpeg successfully deleted',
  })
  message: string;
}
