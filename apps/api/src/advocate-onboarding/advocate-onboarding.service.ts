import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { ExtendedPrismaClientFactoryService } from '../extended-prisma-client-factory/extended-prisma-client-factory.service';
import { CloudflareTurnstileService } from '../cloudflare-turnstile/cloudflare-turnstile.service';
import { SaveApplicationDto } from './dto/save-application.dto';
import { SaveInterestDto } from './dto/save-interest.dto';
import * as crypto from 'crypto';
import * as bcrypt from 'bcrypt';
import { UsersService } from '../users/users.service';
import { MoodleService } from '../moodle/moodle.service';

@Injectable()
export class AdvocateOnboardingService {
  private readonly logger = new Logger(AdvocateOnboardingService.name);
  protected readonly prisma: ReturnType<
    ExtendedPrismaClientFactoryService['createExtendedPrismaClient']
  >;

  constructor(
    private readonly prismaFactory: ExtendedPrismaClientFactoryService,
    private readonly cloudflareTurnstileService: CloudflareTurnstileService,
    private readonly usersService: UsersService,
    private readonly moodleService: MoodleService,
  ) {
    this.prisma = this.prismaFactory.createExtendedPrismaClient();
  }

  private validateInterviewTimeSlot(
    interviewDate: Date,
    interviewStartTime: string,
    interviewEndTime: string,
  ) {
    const startTime = new Date(
      `${interviewDate.toISOString().split('T')[0]}T${interviewStartTime}`,
    );
    const endTime = new Date(
      `${interviewDate.toISOString().split('T')[0]}T${interviewEndTime}`,
    );
    const now = new Date();

    if (startTime < now) {
      throw new BadRequestException(
        'Interview start time must be in the future',
      );
    }

    if (endTime <= startTime) {
      throw new BadRequestException(
        'Interview end time must be after start time',
      );
    }

    const duration = endTime.getTime() - startTime.getTime();
    if (duration < 30 * 60 * 1000 || duration > 120 * 60 * 1000) {
      throw new BadRequestException(
        'Interview duration must be between 30 minutes and 2 hours',
      );
    }
  }

  private async cleanupFailedUser(userId: string) {
    try {
      await this.prisma.user.delete({
        where: { id: userId },
      });
    } catch (error) {
      this.logger.error(`Failed to cleanup user ${userId}: ${error.message}`);
    }
  }

  async saveInterest(saveInterestDto: SaveInterestDto) {
    let createdUser = null;

    try {
      // Validate required fields
      if (
        !saveInterestDto.email ||
        !saveInterestDto.firstName ||
        !saveInterestDto.lastName
      ) {
        throw new BadRequestException(
          'Email, first name, and last name are required',
        );
      }

      // Verify the turnstile token
      try {
        const turnstileResult =
          await this.cloudflareTurnstileService.verifyToken(
            saveInterestDto.cloudflare_turnstile_token,
          );

        if (!turnstileResult.success) {
          this.logger.warn(
            `Invalid turnstile token for email: ${saveInterestDto.email}`,
          );
          throw new BadRequestException('Invalid turnstile token');
        }
      } catch (error) {
        this.logger.error(`Turnstile verification failed: ${error.message}`);
        throw new BadRequestException('Failed to verify turnstile token');
      }

      const {
        firstName,
        lastName,
        email,
        phone,
        city,
        state,
        zip,
        referredBy,
        willingBackgroundCheck,
        affiliateId,
      } = saveInterestDto;

      // Check if user already exists in our system
      try {
        const existingUser = await this.prisma.user.findFirst({
          where: {
            email: email,
            deleted_at: 0,
          },
        });

        if (existingUser) {
          this.logger.warn(
            `Attempt to create duplicate user with email: ${email}`,
          );
          throw new ConflictException('A user with this email already exists');
        }
      } catch (error) {
        if (error instanceof ConflictException) {
          throw error;
        }
        this.logger.error(
          `Database error while checking existing user: ${error.message}`,
        );
        throw new InternalServerErrorException(
          'Failed to check for existing user',
        );
      }

      // Check if user exists in Moodle
      let moodleUser = null;
      try {
        moodleUser = await this.moodleService.findUserByEmail(email);
        if (moodleUser && moodleUser.id) {
          this.logger.log(`Found existing Moodle user with email: ${email}`);
        }
      } catch (error) {
        this.logger.warn(`Error checking Moodle user: ${error.message}`);
        // We'll continue even if Moodle check fails
      }

      const username = await this.usersService.generateUniqueUsername(
        firstName,
        lastName,
      );
      const moodlePassword = this.usersService.generateRandomPassword();

      try {
        // Create Moodle user if doesn't exist already
        let moodleUserId = null;
        if (!moodleUser) {
          try {
            const moodleUserData = await this.moodleService.createUser({
              firstName,
              lastName,
              email,
              phone,
              city,
              state,
              zip,
              username,
              password: moodlePassword,
            });

            if (moodleUserData && moodleUserData.id) {
              moodleUserId = moodleUserData.id;
              this.logger.log(
                `Created new Moodle user for ${email} with ID: ${moodleUserId}`,
              );
            }
          } catch (moodleError) {
            // Log error but continue with our system's user creation
            this.logger.error(
              `Failed to create Moodle user: ${moodleError.message}`,
            );
          }
        } else if (moodleUser && moodleUser.id) {
          moodleUserId = moodleUser.id;
        }

        // Generate a temporary password hash
        const tempPassword = crypto.randomBytes(8).toString('hex');
        const passwordHash = await bcrypt.hash(tempPassword, 10);

        // Create the user using UsersService
        createdUser = await this.usersService.create({
          id: crypto.randomUUID(),
          firstName,
          lastName,
          email,
          phone,
          address_city: city,
          address_state: state,
          address_postalcode: zip,
          advocate_status: 'Interested',
          status: 'Active',
          username,
          passwordHash,
          communication_preference: 'both',
          sms_message_opt_in: true,
          updated_at: new Date(),
          hasMoodleAccount: moodleUserId !== null,
          moodleUserId: moodleUserId.toString(),
          moodlePassword: moodleUserId ? moodlePassword : null, // Store the Moodle password
          affiliateId: affiliateId, // Use the selected affiliate ID
          userRoles: {
            create: {
              role: {
                connect: {
                  key: 'onboarding_advocate',
                },
              },
            },
          },
        });

        // Generate a tracking token
        const trackingToken = crypto.randomUUID();

        // Create the advocate onboarding record with tracking token
        await this.prisma.advocateOnboarding.create({
          data: {
            id: crypto.randomUUID(),
            userId: createdUser.id,
            referredBy,
            canCompleteBackground: willingBackgroundCheck === 'yes',
            trackingToken,
            updated_at: new Date(),
          },
        });

        return { user: createdUser, trackingToken };
      } catch (error) {
        if (createdUser) {
          await this.cleanupFailedUser(createdUser.id);
        }
        throw error;
      }
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof ConflictException ||
        error instanceof InternalServerErrorException
      ) {
        throw error;
      }
      this.logger.error(`Unexpected error in saveInterest: ${error.message}`);
      throw new InternalServerErrorException('Failed to process interest form');
    }
  }

  async saveApplication(saveApplicationDto: SaveApplicationDto) {
    try {
      // Validate required fields
      if (!saveApplicationDto.trackingToken) {
        throw new BadRequestException('Tracking token is required');
      }

      // Validate interview time slot if provided
      if (
        saveApplicationDto.interviewDate &&
        saveApplicationDto.interviewStartTime &&
        saveApplicationDto.interviewEndTime
      ) {
        this.validateInterviewTimeSlot(
          new Date(saveApplicationDto.interviewDate),
          saveApplicationDto.interviewStartTime,
          saveApplicationDto.interviewEndTime,
        );
      }

      // Verify the turnstile token
      try {
        const turnstileResult =
          await this.cloudflareTurnstileService.verifyToken(
            saveApplicationDto.cloudflare_turnstile_token,
          );

        if (!turnstileResult.success) {
          this.logger.warn(
            `Invalid turnstile token for tracking token: ${saveApplicationDto.trackingToken}`,
          );
          throw new BadRequestException('Invalid turnstile token');
        }
      } catch (error) {
        this.logger.error(`Turnstile verification failed: ${error.message}`);
        throw new BadRequestException('Failed to verify turnstile token');
      }

      const {
        trackingToken,
        referredBy,
        canCompleteBackground,
        interests,
        hasMultiLang,
        languages,
        hasExpCrisis,
        hasExpFoster,
        hasExpVictims,
        hasExpWelfare,
        hasExpChildren,
        personalNote,
        groupPref,
        parentingNote,
        availability,
        interviewDate,
        interviewStartTime,
        interviewEndTime,
        interviewMeetingType,
        interviewLocation,
      } = saveApplicationDto;

      // Validate interview meeting type if provided
      if (
        interviewMeetingType &&
        !['Virtual', 'InPerson'].includes(interviewMeetingType)
      ) {
        throw new BadRequestException(
          'interviewMeetingType must be one of the following values: Virtual, InPerson',
        );
      }

      // Find and update the onboarding record using the tracking token
      try {
        const onboarding = await this.prisma.advocateOnboarding.findUnique({
          where: { trackingToken },
        });

        if (!onboarding) {
          this.logger.warn(
            `Onboarding record not found for tracking token: ${trackingToken}`,
          );
          throw new NotFoundException('Onboarding record not found');
        }

        const updatedOnboarding = await this.prisma.advocateOnboarding.update({
          where: { trackingToken },
          data: {
            referredBy,
            canCompleteBackground,
            interests: {
              set: interests,
            },
            hasMultiLang,
            languages: {
              set: languages,
            },
            hasExpCrisis,
            hasExpFoster,
            hasExpVictims,
            hasExpWelfare,
            hasExpChildren,
            personalNote,
            groupPref,
            parentingNote,
            availability,
            interviewDate,
            interviewStartTime,
            interviewEndTime,
            interviewMeetingType,
            interviewLocation,
            updated_at: new Date(),
          },
        });

        return updatedOnboarding;
      } catch (error) {
        if (error instanceof NotFoundException) {
          throw error;
        }
        this.logger.error(
          `Database error while updating application: ${error.message}`,
        );
        throw new InternalServerErrorException(
          'Failed to update application data',
        );
      }
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException ||
        error instanceof InternalServerErrorException
      ) {
        throw error;
      }
      this.logger.error(
        `Unexpected error in saveApplication: ${error.message}`,
      );
      throw new InternalServerErrorException(
        'Failed to process application form',
      );
    }
  }

  async validateToken(token: string) {
    if (!token) {
      this.logger.warn('Validation attempt with empty token');
      throw new BadRequestException('Tracking token is required');
    }

    try {
      const onboardingData = await this.prisma.advocateOnboarding.findFirst({
        where: {
          trackingToken: token,
          user: {
            advocate_status: 'Interested',
          },
        },
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
              phone: true,
              address_city: true,
              address_state: true,
              address_postalcode: true,
            },
          },
        },
      });

      if (!onboardingData) {
        this.logger.warn(`Invalid or expired tracking token: ${token}`);
        throw new NotFoundException('Invalid or expired tracking token');
      }

      return {
        isValid: true,
        data: {
          userId: onboardingData.userId,
          firstName: onboardingData.user.firstName,
          lastName: onboardingData.user.lastName,
          email: onboardingData.user.email,
          phone: onboardingData.user.phone,
          city: onboardingData.user.address_city,
          state: onboardingData.user.address_state,
          zip: onboardingData.user.address_postalcode,
          referredBy: onboardingData.referredBy,
          willingBackgroundCheck: onboardingData.canCompleteBackground,
        },
      };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      this.logger.error(`Error validating token: ${error.message}`);
      throw new InternalServerErrorException('Failed to validate token');
    }
  }
}
