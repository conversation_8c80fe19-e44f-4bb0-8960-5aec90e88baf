import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateIf,
} from 'class-validator';
import { Language } from '@prisma/client';

export enum Interest {
  Advocate = 'Advocate',
  Volunteer = 'Volunteer',
}

export enum MeetingType {
  Virtual = 'Virtual',
  InPerson = 'InPerson',
}

export class SaveApplicationDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  trackingToken: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty()
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phone: string;

  @ApiProperty({ required: false })
  @ValidateIf((o) => o.dateOfBirth !== null && o.dateOfBirth !== undefined)
  @IsOptional()
  @Transform(({ value }) => {
    if (!value) return null;
    const date = new Date(value);
    return isNaN(date.getTime()) ? value : date;
  })
  dateOfBirth?: Date;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  addressStreet: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  addressCity: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  addressState: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  addressPostalcode: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  referredBy?: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  canCompleteBackground?: boolean;

  @ApiProperty({ enum: Interest, isArray: true })
  @IsArray()
  @IsEnum(Interest, { each: true })
  interests: Interest[];

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  hasMultiLang?: boolean;

  @ApiProperty({ enum: Language, isArray: true })
  @IsArray()
  @IsEnum(Language, { each: true })
  languages: Language[];

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  hasExpCrisis?: boolean;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  hasExpFoster?: boolean;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  hasExpVictims?: boolean;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  hasExpWelfare?: boolean;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  hasExpChildren?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  personalNote?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  groupPref?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  parentingNote?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  availability?: string;

  @ApiProperty({ required: false })
  @ValidateIf((o) => o.interviewDate !== null && o.interviewDate !== undefined)
  @IsOptional()
  @Transform(({ value }) => {
    if (!value) return null;
    const date = new Date(value);
    return isNaN(date.getTime()) ? value : date;
  })
  interviewDate?: Date;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  interviewStartTime?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  interviewEndTime?: string;

  @ApiProperty({ enum: MeetingType })
  @IsEnum(MeetingType)
  interviewMeetingType: MeetingType;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  interviewLocation?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  cloudflare_turnstile_token: string;
}
