import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class SaveInterestDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty()
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phone: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  city: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  state: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  zip: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  referredBy?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  willingBackgroundCheck: 'yes' | 'no';

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  affiliateId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  cloudflare_turnstile_token: string;
}
