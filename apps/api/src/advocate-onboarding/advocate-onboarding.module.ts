import { AdvocateOnboardingController } from './advocate-onboarding.controller';
import { AdvocateOnboardingService } from './advocate-onboarding.service';
import { Module } from '@nestjs/common';
import { ExtendedPrismaClientFactoryService } from '../extended-prisma-client-factory/extended-prisma-client-factory.service';
import { CloudflareTurnstileService } from '../cloudflare-turnstile/cloudflare-turnstile.service';
import { UsersModule } from '../users/users.module';
import { MoodleModule } from '../moodle/moodle.module';

@Module({
  imports: [UsersModule, MoodleModule],
  controllers: [AdvocateOnboardingController],
  providers: [
    AdvocateOnboardingService,
    ExtendedPrismaClientFactoryService,
    CloudflareTurnstileService,
  ],
})
export class AdvocateOnboardingModule {}
