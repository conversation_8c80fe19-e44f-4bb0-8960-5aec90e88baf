import { Controller, Post, Body, Get, Query } from '@nestjs/common';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { AdvocateOnboardingService } from './advocate-onboarding.service';
import { SaveApplicationDto } from './dto/save-application.dto';
import { SaveInterestDto } from './dto/save-interest.dto';
import { SkipJwtAuth } from '../auth/skip-jwt-auth.decorator';

@ApiTags('Advocate Onboarding')
@Controller('advocate-onboarding')
@SkipJwtAuth()
export class AdvocateOnboardingController {
  constructor(
    private readonly advocateOnboardingService: AdvocateOnboardingService,
  ) {}

  @Post('save-interest')
  @ApiOperation({ summary: 'Save initial interest form data' })
  @ApiBody({ type: SaveInterestDto, description: 'Interest form data' })
  @ApiResponse({
    status: 201,
    description: 'Interest form data saved successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid interest form data',
  })
  async saveInterest(@Body() saveInterestDto: SaveInterestDto) {
    return this.advocateOnboardingService.saveInterest(saveInterestDto);
  }

  @Post('save-application')
  @ApiOperation({ summary: 'Save complete advocate application' })
  @ApiBody({
    type: SaveApplicationDto,
    description: 'Advocate application data',
  })
  @ApiResponse({
    status: 201,
    description: 'Application saved successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid application data',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  async saveApplication(@Body() saveApplicationDto: SaveApplicationDto) {
    return this.advocateOnboardingService.saveApplication(saveApplicationDto);
  }

  @Get('validate-token')
  @ApiOperation({ summary: 'Validate tracking token and get onboarding data' })
  @ApiQuery({
    name: 't',
    required: true,
    description: 'Tracking token from interest form',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Token is valid and returns onboarding data',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid token',
  })
  @ApiResponse({
    status: 404,
    description: 'Token not found',
  })
  async validateToken(@Query('t') token: string) {
    return this.advocateOnboardingService.validateToken(token);
  }
}
