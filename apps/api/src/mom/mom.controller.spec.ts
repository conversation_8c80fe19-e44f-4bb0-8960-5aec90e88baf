import { Test, TestingModule } from '@nestjs/testing';
import { <PERSON><PERSON><PERSON>roller } from './mom.controller';
import { MomService } from './mom.service';

describe('MomController', () => {
  let controller: MomController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MomController],
      providers: [MomService],
    }).compile();

    controller = module.get<MomController>(MomController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
