import { <PERSON>du<PERSON> } from '@nestjs/common';
import { MomController } from './mom.controller';
import { MomService } from './mom.service';
import { ReferralController } from './referral.controller';
import { PhotoModule } from '../photo/photo.module';

@Module({
  imports: [PhotoModule],
  controllers: [MomController, ReferralController],
  providers: [MomService],
})
export class MomModule {}
