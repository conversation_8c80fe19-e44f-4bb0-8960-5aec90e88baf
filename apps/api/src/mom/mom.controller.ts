import { Controller, Get, Param, Post, Body, Delete } from '@nestjs/common';
import { MomService } from './mom.service';

@Controller('mom')
export class MomController {
  constructor(private readonly momService: MomService) {}

  @Get()
  findAll() {
    return this.momService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.momService.findOne(id);
  }

  @Get(':id/documents')
  getDocuments(@Param('id') id: string) {
    return this.momService.getDocuments(id);
  }

  @Post(':id/photo')
  async uploadMomPhoto(
    @Param('id') id: string,
    @Body() body: { photo: string; contentType: string },
  ) {
    return this.momService.uploadMomPhoto(id, body.photo, body.contentType);
  }

  @Delete(':id/photo')
  async removeMomPhoto(@Param('id') id: string) {
    return this.momService.removeMomPhoto(id);
  }
}
