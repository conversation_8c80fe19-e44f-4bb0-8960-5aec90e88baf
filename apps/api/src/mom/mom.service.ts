import {
  Injectable,
  Inject,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { ENHANCED_PRISMA } from '@zenstackhq/server/nestjs';
import { PhotoService } from '../photo/photo.service';

@Injectable()
export class MomService {
  constructor(
    @Inject(ENHANCED_PRISMA)
    private readonly prisma: PrismaClient,
    private readonly photoService: PhotoService,
  ) {}

  async findAll() {
    try {
      // TODO: Update to only return moms that the calling user has access to.
      return this.prisma.mom.findMany();
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to fetch moms: ${error.message}`,
      );
    }
  }

  async findAllReferrals() {
    try {
      return this.prisma.mom.findMany({
        where: {
          prospect_status: 'prospect',
        },
      });
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to fetch referrals: ${error.message}`,
      );
    }
  }

  async findOne(id: string) {
    try {
      const mom = await this.prisma.mom.findUnique({
        where: { id },
      });

      if (!mom) {
        throw new NotFoundException(`Mom with ID ${id} not found`);
      }

      return mom;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to fetch mom: ${error.message}`,
      );
    }
  }

  async getDocuments(momId: string) {
    try {
      const mom = await this.prisma.mom.findUnique({
        where: { id: momId },
      });

      if (!mom) {
        throw new NotFoundException(`Mom with ID ${momId} not found`);
      }

      return this.prisma.document.findMany({
        where: { mom_id: momId },
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to fetch documents: ${error.message}`,
      );
    }
  }

  async uploadMomPhoto(id: string, photo: string, contentType: string) {
    try {
      const mom = await this.prisma.mom.findUnique({
        where: { id },
      });

      if (!mom) {
        throw new NotFoundException(`Mom with ID ${id} not found`);
      }

      const {
        s3FileName,
        url,
        thumbnailS3FileName,
        thumbnailUrl,
        iconS3FileName,
        iconUrl,
      } = await this.photoService.uploadPhoto(photo, contentType);

      return this.prisma.mom.update({
        where: { id },
        data: {
          photoUrl: url,
          photoS3FileName: s3FileName,
          thumbnailUrl: thumbnailUrl,
          thumbnailS3FileName: thumbnailS3FileName,
          iconUrl: iconUrl,
          iconS3FileName: iconS3FileName,
        },
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to upload mom photo: ${error.message}`,
      );
    }
  }

  async removeMomPhoto(id: string) {
    try {
      const mom = await this.prisma.mom.findUnique({
        where: { id },
      });

      if (!mom) {
        throw new NotFoundException(`Mom with ID ${id} not found`);
      }

      if (mom?.photoS3FileName) {
        await this.photoService.removePhoto(mom.photoS3FileName);
      }

      return this.prisma.mom.update({
        where: { id },
        data: {
          photoUrl: null,
          photoS3FileName: null,
          thumbnailUrl: null,
          thumbnailS3FileName: null,
          iconUrl: null,
          iconS3FileName: null,
        },
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to remove mom photo: ${error.message}`,
      );
    }
  }
}
