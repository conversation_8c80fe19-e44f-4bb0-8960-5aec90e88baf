import {
  <PERSON>,
  <PERSON>,
  Param,
  <PERSON><PERSON>,
  Post,
  Body,
  Logger,
} from '@nestjs/common';
import { DocumentService } from './document.service';
import { Response } from 'express';
import { S3Service } from 'src/s3/s3.service';
import { YupSchemas } from '@suiteapi/models';
import { Readable } from 'stream';
import { ReadableStream } from 'stream/web';

@Controller('document')
export class DocumentController {
  private readonly logger = new Logger(DocumentController.name);

  constructor(
    private readonly documentService: DocumentService,
    private readonly s3Service: S3Service,
  ) {}

  @Post()
  async create(@Body() document: YupSchemas.DocumentSchema) {
    return await this.documentService.create({
      ...document,
      document_name: document.document_name ?? document.filename,
      advocate_id: document.advocate_id ?? null,
      mom_id: document.mom_id ?? null,
      coordinator_id: document.coordinator_id ?? null,
      lesson_template_id: document.lesson_template_id ?? null,
    });
  }

  @Get('advocate/:id')
  async getByAdvocateId(@Param('id') id: string) {
    return await this.documentService.findByAdvocateId(id);
  }

  @Get('lesson-template/:id')
  async getByLessonTemplateId(@Param('id') id: string) {
    return await this.documentService.findByLessonTemplateId(id);
  }

  @Get(':id')
  async get(@Res() res: Response, @Param('id') id: string) {
    try {
      const document = await this.documentService.get(id);

      if (!document.filecontents) {
        return res.status(404).json({ message: 'File contents not found' });
      }

      res.setHeader('Content-Type', document.mimeType);
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="${document.filename}"`,
      );

      try {
        // Handle AWS SDK v3 web stream
        if (document.filecontents.transformToByteArray) {
          // For web streams
          const bytes = await document.filecontents.transformToByteArray();
          return res.send(Buffer.from(bytes));
        }

        // Convert to buffer (generic approach)
        const chunks = [];
        for await (const chunk of document.filecontents as
          | Readable
          | ReadableStream) {
          chunks.push(chunk);
        }
        const buffer = Buffer.concat(chunks);
        return res.send(buffer);
      } catch (error) {
        this.logger.error('Error streaming file:', error);
        return res
          .status(500)
          .json({ message: 'Error streaming file contents' });
      }
    } catch (error) {
      this.logger.error('Error getting document:', error);
      return res.status(500).json({ message: 'Error retrieving document' });
    }
  }
}
