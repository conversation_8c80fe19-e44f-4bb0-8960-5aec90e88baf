import { Module } from '@nestjs/common';

import { DocumentController } from './document.controller';
import { DocumentService } from './document.service';
import { S3Service } from 'src/s3/s3.service';
import { UuidService } from 'src/uuid/uuid.service';
import { ConfigService } from '@nestjs/config';

@Module({
  controllers: [DocumentController],
  providers: [ConfigService, DocumentService, S3Service, UuidService],
})
export class DocumentModule {}
