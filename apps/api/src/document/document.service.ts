import { Inject, Injectable, Logger } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { ENHANCED_PRISMA } from '@zenstackhq/server/nestjs';
import { S3Service } from 'src/s3/s3.service';
import { UuidService } from 'src/uuid/uuid.service';
import { YupSchemas } from '@suiteapi/models';

@Injectable()
export class DocumentService {
  private readonly logger = new Logger(DocumentService.name);

  constructor(
    @Inject(ENHANCED_PRISMA)
    private readonly prisma: PrismaClient,
    private s3Service: S3Service,
    private uuidService: UuidService,
  ) {}

  async create(document: YupSchemas.DocumentSchema) {
    const prefix =
      document.advocate_id ||
      document.mom_id ||
      document.coordinator_id ||
      document.lesson_template_id ||
      'doc';
    const filename = `${prefix}::${this.uuidService.generateUuid()}`;

    await this.s3Service.uploadFile(
      document.filecontents,
      filename,
      document.mimeType,
    );

    // Note: Please do not store filecontents in the DB as this will bloat the DB and negatively impact performance.
    const formattedDocument = {
      filename: document.filename,
      mimeType: document.mimeType,
      advocate_id: document.advocate_id,
      mom_id: document.mom_id,
      coordinator_id: document.coordinator_id,
      lesson_template_id: document.lesson_template_id,
      s3_file_name: filename,
      created_at: new Date(),
      document_name: document.document_name,
      subcategory_id: document.subcategory_id,
      description: document.description,
    };

    return await this.prisma.document.create({
      data: formattedDocument,
    });
  }

  async get(id: string) {
    try {
      const document = await this.prisma.document.findUnique({
        where: { id },
      });

      if (!document) {
        throw new Error('Document not found');
      }

      if (!document.s3_file_name) {
        throw new Error('Document has no associated file');
      }

      const filecontents = await this.s3Service.downloadFile(
        document.s3_file_name,
      );

      return { ...document, filecontents };
    } catch (error) {
      this.logger.error(`Error getting document ${id}:`, error);
      throw error;
    }
  }

  async findByAdvocateId(advocateId: string) {
    return await this.prisma.document.findMany({
      where: {
        advocate_id: advocateId,
      },
    });
  }

  async findByMomId(momId: string) {
    return await this.prisma.document.findMany({
      where: { mom_id: momId },
    });
  }

  async findByLessonTemplateId(lessonTemplateId: string) {
    return await this.prisma.document.findMany({
      where: { lesson_template_id: lessonTemplateId },
      include: {
        documentTags: {
          include: {
            tag: true,
          },
        },
      },
    });
  }
}
