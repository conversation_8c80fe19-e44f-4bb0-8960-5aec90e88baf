import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as bodyParser from 'body-parser';
import * as cookieParser from 'cookie-parser';
import { Logger, ValidationPipe } from '@nestjs/common';
import { SwaggerModule } from '@nestjs/swagger';
import { DocumentBuilder } from '@nestjs/swagger';
import { readFileSync } from 'fs';
import { JwtExceptionFilter } from './auth/jwt-exception.filter';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Override JSON serialization to convert BigInt to string
  (BigInt.prototype as any).toJSON = function () {
    return this.toString();
  };

  app.enableCors({
    origin: process.env.CORS_ORIGINS?.split(','),
    credentials: true,
  });
  app.use(bodyParser.json({ limit: '5mb' }));
  app.use(bodyParser.urlencoded({ limit: '5mb', extended: true }));
  app.use(cookieParser());
  app.useGlobalFilters(new JwtExceptionFilter());

  // Enable validation globally
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );

  app.setGlobalPrefix('api/v1');

  const config = new DocumentBuilder()
    .setTitle('EMA API')
    .setDescription('EMA API')
    .setVersion('1.0')
    .build();

  const documentFactory = () => SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/v1/docs', app, documentFactory);

  const document = readFileSync('./openapi.json', 'utf8');
  SwaggerModule.setup('api/v1/docs/zen', app, () => JSON.parse(document));

  const logger = app.get(Logger) as Logger;

  const port = process.env.PORT ?? 3001;
  logger.log(`Listening on port ${port}`);
  await app.listen(port);
}

bootstrap();
