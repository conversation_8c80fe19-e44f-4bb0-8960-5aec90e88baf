import {
  <PERSON>,
  Post,
  Body,
  Logger,
  Query,
  Param,
  Get,
} from '@nestjs/common';
import {
  WellBeingAssessmentFormData,
  WellnessAssessmentService,
} from './wellness-assessment.service';
import { SkipJwtAuth } from 'src/auth/skip-jwt-auth.decorator';

@Controller('wellness-assessment')
export class WellnessAssessmentController {
  private readonly logger = new Logger(WellnessAssessmentController.name);

  constructor(
    private readonly wellnessAssessmentService: WellnessAssessmentService,
  ) {}

  @SkipJwtAuth()
  @Get('token/:token')
  async verifyToken(@Param('token') token: string) {
    await this.wellnessAssessmentService.verifyToken(token);
  }

  @Post('self-intake/notification')
  async sendSelfIntakeNotification(@Query('mom_id') momId: string) {
    await this.wellnessAssessmentService.sendSelfIntakeNotification(momId);
  }

  @SkipJwtAuth()
  @Post('token/:token')
  async createFromToken(
    @Param('token') token: string,
    @Body() data: WellBeingAssessmentFormData,
  ) {
    await this.wellnessAssessmentService.createByToken(token, data);
  }

  @Post('mom/:momId')
  async create(
    @Param('momId') momId: string,
    @Body()
    data: WellBeingAssessmentFormData & {
      updateMom: boolean;
      createWellnessAssessment: boolean;
    },
  ) {
    await this.wellnessAssessmentService.createByMomId(momId, data);
  }
}
