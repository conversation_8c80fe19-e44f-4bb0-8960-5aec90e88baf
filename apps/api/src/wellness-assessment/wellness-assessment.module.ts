import { Modu<PERSON> } from '@nestjs/common';
import { WellnessAssessmentService } from './wellness-assessment.service';
import { WellnessAssessmentController } from './wellness-assessment.controller';
import { ExtendedPrismaClientFactoryService } from 'src/extended-prisma-client-factory/extended-prisma-client-factory.service';
import { UuidModule } from '../uuid/uuid.module';
import { MagicLinkModule } from '../magic-link/magic-link.module';

@Module({
  imports: [UuidModule, MagicLinkModule],
  controllers: [WellnessAssessmentController],
  providers: [WellnessAssessmentService, ExtendedPrismaClientFactoryService],
})
export class WellnessAssessmentModule {}
