import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import {
  Prisma,
  NotificationTemplate,
  NotificationStatus,
  <PERSON>,
} from '@prisma/client';
import { ExtendedPrismaClientFactoryService } from 'src/extended-prisma-client-factory/extended-prisma-client-factory.service';
import { MagicLinkService } from '../magic-link/magic-link.service';
import { UuidService } from '../uuid/uuid.service';

type MomWithChildren = Mom & {
  children: {
    [key: string]: unknown;
  }[];
};

type FormValues = Record<string, unknown>;

export interface WellBeingAssessmentFormData {
  basicForm: { values: FormValues };
  clientForm: { values: FormValues };
  childForms: { values: FormValues }[];
  childrenForm: { values: FormValues };
  wellBeingForm: { values: FormValues };
}

@Injectable()
export class WellnessAssessmentService {
  private readonly logger = new Logger(WellnessAssessmentService.name);
  private prisma: ReturnType<
    ExtendedPrismaClientFactoryService['createExtendedPrismaClient']
  >;

  constructor(
    private readonly extendedPrismaFactory: ExtendedPrismaClientFactoryService,
    private readonly uuidService: UuidService,
    private readonly magicLinkService: MagicLinkService,
  ) {
    this.prisma = this.extendedPrismaFactory.createExtendedPrismaClient();
  }

  async verifyToken(token: string) {
    const magicLink = await this.magicLinkService.findByToken(token);
    if (!magicLink) {
      throw new NotFoundException('Magic link found');
    }
  }

  async sendSelfIntakeNotification(momId: string) {
    const mom = await this.prisma.mom.findUnique({
      where: {
        id: momId,
      },
      include: {
        assigned_user: true,
      },
    });

    const token = this.uuidService.generateUuid();
    await this.magicLinkService.createSelfIntake({
      userId: mom.assigned_user_id,
      token,
    });

    await this.prisma.notification.create({
      data: {
        template: NotificationTemplate.wellness_assessment_self_intake_mom,
        status: NotificationStatus.pending,
        template_params: {
          momName: `${mom.first_name} ${mom.last_name}`,
          token,
        },
        recipient_user: {
          connect: {
            id: mom.assigned_user_id,
          },
        },
      },
    });
  }

  async createByToken(token: string, data: WellBeingAssessmentFormData) {
    this.logger.log('Creating wellness assessment by token');
    this.logger.log({ token, data });

    const magicLink = await this.magicLinkService.findByToken(token);
    if (!magicLink) {
      throw new NotFoundException('Magic link found');
    }

    const mom = await this.prisma.mom.findFirst({
      include: { children: true },
      where: {
        assigned_user_id: magicLink.user_id,
      },
    });
    if (!mom) {
      throw new NotFoundException('Mom not found');
    }

    await this.prisma.notification.create({
      data: {
        template: NotificationTemplate.new_wellness_assessment_coordinator,
        status: NotificationStatus.pending,
        template_params: {
          momName: mom.first_name + ' ' + mom.last_name,
        },
        recipient_user: {
          connect: {
            id: mom.assigned_user_id,
          },
        },
      },
    });

    await this.prisma.mom.update({
      data: this.getMomUpdateInput(mom, data),
      where: { id: mom.id },
    });

    await this.prisma.wellnessAssessment.create({
      data: this.getWellnessAssessmentCreateInput(mom.id, data),
    });

    await this.magicLinkService.expireByToken(token);
  }

  async createByMomId(
    momId: string,
    data: WellBeingAssessmentFormData & {
      updateMom: boolean;
      createWellnessAssessment: boolean;
    },
  ) {
    this.logger.log('Creating wellness assessment by mom id');
    this.logger.log({ momId, data });

    const mom = await this.prisma.mom.findFirst({
      include: { children: true },
      where: { id: momId },
    });
    if (!mom) {
      throw new NotFoundException('Mom not found');
    }

    if (data.updateMom) {
      await this.prisma.mom.update({
        data: this.getMomUpdateInput(mom, data),
        where: { id: mom.id },
      });
    }

    if (data.createWellnessAssessment) {
      await this.prisma.wellnessAssessment.create({
        data: this.getWellnessAssessmentCreateInput(mom.id, data),
      });
    }
  }

  private encodeArray = (data: unknown) =>
    Array.isArray(data)
      ? data.map((item) => item.value).join(',')
      : String(data);

  private assertString = (data: unknown) =>
    typeof data === 'string' ? data : undefined;

  private assertNumber = (data: unknown) =>
    typeof data === 'number' ? data : undefined;

  private assertBoolean = (data: unknown) =>
    typeof data === 'boolean' ? data : undefined;

  private getMomUpdateInput = (
    mom: MomWithChildren,
    {
      basicForm,
      clientForm,
      childForms,
      childrenForm,
    }: WellBeingAssessmentFormData,
  ): Prisma.MomUpdateInput => {
    const newChildren: ({ id: string } & FormValues)[] = childForms.map(
      ({ values }, index) => ({
        id: `${mom.id}_${index}`,
        ...values,
      }),
    );

    const childrenIdsToDelete = mom.children
      .map((child) => child.id as string)
      .filter((childId) => !newChildren.some((child) => child.id === childId));

    return {
      first_name: this.assertString(basicForm.values.first_name),
      last_name: this.assertString(basicForm.values.last_name),
      primary_address_street: this.assertString(
        basicForm.values.primary_address_street,
      ),
      primary_address_street_two_c: this.assertString(
        basicForm.values.primary_address_street_two_c,
      ),
      primary_address_city: this.assertString(
        basicForm.values.primary_address_city,
      ),
      primary_address_state: this.assertString(
        basicForm.values.primary_address_state,
      ),
      primary_address_postalcode: this.assertString(
        basicForm.values.primary_address_postalcode,
      ),
      address_access_c: this.assertString(basicForm.values.address_access_c),
      birthdate: this.assertString(basicForm.values.birthdate),
      phone_other: this.assertString(basicForm.values.phone_other),
      phone_alternate_c: this.assertString(basicForm.values.phone_alternate_c),
      email1: this.assertString(basicForm.values.email1),
      emergency_contact_name_c: this.assertString(
        basicForm.values.emergency_contact_name_c,
      ),
      emergency_contact_relation_c: this.assertString(
        basicForm.values.emergency_contact_relation_c,
      ),
      emergency_contact_number_c: this.assertString(
        basicForm.values.emergency_contact_number_c,
      ),

      caregiver_type_c: this.assertString(clientForm.values.caregiver_type_c),
      race_c: this.assertString(clientForm.values.race_c),
      languages: this.assertString(clientForm.values.languages),
      cultural_heritage_c: this.assertString(
        clientForm.values.cultural_heritage_c,
      ),
      martial_status: this.assertString(clientForm.values.martial_status),
      currently_pregnant_c: this.assertString(
        clientForm.values.currently_pregnant_c,
      ),
      pregnant_due_date: this.assertString(clientForm.values.pregnant_due_date),

      number_of_children_c: this.assertNumber(
        childrenForm.values.number_of_children_c,
      ),
      number_of_children_in_home_c: this.assertNumber(
        childrenForm.values.number_of_children_in_home_c,
      ),

      children: {
        deleteMany: {
          id: {
            in: childrenIdsToDelete,
          },
        },
        upsert: newChildren.map((child) => {
          const input: Prisma.ChildCreateWithoutMomInput = {
            first_name: this.assertString(child.first_name) || '',
            gender: this.assertString(child.gender),
            lives_with: this.assertString(child.lives_with),
            legal_custody_status: this.assertString(child.legal_custody_status),
            birthdate: this.assertString(child.birthdate),
            father_involved: Array.isArray(child.father_involved)
              ? child.father_involved.map((value) => value.value)
              : [],
            father_involvement: this.assertString(child.father_involvement),
            active_child_welfare_involvement: this.assertString(
              child.active_child_welfare_involvement,
            ),
            date_of_child_welfare_involvement: this.assertString(
              child.date_of_child_welfare_involvement,
            ),
            family_preservation_goal: this.assertString(
              child.family_preservation_goal,
            ),
            family_preservation_impact: this.assertString(
              child.family_preservation_impact,
            ),
          };

          return {
            update: input,
            create: input,
            where: {
              id: child.id,
            },
          };
        }),
      },
    };
  };

  private getWellnessAssessmentCreateInput = (
    momId: string,
    { wellBeingForm: { values } }: WellBeingAssessmentFormData,
  ): Prisma.WellnessAssessmentCreateManyInput => {
    return {
      completed_ahead: this.assertBoolean(values.completed_ahead),
      meeting_method: this.assertString(values.meeting_method),
      staff_name_c: this.assertString(values.staff_name_c),
      start_date_c: this.assertString(values.start_date_c),
      completed_date: this.assertString(values.completed_date),

      cw_home_status: this.assertString(values.cw_home_status),
      cw_involvement_as_child: this.assertString(
        values.cw_involvement_as_child,
      ),
      cw_involvement_as_mom: this.assertBoolean(values.cw_involvement_as_mom),
      cw_allegations: this.assertBoolean(values.cw_allegations),
      cw_maltreatment_type: this.encodeArray(values.cw_maltreatment_type),
      cw_active_involvement: this.assertString(values.cw_active_involvement),
      cw_date_of_involvement: this.assertString(values.cw_date_of_involvement),
      cw_fp_goal: this.assertString(values.cw_fp_goal),
      cw_fp_impact: this.assertString(values.cw_fp_impact),
      cw_notes: this.assertString(values.cw_notes),
      cw_score: this.assertNumber(values.cw_score),

      cc_reliable_care: this.assertString(values.cc_reliable_care),
      cc_affordability: this.assertString(values.cc_affordability),
      cc_childcare_safety: this.assertBoolean(values.cc_childcare_safety),
      cc_backup_care: this.assertString(values.cc_backup_care),
      cc_school_enrollment: this.assertString(values.cc_school_enrollment),
      cc_special_ed: this.assertBoolean(values.cc_special_ed),
      cc_ed_concerns: this.assertBoolean(values.cc_ed_concerns),
      cc_health_ins: this.assertString(values.cc_health_ins),
      cc_special_med: this.assertString(values.cc_special_med),
      cc_med_access: this.assertString(values.cc_med_access),
      cc_notes: this.assertString(values.cc_notes),
      cc_score: this.assertNumber(values.cc_score),

      home_type: this.assertString(values.home_type),
      home_category: this.assertString(values.home_category),
      home_name_on_lease: this.assertString(values.home_name_on_lease),
      home_security_concerns: this.assertString(values.home_security_concerns),
      home_recent_homeless: this.assertString(values.home_recent_homeless),
      home_voucher: this.assertString(values.home_voucher),
      home_perc_toward: this.assertString(values.home_perc_toward),
      home_safe: this.assertBoolean(values.home_safe),
      home_risk_in_home: this.assertBoolean(values.home_risk_in_home),
      home_notes: this.assertString(values.home_notes),
      home_score: this.assertNumber(values.home_score),

      trnprt_access: this.assertString(values.trnprt_access),
      trnprt_affordable: this.assertString(values.trnprt_affordable),
      trnprt_seat_access: this.assertBoolean(values.trnprt_seat_access),
      trnprt_private_safety: this.assertString(values.trnprt_private_safety),
      trnprt_license: this.assertString(values.trnprt_license),
      trnprt_notes: this.assertString(values.trnprt_notes),
      trnprt_score: this.assertNumber(values.trnprt_score),

      res_positive_outlook: this.assertString(values.res_positive_outlook),
      res_communication: this.assertString(values.res_communication),
      res_traditions: this.assertString(values.res_traditions),
      res_fr_pfs_summary: this.assertString(values.res_fr_pfs_summary),
      res_overall_sat: this.assertString(values.res_overall_sat),
      res_happiness: this.assertString(values.res_happiness),
      res_sat_summary: this.assertString(values.res_sat_summary),
      res_fulfillment: this.assertString(values.res_fulfillment),
      res_purpose: this.assertString(values.res_purpose),
      res_purpose_summary: this.assertString(values.res_purpose_summary),
      res_goodness: this.assertString(values.res_goodness),
      res_sacrifice: this.assertString(values.res_sacrifice),
      res_virtue_summary: this.assertString(values.res_virtue_summary),
      res_belief_self: this.assertString(values.res_belief_self),
      res_support: this.assertBoolean(values.res_support),
      res_attendance: this.assertString(values.res_attendance),
      res_notes: this.assertString(values.res_notes),
      res_score: this.assertNumber(values.res_score),

      well_gad_q1: this.assertString(values.well_gad_q1),
      well_gad_q2: this.assertString(values.well_gad_q2),
      well_gad_q3: this.assertString(values.well_gad_q3),
      well_gad_q4: this.assertString(values.well_gad_q4),
      well_gad_q5: this.assertString(values.well_gad_q5),
      well_gad_q6: this.assertString(values.well_gad_q6),
      well_gad_q7: this.assertString(values.well_gad_q7),
      well_gad_total: this.assertString(values.well_gad_total),
      well_phq_q1: this.assertString(values.well_phq_q1),
      well_phq_q2: this.assertString(values.well_phq_q2),
      well_phq_q3: this.assertString(values.well_phq_q3),
      well_phq_q4: this.assertString(values.well_phq_q4),
      well_phq_q5: this.assertString(values.well_phq_q5),
      well_phq_q6: this.assertString(values.well_phq_q6),
      well_phq_q7: this.assertString(values.well_phq_q7),
      well_phq_q8: this.assertString(values.well_phq_q8),
      well_phq_q9: this.assertString(values.well_phq_q9),
      well_phq_total: this.assertString(values.well_phq_total),
      well_health_insurance: this.assertBoolean(values.well_health_insurance),
      well_medical_care: this.assertString(values.well_medical_care),
      well_discouragement: this.assertString(values.well_discouragement),
      well_discouragement_summary: this.assertString(
        values.well_discouragement_summary,
      ),
      well_counseling: this.assertString(values.well_counseling),
      well_counseling_summary: this.assertString(
        values.well_counseling_summary,
      ),
      well_strategies: this.assertString(values.well_strategies),
      well_strategies_summary: this.assertString(
        values.well_strategies_summary,
      ),
      well_phys_health: this.assertString(values.well_phys_health),
      well_mental_health: this.assertString(values.well_mental_health),
      well_health_summary: this.assertString(values.well_health_summary),
      well_counseling_past: this.assertString(values.well_counseling_past),
      well_counseling_interest: this.assertString(
        values.well_counseling_interest,
      ),
      well_reflections: this.assertString(values.well_reflections),
      well_notes: this.assertString(values.well_notes),
      well_score: this.assertNumber(values.well_score),

      subs_recency: this.assertString(values.subs_recency),
      subs_support_needed: this.assertString(values.subs_support_needed),
      subs_treatment_history: this.assertBoolean(values.subs_treatment_history),
      subs_notes: this.assertString(values.subs_notes),
      subs_score: this.assertNumber(values.subs_score),

      soc_status: this.assertString(values.soc_status),
      soc_length: this.assertString(values.soc_length),
      soc_tension: this.assertString(values.soc_tension),
      soc_resolve_arguments: this.assertString(values.soc_resolve_arguments),
      soc_wast_sf: this.assertString(values.soc_wast_sf),
      soc_dynamics: this.assertString(values.soc_dynamics),
      soc_rel_reflections: this.assertString(values.soc_rel_reflections),
      soc_rel_notes: this.assertString(values.soc_rel_notes),
      soc_faith_id: this.assertString(values.soc_faith_id),
      soc_pfs_supportive_rels: this.assertString(
        values.soc_pfs_supportive_rels,
      ),
      soc_pfs_supportive_advice: this.assertString(
        values.soc_pfs_supportive_advice,
      ),
      soc_pfs_support_goals: this.assertString(values.soc_pfs_support_goals),
      soc_pfs_emergency_contact: this.assertString(
        values.soc_pfs_emergency_contact,
      ),
      soc_trusted_network: this.encodeArray(values.soc_trusted_network),
      soc_pfs_summary_score: this.assertString(values.soc_pfs_summary_score),
      soc_frs_content_with_rels: this.assertString(
        values.soc_frs_content_with_rels,
      ),
      soc_frs_rel_sat: this.assertString(values.soc_frs_rel_sat),
      soc_frs_summary: this.assertString(values.soc_frs_summary),
      soc_reflections: this.assertString(values.soc_reflections),
      soc_notes: this.assertString(values.soc_notes),
      soc_score: this.assertNumber(values.soc_score),

      emp_fin_struggles: this.encodeArray(values.emp_fin_struggles),
      emp_challenges: this.encodeArray(values.emp_challenges),
      emp_difficulty: this.assertString(values.emp_difficulty),
      emp_afford_food: this.assertString(values.emp_afford_food),
      emp_concrete_pfs_total: this.assertString(values.emp_concrete_pfs_total),
      emp_support_received: this.encodeArray(values.emp_support_received),
      emp_support_needed: this.encodeArray(values.emp_support_needed),
      emp_diff_manage_bills: this.assertString(values.emp_diff_manage_bills),
      emp_emergency_funds: this.assertString(values.emp_emergency_funds),
      emp_fin_notes: this.assertString(values.emp_fin_notes),
      emp_status: this.assertString(values.emp_status),
      emp_duration_current: this.assertString(values.emp_duration_current),
      emp_work_eligibility: this.assertString(values.emp_work_eligibility),
      emp_highest_ed: this.assertString(values.emp_highest_ed),
      emp_notes: this.assertString(values.emp_notes),
      emp_score: this.assertNumber(values.emp_score),

      legal_current: this.assertString(values.legal_current),
      legal_rep: this.assertString(values.legal_rep),
      legal_rep_access: this.assertString(values.legal_rep_access),
      legal_plan_childcare: this.assertString(values.legal_plan_childcare),
      legal_notes: this.assertString(values.legal_notes),
      legal_score: this.assertNumber(values.legal_score),

      naa_child_behavior: this.assertString(values.naa_child_behavior),
      naa_discipline: this.assertString(values.naa_discipline),
      naa_power_struggles: this.assertString(values.naa_power_struggles),
      naa_emotions: this.assertString(values.naa_emotions),
      naa_nurture_pfs_summary: this.assertString(
        values.naa_nurture_pfs_summary,
      ),
      naa_reflections: this.assertString(values.naa_reflections),
      naa_notes: this.assertString(values.naa_notes),
      naa_score: this.assertNumber(values.naa_score),

      mom_id: momId,
    };
  };
}
