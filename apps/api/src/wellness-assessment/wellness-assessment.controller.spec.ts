import { Test, TestingModule } from '@nestjs/testing';
import { WellnessAssessmentController } from './wellness-assessment.controller';
import { WellnessAssessmentService } from './wellness-assessment.service';

describe('WellnessAssessmentController', () => {
  let controller: WellnessAssessmentController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WellnessAssessmentController],
      providers: [WellnessAssessmentService],
    }).compile();

    controller = module.get<WellnessAssessmentController>(
      WellnessAssessmentController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
