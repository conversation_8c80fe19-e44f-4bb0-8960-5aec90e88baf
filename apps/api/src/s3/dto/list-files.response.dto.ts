import { ApiProperty } from '@nestjs/swagger';

export class S3FileDto {
  @ApiProperty({
    description: 'The key (path) of the file in S3',
    example: 'images/example.jpg',
  })
  key: string;

  @ApiProperty({
    description: 'The size of the file in bytes',
    example: 1024,
  })
  size: number;

  @ApiProperty({
    description: 'The last modified date of the file',
    example: '2023-04-06T12:00:00Z',
  })
  lastModified: Date;

  @ApiProperty({
    description: 'The URL to access the file',
  })
  url: string;
}

export class ListFilesResponseDto {
  @ApiProperty({
    description: 'The name of the S3 bucket',
    example: 'my-bucket',
  })
  bucket: string;

  @ApiProperty({
    description: 'List of files in the bucket',
    type: [S3FileDto],
  })
  files: S3FileDto[];
}
