import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand,
  PutBucket<PERSON>orsCommand,
  PutBucketPolicyCommand,
  PutP<PERSON>licAccessBlockCommand,
  GetBucketCorsCommand,
  GetBucketPolicyCommand,
  GetPublicAccessBlockCommand,
  HeadBucketCommand,
  HeadObjectCommand,
  ListObjectsV2Command,
} from '@aws-sdk/client-s3';

@Injectable()
export class S3Service {
  private s3: S3Client;
  private readonly logger = new Logger(S3Service.name);

  constructor(private readonly configService: ConfigService) {
    this.s3 = new S3Client({
      region: configService.get('AWS_REGION'),
      credentials: {
        accessKeyId: configService.get('AWS_ACCESS_KEY_ID'),
        secretAccessKey: configService.get('AWS_SECRET_ACCESS_KEY'),
      },
    });
    this.verifyConnection();
  }

  private async verifyConnection() {
    const bucketName = this.configService.get('AWS_S3_BUCKET_NAME');
    try {
      await this.s3.send(new HeadBucketCommand({ Bucket: bucketName }));
      this.logger.log(`Successfully connected to S3 bucket: ${bucketName}`);
    } catch (error) {
      this.logger.error(`Failed to connect to S3 bucket: ${bucketName}`, error);
      if (error.name === 'NoSuchBucket') {
        throw new Error(
          `S3 bucket ${bucketName} does not exist. Please create it first.`,
        );
      } else if (
        error.name === 'InvalidAccessKeyId' ||
        error.name === 'SignatureDoesNotMatch'
      ) {
        throw new Error(
          'Invalid AWS credentials. Please check your AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY.',
        );
      }
      throw error;
    }
  }

  async checkBucketConfiguration() {
    const bucketName = this.configService.get('AWS_S3_BUCKET_NAME');
    try {
      // Check CORS configuration
      const corsCommand = new GetBucketCorsCommand({ Bucket: bucketName });
      await this.s3.send(corsCommand);
      this.logger.log('CORS configuration is already set up');

      // Check bucket policy
      const policyCommand = new GetBucketPolicyCommand({ Bucket: bucketName });
      await this.s3.send(policyCommand);
      this.logger.log('Bucket policy is already set up');

      // Check public access block
      const publicAccessCommand = new GetPublicAccessBlockCommand({
        Bucket: bucketName,
      });
      await this.s3.send(publicAccessCommand);
      this.logger.log('Public access block is already configured');

      return true;
    } catch (error) {
      if (
        error.name === 'NoSuchCORSConfiguration' ||
        error.name === 'NoSuchBucketPolicy' ||
        error.name === 'NoSuchPublicAccessBlockConfiguration'
      ) {
        return false;
      }
      throw error;
    }
  }

  async configureBucketAccess() {
    const bucketName = this.configService.get('AWS_S3_BUCKET_NAME');

    try {
      // Check if configuration is already in place
      const isConfigured = await this.checkBucketConfiguration();
      if (isConfigured) {
        this.logger.log('S3 bucket is already properly configured');
        return;
      }

      this.logger.log('Configuring S3 bucket access...');

      // Configure CORS
      await this.configureCors();
      this.logger.log('CORS configuration applied successfully');

      // Configure public access block
      const publicAccessCommand = new PutPublicAccessBlockCommand({
        Bucket: bucketName,
        PublicAccessBlockConfiguration: {
          BlockPublicAcls: true,
          BlockPublicPolicy: false,
          IgnorePublicAcls: true,
          RestrictPublicBuckets: false,
        },
      });

      // Configure bucket policy
      const bucketPolicy = {
        Version: '2012-10-17',
        Statement: [
          {
            Sid: 'PublicReadGetObject',
            Effect: 'Allow',
            Principal: '*',
            Action: ['s3:GetObject'],
            Resource: [`arn:aws:s3:::${bucketName}/*`],
          },
        ],
      };

      const policyCommand = new PutBucketPolicyCommand({
        Bucket: bucketName,
        Policy: JSON.stringify(bucketPolicy),
      });

      await this.s3.send(publicAccessCommand);
      this.logger.log('Public access block configuration applied successfully');

      await this.s3.send(policyCommand);
      this.logger.log('Bucket policy applied successfully');
    } catch (error) {
      this.logger.error('Failed to configure S3 bucket access:', error);
      if (error.name === 'AccessDenied') {
        throw new Error(
          'Insufficient permissions to configure S3 bucket. Please check AWS credentials and permissions.',
        );
      }
      throw error;
    }
  }

  async configureCors() {
    const command = new PutBucketCorsCommand({
      Bucket: this.configService.get('AWS_S3_BUCKET_NAME'),
      CORSConfiguration: {
        CORSRules: [
          {
            AllowedHeaders: ['*'],
            AllowedMethods: ['GET', 'PUT', 'POST', 'DELETE', 'HEAD'],
            AllowedOrigins: ['*'],
            ExposeHeaders: ['ETag'],
            MaxAgeSeconds: 3000,
          },
        ],
      },
    });

    try {
      await this.s3.send(command);
    } catch (error) {
      this.logger.error('Error configuring CORS:', error);
      if (error.name === 'AccessDenied') {
        throw new Error(
          'Insufficient permissions to configure CORS. Please check AWS credentials and permissions.',
        );
      }
      throw error;
    }
  }

  private getPublicUrl(filename: string): string {
    const bucketName = this.configService.get('AWS_S3_BUCKET_NAME');
    const region = this.configService.get('AWS_REGION');
    return `https://${bucketName}.s3.${region}.amazonaws.com/${filename}`;
  }

  async uploadFile(
    file: Buffer | string,
    filename: string,
    contentType: string,
  ) {
    const buffer =
      typeof file === 'string'
        ? Buffer.from(file.replace(/^data:image\/\w+;base64,/, ''), 'base64')
        : file;

    const command = new PutObjectCommand({
      Bucket: this.configService.get('AWS_S3_BUCKET_NAME'),
      Key: filename,
      Body: buffer,
      ContentType: contentType,
      CacheControl: 'public, max-age=31536000',
    });

    try {
      await this.s3.send(command);
      this.logger.log(`File uploaded successfully: ${filename}`);
      return {
        filename,
        url: this.getPublicUrl(filename),
        contentType,
      };
    } catch (error) {
      this.logger.error(`Failed to upload file ${filename}:`, error);
      throw error;
    }
  }

  private async fileExists(filename: string): Promise<boolean> {
    const bucketName = this.configService.get('AWS_S3_BUCKET_NAME');
    try {
      await this.s3.send(
        new HeadObjectCommand({
          Bucket: bucketName,
          Key: filename,
        }),
      );
      this.logger.log(`File exists: ${filename} in bucket ${bucketName}`);
      return true;
    } catch (error) {
      if (error.name === 'NotFound') {
        this.logger.warn(`File not found: ${filename} in bucket ${bucketName}`);
        return false;
      }
      this.logger.error(`Error checking if file exists: ${filename}`, {
        error: error.message,
        code: error.code,
        name: error.name,
        stack: error.stack,
      });
      throw error;
    }
  }

  async downloadFile(filename: string) {
    const bucketName = this.configService.get('AWS_S3_BUCKET_NAME');
    this.logger.log(
      `Attempting to download file: ${filename} from bucket: ${bucketName}`,
    );

    try {
      this.logger.log(`Checking if file exists: ${filename}`);
      const exists = await this.fileExists(filename);
      if (!exists) {
        this.logger.error(
          `File ${filename} does not exist in S3 bucket ${bucketName}`,
        );
        throw new Error(`File ${filename} does not exist in S3 bucket`);
      }

      this.logger.log(
        `File exists, creating GetObjectCommand for: ${filename}`,
      );
      const command = new GetObjectCommand({
        Bucket: bucketName,
        Key: filename,
      });

      this.logger.log(`Sending GetObjectCommand for: ${filename}`);
      const response = await this.s3.send(command);
      if (!response.Body) {
        this.logger.error(`No body returned for file: ${filename}`);
        throw new Error(`No body returned for file: ${filename}`);
      }

      this.logger.log(
        `File downloaded successfully: ${filename}, response type: ${response.Body.constructor.name}`,
      );
      return response.Body;
    } catch (error) {
      this.logger.error(`Failed to download file ${filename}:`, {
        error: error.message,
        code: error.code,
        name: error.name,
        stack: error.stack,
      });
      if (error.name === 'NoSuchKey' || error.name === 'NotFound') {
        throw new Error(`File ${filename} does not exist in S3 bucket`);
      }
      throw error;
    }
  }

  async deleteFile(filename: string) {
    const exists = await this.fileExists(filename);
    if (!exists) {
      this.logger.warn(
        `File ${filename} does not exist in S3 bucket, skipping delete`,
      );
      return;
    }

    const command = new DeleteObjectCommand({
      Bucket: this.configService.get('AWS_S3_BUCKET_NAME'),
      Key: filename,
    });

    try {
      await this.s3.send(command);
      this.logger.log(`File deleted successfully: ${filename}`);
    } catch (error) {
      this.logger.error(`Failed to delete file ${filename}:`, error);
      throw error;
    }
  }

  async listFiles(
    prefix?: string,
    maxKeys: number = 1000,
    continuationToken?: string,
  ) {
    const command = new ListObjectsV2Command({
      Bucket: this.configService.get('AWS_S3_BUCKET_NAME'),
      Prefix: prefix,
      MaxKeys: maxKeys,
      ContinuationToken: continuationToken,
    });

    try {
      const response = await this.s3.send(command);
      this.logger.log(
        `Listed ${response.Contents?.length || 0} files with prefix: ${prefix || 'root'}`,
      );

      return {
        files:
          response.Contents?.map((file) => ({
            key: file.Key,
            size: file.Size,
            lastModified: file.LastModified,
            url: file.Key ? this.getPublicUrl(file.Key) : undefined,
          })) || [],
        isTruncated: response.IsTruncated,
        continuationToken: response.NextContinuationToken,
      };
    } catch (error) {
      this.logger.error(`Failed to list files with prefix ${prefix}:`, error);
      throw error;
    }
  }

  getBucketName(): string {
    return this.configService.get('AWS_S3_BUCKET_NAME');
  }
}
