import { <PERSON>, Get, Param, <PERSON>s } from '@nestjs/common';
import { S3Service } from './s3.service';
import { Response } from 'express';
import { Readable } from 'stream';
import { Logger } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ListFilesResponseDto } from './dto/list-files.response.dto';

@ApiTags('S3')
@Controller('s3')
export class S3Controller {
  private readonly logger = new Logger(S3Controller.name);

  constructor(private readonly s3Service: S3Service) {}

  @Get('list-files')
  @ApiOperation({ summary: 'List all files in the S3 bucket' })
  @ApiResponse({
    status: 200,
    description: 'List of files in the bucket',
    type: ListFilesResponseDto,
  })
  async listFiles(): Promise<ListFilesResponseDto> {
    const files = await this.s3Service.listFiles();
    return {
      bucket: this.s3Service.getBucketName(),
      files: files.files.map((file) => ({
        key: file.key,
        size: file.size,
        lastModified: file.lastModified,
        url: file.url,
      })),
    };
  }

  // this is used to hide the public url of the file
  @Get('proxy/:filename')
  @ApiOperation({ summary: 'Proxy a file from S3' })
  @ApiResponse({ status: 200, description: 'File stream' })
  @ApiResponse({ status: 404, description: 'File not found' })
  async getFile(@Param('filename') filename: string, @Res() res: Response) {
    this.logger.log(`Attempting to download file: ${filename}`);
    try {
      // Try direct filename first
      this.logger.log(`Trying direct filename: ${filename}`);
      const file = await this.s3Service.downloadFile(filename);
      if (!file) {
        this.logger.error(`File not found: ${filename}`);
        return res.status(404).json({ message: 'File not found' });
      }

      // Set appropriate content type based on file extension
      const extension = filename.split('.').pop()?.toLowerCase();

      // Determine content type based on file extension
      let contentType = 'application/octet-stream'; // Default content type

      if (extension === 'jpeg' || extension === 'jpg') {
        contentType = 'image/jpeg';
      } else if (extension === 'png') {
        contentType = 'image/png';
      }

      res.set({
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000',
      });

      this.logger.log(`Streaming file: ${filename}`);

      // Handle AWS SDK v3 response
      if (file && typeof file === 'object' && 'transformToByteArray' in file) {
        try {
          const bytes = await file.transformToByteArray();
          res.send(Buffer.from(bytes));
        } catch (error) {
          this.logger.error(`Error converting stream: ${error.message}`);
          return res.status(500).json({ message: 'Error processing file' });
        }
      } else if (
        file &&
        typeof file === 'object' &&
        'pipe' in file &&
        typeof file.pipe === 'function'
      ) {
        // For Node.js streams
        (file as unknown as Readable).pipe(res);
      } else {
        this.logger.error(`Unsupported stream type for file: ${filename}`);
        return res.status(500).json({ message: 'Unsupported stream type' });
      }
    } catch (error) {
      this.logger.error(`Error downloading file: ${filename}`, error);
      // If we get here, we couldn't find the file
      this.logger.error(`File not found: ${filename}`);
      return res.status(404).json({ message: 'File not found' });
    }
  }
}
