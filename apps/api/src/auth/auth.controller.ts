import {
  Body,
  Controller,
  Get,
  Logger,
  Post,
  Request,
  Res,
  UnauthorizedException,
  GoneException,
  NotFoundException,
  BadRequestException,
  UseGuards,
} from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { EmaAuthTokenDto } from '@suiteapi/models';
import {
  Request as ExpressRequest,
  Response as ExpressResponse,
} from 'express';
import { UsersService } from '../users/users.service';
import { MagicLinkService } from '../magic-link/magic-link.service';
import { AuthService } from './auth.service';
import { ChangePasswordDto } from './dto/change-password.dto';
import { LocalAuthGuard } from './local-auth.guard';
import { SkipJwtAuth } from './skip-jwt-auth.decorator';
import { SkipMustChangePasswordGuard } from './skip-must-change-password-guard.decorator';
import { YupSchemas } from '@suiteapi/models';
import * as bcrypt from 'bcrypt';
import { User<PERSON>reateGaurd } from './user-create.gaurd';

const REFRESH_TOKEN_COOKIE_NAME = 'ema_rt';

@Controller()
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(
    private readonly authService: AuthService,
    private readonly userService: UsersService,
    private readonly magicLinkService: MagicLinkService,
  ) {}

  @SkipJwtAuth()
  @UseGuards(LocalAuthGuard)
  @Post('auth/login')
  async login(@Request() req, @Res({ passthrough: true }) response) {
    const authResult = await this.authService.login(req.user);

    this.setRefreshTokenCookie(response, authResult.refresh_token);

    // Return the user data without the token
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { refresh_token, ...result } = authResult;
    return result;
  }

  @SkipJwtAuth()
  @Get('auth/refresh')
  async refresh(
    @Request() req: ExpressRequest,
    @Res({ passthrough: true }) response: ExpressResponse,
  ) {
    this.logger.debug(req.cookies);

    const refreshToken = req.cookies?.[REFRESH_TOKEN_COOKIE_NAME];
    if (!refreshToken) {
      throw new UnauthorizedException('Refresh token not found');
    }

    const authResult = await this.authService.refreshToken(refreshToken);

    this.setRefreshTokenCookie(response, authResult.refresh_token);

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { refresh_token, ...result } = authResult;
    return result;
  }

  @Post('auth/logout')
  async logout(
    @Request() req: ExpressRequest,
    @Res({ passthrough: true }) response: ExpressResponse,
  ) {
    response.clearCookie(REFRESH_TOKEN_COOKIE_NAME);
    await this.userService.deleteRefreshToken(
      (req.user as EmaAuthTokenDto).sub,
    );
    return { message: 'Logged out successfully' };
  }

  @UseGuards(UserCreateGaurd)
  @Post('auth/user')
  async register(
    @Body()
    user: Prisma.UserCreateInput & {
      password: string;
      userRoles?: unknown;
      affiliateId: string;
    },
  ) {
    return this.authService.createUser(user);
  }

  @SkipJwtAuth()
  @Post('auth/forgot-password')
  async forgotPassword(@Body() data: YupSchemas.ForgotPasswordType) {
    try {
      data = await YupSchemas.forgotPasswordRequestResetSchema.validate(data, {
        stripUnknown: true,
      });
    } catch (error) {
      console.error('Error validating request body', error);
      throw new BadRequestException(
        'Invalid request body, username is required',
      );
    }

    const { username } = data;

    // find user by username
    const foundUser = await this.userService.findOne(username);

    if (!foundUser) {
      throw new UnauthorizedException('User not found');
    }

    // generate a token / magic link
    const { token } = await this.authService.createMagicLink(foundUser);

    // send the token to the user
    const result = await this.authService.sendForgotPasswordEmail(
      foundUser,
      token,
    );

    if (!result) {
      throw new UnauthorizedException('Failed to send email');
    }

    // once email has been queue, we can redirect the user to the status page
    return {
      message: 'Email sent',
    };
  }

  @SkipJwtAuth()
  @Post('auth/forgot-reset-password')
  async forgotResetPassword(
    @Body() data: YupSchemas.ResetForgottenPasswordType,
  ) {
    try {
      data = await YupSchemas.resetForgottenPasswordSchema.validate(data, {
        stripUnknown: true,
      });
    } catch (error) {
      console.error('Error validating request body', error);
      throw new BadRequestException(
        'Invalid request body, password and token are required',
      );
    }

    const { password, confirmPassword, username, token } = data;

    if (password !== confirmPassword) {
      throw new BadRequestException('Passwords do not match');
    }

    if (!token) {
      throw new BadRequestException('Token is required');
    }

    let foundToken: YupSchemas.MagicLinkType | null;

    try {
      foundToken = await this.magicLinkService.findByToken(token);
    } catch (error) {
      console.error('Error finding token:', error);
      throw new GoneException('Token not found');
    }

    if (!foundToken) {
      throw new NotFoundException('Token not found or it has expired');
    }

    const user = await this.userService.findById(foundToken.user_id);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // check if the username matches, if not, throw an error
    if (user.username !== username) {
      throw new BadRequestException('Username does not match');
    }

    // expire the magic link
    await this.magicLinkService.expireByToken(token);
    const hashedPassword = await bcrypt.hash(password, 10);

    // update the passwordm
    const updatedUser = await this.userService.updatePassword(
      user.username,
      hashedPassword,
    );

    if (!updatedUser) {
      throw new NotFoundException('User not found');
    }

    // TODO: send email to user notifying them of the password change

    return updatedUser;
  }

  @SkipMustChangePasswordGuard()
  @Post('auth/change-password')
  async changePassword(
    @Body() user: ChangePasswordDto,
    @Request() req: ExpressRequest,
  ) {
    return {
      wasSuccessful: await this.authService.changePassword(
        // Change password functionality will only change passwords for the logged in/requesting user.
        (req.user as EmaAuthTokenDto).username,
        user,
      ),
    };
  }

  @Get('profile')
  getProfile(@Request() req) {
    return req.user;
  }

  @Post('auth/accept-terms')
  async acceptTerms(@Request() req: ExpressRequest) {
    const userId = (req.user as EmaAuthTokenDto).sub;
    
    // Update the user's hasAcceptedTerms field
    const updatedUser = await this.userService.updateUser(userId, {
      hasAcceptedTerms: true,
      termsAcceptedAt: new Date(),
    });
    
    // Generate a new token with updated user info
    const authResult = await this.authService.login(updatedUser);
    
    // Return the updated token and user data
    return {
      access_token: authResult.access_token,
      roles: updatedUser.userRoles.map(ur => ur.role.key),
    };
  }

  private setRefreshTokenCookie(
    response: ExpressResponse,
    refreshToken: string,
  ) {
    response.cookie(REFRESH_TOKEN_COOKIE_NAME, refreshToken, {
      path: '/api/v1/auth',
      httpOnly: true,
      secure: true,
      sameSite: 'none',
      expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    });
  }
}
