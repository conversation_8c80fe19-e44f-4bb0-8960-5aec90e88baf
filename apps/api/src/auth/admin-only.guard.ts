import {
  CanActivate,
  ExecutionContext,
  Injectable,
  ForbiddenException,
} from '@nestjs/common';
import { EmaAuthTokenDto } from '@suiteapi/models';

@Injectable()
export class AdminOnly implements CanActivate {
  constructor() {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const request = context.switchToHttp().getRequest();
      const user = request.user satisfies EmaAuthTokenDto;
      if (!user || !user.roles.includes('administrator')) {
        throw new ForbiddenException(
          'You are not authorized to access this resource',
        );
      }

      return true;
    } catch (error) {
      throw new ForbiddenException(
        error.message || 'session expired! Please sign In',
      );
    }
  }
}
