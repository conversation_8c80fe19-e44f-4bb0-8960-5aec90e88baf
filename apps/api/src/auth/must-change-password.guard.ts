import {
  CanActivate,
  ExecutionContext,
  Injectable,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { EmaAuthTokenDto } from '@suiteapi/models';
import { SKIP_MUST_CHANGE_PASSWORD_GUARD_KEY } from './skip-must-change-password-guard.decorator';
import { SKIP_JWT_AUTH_KEY } from './skip-jwt-auth.decorator';

@Injectable()
export class MustChangePasswordGuard implements CanActivate {
  private readonly logger = new Logger(MustChangePasswordGuard.name);
  constructor(private reflector: Reflector) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      // If JwtAuth is skipped, then we should skip the MustChangePasswordGuard as well.
      const shouldSkipJwtAuth = this.getDecoratorValue(
        context,
        SKIP_JWT_AUTH_KEY,
      );
      if (shouldSkipJwtAuth) {
        return true;
      }

      const shouldSkipMustChangePasswordGuard = this.getDecoratorValue(
        context,
        SKIP_MUST_CHANGE_PASSWORD_GUARD_KEY,
      );
      if (shouldSkipMustChangePasswordGuard) {
        return true;
      }

      const request = context.switchToHttp().getRequest();
      const user = request.user satisfies EmaAuthTokenDto;
      if (!user || user.mustChangePassword) {
        throw new ForbiddenException(
          'You must change your password to access this resource',
        );
      }

      return true;
    } catch (error) {
      this.logger.error(
        `Error checking if user must change password: ${error.message}`,
      );
      throw new ForbiddenException(
        'Error checking if user must change password',
      );
    }
  }

  private getDecoratorValue(context: ExecutionContext, key: string): boolean {
    return this.reflector.getAllAndOverride<boolean>(key, [
      context.getHandler(),
      context.getClass(),
    ]);
  }
}
