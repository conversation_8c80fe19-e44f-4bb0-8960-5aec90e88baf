import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { UsersModule } from '../users/users.module';
import { LocalStrategy } from './local.strategy';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { JwtStrategy } from './jwt.strategy';
import { JwtAuthGuard } from './jwt-auth.guard';
import { APP_GUARD } from '@nestjs/core';
import { ConfigModule } from '@nestjs/config';
import { ConfigService } from '@nestjs/config';
import { AuthController } from './auth.controller';
import { ExtendedPrismaClientFactoryService } from '../extended-prisma-client-factory/extended-prisma-client-factory.service';
import { ZenstackAuthUserBuilderService } from './zenstack.auth.user.builder.service';
import { MustChangePasswordGuard } from './must-change-password.guard';
import { MagicLinkService } from 'src/magic-link/magic-link.service';
import { UuidService } from 'src/uuid/uuid.service';
import { PasswordValidationService } from './password-validation.service';

@Module({
  imports: [
    UsersModule,
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SIGNING_SECRET'),
        signOptions: {
          // Refresh Tokens don't function on localhost, http, environments.
          // So, non-production access tokens are good for 3 days.
          expiresIn:
            configService.get<string>('NODE_ENV') === 'production'
              ? '15m'
              : '3d',
        },
      }),
    }),
  ],
  providers: [
    AuthService,
    ZenstackAuthUserBuilderService,
    LocalStrategy,
    JwtStrategy,
    ConfigService,
    { provide: APP_GUARD, useClass: JwtAuthGuard },
    { provide: APP_GUARD, useClass: MustChangePasswordGuard },
    ExtendedPrismaClientFactoryService,
    MagicLinkService,
    UuidService,
    PasswordValidationService,
  ],
  exports: [AuthService, ZenstackAuthUserBuilderService],
  controllers: [AuthController],
})
export class AuthModule {}
