import { Catch, ExceptionFilter, ArgumentsHost, Logger } from '@nestjs/common';
import { JsonWebTokenError } from '@nestjs/jwt';
import { Response } from 'express';

@Catch(JsonWebTokenError)
export class JwtExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(JwtExceptionFilter.name);

  catch(exception: JsonWebTokenError, host: ArgumentsHost) {
    this.logger.warn(exception);
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    response.status(401).json({
      message: 'A valid token is required to access this resource',
    });
  }
}
