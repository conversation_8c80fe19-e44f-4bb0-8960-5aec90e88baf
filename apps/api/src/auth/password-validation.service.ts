import { Injectable, BadRequestException } from '@nestjs/common';
import { 
  validatePassword, 
  DEFAULT_PASSWORD_REQUIREMENTS,
  type PasswordValidationResult,
  type PasswordRequirements 
} from '@suiteapi/models';

@Injectable()
export class PasswordValidationService {
  private readonly requirements: PasswordRequirements = DEFAULT_PASSWORD_REQUIREMENTS;

  /**
   * Validates a password and throws an exception if it doesn't meet requirements
   */
  validatePasswordOrThrow(password: string): void {
    const validation = this.validatePassword(password);
    
    if (!validation.isValid) {
      throw new BadRequestException({
        message: 'Password does not meet security requirements',
        errors: validation.errors,
        requirements: this.getRequirementsList(),
      });
    }
  }

  /**
   * Validates a password and returns the validation result
   */
  validatePassword(password: string): PasswordValidationResult {
    return validatePassword(password, this.requirements);
  }

  /**
   * Gets the list of password requirements
   */
  getRequirementsList(): string[] {
    return [
      `At least ${this.requirements.minLength} characters long`,
      'At least one uppercase letter (A-Z)',
      'At least one lowercase letter (a-z)', 
      'At least one number (0-9)',
      `At least one special character (${this.requirements.specialChars})`,
    ];
  }

  /**
   * Validates that two passwords match
   */
  validatePasswordsMatch(password: string, confirmPassword: string): void {
    if (password !== confirmPassword) {
      throw new BadRequestException('Passwords do not match');
    }
  }

  /**
   * Comprehensive password validation including confirmation
   */
  validatePasswordWithConfirmation(password: string, confirmPassword: string): void {
    this.validatePasswordOrThrow(password);
    this.validatePasswordsMatch(password, confirmPassword);
  }
}
