import {
  CanActivate,
  ExecutionContext,
  Injectable,
  ForbiddenException,
} from '@nestjs/common';
import { EmaAuthTokenDto } from '@suiteapi/models';

@Injectable()
export class UserCreateGaurd implements CanActivate {
  constructor() {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const { user, body } = context.switchToHttp().getRequest<{
        user?: EmaAuthTokenDto;
        body?: {
          affiliateId?: string;
          userRoles?: { create?: { role?: { connect?: { key?: string } } }[] };
        };
      }>();

      if (user?.roles.includes('administrator')) {
        return true;
      }

      if (
        user?.roles.includes('supervisor') &&
        !body?.userRoles?.create?.find(
          (ur) => ur.role?.connect?.key === 'administrator',
        ) &&
        user?.affiliateId?.length > 0 &&
        user?.affiliateId === body?.affiliateId
      ) {
        return true;
      }

      throw new ForbiddenException(
        'You are not authorized to access this resource',
      );
    } catch (error) {
      throw new ForbiddenException(
        error.message || 'session expired! Please sign In',
      );
    }
  }
}
