import { Injectable } from '@nestjs/common';
import { EmaAuthTokenDto } from '@suiteapi/models';

export interface Zenstack<PERSON>uth<PERSON>ser
  extends EmaAuthTokenDto,
    Record<string, unknown> {
  id: string;
  fullName: string;
}

@Injectable()
export class ZenstackAuthUserBuilderService {
  constructor() {}

  buildZenstackAuthUser(tokenData: EmaAuthTokenDto): ZenstackAuthUser {
    return {
      id: tokenData.sub,
      fullName: `${tokenData.firstName} ${tokenData.lastName}`,
      ...tokenData,
    };
  }
}
