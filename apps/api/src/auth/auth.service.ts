import {
  ForbiddenException,
  Injectable,
  Inject,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import {
  EmaAuthTokenDto,
  EmaRefreshTokenDto,
  YupSchemas,
} from '@suiteapi/models';
import { UuidService } from 'src/uuid/uuid.service';
import { MagicLinkService } from 'src/magic-link/magic-link.service';
import {
  Prisma,
  User,
  PrismaClient,
  NotificationStatus,
  NotificationTemplate,
} from '@prisma/client';
import { ENHANCED_PRISMA } from '@zenstackhq/server/nestjs';
import { ChangePasswordDto } from './dto/change-password.dto';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private uuidService: UuidService,
    private magicLinkService: MagicLinkService,
    @Inject(ENHANCED_PRISMA)
    private readonly prisma: PrismaClient,
  ) {}

  async createMagicLink(user: User) {
    const uuid = this.uuidService.generateUuid();
    const magicLinkToken = `${uuid}`;

    const magicLink = await this.magicLinkService.createForgotPassword({
      userId: user.id,
      token: magicLinkToken,
    });

    return {
      token: magicLinkToken,
      jwt: this.jwtService.sign(
        { magicLink, sub: user.id },
        { expiresIn: '1h' },
      ),
    };
  }

  async sendForgotPasswordEmail(user: User, token: string) {
    return await this.prisma.notification.create({
      data: {
        recipient_user_id: user.id,
        status: NotificationStatus.pending,
        template: NotificationTemplate.forgot_password,
        template_params: { token },
      },
    });
  }

  async validateMagicLink(magicLinkToken: string) {
    const magicLink = await this.magicLinkService.findByToken(magicLinkToken);
    if (!magicLink) {
      return null;
    }

    const user = await this.usersService.findOneWithRoles({
      id: magicLink.user_id,
    });

    if (!user) {
      return null;
    }

    const payload = <EmaAuthTokenDto>{
      username: user.username,
      sub: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      roles: user.userRoles.map((ur) => ur.role.key),
      affiliateId: user.affiliateId,
    };

    return {
      access_token: this.jwtService.sign(payload),
    };
  }

  async validateUser(username: string, pass: string) {
    const user = await this.usersService.findOneWithRoles({
      username_deleted_at: {
        username,
        deleted_at: 0,
      },
    });
    if (!user) {
      return null;
    }

    const isPasswordValid = await bcrypt.compare(pass, user.passwordHash);
    if (!isPasswordValid) {
      return null;
    }

    this.logger.debug(`User ${user.username} validated`);

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { passwordHash, refreshTokenHash, ...result } = user;
    return {
      ...result,
      userRoles: user.userRoles,
    };
  }

  validateToken<T>(token: string): T {
    return <T>this.jwtService.verify(token);
  }

  async login(user: Awaited<ReturnType<typeof this.validateUser>>) {
    const refreshToken = await bcrypt.genSalt(10);
    const refreshTokenHash = await bcrypt.hash(refreshToken, 10);

    await this.usersService.updateRefreshToken(user.id, refreshTokenHash);

    const payload = <EmaAuthTokenDto>{
      username: user.username,
      sub: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      roles: user.userRoles.map((ur) => ur.role.key),
      affiliateId: user.affiliateId,
      mustChangePassword: user.mustChangePassword,
      hasAcceptedTerms: user.hasAcceptedTerms || false,
    };

    return {
      access_token: this.jwtService.sign(payload),
      refresh_token: this.jwtService.sign(
        { refreshToken, sub: user.id },
        { expiresIn: '7d' },
      ),
    };
  }

  async refreshToken(refreshToken: string) {
    const refreshTokenPayload =
      this.validateToken<EmaRefreshTokenDto>(refreshToken);

    const user = await this.usersService.findOneWithRoles({
      id: refreshTokenPayload.sub,
    });

    if (!user || !user.refreshTokenHash) {
      throw new UnauthorizedException('Invalid user');
    }

    const isRefreshTokenValid = await bcrypt.compare(
      refreshTokenPayload.refreshToken,
      user.refreshTokenHash,
    );

    if (!isRefreshTokenValid) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    const newRefreshToken = await bcrypt.genSalt(10);
    const newRefreshTokenHash = await bcrypt.hash(newRefreshToken, 10);

    await this.usersService.updateRefreshToken(user.id, newRefreshTokenHash);

    const payload = <EmaAuthTokenDto>{
      username: user.username,
      sub: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      roles: user.userRoles.map((ur) => ur.role.key),
      affiliateId: user.affiliateId,
    };

    return {
      access_token: this.jwtService.sign(payload),
      refresh_token: this.jwtService.sign(
        { refreshToken: newRefreshToken, sub: user.id },
        { expiresIn: '7d' },
      ),
    };
  }

  async createUser(
    user: Prisma.UserCreateInput & {
      password: string;
      userRoles?: unknown;
      affiliateId: string;
    },
  ) {
    const hashedPassword = await bcrypt.hash(user.password, 10);

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, passwordHash, ...userData } = user;

    const newUser = await this.usersService.create({
      ...userData,
      passwordHash: hashedPassword,
      mustChangePassword: true,
    });

    // Users for Interested Advocates are created immediately but they are not allowed to login to the system
    // until they're reviewed/approved by a Supervisor. So we don't want to send account creation notifications
    // to them until they are approved.
    if (newUser?.advocate_status !== YupSchemas.AdvocateStatus.INTERESTED) {
      // Create a notification for the new user to welcome them to the platform.
      await this.prisma.notification.create({
        data: {
          recipient_user_id: newUser.id,
          status: NotificationStatus.pending,
          template: NotificationTemplate.welcome_to_email,
          template_params: {
            username: user.username,
            password: user.password,
          },
        },
      });
    }

    return newUser;
  }

  async changePassword(username: string, passwordData: ChangePasswordDto) {
    const validatedUser = await this.validateUser(
      username,
      passwordData.currentPassword,
    );

    // Change password functionality will only change passwords for the logged in/requesting user.
    if (!validatedUser || validatedUser.username !== username) {
      throw new ForbiddenException('Unable to validate user');
    }

    const hashedPassword = await bcrypt.hash(passwordData.newPassword, 10);
    return await this.usersService.updatePassword(username, hashedPassword);
  }
}
