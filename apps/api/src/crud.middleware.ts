import { Injectable, Logger, NestMiddleware } from '@nestjs/common';
import { enhance } from '@zenstackhq/runtime';
import { ZenStackMiddleware } from '@zenstackhq/server/express';
import { Request, Response, NextFunction } from 'express';
import { ExtendedPrismaClientFactoryService } from './extended-prisma-client-factory/extended-prisma-client-factory.service';
import { AuthService } from './auth/auth.service';
import { EmaAuthTokenDto } from '@suiteapi/models';
import { ZenstackAuthUserBuilderService } from './auth/zenstack.auth.user.builder.service';

const openEntities = ['agency'];

@Injectable()
export class CrudMiddleware implements NestMiddleware {
  private readonly logger = new Logger(CrudMiddleware.name);
  constructor(
    private readonly prismaFactory: ExtendedPrismaClientFactoryService,
    private readonly authService: AuthService,
    private readonly zenstackAuthUserBuilderService: ZenstackAuthUserBuilderService,
  ) {}

  use(req: Request, _res: Response, next: NextFunction) {
    this.logger.log('Executing CrudMiddleware');

    const requestedEntity = req.url.split('/')[1];
    const token = req.headers['authorization']?.replace('Bearer ', '');
    let user: EmaAuthTokenDto | undefined;

    try {
      user = token
        ? this.authService.validateToken<EmaAuthTokenDto>(token)
        : undefined;
    } catch (error) {
      this.logger.error('Error validating token', error);

      // If the requested entity is an open entity, we consider the validation error resolved
      // b/c we allow unauthenticated requests on openEntities.
      // Otherwise, we re-throw the validation error.
      if (!openEntities.includes(requestedEntity)) {
        throw error;
      }
    }

    // construct an Express middleware and forward the request/response
    const inner = ZenStackMiddleware({
      // get an enhanced PrismaClient for the current user
      getPrisma: () =>
        enhance(
          this.prismaFactory.createExtendedPrismaClient(
            user
              ? this.zenstackAuthUserBuilderService.buildZenstackAuthUser(user)
              : undefined,
          ),
          {
            user: user
              ? this.zenstackAuthUserBuilderService.buildZenstackAuthUser(user)
              : undefined,
          },
        ),
    });

    inner(req, _res, next);
  }
}
