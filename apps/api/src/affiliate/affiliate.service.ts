import { Inject, Injectable } from '@nestjs/common';
import { Affiliate, PrismaClient } from '@prisma/client';
import { ENHANCED_PRISMA } from '@zenstackhq/server/nestjs';
import { PlaceRecord, ZipCodeService } from 'src/zip-code/zip-code.service';

@Injectable()
export class AffiliateService {
  constructor(
    private readonly zipCodeService: ZipCodeService,
    @Inject(ENHANCED_PRISMA)
    private readonly prisma: PrismaClient,
  ) {}

  async findAll(zip: string, within: number) {
    let affiliateLocations: Affiliate[] = [];
    if (zip && within) {
      affiliateLocations = await this.prisma.affiliate.findMany();
      const closestZipcodes = within
        ? await this.zipCodeService.getZipCodesWithinDistance(zip, within)
        : await this.zipCodeService.getZipCodesWithDistance(zip);

      const affiliatesWithinRange = this.filterAffiliatesByZip(
        affiliateLocations,
        closestZipcodes,
      );
      affiliateLocations = this.sortAffiliatesByDistance(
        affiliatesWithinRange,
        closestZipcodes,
      );
    } else {
      affiliateLocations = await this.prisma.affiliate.findMany();
    }

    return affiliateLocations;
  }

  private filterAffiliatesByZip(
    affiliates: Affiliate[],
    zipcodes: PlaceRecord[],
  ): Affiliate[] {
    return affiliates.filter((affiliate) =>
      zipcodes.find(
        (z) => z.postal_code == affiliate.billing_address_postalcode,
      ),
    );
  }

  private sortAffiliatesByDistance = (
    affiliates: Affiliate[],
    zipcodes: PlaceRecord[],
  ): Affiliate[] => {
    const getDistForZip = (zip: string) =>
      zipcodes.find((z) => z.postal_code == zip)?.distance ?? NaN;

    const sortedAffiliates = affiliates.sort((a, b) => {
      if (a.billing_address_postalcode && b.billing_address_postalcode) {
        const dA = getDistForZip(a.billing_address_postalcode);
        const dB = getDistForZip(b.billing_address_postalcode);
        if (dA == dB) return 0;
        return dA < dB ? -1 : 1;
      }

      return NaN;
    });

    return sortedAffiliates;
  };
}
