import {
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { AffiliateService } from './affiliate.service';
import { SkipJwtAuth } from 'src/auth/skip-jwt-auth.decorator';

@SkipJwtAuth()
@Controller('affiliate')
export class AffiliateController {
  constructor(private readonly affiliateService: AffiliateService) {}

  @Get()
  async findAll(@Query('zip') zip: string, @Query('within') within: string) {
    try {
      const affiliates = await this.affiliateService.findAll(
        zip,
        Number(within),
      );

      return {
        affiliate_locations: affiliates ?? [],
        services: [],
      };
    } catch (error) {
      throw new HttpException(
        { error: `Failed to fetch affiliates: ${error.message}` },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
