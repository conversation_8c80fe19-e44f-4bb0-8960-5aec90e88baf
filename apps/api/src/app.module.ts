import {
  Logger,
  MiddlewareConsumer,
  Module,
  NestModule,
  OnModuleInit,
} from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { SelfReferralModule } from './self-referral/self-referral.module';
import { AffiliateModule } from './affiliate/affiliate.module';
import { ZipCodeService } from './zip-code/zip-code.service';
import { CommunityReferralModule } from './community-referral/community-referral.module';
import { DocumentService } from './document/document.service';
import { UuidService } from './uuid/uuid.service';
import { DocumentModule } from './document/document.module';
import { WellnessAssessmentModule } from './wellness-assessment/wellness-assessment.module';
import { MomModule } from './mom/mom.module';
import { ClsModule, ClsService } from 'nestjs-cls';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { AuthInterceptor } from './auth.interceptor';
import { ExtendedPrismaClientFactoryService } from './extended-prisma-client-factory/extended-prisma-client-factory.service';
import { ZenStackModule } from '@zenstackhq/server/nestjs';
import { enhance } from '@zenstackhq/runtime';
import { CrudMiddleware } from './crud.middleware';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { JwtService } from '@nestjs/jwt';
import { SearchService } from './search/search.service';
import { SearchModule } from './search/search.module';
import { MagicLinkModule } from './magic-link/magic-link.module';
import { S3Module } from './s3/s3.module';
import { PhotoModule } from './photo/photo.module';
import { S3Service } from './s3/s3.service';
import { AdvocateOnboardingModule } from './advocate-onboarding/advocate-onboarding.module';
import { MoodleModule } from './moodle/moodle.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    ClsModule.forRoot({
      global: true,
      middleware: {
        mount: true,
      },
    }),
    ZenStackModule.registerAsync({
      global: true,
      useFactory: (
        extendedPrismaClientFactory: ExtendedPrismaClientFactoryService,
        cls: ClsService,
      ) => {
        return {
          getEnhancedPrisma: () =>
            enhance(
              extendedPrismaClientFactory.createExtendedPrismaClient(
                cls.get('user'),
              ),
              {
                user: cls.get('user'),
              },
            ),
        };
      },
      inject: [ExtendedPrismaClientFactoryService, ClsService],
      extraProviders: [ExtendedPrismaClientFactoryService],
    }),
    SelfReferralModule,
    AffiliateModule,
    CommunityReferralModule,
    DocumentModule,
    WellnessAssessmentModule,
    MomModule,
    AuthModule,
    UsersModule,
    SearchModule,
    MagicLinkModule,
    S3Module,
    PhotoModule,
    AdvocateOnboardingModule,
    MoodleModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    ZipCodeService,
    DocumentService,
    UuidService,
    JwtService,
    Logger,
    {
      provide: APP_INTERCEPTOR,
      useClass: AuthInterceptor,
    },
    ExtendedPrismaClientFactoryService,
    SearchService,
    S3Service,
  ],
})
export class AppModule implements NestModule, OnModuleInit {
  constructor(
    private readonly s3Service: S3Service,
    private readonly logger: Logger,
  ) {}

  async onModuleInit() {
    try {
      await this.s3Service.configureBucketAccess();
      this.logger.log('S3 bucket configuration completed successfully');
    } catch (error) {
      this.logger.error('Failed to configure S3 bucket access:', error);
      if (error.message.includes('Insufficient permissions')) {
        this.logger.warn(
          'Please ensure the AWS credentials have the necessary permissions to configure the S3 bucket',
        );
      }
    }
  }

  configure(consumer: MiddlewareConsumer) {
    consumer.apply(CrudMiddleware).forRoutes('/zen');
  }
}
