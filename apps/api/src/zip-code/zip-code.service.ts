import * as fs from 'fs';
import * as path from 'path';
import { Injectable } from '@nestjs/common';
import { parse } from 'csv-parse';

export type PlaceRecord = {
  country_code: string; // ISO country code, e.g., 'US'
  postal_code: string; // Postal code, e.g., '38668'
  place_name: string; // Name of the place, e.g., 'Senatobia'
  state: string; // First order subdivision (state), e.g., 'Mississippi'
  state_abbr: string; // Code for the first order subdivision (state), e.g., 'MS'
  county_prov: string; // Second order subdivision (county/province), e.g., 'Tate'
  county_code: string; // Code for the second order subdivision (county/province), e.g., '137'
  community: string | null; // Third order subdivision (community), which may be null
  community_code: string | null; // Code for the third order subdivision (community), which may be null
  latitude: number; // Latitude in WGS84 format, e.g., 34.6323
  longitude: number; // Longitude in WGS84 format, e.g., -89.8855
  accuracy: number; // Accuracy of the lat/lng, e.g., 4
  distance?: number; // Distance from the target zip code, if calculated
};

@Injectable()
export class ZipCodeService {
  private static _zipCodeCache: PlaceRecord[] | null = null;

  private static readonly R = 3958.899; // Radius of the earth in km

  private static readonly Rd = Math.PI / 180; // Degree to radian conversion

  private static deg2rad = (deg: number): number => deg * ZipCodeService.Rd;

  private static _getDistanceFromLatLonInMi(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ): number {
    const dLat = ZipCodeService.deg2rad(lat2 - lat1); // deg2rad below
    const dLon = ZipCodeService.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(ZipCodeService.deg2rad(lat1)) *
        Math.cos(ZipCodeService.deg2rad(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const d = ZipCodeService.R * c; // Distance in km
    return d;
  }

  static async getZipCodes(): Promise<PlaceRecord[] | Error> {
    if (ZipCodeService._zipCodeCache) {
      return ZipCodeService._zipCodeCache;
    }

    try {
      // Read the file as a string
      const filePath = path.join(process.cwd(), 'data', 'zips', 'US.txt');
      const fileContent = fs.readFileSync(filePath, 'utf8');

      // Create a new promise to parse the CSV data
      const parsedData = await new Promise<PlaceRecord[] | Error>(
        (resolve, reject) => {
          const records: PlaceRecord[] = [];
          const parser = parse(fileContent, {
            delimiter: '\t',
            trim: true,
            columns: false,
          });

          parser.on('readable', () => {
            let record: string[] | null = parser.read() as string[];
            while (record) {
              // Push each record (row) into the records array
              records.push({
                country_code: record[0],
                postal_code: record[1],
                place_name: record[2],
                state: record[3],
                state_abbr: record[4],
                county_prov: record[5],
                county_code: record[6],
                community: record[7],
                community_code: record[8],
                latitude: parseFloat(record[9]),
                longitude: parseFloat(record[10]),
                accuracy: parseInt(record[11], 10),
              } as PlaceRecord);
              record = parser.read() as string[] | null;
            }
          });

          parser.on('end', () => {
            resolve(records);
          });

          parser.on('error', (err) => {
            reject(err);
          });
        },
      );

      ZipCodeService._zipCodeCache =
        parsedData instanceof Error ? null : parsedData;

      return parsedData;
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (_error) {
      return [];
    }
  }

  async getZipCodesWithDistance(zipcode: string): Promise<PlaceRecord[]> {
    const zipCodes = await ZipCodeService.getZipCodes();

    if (zipCodes instanceof Error) {
      throw new Error('Zipcodes not found');
    }

    const targetZip = zipCodes.find((z) => z.postal_code === zipcode);

    if (!targetZip) {
      throw new Error('Zipcode not found');
    }

    return zipCodes.map((z) => ({
      ...z,
      distance: ZipCodeService._getDistanceFromLatLonInMi(
        targetZip.latitude,
        targetZip.longitude,
        z.latitude,
        z.longitude,
      ),
    }));
  }

  async getNearestZipCodes(
    zipcode: string,
    count: number,
  ): Promise<PlaceRecord[]> {
    const zipCodesWithDistance = await this.getZipCodesWithDistance(zipcode);
    zipCodesWithDistance.sort((a, b) => (a.distance ?? 0) - (b.distance ?? 0));
    return zipCodesWithDistance.slice(0, count + 1);
  }

  async getZipCodesWithinDistance(
    zipcode: string,
    distance: number,
  ): Promise<PlaceRecord[]> {
    const zipCodesWithDistance = await this.getZipCodesWithDistance(zipcode);
    zipCodesWithDistance.sort((a, b) => (a.distance ?? 0) - (b.distance ?? 0));
    return zipCodesWithDistance.filter((z) => (z.distance ?? 0) <= distance);
  }
}
