import { Module, forwardRef } from '@nestjs/common';
import { MoodleService } from './moodle.service';
import { MoodleController } from './moodle.controller';
import { MoodleWebhookController } from './moodle-webhook.controller';
import { ExtendedPrismaClientFactoryService } from '../extended-prisma-client-factory/extended-prisma-client-factory.service';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [forwardRef(() => UsersModule)],
  controllers: [MoodleController, MoodleWebhookController],
  providers: [MoodleService, ExtendedPrismaClientFactoryService],
  exports: [MoodleService],
})
export class MoodleModule {}
