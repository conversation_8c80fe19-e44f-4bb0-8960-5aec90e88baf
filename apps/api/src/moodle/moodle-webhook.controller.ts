import {
  Body,
  Controller,
  Post,
  Logger,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { MoodleService } from './moodle.service';

interface MoodleWebhookEvent {
  eventName: string;
  userId: string;
  // Other fields might be added based on <PERSON>odle's webhook payload structure
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

@Controller('moodle/webhooks')
export class MoodleWebhookController {
  private readonly logger = new Logger(MoodleWebhookController.name);

  constructor(private readonly moodleService: MoodleService) {}

  @Post('user-events')
  @HttpCode(HttpStatus.OK)
  async handleUserEvents(@Body() event: MoodleWebhookEvent) {
    this.logger.log(`Received Moodle webhook event: ${event.eventName}`);

    try {
      switch (event.eventName) {
        case 'user_deleted':
          await this.moodleService.handleUserDeleted(event.userId);
          break;
        case 'user_updated':
          await this.moodleService.handleUserUpdated(event);
          break;
        default:
          this.logger.warn(`Unknown Moodle event type: ${event.eventName}`);
      }

      return { success: true };
    } catch (error) {
      this.logger.error(`Error handling Moodle webhook: ${error.message}`);
      // We still return 200 to acknowledge receipt of the webhook
      return { success: false, error: error.message };
    }
  }
}
