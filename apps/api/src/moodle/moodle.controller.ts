import { <PERSON>, <PERSON>, Param, BadRequestException } from '@nestjs/common';
import { MoodleService } from './moodle.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Moodle')
@Controller('moodle')
export class MoodleController {
  constructor(
    private readonly moodleService: MoodleService,
  ) {}

  @Post('user/:id/password')
  @ApiOperation({
    summary: 'Generate a temporary Moodle password for notifications',
  })
  @ApiResponse({
    status: 200,
    description: 'Moodle password generated successfully',
  })
  async generateMoodlePassword(@Param('id') userId: string) {
    try {
      const password = await this.moodleService.getMoodlePassword(userId);

      if (password) {
        return { password };
      } else {
        throw new BadRequestException('Failed to generate Moodle password');
      }
    } catch (error) {
      throw new BadRequestException(`Failed to generate Moodle password: ${error.message}`);
    }
  }
}
