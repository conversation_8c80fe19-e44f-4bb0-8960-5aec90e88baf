import { Test, TestingModule } from '@nestjs/testing';
import { MoodleService } from './moodle.service';
import { ConfigService } from '@nestjs/config';
import { ExtendedPrismaClientFactoryService } from '../extended-prisma-client-factory/extended-prisma-client-factory.service';

describe('MoodleService', () => {
  let service: MoodleService;

  const mockConfigService = {
    get: jest.fn().mockImplementation((key: string) => {
      switch (key) {
        case 'MOODLE_URL_BASE':
          return 'http://test-moodle.com';
        case 'MOODLE_TOKEN':
          return 'test-token';
        default:
          return undefined;
      }
    }),
  };

  const mockPrismaClient = {
    $queryRaw: jest.fn(),
    $executeRaw: jest.fn(),
    $executeRawUnsafe: jest.fn(),
  };

  const mockPrismaFactory = {
    createExtendedPrismaClient: jest.fn().mockReturnValue(mockPrismaClient),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MoodleService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: ExtendedPrismaClientFactoryService,
          useValue: mockPrismaFactory,
        },
      ],
    }).compile();

    service = module.get<MoodleService>(MoodleService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
