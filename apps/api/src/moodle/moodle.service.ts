import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { ExtendedPrismaClientFactoryService } from '../extended-prisma-client-factory/extended-prisma-client-factory.service';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { UsersService } from '../users/users.service';

interface MoodleUser {
  id: string;
  username: string;
  firstname: string;
  lastname: string;
  email: string;
  phone1?: string;
  city?: string;
  country?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

interface MoodleWebhookEvent {
  eventName: string;
  userId: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

@Injectable()
export class MoodleService {
  private readonly logger = new Logger(MoodleService.name);
  private readonly moodleBaseUrl: string;
  private readonly moodleToken: string;
  protected readonly prisma: ReturnType<
    ExtendedPrismaClientFactoryService['createExtendedPrismaClient']
  >;

  constructor(
    private readonly configService: ConfigService,
    private readonly prismaFactory: ExtendedPrismaClientFactoryService,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: UsersService,
  ) {
    this.prisma = this.prismaFactory.createExtendedPrismaClient();
    this.moodleBaseUrl = this.configService.get<string>('MOODLE_URL_BASE');
    this.moodleToken = this.configService.get<string>('MOODLE_TOKEN');
  }

  /**
   * Check if a user exists in Moodle
   * @param email User's email address
   * @returns User info if exists, null otherwise
   */
  async findUserByEmail(email: string): Promise<MoodleUser | null> {
    this.logger.log(`Searching for Moodle user by email: ${email}`);
    try {
      const response = await axios.get(
        `${this.moodleBaseUrl}/webservice/rest/server.php`,
        {
          params: {
            wstoken: this.moodleToken,
            wsfunction: 'core_user_get_users_by_field',
            moodlewsrestformat: 'json',
            field: 'email',
            values: [email],
          },
        },
      );

      if (
        response.data &&
        Array.isArray(response.data) &&
        response.data.length > 0
      ) {
        return response.data[0];
      }
      return null;
    } catch (error) {
      this.logger.error(
        `Failed to find Moodle user by email: ${error.message}`,
      );
      return null;
    }
  }

  /**
   * Check if a user exists in Moodle
   * @param username User's username
   * @returns User info if exists, null otherwise
   */
  async findUserByUsername(username: string): Promise<MoodleUser | null> {
    this.logger.log(`Searching for Moodle user by username: ${username}`);
    try {
      const response = await axios.get(
        `${this.moodleBaseUrl}/webservice/rest/server.php`,
        {
          params: {
            wstoken: this.moodleToken,
            wsfunction: 'core_user_get_users_by_field',
            moodlewsrestformat: 'json',
            field: 'username',
            values: [username],
          },
        },
      );

      if (
        response.data &&
        Array.isArray(response.data) &&
        response.data.length > 0
      ) {
        return response.data[0];
      }
      return null;
    } catch (error) {
      this.logger.error(
        `Failed to find Moodle user by username: ${error.message}`,
      );
      return null;
    }
  }

  /**
   * Create a new user in Moodle
   * @param userData User data from interest form
   * @returns Created user info
   */
  async createUser(userData: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    city?: string;
    state?: string;
    zip?: string;
    username?: string;
    password?: string;
  }): Promise<MoodleUser> {
    try {
      const response = await axios.post(
        `${this.moodleBaseUrl}/webservice/rest/server.php`,
        null,
        {
          params: {
            wstoken: this.moodleToken,
            wsfunction: 'core_user_create_users',
            moodlewsrestformat: 'json',
            users: [
              {
                username: userData.username,
                password: userData.password,
                firstname: userData.firstName,
                lastname: userData.lastName,
                email: userData.email,
                phone1: userData.phone || '',
                city: userData.city || '',
                country: 'US',
                customfields: [
                  {
                    type: 'state',
                    value: userData.state || '',
                  },
                  {
                    type: 'zipcode',
                    value: userData.zip || '',
                  },
                ],
                preferences: [
                  {
                    type: 'auth_forcepasswordchange',
                    value: '0',
                  },
                ],
              },
            ],
          },
        },
      );

      if (
        response.data &&
        Array.isArray(response.data) &&
        response.data.length > 0
      ) {
        return response.data[0];
      }
      throw new Error('Failed to create Moodle user');
    } catch (error) {
      this.logger.error(`Failed to create Moodle user: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update an existing user in Moodle
   * @param userData User data to update
   * @returns Updated user info
   */
  async updateUser(userData: {
    userId: string;
    firstName?: string;
    lastName?: string;
    email?: string;
    username?: string;
    phone?: string;
    city?: string;
    state?: string;
    zip?: string;
    password?: string;
  }): Promise<{ success: boolean }> {
    try {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const userUpdateData: any = {
        id: userData.userId,
      };

      if (userData.firstName) userUpdateData.firstname = userData.firstName;
      if (userData.lastName) userUpdateData.lastname = userData.lastName;
      if (userData.email) userUpdateData.email = userData.email;
      if (userData.username) userUpdateData.username = userData.username;
      if (userData.phone) userUpdateData.phone1 = userData.phone;
      if (userData.city) userUpdateData.city = userData.city;
      if (userData.password) userUpdateData.password = userData.password;
      if (userData.password) userUpdateData.password = userData.password;

      const customfields = [];
      if (userData.state) {
        customfields.push({
          type: 'state',
          value: userData.state,
        });
      }
      if (userData.zip) {
        customfields.push({
          type: 'zipcode',
          value: userData.zip,
        });
      }
      if (customfields.length > 0) {
        userUpdateData.customfields = customfields;
      }

      await axios.post(
        `${this.moodleBaseUrl}/webservice/rest/server.php`,
        null,
        {
          params: {
            wstoken: this.moodleToken,
            wsfunction: 'core_user_update_users',
            moodlewsrestformat: 'json',
            users: [userUpdateData],
          },
        },
      );

      // core_user_update_users returns empty on success
      return { success: true };
    } catch (error) {
      this.logger.error(`Failed to update Moodle user: ${error.message}`);
      throw error;
    }
  }

  /**
   * Delete a user in Moodle
   * @param userId Moodle user ID
   * @returns Operation result
   */
  async deleteUser(userId: string): Promise<{ success: boolean }> {
    try {
      await axios.post(
        `${this.moodleBaseUrl}/webservice/rest/server.php`,
        null,
        {
          params: {
            wstoken: this.moodleToken,
            wsfunction: 'core_user_delete_users',
            moodlewsrestformat: 'json',
            userids: [userId],
          },
        },
      );

      // core_user_delete_users returns empty on success
      return { success: true };
    } catch (error) {
      this.logger.error(`Failed to delete Moodle user: ${error.message}`);
      throw error;
    }
  }

  /**
   * Handle user deleted event from Moodle
   * @param moodleUserId The Moodle user ID that was deleted
   */
  async handleUserDeleted(moodleUserId: string): Promise<void> {
    this.logger.log(
      `Processing Moodle user deleted event for user ID: ${moodleUserId}`,
    );

    try {
      // Check if the Moodle feature is enabled/supported in the database
      const isMoodleEnabled = this.isMoodleEnabled();
      if (!isMoodleEnabled) {
        this.logger.warn('Moodle integration not enabled in this environment');
        return;
      }

      // Use a raw query or type assertion to work around the TypeScript issues
      // This will fail at runtime if the column doesn't exist, but our try-catch will handle it
      const users = (await this.prisma.$queryRaw`
        SELECT * FROM "User" 
        WHERE "moodleUserId" = ${moodleUserId} 
        AND "deleted_at" = 0
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      `) as any[]; // eslint-disable-line @typescript-eslint/no-explicit-any

      if (!users || users.length === 0) {
        this.logger.warn(`No users found with Moodle ID: ${moodleUserId}`);
        return;
      }

      // Update all matching users to remove Moodle association using raw queries
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const updates = (users as any[]).map((user) => {
        try {
          return this.prisma.$executeRaw`
            UPDATE "User" 
            SET "moodleUserId" = NULL 
            WHERE "id" = ${user.id}
          `;
        } catch (error) {
          this.logger.error(`Error updating user ${user.id}: ${error.message}`);
          return Promise.resolve(); // Return a resolved promise for Promise.all
        }
      });

      await Promise.all(updates);
      this.logger.log(
        `Updated ${updates.length} users to remove Moodle association`,
      );
    } catch (error) {
      this.logger.error(
        `Error handling Moodle user deleted event: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Handle user updated event from Moodle
   * @param event The Moodle webhook event data
   */
  async handleUserUpdated(event: MoodleWebhookEvent): Promise<void> {
    this.logger.log(
      `Processing Moodle user updated event for user ID: ${event.userId}`,
    );

    try {
      // Check if the Moodle feature is enabled/supported in the database
      const isMoodleEnabled = this.isMoodleEnabled();
      if (!isMoodleEnabled) {
        this.logger.warn('Moodle integration not enabled in this environment');
        return;
      }

      // Get updated user details from Moodle
      const userData = await this.getMoodleUserById(event.userId);

      if (!userData) {
        this.logger.warn(`Could not find Moodle user with ID: ${event.userId}`);
        return;
      }

      // Use a raw query to find users by Moodle ID
      const users = (await this.prisma.$queryRaw`
        SELECT * FROM "User" 
        WHERE "moodleUserId" = ${event.userId} 
        AND "deleted_at" = 0
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      `) as any[]; // eslint-disable-line @typescript-eslint/no-explicit-any

      if (!users || users.length === 0) {
        this.logger.warn(`No users found with Moodle ID: ${event.userId}`);
        return;
      }

      // Prepare update data based on Moodle user data
      const updateFields = [];
      if (userData.email) updateFields.push(`"email" = '${userData.email}'`);
      if (userData.firstname)
        updateFields.push(`"firstName" = '${userData.firstname}'`);
      if (userData.lastname)
        updateFields.push(`"lastName" = '${userData.lastname}'`);
      if (userData.phone1) updateFields.push(`"phone" = '${userData.phone1}'`);
      if (userData.city)
        updateFields.push(`"address_city" = '${userData.city}'`);

      if (updateFields.length === 0) {
        this.logger.warn('No fields to update from Moodle data');
        return;
      }

      // Update records using raw queries
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const updates = (users as any[]).map((user) => {
        try {
          const updateQuery = `
            UPDATE "User" 
            SET ${updateFields.join(', ')} 
            WHERE "id" = '${user.id}'
          `;
          return this.prisma.$executeRawUnsafe(updateQuery);
        } catch (error) {
          this.logger.error(`Error updating user ${user.id}: ${error.message}`);
          return Promise.resolve(); // Return a resolved promise for Promise.all
        }
      });

      await Promise.all(updates);
      this.logger.log(
        `Updated ${updates.length} users with latest Moodle data`,
      );
    } catch (error) {
      this.logger.error(
        `Error handling Moodle user updated event: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Get Moodle user by ID
   * @param userId Moodle user ID
   * @returns User data or null if not found
   */
  async getMoodleUserById(userId: string): Promise<MoodleUser | null> {
    try {
      const response = await axios.get(
        `${this.moodleBaseUrl}/webservice/rest/server.php`,
        {
          params: {
            wstoken: this.moodleToken,
            wsfunction: 'core_user_get_users_by_field',
            moodlewsrestformat: 'json',
            field: 'id',
            values: [userId],
          },
        },
      );

      if (
        response.data &&
        Array.isArray(response.data) &&
        response.data.length > 0
      ) {
        return response.data[0];
      }
      return null;
    } catch (error) {
      this.logger.error(`Failed to get Moodle user by ID: ${error.message}`);
      return null;
    }
  }

  /**
   * Get the stored Moodle password for a user, or generate a new one if needed
   * @param userId User ID from our database
   * @returns The Moodle password or null if failed
   */
  async getMoodlePassword(userId: string): Promise<string | null> {
    try {
      // Get the user from our database
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user || !user.moodleUserId) {
        this.logger.warn(`User ${userId} not found or has no Moodle account`);
        return null;
      }

      // If we have a stored password, return it
      if (user.moodlePassword) {
        this.logger.log(`Retrieved stored Moodle password for user ${userId}`);
        return user.moodlePassword;
      }

      // If no stored password, generate a new one and store it
      const newPassword = this.usersService.generateRandomPassword();

      // Update the password in Moodle
      await this.updateUser({
        userId: user.moodleUserId,
        password: newPassword,
      });

      // Store the password in our database
      await this.prisma.user.update({
        where: { id: userId },
        data: { moodlePassword: newPassword },
      });

      this.logger.log(
        `Generated and stored new password for user ${userId} (Moodle ID: ${user.moodleUserId})`,
      );

      return newPassword;
    } catch (error) {
      this.logger.error(
        `Failed to get Moodle password for user ${userId}: ${error.message}`,
      );
      return null;
    }
  }

  /**
   * Check if Moodle integration is enabled in the current environment
   * @returns boolean indicating if Moodle is enabled
   */
  private isMoodleEnabled(): boolean {
    try {
      // Check if environment supports Moodle
      const isMoodleConfigured = this.moodleBaseUrl && this.moodleToken;
      if (!isMoodleConfigured) {
        return false;
      }

      // Check if database schema supports Moodle
      // This is a simple check to avoid errors when Moodle columns don't exist
      return true;
    } catch (error) {
      this.logger.warn(`Moodle not enabled: ${error.message}`);
      return false;
    }
  }
}
