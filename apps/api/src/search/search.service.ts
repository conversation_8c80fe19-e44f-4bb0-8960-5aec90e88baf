import { Injectable, Inject } from '@nestjs/common';
import { ENHANCED_PRISMA } from '@zenstackhq/server/nestjs';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class SearchService {
  constructor(
    @Inject(ENHANCED_PRISMA)
    private readonly prisma: PrismaClient,
  ) {}

  async findResults(query: string) {
    try {
      // Search in moms
      const moms = await this.prisma.mom.findMany({
        where: {
          OR: [
            { first_name: { contains: query, mode: 'insensitive' } },
            { last_name: { contains: query, mode: 'insensitive' } },
            { phone_other: { contains: query, mode: 'insensitive' } },
            { email1: { contains: query, mode: 'insensitive' } },
          ],
        },
      });

      // Search in users
      const users = await this.prisma.user.findMany({
        where: {
          OR: [
            { firstName: { contains: query, mode: 'insensitive' } },
            { lastName: { contains: query, mode: 'insensitive' } },
            { email: { contains: query, mode: 'insensitive' } },
            { phone: { contains: query, mode: 'insensitive' } },
          ],
        },
        include: {
          userRoles: {
            include: {
              role: true,
            },
          },
        },
      });

      const results = [
        ...moms.map((m) => ({
          id: m.id,
          name: `${m.first_name} ${m.last_name}`,
          role: 'mom',
          email: m.email1,
          phone: m.phone_other,
        })),
        ...users.map((u) => {
          return {
            id: u.id,
            name: `${u.firstName} ${u.lastName}`,
            role: u.userRoles[0]?.role.key,
            email: u.email,
            phone: u.phone,
          };
        }),
      ];

      return { results };
    } catch (error) {
      console.error('Error searching for a person:', error);
      throw new Error('Failed to search for a person');
    }
  }
}
