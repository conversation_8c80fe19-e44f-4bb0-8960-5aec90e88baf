import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class CloudflareTurnstileService {
  private readonly secretKey: string;
  private readonly siteKey: string;

  constructor(private readonly configService: ConfigService) {
    this.secretKey =
      this.configService.get<string>('CLOUDFLARE_TURNSTILE_SECRET') || '';
    this.siteKey =
      this.configService.get<string>('CLOUDFLARE_TURNSTILE_SITE_KEY') || '';
  }

  async verifyToken(token: string): Promise<{ success: boolean }> {
    try {
      // Check if Turnstile should be bypassed
      if (
        this.configService.get<string>('SHOULD_BYPASS_TURNSTILE_AUTH') ===
        'true'
      ) {
        return { success: true };
      }

      if (!token) {
        console.error('Invalid turnstile: empty token');
        return { success: false };
      }

      const response = await fetch(
        'https://challenges.cloudflare.com/turnstile/v0/siteverify',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            secret: this.secretKey,
            response: token,
          }),
        },
      );

      const data = await response.json() as { success: boolean };
      console.info('Turnstile validation response:', data);
      return { success: data.success };
    } catch (error) {
      console.error('Error verifying turnstile token:', error);
      return { success: false };
    }
  }
}
