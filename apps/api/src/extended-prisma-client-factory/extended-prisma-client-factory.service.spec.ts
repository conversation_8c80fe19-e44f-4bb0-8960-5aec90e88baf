import { Test, TestingModule } from '@nestjs/testing';
import { ExtendedPrismaClientFactoryService } from './extended-prisma-client-factory.service';

describe('ExtendedPrismaClientFactoryService', () => {
  let service: ExtendedPrismaClientFactoryService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ExtendedPrismaClientFactoryService],
    }).compile();

    service = module.get<ExtendedPrismaClientFactoryService>(
      ExtendedPrismaClientFactoryService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
