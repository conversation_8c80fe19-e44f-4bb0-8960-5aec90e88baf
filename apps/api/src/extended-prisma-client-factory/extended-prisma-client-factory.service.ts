import { Injectable } from '@nestjs/common';
import { NotificationTemplate, PrismaClient } from '@prisma/client';
import { ZenstackAuthUser } from 'src/auth/zenstack.auth.user.builder.service';
import { YupSchemas } from '@suiteapi/models';

interface PoorMansCacheItem {
  extendedPrismaClient: ReturnType<
    ExtendedPrismaClientFactoryService['getNewExtendedPrismaClient']
  >;
  timestamp: number;
}

const CACHE_CLEAN_INTERVAL_MS = 1000 * 60 * 2; // 2 minutes.
const CACHE_EXPIRATION_TIME_MS = 1000 * 60 * 15; // 15 minutes.

@Injectable()
export class ExtendedPrismaClientFactoryService {
  private static poorMansCache: Map<string, PoorMansCacheItem> = new Map();
  private static poorMansCacheTimer = setInterval(() => {
    const cacheIdsToDelete: string[] = [];
    ExtendedPrismaClientFactoryService.poorMansCache.forEach((item, key) => {
      if (item.timestamp < new Date().getTime()) {
        cacheIdsToDelete.push(key);
      }
    });
    cacheIdsToDelete.forEach((id) => {
      console.log('Deleting cache item', id);

      ExtendedPrismaClientFactoryService.poorMansCache.delete(id);
    });
  }, CACHE_CLEAN_INTERVAL_MS);

  createExtendedPrismaClient(user?: ZenstackAuthUser) {
    if (ExtendedPrismaClientFactoryService.poorMansCache.has(user?.id)) {
      return ExtendedPrismaClientFactoryService.poorMansCache.get(user?.id)
        .extendedPrismaClient;
    }

    const extendedPrismaClient = this.getNewExtendedPrismaClient(user);
    ExtendedPrismaClientFactoryService.poorMansCache.set(user?.id, {
      extendedPrismaClient,
      timestamp: new Date().getTime() + CACHE_EXPIRATION_TIME_MS,
    });
    return extendedPrismaClient;
  }

  private getNewExtendedPrismaClient(user?: ZenstackAuthUser) {
    const prisma = new PrismaClient().$extends({
      query: {
        $allModels: {
          async create({ args, query }) {
            return query({
              ...args,
              data: {
                ...ExtendedPrismaClientFactoryService.processNestedCreates(
                  args.data,
                  user,
                ),
                created_by_id: user?.id,
                created_by_name: user?.fullName,
              },
            });
          },
          async createMany({ args, query }) {
            return query({
              ...args,
              data: Array.isArray(args.data)
                ? args.data.map((item) => ({
                    ...ExtendedPrismaClientFactoryService.processNestedCreates(
                      item,
                      user,
                    ),
                    created_by_id: user?.id,
                    created_by_name: user?.fullName,
                  }))
                : {
                    ...ExtendedPrismaClientFactoryService.processNestedCreates(
                      args.data,
                      user,
                    ),
                    created_by_id: user?.id,
                    created_by_name: user?.fullName,
                  },
            });
          },
          async update({ args, query }) {
            const {
              created_by_id,
              created_by_name,
              updated_by_id,
              updated_by_name,
              ...rest
            } = args.data;

            return query({
              ...args,
              data: {
                ...ExtendedPrismaClientFactoryService.processNestedUpdates(
                  rest,
                  user,
                ),
                updated_by_id: user?.id,
                updated_by_name: user?.fullName,
              },
            });
          },
          async updateMany({ args, query }) {
            const {
              created_by_id,
              created_by_name,
              updated_by_id,
              updated_by_name,
              ...rest
            } = args.data;

            return query({
              ...args,
              data: {
                ...ExtendedPrismaClientFactoryService.processNestedUpdateMany(
                  rest,
                  user,
                ),
                updated_by_id: user?.id,
                updated_by_name: user?.fullName,
              },
            });
          },
          async delete({ model, args }) {
            // Update deleted_at instead of actually deleting. aka soft-delete.
            return prisma[model].update({
              ...args,
              data: {
                deleted_at: Date.now(),
              },
            });
          },
          async deleteMany({ model, args }) {
            // Update deleted_at instead of actually deleting. aka soft-delete.
            return prisma[model].updateMany({
              ...args,
              data: {
                deleted_at: Date.now(),
              },
            });
          },
        },
        pairing: {
          async create({ args, query }) {
            const momId = args.data.momId || args.data.mom?.connect?.id;
            const advocateId =
              args.data.advocateUserId || args.data.advocateUser?.connect?.id;

            if (momId) {
              let mom = null;

              if (advocateId) {
                mom = await ExtendedPrismaClientFactoryService.fetchMom(
                  prisma,
                  momId,
                );

                await ExtendedPrismaClientFactoryService.processPairingAdvocateChange(
                  {
                    prisma,
                    mom,
                    advocateUserId: advocateId,
                  },
                );
              }

              if (
                args.data.status ===
                YupSchemas.PairingStatusType.WAITING_TO_BE_PAIRED
              ) {
                if (!mom) {
                  mom = await ExtendedPrismaClientFactoryService.fetchMom(
                    prisma,
                    momId,
                  );
                }
                await ExtendedPrismaClientFactoryService.sendWaitingToBePairedNotification(
                  {
                    prisma,
                    mom,
                  },
                );
              }
            }

            return query({
              ...args,
            });
          },
          async update({ args, query }) {
            const momId =
              (args.data.momId as string) || args.data.mom?.connect?.id;
            const advocateId =
              (args.data.advocateUserId as string) ||
              args.data.advocateUser?.connect?.id;

            if (!advocateId && !args.data.status) {
              return query({
                ...args,
              });
            }

            let mom = null;
            if (advocateId) {
              const originalPairing = await prisma.pairing.findUnique({
                where: args.where,
                select: {
                  advocateUserId: true,
                  momId: true,
                },
              });

              // Note: It seems like it'd be more performant to include the fetch of this mom in the fetch of the pairing above.
              // If we do it that way and the update statement is changing the momId as well as the advocateId, we could end up passing
              // the incorrect mom into the handlers and down the line. That's why this fetch has to happen separately from the pairing
              // fetch above, so that it can prioritize the new momId, if present, over the original momId.
              mom = await ExtendedPrismaClientFactoryService.fetchMom(
                prisma,
                momId ?? originalPairing?.momId,
              );

              if (originalPairing?.advocateUserId !== advocateId) {
                await ExtendedPrismaClientFactoryService.processPairingAdvocateChange(
                  {
                    prisma,
                    mom,
                    advocateUserId: advocateId,
                  },
                );
              }

              if (
                args.data.status ===
                YupSchemas.PairingStatusType.WAITING_TO_BE_PAIRED
              ) {
                if (!mom) {
                  mom = await ExtendedPrismaClientFactoryService.fetchMom(
                    prisma,
                    momId ?? originalPairing?.momId,
                  );
                }

                await ExtendedPrismaClientFactoryService.sendWaitingToBePairedNotification(
                  {
                    prisma,
                    mom,
                  },
                );
              }
            }

            return query({
              ...args,
            });
          },
        },
        mom: {
          async update({ args, query }) {
            const { status: newStatus, ...rest } = args.data;

            if (!newStatus) {
              return query({
                ...args,
              });
            }

            const originalMom = await prisma.mom.findUnique({
              where: args.where,
              select: {
                id: true,
                status: true,
              },
            });

            if (
              originalMom?.status !== newStatus &&
              newStatus === YupSchemas.MomStatus.ACTIVE
            ) {
              await prisma.notification.create({
                data: {
                  template: YupSchemas.NotificationTemplate
                    .MOM_ACCEPTED_INTO_PROGRAM as NotificationTemplate,
                  status: YupSchemas.NotificationStatus.PENDING,
                  mom: { connect: { id: originalMom.id } },
                },
              });

              // If the Mom's status is being set to active, we should also set prospect_status to 'engaged_in_program' so that
              // Mom shows up in the correct places on the UI dashboards.
              return query({
                ...args,
                data: {
                  ...args.data,
                  // On the off chance that someone is making an update that sets the status to Active and also explicitly sets the
                  // prospect_status, we'll assume that you know what you're doing and use the explicitly set prospect_status.
                  prospect_status:
                    args.data.prospect_status ||
                    YupSchemas.ProspectStatus.ENGAGED_IN_PROGRAM,
                },
              });
            }

            return query({
              ...args,
            });
          },
        },
      },
    });

    return prisma;
  }

  private static async fetchMom(prisma: PrismaClient, momId: string) {
    if (!momId) {
      return null;
    }

    return await prisma.mom.findUnique({
      where: { id: momId },
      select: {
        id: true,
        first_name: true,
        last_name: true,
        assigned_user_id: true,
      },
    });
  }

  private static async sendWaitingToBePairedNotification({
    prisma,
    mom,
  }: {
    prisma: PrismaClient;
    mom: {
      id: string;
      first_name: string;
      last_name: string;
      assigned_user_id: string;
    };
  }) {
    if (!mom) {
      return;
    }

    await prisma.notification.create({
      data: {
        template:
          YupSchemas.NotificationTemplate.MOM_READY_FOR_ADVOCATE_ASSIGNMENT,
        template_params: {
          momName: `${mom.first_name} ${mom.last_name}`,
        },
        recipient_user: {
          connect: {
            id: mom.assigned_user_id,
          },
        },
        status: YupSchemas.NotificationStatus.PENDING,
      },
    });
  }

  private static async processPairingAdvocateChange({
    prisma,
    mom,
    advocateUserId,
  }: {
    prisma: PrismaClient;
    mom: {
      id: string;
      first_name: string;
      last_name: string;
      assigned_user_id: string;
    };
    advocateUserId: string;
  }) {
    if (!mom || !advocateUserId) {
      return;
    }

    await prisma.notification.create({
      data: {
        template: YupSchemas.NotificationTemplate
          .NEW_MOM_ASSIGNED_ADVOCATE as NotificationTemplate,
        template_params: {
          momName: `${mom.first_name} ${mom.last_name}`,
        },
        status: YupSchemas.NotificationStatus.PENDING,
        recipient_user: { connect: { id: advocateUserId } },
      },
    });

    const advocate = await prisma.user.findUnique({
      where: { id: advocateUserId },
      select: { firstName: true, lastName: true },
    });

    await prisma.notification.create({
      data: {
        template: YupSchemas.NotificationTemplate
          .NEW_ADVOCATE_ASSIGNED_TO_MOM as NotificationTemplate,
        template_params: {
          advocateFirstName: advocate?.firstName,
          advocateName: `${advocate?.firstName} ${advocate?.lastName}`,
        },
        status: YupSchemas.NotificationStatus.PENDING,
        mom: { connect: { id: mom.id } },
      },
    });
  }

  // Helper function to recursively process nested creates
  // This needs to be static. It doesn't work as an instance method
  private static processNestedCreates(data: any, user?: ZenstackAuthUser) {
    if (!data || typeof data !== 'object') return data;

    const processed = { ...data };

    for (const [key, value] of Object.entries(data)) {
      if (value && typeof value === 'object') {
        // Skip processing if value is a Date object
        if (value instanceof Date) {
          processed[key] = value;
        } else if ('create' in value) {
          // Handle nested create
          processed[key] = {
            ...value,
            create: Array.isArray(value.create)
              ? value.create.map((item) => ({
                  ...ExtendedPrismaClientFactoryService.processNestedCreates(
                    item,
                    user,
                  ),
                  created_by_id: user?.id,
                  created_by_name: user?.fullName,
                }))
              : {
                  ...ExtendedPrismaClientFactoryService.processNestedCreates(
                    value.create,
                    user,
                  ),
                  created_by_id: user?.id,
                  created_by_name: user?.fullName,
                },
          };
        } else if ('createMany' in value) {
          // Handle createMany
          processed[key] = {
            ...value,
            createMany: {
              // Note: the typings for these references are being weird. Needed to use 'as' here. :(
              ...(value.createMany as any),
              data: ((value.createMany as any).data as any[]).map((item) => ({
                ...ExtendedPrismaClientFactoryService.processNestedCreates(
                  item,
                  user,
                ),
                created_by_id: user?.id,
                created_by_name: user?.fullName,
              })),
            },
          };
        }
      }
    }
    return processed;
  }

  // Helper function to recursively process nested updateMany entries
  // This needs to be static. It doesn't work as an instance method
  private static processNestedUpdateMany(data: any, user?: ZenstackAuthUser) {
    if (!data || typeof data !== 'object') return data;

    const processed = { ...data };

    for (const [key, value] of Object.entries(data)) {
      if (value && typeof value === 'object') {
        // Skip processing if value is a Date object
        if (value instanceof Date) {
          processed[key] = value;
        } else if ('updateMany' in value) {
          // Handle nested update
          processed[key] = {
            ...value,
            updateMany: {
              ...(value.updateMany as any),
              data: {
                ...ExtendedPrismaClientFactoryService.processNestedUpdateMany(
                  (value.updateMany as any).data,
                  user,
                ),
                updated_by_id: user?.id,
                updated_by_name: user?.fullName,
              },
            },
          };
        } else {
          processed[key] = {
            ...value,
            data: {
              ...ExtendedPrismaClientFactoryService.processNestedUpdateMany(
                (value as any).data,
                user,
              ),
              updated_by_id: user?.id,
              updated_by_name: user?.fullName,
            },
          };
        }
      }
    }
    return processed;
  }

  // Helper function to recursively process nested updates
  // This needs to be static. It doesn't work as an instance method
  private static processNestedUpdates(data: any, user?: ZenstackAuthUser) {
    if (!data || typeof data !== 'object') return data;

    const processed = { ...data };

    for (const [key, value] of Object.entries(data)) {
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        // Skip processing if value is a Date object
        if (value instanceof Date) {
          processed[key] = value;
        } else if (
          'update' in value &&
          typeof value.update === 'object' &&
          'data' in value.update
        ) {
          // Handle nested update
          processed[key] = {
            ...value,
            update: {
              ...value.update,
              data: {
                ...ExtendedPrismaClientFactoryService.processNestedUpdates(
                  value.update.data,
                  user,
                ),
                updated_by_id: user?.id,
                updated_by_name: user?.fullName,
              },
            },
          };
        } else {
          processed[key] = {
            ...ExtendedPrismaClientFactoryService.processNestedUpdates(
              value,
              user,
            ),
            ...value,
          };
        }
      }
    }
    return processed;
  }
}
