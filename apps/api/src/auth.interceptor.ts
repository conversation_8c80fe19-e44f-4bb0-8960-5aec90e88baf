import {
  Call<PERSON><PERSON>ler,
  ExecutionContext,
  Injectable,
  Logger,
  NestInterceptor,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ClsService } from 'nestjs-cls';
import { EmaAuthTokenDto } from '@suiteapi/models';
import { ZenstackAuthUserBuilderService } from './auth/zenstack.auth.user.builder.service';

@Injectable()
export class AuthInterceptor implements NestInterceptor {
  private readonly logger = new Logger(AuthInterceptor.name);

  constructor(
    private readonly cls: ClsService,
    private readonly jwtService: JwtService,
    private readonly zenstackAuthUserBuilderService: ZenstackAuthUserBuilderService,
  ) {}

  async intercept(context: ExecutionContext, next: CallHandler) {
    this.logger.log('Intercepting request');
    const request = context.switchToHttp().getRequest();
    const token = request.headers['authorization']?.replace('Bearer ', '');

    if (token) {
      // NOTE: Token doesn't need verified here, it's verified in the JwtAuthStrategy in the auth module.
      const decodedToken = this.jwtService.decode<EmaAuthTokenDto>(token);
      if (decodedToken) {
        this.cls.set(
          'user',
          this.zenstackAuthUserBuilderService.buildZenstackAuthUser(
            decodedToken,
          ),
        );
      }
    }

    return next.handle();
  }
}
