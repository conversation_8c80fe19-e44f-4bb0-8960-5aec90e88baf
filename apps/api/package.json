{"name": "api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "zenstack generate && nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "zenstack generate && NO_CLEAR=true nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "PORT=8080 node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint-ci": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:moodle-sync": "ts-node -r tsconfig-paths/register src/scripts/test-moodle-sync.ts", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "deploy:prisma:migrations": "zenstack generate && prisma migrate deploy && prisma db seed", "create:prisma:migration": "zenstack generate && prisma migrate dev --create-only --name ", "clean": "rm -rf dist && rm -rf .turbo", "nuke": "pnpm run clean && rm -rf node_modules", "zenstack:generate": "zenstack generate", "prisma:db:push": "prisma db push", "zenstack:format": "bash format-zmodels.sh"}, "dependencies": {"@aws-sdk/client-s3": "^3.726.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^11.0.3", "@prisma/client": "~6.6.0", "@suiteapi/models": "workspace:models@*", "@zenstackhq/openapi": "2.14.0", "@zenstackhq/runtime": "2.14.0", "@zenstackhq/server": "2.14.0", "@zenstackhq/tanstack-query": "2.14.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "chalk": "^4.1.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.7", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "ics": "^3.8.1", "nestjs-cls": "^5.0.0", "package": "^1.0.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "sharp": "^0.33.1", "uuid": "^11.0.5", "yup": "^1.4.0"}, "devDependencies": {"@faker-js/faker": "^9.6.0", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.24.0", "@typescript-eslint/parser": "^8.24.0", "csv-parse": "^5.5.6", "dotenv": "^16.5.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "~6.6.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.14.0", "zenstack": "2.14.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1", "^utils/(.*)$": "<rootDir>/../utils/$1"}}, "prisma": {"seed": "ts-node --transpile-only prisma/seed.ts"}}