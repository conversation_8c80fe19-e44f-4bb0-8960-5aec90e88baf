// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init
import "schemas/base"
import "schemas/aapi-score"
import "schemas/action-item"
import "schemas/affiliate"
import "schemas/agency"
import "schemas/benevolence-need"
import "schemas/child"
import "schemas/connection-log"
import "schemas/coordinator-note"
import "schemas/document"
import "schemas/event-respondent"
import "schemas/event"
import "schemas/goal"
import "schemas/lesson-template"
import "schemas/lesson"
import "schemas/mom"
import "schemas/notification"
import "schemas/pairing"
import "schemas/role"
import "schemas/session-note"
import "schemas/session"
import "schemas/track"
import "schemas/user-role"
import "schemas/user"
import "schemas/wellness-assessment"
import "schemas/tag"
import "schemas/document-tag"
import "schemas/affiliate-agency"
import "schemas/advocate-onboarding"
import "schemas/audit-log"
import "schemas/service-referral"
import "schemas/magic-link"
import "schemas/assessment"
import "schemas/assessment-question"
import "schemas/assessment-construct"
import "schemas/assessment-result"
import "schemas/assessment-result-question-response"

generator client {
    provider = "prisma-client-js"
}

datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

plugin openapi {
    provider = '@zenstackhq/openapi'
    flavor = 'rpc'
    output = 'openapi.json'
}

plugin hooks {
    provider = '@zenstackhq/tanstack-query'
    target = 'react'
    output = '../portal/src/hooks/generated'
    portable = true
}

type AuthUser {
    id          String   @id
    roles       String[]
    affiliateId String
    fullName    String

    @@auth
}
