import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import axios from 'axios';
import { MoodleService } from '../src/moodle/moodle.service';
import { MoodleWebhookController } from '../src/moodle/moodle-webhook.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ExtendedPrismaClientFactoryService } from '../src/extended-prisma-client-factory/extended-prisma-client-factory.service';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('Moodle Integration Tests', () => {
  let app: INestApplication;
  let moodleService: MoodleService;
  let configService: ConfigService;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let mockPrisma: any;

  const mockUser = {
    id: 123,
    username: 'testuser_123456',
    firstname: '<PERSON>',
    lastname: '<PERSON><PERSON>',
    email: '<EMAIL>',
    phone1: '555-1234',
    city: 'Test City',
    country: 'US',
  };

  const mockUserData = {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '555-1234',
    city: 'Test City',
    state: 'CA',
    zip: '12345',
  };

  beforeAll(async () => {
    // Create a comprehensive mock Prisma client
    mockPrisma = {
      $queryRaw: jest.fn().mockResolvedValue([]),
      $executeRaw: jest.fn().mockResolvedValue(1),
      $executeRawUnsafe: jest.fn().mockResolvedValue(1),
      user: {
        findMany: jest.fn(),
        update: jest.fn(),
      },
    };

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          envFilePath: '.env',
          isGlobal: true,
        }),
      ],
      controllers: [MoodleWebhookController],
      providers: [
        MoodleService,
        {
          provide: ExtendedPrismaClientFactoryService,
          useValue: {
            createExtendedPrismaClient: jest.fn(() => mockPrisma),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              const config = {
                MOODLE_URL_BASE: 'http://localhost:8080',
                MOODLE_TOKEN: 'your-dev-token-here',
              };
              return config[key];
            }),
          },
        },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    moodleService = moduleFixture.get<MoodleService>(MoodleService);
    configService = moduleFixture.get<ConfigService>(ConfigService);
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset mock implementations
    mockPrisma.$queryRaw.mockResolvedValue([]);
    mockPrisma.$executeRaw.mockResolvedValue(1);
    mockPrisma.$executeRawUnsafe.mockResolvedValue(1);
  });

  describe('MoodleService', () => {
    describe('findUserByEmail', () => {
      it('should find user by email', async () => {
        mockedAxios.get.mockResolvedValue({
          data: [mockUser],
        });

        const result = await moodleService.findUserByEmail(
          '<EMAIL>',
        );

        expect(result).toEqual(mockUser);
        expect(mockedAxios.get).toHaveBeenCalledWith(
          'http://localhost:8080/webservice/rest/server.php',
          {
            params: {
              wstoken: 'your-dev-token-here',
              wsfunction: 'core_user_get_users_by_field',
              moodlewsrestformat: 'json',
              field: 'email',
              values: ['<EMAIL>'],
            },
          },
        );
      });

      it('should return null when user not found', async () => {
        mockedAxios.get.mockResolvedValue({
          data: [],
        });

        const result = await moodleService.findUserByEmail(
          '<EMAIL>',
        );

        expect(result).toBeNull();
      });

      it('should handle API errors gracefully', async () => {
        mockedAxios.get.mockRejectedValue(new Error('API Error'));

        const result = await moodleService.findUserByEmail('<EMAIL>');

        expect(result).toBeNull();
      });
    });

    describe('createUser', () => {
      it('should create user successfully', async () => {
        const createdUser = { ...mockUser, id: 124 };
        mockedAxios.post.mockResolvedValue({
          data: [createdUser],
        });

        const result = await moodleService.createUser(mockUserData);

        expect(result).toEqual(createdUser);
        expect(mockedAxios.post).toHaveBeenCalledWith(
          'http://localhost:8080/webservice/rest/server.php',
          null,
          expect.objectContaining({
            params: expect.objectContaining({
              wstoken: 'your-dev-token-here',
              wsfunction: 'core_user_create_users',
              moodlewsrestformat: 'json',
              users: expect.arrayContaining([
                expect.objectContaining({
                  firstname: 'John',
                  lastname: 'Doe',
                  email: '<EMAIL>',
                }),
              ]),
            }),
          }),
        );
      });

      it('should throw error when creation fails', async () => {
        mockedAxios.post.mockResolvedValue({
          data: [],
        });

        await expect(moodleService.createUser(mockUserData)).rejects.toThrow(
          'Failed to create Moodle user',
        );
      });

      it('should handle API errors', async () => {
        mockedAxios.post.mockRejectedValue(new Error('API Error'));

        await expect(moodleService.createUser(mockUserData)).rejects.toThrow(
          'API Error',
        );
      });
    });

    describe('handleUserDeleted', () => {
      it('should handle user deleted event', async () => {
        mockPrisma.$queryRaw.mockResolvedValue([{ id: 'user-1' }]);

        await moodleService.handleUserDeleted('123');

        expect(mockPrisma.$queryRaw).toHaveBeenCalled();
        expect(mockPrisma.$executeRaw).toHaveBeenCalled();
      });

      it('should handle no users found', async () => {
        mockPrisma.$queryRaw.mockResolvedValue([]);

        await moodleService.handleUserDeleted('123');

        expect(mockPrisma.$queryRaw).toHaveBeenCalled();
        expect(mockPrisma.$executeRaw).not.toHaveBeenCalled();
      });
    });

    describe('handleUserUpdated', () => {
      it('should handle user updated event', async () => {
        mockedAxios.get.mockResolvedValue({
          data: [mockUser],
        });
        mockPrisma.$queryRaw.mockResolvedValue([{ id: 'user-1' }]);

        const event = {
          eventName: 'user_updated',
          userId: '123',
        };

        await moodleService.handleUserUpdated(event);

        expect(mockedAxios.get).toHaveBeenCalled();
        expect(mockPrisma.$queryRaw).toHaveBeenCalled();
        expect(mockPrisma.$executeRawUnsafe).toHaveBeenCalled();
      });
    });
  });

  describe('Webhook Endpoints', () => {
    describe('POST /moodle/webhooks/user-events', () => {
      it('should handle user_deleted webhook', async () => {
        mockPrisma.$queryRaw.mockResolvedValue([]);

        const webhookData = {
          eventName: 'user_deleted',
          userId: '123',
        };

        const response = await request(app.getHttpServer())
          .post('/moodle/webhooks/user-events')
          .send(webhookData)
          .expect(200);

        expect(response.body.success).toBe(true);
      });

      it('should handle user_updated webhook', async () => {
        mockedAxios.get.mockResolvedValue({
          data: [mockUser],
        });
        mockPrisma.$queryRaw.mockResolvedValue([]);

        const webhookData = {
          eventName: 'user_updated',
          userId: '123',
        };

        const response = await request(app.getHttpServer())
          .post('/moodle/webhooks/user-events')
          .send(webhookData)
          .expect(200);

        expect(response.body.success).toBe(true);
      });

      it('should handle unknown event types', async () => {
        const webhookData = {
          eventName: 'unknown_event',
          userId: '123',
        };

        const response = await request(app.getHttpServer())
          .post('/moodle/webhooks/user-events')
          .send(webhookData)
          .expect(200);

        expect(response.body.success).toBe(true);
      });

      it('should return success false on error but still 200 status', async () => {
        // Mock an error in the service
        jest
          .spyOn(moodleService, 'handleUserDeleted')
          .mockRejectedValue(new Error('Test error'));

        const webhookData = {
          eventName: 'user_deleted',
          userId: '123',
        };

        const response = await request(app.getHttpServer())
          .post('/moodle/webhooks/user-events')
          .send(webhookData)
          .expect(200);

        expect(response.body.success).toBe(false);
        expect(response.body.error).toBe('Test error');
      });
    });
  });

  describe('Moodle API Connection Tests', () => {
    it('should validate Moodle configuration', () => {
      const moodleUrl = configService.get<string>('MOODLE_URL_BASE');
      const moodleToken = configService.get<string>('MOODLE_TOKEN');

      expect(moodleUrl).toBeDefined();
      expect(moodleToken).toBeDefined();
      expect(moodleUrl).toMatch(/^https?:\/\//);
    });

    it('should test Moodle API accessibility', async () => {
      // Mock a successful response for API health check
      mockedAxios.get.mockResolvedValue({
        data: { status: 'ok' },
      });

      const response = await axios.get(
        `${configService.get('MOODLE_URL_BASE')}/webservice/rest/server.php`,
        {
          params: {
            wstoken: configService.get('MOODLE_TOKEN'),
            wsfunction: 'core_webservice_get_site_info',
            moodlewsrestformat: 'json',
          },
        },
      );

      expect(response.data).toBeDefined();
      expect(mockedAxios.get).toHaveBeenCalledWith(
        'http://localhost:8080/webservice/rest/server.php',
        {
          params: {
            wstoken: 'your-dev-token-here',
            wsfunction: 'core_webservice_get_site_info',
            moodlewsrestformat: 'json',
          },
        },
      );
    });
  });

  describe('Integration Scenarios', () => {
    it('should handle complete user lifecycle', async () => {
      // 1. Create user
      mockedAxios.post.mockResolvedValue({
        data: [{ ...mockUser, id: 125 }],
      });

      const createdUser = await moodleService.createUser(mockUserData);
      expect(createdUser.id).toBe(125);

      // 2. Find user by email
      mockedAxios.get.mockResolvedValue({
        data: [createdUser],
      });

      const foundUser = await moodleService.findUserByEmail(mockUserData.email);
      expect(foundUser).toEqual(createdUser);

      // 3. Update user (simulate webhook)
      // Mock successful webhook handling
      jest.spyOn(moodleService, 'handleUserUpdated').mockResolvedValue();

      const updateEvent = {
        eventName: 'user_updated',
        userId: '125',
      };

      const webhookResponse = await request(app.getHttpServer())
        .post('/moodle/webhooks/user-events')
        .send(updateEvent)
        .expect(200);

      expect(webhookResponse.body.success).toBe(true);

      // 4. Delete user (simulate webhook)
      // Mock successful webhook handling
      jest.spyOn(moodleService, 'handleUserDeleted').mockResolvedValue();

      const deleteEvent = {
        eventName: 'user_deleted',
        userId: '125',
      };

      const deleteResponse = await request(app.getHttpServer())
        .post('/moodle/webhooks/user-events')
        .send(deleteEvent)
        .expect(200);

      expect(deleteResponse.body.success).toBe(true);
    });

    it('should handle webhook event simulation', async () => {
      const webhookEvents = [
        {
          eventName: 'user_created',
          userId: '126',
          username: 'newuser_126',
          firstName: 'Jane',
          lastName: 'Smith',
          email: '<EMAIL>',
        },
        {
          eventName: 'course_completed',
          userId: '126',
          courseId: '101',
          courseName: 'Introduction to Advocacy',
          completionDate: Date.now(),
        },
        {
          eventName: 'module_completed',
          userId: '126',
          courseId: '101',
          moduleId: '1001',
          moduleName: 'Module 1: Basics',
          moduleType: 'lesson',
          completionDate: Date.now(),
        },
      ];

      for (const event of webhookEvents) {
        // Note: These events aren't handled by the current controller
        // but demonstrate the webhook structure from the Moodle plugin
        expect(event.eventName).toBeDefined();
        expect(event.userId).toBeDefined();
      }
    });
  });
});
