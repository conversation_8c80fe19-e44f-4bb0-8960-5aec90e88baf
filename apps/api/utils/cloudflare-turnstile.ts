// TODO: Fix this... make it a NestJS Service at least. Better yet would be a PassportJS Strategy with a decorator/guard to enforce it where appropriate.
import { Logger } from '@nestjs/common';

// Create a logger with a context name
const logger = new Logger('CloudflareTurnstile');

export const isTurnstileTokenValidAsync = async (
  turnstileToken: string,
): Promise<boolean> => {
  try {
    // Check both environment variables with the different prefixes
    if (process.env.SHOULD_BYPASS_TURNSTILE_AUTH === 'true') {
      logger.log('Bypassing turnstile validation due to environment config');
      return true;
    }

    if (!turnstileToken) {
      logger.log('Invalid turnstile: empty token');
      return false;
    }

    const turnstileResp = await fetch(
      'https://challenges.cloudflare.com/turnstile/v0/siteverify',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          secret: process.env.CLOUDFLARE_TURNSTILE_SECRET,
          response: turnstileToken,
        }),
      },
    );

    const turnstileRespBody = await turnstileResp.json();
    logger.log('Turnstile validation response:', turnstileRespBody);
    return !!turnstileRespBody.success;
  } catch (err) {
    logger.error('Turnstile validation error:', err);
    return false;
  }
};
