import "base"
import "mom"

model AAPIScore extends Base {

    @@allow('all', true) // Allow all operations on this model - TODO: restrict this later

    mom_id                   String @unique
    mom                      Mom    @relation(fields: [mom_id], references: [id])
    constructAPreAssessment  Float?
    constructAPostAssessment Float?
    constructBPreAssessment  Float?
    constructBPostAssessment Float?
    constructCPreAssessment  Float?
    constructCPostAssessment Float?
    constructDPreAssessment  Float?
    constructDPostAssessment Float?
    constructEPreAssessment  Float?
    constructEPostAssessment Float?
}

