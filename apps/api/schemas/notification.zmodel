import "base"
import "mom"
import "user"
import "event-respondent"

enum NotificationStatus {
  draft
  pending
  processing
  sent
  failed
}

enum NotificationTemplate {
  mom_assigned_to_coordinator
  new_referral_supervisor
  forgot_password
  wellness_assessment_self_intake_mom
  new_wellness_assessment_coordinator
  event_registration
  session_note_submitted
  session_note_rejected
  mom_ready_for_advocate_assignment
  benevolence_need_submitted
  advocate_invite_training
  upcoming_session_reminder
  new_mom_assigned_advocate
  new_advocate_assigned_to_mom
  mom_accepted_into_program
  welcome_to_email
}

model Notification extends Base {

  @@allow('all', true) // Allow all operations on this model - TODO: restrict this later

  mom_id            String?
  mom               Mom?             @relation(fields: [mom_id], references: [id])
  recipient_user_id String?
  recipient_user    User?            @relation('recipient_user_relation', fields: [recipient_user_id], references: [id])
  sending_user_id   String?
  sending_user      User?            @relation('sending_user_relation', fields: [sending_user_id], references: [id])
  event_respondent_id          String?
  event_respondent             EventRespondent?           @relation(fields: [event_respondent_id], references: [id])
  status            NotificationStatus
  template          NotificationTemplate
  template_params   Json?
  failure_reason    String?
  date_sent         DateTime?
  date_received     DateTime?
  date_read         DateTime?
  date_replied      DateTime?
}
