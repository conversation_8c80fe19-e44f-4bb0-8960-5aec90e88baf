import "base"
import "document"
import "session-note"
import "lesson-template"
import "pairing"

enum LessonStatusType {
    not_started
    in_progress
    completed
}

model Lesson extends Base {

    @@allow('all', true) // Allow all operations on this model - TODO: restrict this later  

    title                     String
    description               String?
    priority                  Int?
    order                     Int?
    duration_days             Int?
    status                    LessonStatusType?
    documents                 Document[]
    source_lesson_template_id String?
    source_lesson_template    LessonTemplate?   @relation(fields: [source_lesson_template_id], references: [id])
    pairing_id                String?
    pairing                   Pairing?          @relation(fields: [pairing_id], references: [id])
    covered_in_session_notes  SessionNote[]
}
