import "base"
import "mom"
import "user"

enum ContactMethodType {
    Call
    SMS_Text
    Email
    In_Person
    Video
}

model ConnectionLog extends Base {

    @@allow('read', check(mom))
    @@allow('all', check(mom, 'update'))
    @@deny('all', is_visible_to_advocates_c == false && has(auth().roles, 'advocate', true) && !hasSome(auth().roles, ['administrator', 'coordinator', 'supervisor'], true))

    contact_method_c          ContactMethodType
    summary_c                 String
    is_visible_to_advocates_c Boolean
    date_created_c            DateTime
    name                      String?
    mom_id                    String?
    mom                       Mom?              @relation(fields: [mom_id], references: [id])
    user_id                   String?
    user                      User?             @relation(fields: [user_id], references: [id])
}

