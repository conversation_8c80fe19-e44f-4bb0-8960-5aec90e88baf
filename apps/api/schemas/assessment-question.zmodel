import "base"
import "assessment"
import "assessment-construct"
import "assessment-result-question-response"

enum ResponseType {
    int
    string
}

model AssessmentQuestion extends Base {

    @@allow('all', true) // Allow all operations on this model - TODO: restrict this later

    question     String
    order        Int
    responseType ResponseType
    assessmentId String
    assessment   Assessment @relation(fields: [assessmentId], references: [id])
    assessmentConstructId String?
    assessmentConstruct AssessmentConstruct? @relation(fields: [assessmentConstructId], references: [id])
    assessmentResultQuestionResponses AssessmentResultQuestionResponse[]
}

