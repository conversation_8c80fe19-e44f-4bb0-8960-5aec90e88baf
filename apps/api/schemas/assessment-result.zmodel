import "base"
import "assessment"
import "mom"
import "assessment-result-question-response"

model AssessmentResult extends Base {

    @@allow('all', true) // Allow all operations on this model - TODO: restrict this later

    assessmentId                      String
    assessment                        Assessment @relation(fields: [assessmentId], references: [id])
    momId                             String
    mom                               Mom        @relation(fields: [momId], references: [id])
    completedAt                       DateTime?
    assessmentResultQuestionResponses AssessmentResultQuestionResponse[]
}

