import "base"
import "assessment-result"
import "assessment-question"

model AssessmentResultQuestionResponse extends Base {

    @@allow('all', true) // Allow all operations on this model - TODO: restrict this later

    assessmentResultId      String
    assessmentResult        AssessmentResult @relation(fields: [assessmentResultId], references: [id])
    assessmentQuestionId    String
    assessmentQuestion      AssessmentQuestion @relation(fields: [assessmentQuestionId], references: [id])
    intResponse             Int?
    stringResponse          String?
}

