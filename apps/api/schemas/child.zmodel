import "../schema"
import "base"
import "mom"

model Child extends Base {
  first_name String
  birthdate DateTime?
  gender String?

  // Additional fields from child form
  lives_with String?
  legal_custody_status String?
  father_involved String[]
  father_involvement String?
  additional_info String?

  // Well-being assessment fields
  active_child_welfare_involvement String?
  date_of_child_welfare_involvement DateTime?
  family_preservation_goal String?
  family_preservation_impact String?

  // Relation to Mom
  mom_id String
  mom Mom @relation("MomToChild", fields: [mom_id], references: [id])

  @@allow('all', true)
}