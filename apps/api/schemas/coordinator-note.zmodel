import "base"
import "mom"
import "user"

enum CoordinatorNoteType {
    safety_or_concern_update
    court_update
    interview_advocate
}

model CoordinatorN<PERSON> extends Base {

  // if the user does not have supervisor, admin, or coordinator role, then they can only view type_c === court_update and they cannot update or delete a coordinator note
  
  @@allow('create,read,update', has(auth().roles, 'supervisor', true) || has(auth().roles, 'administrator', true) || has(auth().roles, 'coordinator', true))
    // For other users (advocates), only allow reading court updates
  @@allow('read', auth() != null && type_c == 'court_update')

    name                 String?
    description          String
    isVisibleToAdvocates <PERSON><PERSON><PERSON>             @default(true)
    type_c               CoordinatorNoteType
    mom_id               String?
    mom                  Mom?                @relation(fields: [mom_id], references: [id])
    coordinator_id       String?
    coordinator          User?               @relation('coordinator_relation', fields: [coordinator_id], references: [id])
    advocate_id          String?
    advocate             User?               @relation('advocate_relation', fields: [advocate_id], references: [id])
}

