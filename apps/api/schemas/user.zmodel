import "base"
import "user-role"
import "affiliate"
import "connection-log"
import "pairing"
import "benevolence-need"
import "coordinator-note"
import "event-respondent"
import "notification"
import "document"
import "mom"
import "advocate-onboarding"
import "magic-link"
import "session"

enum AdvocateStatus {
    Applied
    Interested
    Rejected
    Inactive
    Active
    In_Training
    Awaiting_Application
    Awaiting_Pairing
}

enum UserStatus {
    Active
    Inactive
}

enum CommunicationPreference {
    text_message
    email
    both
}

model User extends Base {

    @@allow('read,update', auth().id == id)
    @@allow('all', has(auth().roles, 'administrator', true))
    @@allow('create,update,delete', (has(auth().roles, 'supervisor', true) && auth().affiliateId == affiliateId))
    @@allow('update', (has(auth().roles, 'coordinator', true) && auth().affiliateId == affiliateId))
    @@allow('read', ((has(auth().roles, 'coordinator', true) || has(auth().roles, 'supervisor', true)) && auth().affiliateId == affiliateId))

    @@unique([username, deleted_at])

    username                    String
    email                       String
    firstName                   String?
    lastName                    String?
    passwordHash                String                       @omit
    refreshTokenHash            String?                      @omit
    home_church                 String?
    secondary_email             String?
    communication_preference    CommunicationPreference?
    sms_message_opt_in          Boolean?
    date_of_birth               DateTime?
    timezone                    String?
    status                      UserStatus?
    phone                       String?
    secondary_phone             String?
    address_street              String?
    address_city                String?
    address_state               String?
    address_postalcode          String?
    description                 String?
    phone_work                  String?
    phone_mobile                String?
    phone_home                  String?
    phone_other                 String?
    advocate_capacity_for_moms  Int?
    userRoles                   UserRole[]
    availability                String?
    language_preference_c       Language?
    language_notes_c            String?
    languages_c                 Language[]
    affiliateId                 String?
    affiliate                   Affiliate?                   @relation(fields: [affiliateId], references: [id])
    connectionlogs              ConnectionLog[]
    photoUrl                    String?                      // S3 URL for the user's photo
    photoS3FileName             String?                      // S3 filename for the user's photo
    thumbnailUrl                String?                      // S3 URL for the user's thumbnail photo
    thumbnailS3FileName         String?                      // S3 filename for the user's thumbnail photo
    iconUrl                     String?                      // S3 URL for the user's icon photo (small)
    iconS3FileName              String?                      // S3 filename for the user's icon photo
    mustChangePassword          Boolean                      @default(false)
    hasAcceptedTerms            Boolean                      @default(false)
    termsAcceptedAt             DateTime?                    
    advocate_status             AdvocateStatus?
    pairings                    Pairing[]
    resolvedBenevolenceNeeds    BenevolenceNeed[]
    eventRespondents            EventRespondent[]

    recipientNotifications      Notification[]               @relation("recipient_user_relation")

    sendingUserNotifications    Notification[]               @relation("sending_user_relation")

    advocateCoordinatorNotes    CoordinatorNote[]            @relation("advocate_relation")

    coordinatorCoordinatorNotes CoordinatorNote[]            @relation("coordinator_relation")
    coordinatorDocuments        Document[]                   @relation("CoordinatorDocuments")
    advocateDocuments           Document[]                   @relation("AdvocateDocuments")

    // For users with advocate role - their assigned coordinators
    assignedCoordinators        User[]    @relation("AdvocateToCoordinator")
    // For users with coordinator role - advocates they are assigned to
    assignedAdvocates          User[]    @relation("AdvocateToCoordinator")

    magicLinks                 MagicLink[]                   @relation("user_id")
    assignedMoms               Mom[]                         @relation("CoordinatorToMom")
    assignedSessions           Session[]                     @relation("CoordinatorToSession")

    // advocate application
    advocateOnboarding         AdvocateOnboarding? @relation("AdvocateAppUser")

    // Moodle integration
    hasMoodleAccount           Boolean                      @default(false)
    moodleUserId               String?
    moodlePassword             String?
}
