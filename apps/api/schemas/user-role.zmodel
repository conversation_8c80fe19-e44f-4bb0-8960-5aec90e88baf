import "base"
import "user"
import "role"

model UserRole extends Base {

    @@allow('read', auth().id == user_id || has(auth().roles, 'coordinator', true) || has(auth().roles, 'supervisor', true))
    @@allow('all', has(auth().roles, 'administrator', true))

    user    User   @relation(fields: [user_id], references: [id])
    user_id String
    role    Role   @relation(fields: [role_id], references: [id])
    role_id String
}

