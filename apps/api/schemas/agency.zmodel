import "base"
import "mom"
import "affiliate-agency"
model Agency extends Base {

    @@allow('all', true) // Allow all operations on this model - TODO: restrict this later

    name               String?
    agency_name        String
    agency_phone       String?
    address            String?
    address_city       String?
    address_state      String?
    address_postalcode String?
    contact_first_name String
    contact_last_name  String
    contact_email      String?
    contact_phone      String?
    moms               Mom[]    @relation(name: "MomToAgency")
    referred_moms      Mom[]    @relation(name: "MomToReferredAgency")

    affiliateAgencies  AffiliateAgency[]
}

