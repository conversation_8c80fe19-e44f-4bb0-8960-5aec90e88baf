import "base"
import "affiliate"
import "agency"

model AffiliateAgency extends Base {

    @@allow('all', true) // Allow all operations on this model - TODO: restrict this later

    affiliate Affiliate @relation(fields: [affiliate_id], references: [id])
    affiliate_id String
    agency Agency @relation(fields: [agency_id], references: [id])
    agency_id String

    @@unique([affiliate_id, agency_id, deleted_at])
} 