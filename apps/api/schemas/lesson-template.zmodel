import "base"
import "track"
import "document"
import "session-note"
import "lesson"

model LessonTemplate extends Base {

    @@allow('all', true) // Allow all operations on this model - TODO: restrict this later  

    title            String
    description      String?
    priority         Int?
    order            Int?
    duration_days    Int?
    track_id         String
    track            Track      @relation(fields: [track_id], references: [id])
    documents        Document[]

    lesson_instances Lesson[]
}

