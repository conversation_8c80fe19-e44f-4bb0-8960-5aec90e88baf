import "base"
import "mom"
import "user"   
import "service-referral"

enum BenevolenceNeedType {
    Financial
    Physical
    Other
}

model BenevolenceNeed extends Base {

    @@allow('all', true) // Allow all operations on this model - TODO: restrict this later

    name                           String
    description                    String?
    momId                          String
    mom                            Mom                 @relation(fields: [momId], references: [id])
    amount_c                       Float?
    provided_date_c                DateTime?
    did_address_need_c             <PERSON>olean             @default(false)
    financial_amount_gifted_c      Float?
    financial_amount_requested_c   Float?
    financial_mom_contribution_c   Float?
    financial_prevention_plan_c    String?
    is_urgent_c                    <PERSON>olean             @default(false)
    non_addressal_comment_c        String?
    notes_c                        String?
    other_is_referral_needed_c     <PERSON><PERSON><PERSON>             @default(false)
    pg_fulfillment_method_c        String? // DropDown
    physical_good_monetary_value_c Float?
    resolved_date_c                DateTime?
    type_c                         BenevolenceNeedType @default(Financial) // DropDown
    resolvedByUserId               String?
    resolvedByUser                 User?               @relation(fields: [resolvedByUserId], references: [id])
    serviceReferrals               ServiceReferral[]   @relation("ServiceReferrals")

}

