import "base"
import "mom"
import "user"
import "affiliate-agency"
import "track"

enum AffiliateStatusType {
    Active
    Inactive
}

model Affiliate extends Base {

    @@allow('all', true) // Allow all operations on this model - TODO: restrict this later

    name                       String
    description                String?
    agency_nickname            String?
    billing_address_street     String?
    billing_address_street_2   String?
    billing_address_street_3   String?
    billing_address_street_4   String?
    billing_address_city       String?
    billing_address_state      String?
    billing_address_postalcode String?
    billing_address_country    String?
    phone_office               String?
    website                    String?
    email1                     String?
    moms                       Mom[]
    users                      User[]
    status                     AffiliateStatusType?
    contact_name               String?

    affiliateAgencies          AffiliateAgency[]
    tracks Track[]
}

