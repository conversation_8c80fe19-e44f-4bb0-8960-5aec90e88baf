import "base"
import "../schema"
import "aapi-score"
import "affiliate"
import "connection-log"
import "agency"
import "document"
import "pairing"
import "goal"
import "benevolence-need"
import "coordinator-note"
import "notification"
import "user"
import "child"
import "event-respondent"
import "service-referral"
import "wellness-assessment"
import "assessment-result"
import "session"

enum ReferralSubStatus {
  unable_to_contact
  client_declined_services
  ineligible_for_program
  duplicate_referral
  referred_to_alternate_program
}

enum ProspectStatus {
  prospect
  engaged_in_program
  did_not_engage_in_program
  prospect_intake_scheduled
}

enum MomStatus {
  inactive
  active
}

enum Language {
  english
  spanish
  chinese_mandarin
  vietnamese
  tagalog
  arabic
  korean
  russian
  french
  hindi
  portuguese
  bengali
  urdu
  german
  haitian_creole
  polish
  italian
  japanese
  persian_farsi
  gujarati
  other
}

model Mom extends Base {

    @@allow(
        'create,read,update',
        has(auth().roles, 'advocate') &&
        !has(auth().roles, 'administrator') &&
        pairings?[advocateUserId == auth().id]
    )

    @@allow(
        'create,read,update',
        has(auth().roles, 'coordinator') &&
        !has(auth().roles, 'administrator') &&
        assigned_user_id == auth().id
    )

    @@allow(
        'create,read,update',
        has(auth().roles, 'supervisor') &&
        !has(auth().roles, 'administrator') &&
        auth().affiliateId == affiliate_id
    )

    @@allow('create,read', has(auth().roles, 'administrator'))

    first_name                     String?
    last_name                      String?
    prospect_status                ProspectStatus?
    status                         MomStatus?
    referral_sub_status            ReferralSubStatus?
    status_description             String?
    phone_other                    String?
    phone_alternate_c              String?
    race_c                         String?
    languages                      String?
    cultural_heritage_c            String?
    martial_status                 String?
    currently_pregnant_c           String?
    pregnant_due_date              DateTime?
    number_of_children_c           Int @default(0)
    number_of_children_in_home_c   Int @default(0)
    children_in_home               Int @default(0)
    need_details_c                 String?
    what_else_c                    String?
    caregiver_type_c               String?
    gender_c                       String?
    preferred_contact_method_c     CommunicationPreference?
    sms_message_opt_in             Boolean?
    email1                         String?
    emergency_contact_name_c       String?
    emergency_contact_relation_c   String?
    emergency_contact_number_c     String?
    referral_type_c                String?
    supports_court_order_c         Boolean?
    service_selected_c             String?
    account_name                   String?
    affiliate_id                   String?
    affiliate                      Affiliate?        @relation(fields: [affiliate_id], references: [id])
    agency_id                      String?
    agency                         Agency?           @relation(name: "MomToAgency", fields: [agency_id], references: [id])
    referred_to_agency_id          String?
    referred_to_agency             Agency?           @relation(name: "MomToReferredAgency", fields: [referred_to_agency_id], references: [id])
    referred_to_agency_reason      String?
    consent_obtained_c             Boolean?
    referring_contact_first_name_c String?
    referring_contact_last_name_c  String?
    referring_contact_email_c      String?
    referring_contact_phone_c      String?
    birthdate                      DateTime?
    discharge_reason_c             String?
    closed_date_c                  DateTime?
    language_preference_c          Language?
    language_notes_c               String?
    languages_c                    Language[]
    primary_address_street         String?
    primary_address_street_two_c   String?
    primary_address_city           String?
    primary_address_state          String?
    primary_address_postalcode     String?
    primary_address_county_c       String?
    connected_benevolance_c        Boolean?
    connected_childcare_c          Boolean?
    connected_closet_c             Boolean?
    connected_education_c          Boolean?
    connected_health_c             Boolean?
    connected_housing_c            Boolean?
    connected_legal_c              Boolean?
    connected_mental_health_c      Boolean?
    connected_substance_c          Boolean?
    photoUrl                       String?
    photoS3FileName                String?
    thumbnailUrl                   String?
    thumbnailS3FileName            String?
    iconUrl                        String?
    iconS3FileName                 String?
    date_entered                   String?
    address_access_c               String?
    assigned_user_id               String?
    assigned_user                  User?             @relation("CoordinatorToMom", fields: [assigned_user_id], references: [id])
    documents                      Document[]
    connectionlogs                 ConnectionLog[]
    pairings                       Pairing[]
    goals                          Goal[]
    benevolenceNeeds               BenevolenceNeed[]
    coordinatorNotes               CoordinatorNote[]
    aapiScore                      AAPIScore?        @relation()
    notifications                  Notification[]
    service_referrals              ServiceReferral[]
    children                       Child[]    @relation("MomToChild")
    eventRespondents               EventRespondent[]
    wellnessAssessments            WellnessAssessment[]
    assessmentResults              AssessmentResult[]
    sessions                       Session[]
}

