import "base"
import "event"
import "user"
import "mom"
import "notification"

model EventRespondent extends Base {

    @@allow('all', true) // Allow all operations on this model - TODO: restrict this later

    event_id       String
    event          Event   @relation(fields: [event_id], references: [id])
    user_id        String?  
    user           User?   @relation(fields: [user_id], references: [id])
    mom_id         String?
    mom            Mom?    @relation(fields: [mom_id], references: [id])
    hasBeenInvited Boolean @default(false)
    didRsvp        Boolean @default(false)
    didCheckin     <PERSON>an @default(false)
    needsTransport Boolean @default(false)
    childrenCount  Int     @default(0)
    name           String?
    address        String?
    phone_number   String?
    email          String?
    notifications  Notification[]
}
