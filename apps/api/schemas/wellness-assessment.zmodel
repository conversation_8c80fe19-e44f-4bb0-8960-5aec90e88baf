import "base"
import "mom"

model WellnessAssessment extends Base {

    @@allow('all', true) // Allow all operations on this model - TODO: restrict this later

    completed_ahead               Boolean?  // Checkbox
    meeting_method                String?   // DropDown
    staff_name_c                  String?   // TextArea
    start_date_c                  DateTime? // Date
    completed_date                DateTime? // Date

    cw_home_status                String?   // DropDown
    cw_involvement_as_child       String?   // DropDown
    cw_involvement_as_mom         Boolean?  // Checkbox
    cw_allegations                Boolean?  // Checkbox
    cw_maltreatment_type          String?   // MultiSelect
    cw_active_involvement         String?   // DropDown
    cw_date_of_involvement        DateTime? // Date
    cw_fp_goal                    String?   // DropDown
    cw_fp_impact                  String?   // DropDown
    cw_notes                      String?   // TextArea
    cw_score                      Int?

    cc_reliable_care              String?   // DropDown
    cc_affordability              String?   // DropDown
    cc_childcare_safety           Boolean?  // Checkbox
    cc_backup_care                String?   // DropDown
    cc_school_enrollment          String?   // DropDown
    cc_special_ed                 Boolean?  // Checkbox
    cc_ed_concerns                Boolean?  // Checkbox
    cc_health_ins                 String?   // DropDown
    cc_special_med                String?   // DropDown
    cc_med_access                 String?   // DropDown
    cc_notes                      String?   // TextArea
    cc_score                      Int?

    home_type                     String?   // DropDown
    home_category                 String?   // DropDown
    home_name_on_lease            String?   // DropDown
    home_security_concerns        String?   // DropDown
    home_recent_homeless          String?   // DropDown
    home_voucher                  String?   // DropDown
    home_perc_toward              String?   // DropDown
    home_safe                     Boolean?  // Checkbox
    home_risk_in_home             Boolean?  // Checkbox
    home_notes                    String?   // TextArea
    home_score                    Int?

    trnprt_access                 String?   // DropDown
    trnprt_affordable             String?   // DropDown
    trnprt_seat_access            Boolean?  // Checkbox
    trnprt_private_safety         String?   // DropDown
    trnprt_license                String?   // DropDown
    trnprt_notes                  String?   // TextArea
    trnprt_score                  Int?

    res_positive_outlook        String?   // DropDown
    res_communication           String?   // DropDown
    res_traditions              String?   // DropDown
    res_fr_pfs_summary          String?   // DropDown
    res_overall_sat             String?   // Integer
    res_happiness               String?   // Integer
    res_sat_summary             String?   // DropDown
    res_fulfillment             String?   // Integer
    res_purpose                 String?   // Integer
    res_purpose_summary         String?   // DropDown
    res_goodness                String?   // Integer
    res_sacrifice               String?   // Integer
    res_virtue_summary          String?   // DropDown
    res_belief_self             String?   // Integer
    res_support                 Boolean?  // Checkbox
    res_attendance              String?   // DropDown
    res_reflections             String?   // TextArea
    res_notes                   String?   // TextArea
    res_score                   Int?

    well_gad_q1                   String?   // DropDown
    well_gad_q2                   String?   // DropDown
    well_gad_q3                   String?   // DropDown
    well_gad_q4                   String?   // DropDown
    well_gad_q5                   String?   // DropDown
    well_gad_q6                   String?   // DropDown
    well_gad_q7                   String?   // DropDown
    well_gad_total                String?   // DropDown
    well_phq_q1                   String?   // DropDown
    well_phq_q2                   String?   // DropDown
    well_phq_q3                   String?   // DropDown
    well_phq_q4                   String?   // DropDown
    well_phq_q5                   String?   // DropDown
    well_phq_q6                   String?   // DropDown
    well_phq_q7                   String?   // DropDown
    well_phq_q8                   String?   // DropDown
    well_phq_q9                   String?   // DropDown
    well_phq_total                String?   // DropDown
    well_health_insurance         Boolean?  // Checkbox
    well_medical_care             String?   // DropDown
    well_discouragement           String?   // DropDown
    well_discouragement_summary   String?   // DropDown
    well_counseling               String?   // DropDown
    well_counseling_summary       String?   // DropDown
    well_strategies               String?   // DropDown
    well_strategies_summary       String?   // DropDown
    well_phys_health              String?   // Integer
    well_mental_health            String?   // Integer
    well_health_summary           String?   // DropDown
    well_counseling_past          String?   // DropDown
    well_counseling_interest      String?   // DropDown
    well_reflections              String?   // TextArea
    well_notes                    String?   // TextArea
    well_score                    Int?

    subs_recency                  String?   // DropDown
    subs_support_needed           String?   // DropDown
    subs_treatment_history        Boolean?  // Checkbox
    subs_notes                    String?   // TextArea
    subs_score                    Int?

    soc_status                    String?   // DropDown
    soc_length                    String?   // DropDown
    soc_tension                   String?   // DropDown
    soc_resolve_arguments         String?   // DropDown
    soc_wast_sf                   String?   // DropDown
    soc_dynamics                  String?   // TextArea
    soc_rel_reflections           String?   // TextArea
    soc_rel_notes                 String?   // TextArea
    soc_faith_id                  String?   // DropDown
    soc_pfs_supportive_rels       String?   // DropDown
    soc_pfs_supportive_advice     String?   // DropDown
    soc_pfs_support_goals         String?   // DropDown
    soc_pfs_emergency_contact     String?   // DropDown
    soc_trusted_network           String?   // MultiSelect
    soc_pfs_summary_score         String?   // DropDown
    soc_frs_content_with_rels     String?   // Integer
    soc_frs_rel_sat               String?   // Integer
    soc_frs_summary               String?   // DropDown
    soc_reflections               String?   // TextArea
    soc_notes                     String?   // TextArea
    soc_score                     Int?

    emp_fin_struggles             String?   // MultiSelect
    emp_challenges                String?   // MultiSelect
    emp_difficulty                String?   // DropDown
    emp_afford_food               String?   // DropDown
    emp_concrete_pfs_total        String?   // DropDown
    emp_support_received          String?   // MultiSelect
    emp_support_needed            String?   // MultiSelect
    emp_diff_manage_bills         String?   // DropDown
    emp_emergency_funds           String?   // DropDown
    emp_fin_notes                 String?   // TextArea
    emp_status                    String?   // DropDown
    emp_duration_current          String?   // DropDown
    emp_work_eligibility          String?   // DropDown
    emp_highest_ed                String?   // DropDown
    emp_notes                     String?   // TextArea
    emp_score                     Int?

    legal_current                 String?   // DropDown
    legal_rep                     String?   // DropDown
    legal_rep_access              String?   // DropDown
    legal_plan_childcare          String?   // DropDown
    legal_notes                   String?   // TextArea
    legal_score                   Int?

    naa_child_behavior            String?   // DropDown
    naa_discipline                String?   // DropDown
    naa_power_struggles           String?   // DropDown
    naa_emotions                  String?   // DropDown
    naa_nurture_pfs_summary       String?   // DropDown
    naa_reflections               String?   // TextArea
    naa_notes                     String?   // TextArea
    naa_score                     Int?

    mom_id                        String
    mom Mom                       @relation(fields: [mom_id], references: [id])
}
