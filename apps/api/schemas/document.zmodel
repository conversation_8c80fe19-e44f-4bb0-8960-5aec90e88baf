import "base"
import "mom"
import "lesson-template"
import "lesson"
import "user"
import "document-tag"

model Document extends Base {

    @@allow('all', true) // Allow all operations on this model - TODO: restrict this later

    document_name      String?
    filecontents       String?
    filename           String?
    mimeType           String?
    description        String?
    external_url_c     String?   // URL for external resources
    s3_file_name       String?
    is_primary_lesson_resource Boolean @default(false)
    subcategory_id     String?   // Document subcategory
    coordinator_id     String?
    coordinator        User?           @relation("CoordinatorDocuments", fields: [coordinator_id], references: [id])
    advocate_id        String?
    advocate           User?           @relation("AdvocateDocuments", fields: [advocate_id], references: [id])
    mom_id             String?
    mom                Mom?            @relation(fields: [mom_id], references: [id])
    lesson_template_id String?
    lessonTemplate     LessonTemplate? @relation(fields: [lesson_template_id], references: [id])
    lesson_id          String?
    lesson             Lesson?         @relation(fields: [lesson_id], references: [id])

    // Relation to DocumentTag junction model
    documentTags       DocumentTag[]
}

