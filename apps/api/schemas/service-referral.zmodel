import "base"
import "mom"
import "benevolence-need"

enum ServiceType {
    benevolence
    care_communities
    childcare
    closet_visit
    crisis_resources
    education
    group_parenting
    health
    housing
    legal
    mental_health
    substance
    therapy
}

enum OutcomeType {
    successful
    unsuccessful
    unknown
}

model ServiceReferral extends Base {
    @@allow('all', true) 

    service             ServiceType  
    outcome             OutcomeType  
    start_date          DateTime
    provider            String
    mom_id              String
    mom                 Mom          @relation(fields: [mom_id], references: [id])
    benevolence_need_id String?
    benevolence_need    BenevolenceNeed? @relation("ServiceReferrals", fields: [benevolence_need_id], references: [id])

    @@index([mom_id])
    @@index([benevolence_need_id])
}
