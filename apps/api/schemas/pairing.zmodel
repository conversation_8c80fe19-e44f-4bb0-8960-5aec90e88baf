import "base"
import "mom"
import "user"
import "track"
import "lesson"
import "session"
enum PairingStatusType {
    waiting_to_be_paired
    paired
    pairing_complete
}

enum TrackStatusType {
    in_program
    discharged_incomplete
    complete
}

enum InProgramTrackSubStatusType {
    waiting_to_begin
    session_in_progress
}

enum DischargeIncompleteSubStatusType {
    track_requirements_unmet
    client_choice
    relocated
    other
}

enum IncompleteReasonSubStatusType {
    no_advocate
    extended_wait
    priorities_shifted
    achieved_outcomes
}

enum CompleteReasonSubStatusType {
    completed_full_track
    completed_without_support_sessions
    completed_without_post_assessment
}


model Pairing extends Base {

    @@allow('all', true) // Allow all operations on this model - TODO: restrict this later

    name           String
    description    String?
    momId          String?
    mom            Mom?               @relation(fields: [momId], references: [id])
    advocateUserId String?
    advocateUser   User?              @relation(fields: [advocateUserId], references: [id])
    trackId        String?
    track          Track?             @relation(fields: [trackId], references: [id])
    status         PairingStatusType?
    track_status     TrackStatusType?
    in_program_track_sub_status InProgramTrackSubStatusType?
    discharge_incomplete_sub_status DischargeIncompleteSubStatusType?
    incomplete_reason_sub_status IncompleteReasonSubStatusType?
    complete_reason_sub_status CompleteReasonSubStatusType?

    lessons        Lesson[]

    sessions       Session[]
}

