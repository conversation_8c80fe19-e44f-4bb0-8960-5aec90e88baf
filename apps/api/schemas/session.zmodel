import "base"
import "lesson"
import "pairing"
import "mom"
import "user"
import "session-note"
import "session-group"

enum SessionStatusType {
    Planned
    Held
    NotHeld
}

enum SessionType {
    Support_Session
    Track_Session
    Referral_Session
}

model Session extends Base {

    @@allow('all', true) // Allow all operations on this model - TODO: restrict this later

    date_start             DateTime?
    date_end               DateTime?
    description            String?
    name                   String?
    location               String?
    join_url               String?
    status                 SessionStatusType?
    session_type           SessionType?
    session_note           SessionNote?
    has_reminder_been_sent Boolean            @default(false)

    // Used in the advocate view
    pairing_id             String?
    pairing                Pairing?           @relation(fields: [pairing_id], references: [id])

    // Used in the coordinator view
    mom_id                 String?
    mom                    Mom?               @relation(fields: [mom_id], references: [id])
    assigned_user_id       String?
    assigned_user          User?              @relation("CoordinatorToSession", fields: [assigned_user_id], references: [id])

    session_group_id       String?
    session_group          SessionGroup?      @relation(fields: [session_group_id], references: [id])
}

