import "base"
import "pairing"
import "lesson-template"
import "session-note"
import "affiliate"

enum LanguageType {
    english
    spanish
}

model Track extends Base {

    @@allow('all', true) // Allow all operations on this model - TODO: restrict this later

    title                  String
    description            String?
    track_summary          String?
    mom_summary            String?
    connection_description String?
    language_type          LanguageType?
    pairings               Pairing[]
    lessonTemplates        LessonTemplate[]
    affiliates             Affiliate[]
}

