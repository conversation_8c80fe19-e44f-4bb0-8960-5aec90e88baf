import "base"
import "session"
import "track"
import "lesson-template"
import "lesson"

enum AttendanceAndPromptnessType {
    On_Time
    Late
    No_Show
}

enum MomEngagementType {
    Full
    Partial
    None
}

enum SessionNoteStatusType {
    submitted
    new
    rejected
    approved
}

model SessionNote extends Base {

    @@allow('all', true) // Allow all operations on this model - TODO: restrict this later  

    name                      String
    description               String?
    note                      String?
    attendance_and_promptness AttendanceAndPromptnessType?
    status                    SessionNoteStatusType?
    moms_engagement_c         MomEngagementType?
    new_attempt               Boolean?
    new_attempt_example       String?
    date_submitted_c          DateTime?
    session_id                String                       @unique
    session                   Session                      @relation(fields: [session_id], references: [id])
    covered_lesson_id         String?
    covered_lesson            Lesson?                      @relation(fields: [covered_lesson_id], references: [id])
}
