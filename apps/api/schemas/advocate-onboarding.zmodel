import "base"
import "user"
import "mom"

enum Interest {
    Advocate
    Volunteer
}

enum MeetingType {
    Virtual
    InPerson
}

model AdvocateOnboarding extends Base {
    @@allow('all', true) // Allow all operations on this model - TODO: restrict this later

    // Tracking
    trackingToken           String? @unique

    // Referral information
    referredBy              String?
    canCompleteBackground   Boolean @default(true)
    
    // Interest info
    interests               Interest[]

    // Personal info
    hasMultiLang            Boolean @default(false)
    languages               Language[]
    hasExpCrisis            Boolean @default(false)
    hasExpFoster            Boolean @default(false)
    hasExpVictims           Boolean @default(false)
    hasExpWelfare           Boolean @default(false)
    hasExpChildren          String?
    personalNote            String?

    // Parenting info
    groupPref               String?
    parentingNote           String?

    // Availability
    availability            String?

    // Interview info
    interviewDate           DateTime?
    interviewStartTime      String?
    interviewEndTime        String?
    interviewMeetingType    MeetingType?
    interviewLocation       String?

    // Review info
    reviewNote              String?
    reviewedBy              String?
    reviewedAt              DateTime?
    completedAt             DateTime?

    // Background check completion
    backgroundCheckCompleted Boolean @default(false)

    // User relation
    userId                  String? @unique
    user                    User? @relation("AdvocateAppUser", fields: [userId], references: [id])
} 