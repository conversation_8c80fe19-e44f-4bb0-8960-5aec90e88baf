
abstract model Base {

    @@deny('read', deleted_at != 0) // We don't want to allow reading deleted records

    id              String   @id @default(uuid())
    deleted_at      BigInt   @default(0) @omit // 0 (if the record is not deleted) or the utc timestamp of when the record was deleted
    created_at      DateTime @default(now())
    created_by_id   String? // @default(auth().id)
    created_by_name String? // @default(auth().fullName)
    updated_at      DateTime @updatedAt
    updated_by_id   String?
    updated_by_name String?
}