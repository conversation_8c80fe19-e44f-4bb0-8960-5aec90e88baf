<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Event observer implementations for webhooks
 *
 * @package    local_webhooks
 * @copyright  2025 EMA Team
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

/**
 * Event observer class for webhooks
 */
class local_webhooks_observer {
    /**
     * Observer for user created event
     *
     * @param \core\event\user_created $event The event
     * @return bool Success/failure
     */
    public static function user_created(\core\event\user_created $event) {
        global $DB;
        
        // Get the user data
        $userid = $event->objectid;
        $user = $DB->get_record('user', ['id' => $userid]);
        
        if (!$user) {
            return false;
        }
        
        // Prepare data for webhook
        $data = [
            'userId' => $userid,
            'username' => $user->username,
            'firstName' => $user->firstname,
            'lastName' => $user->lastname,
            'email' => $user->email,
            'phone' => $user->phone1,
            'city' => $user->city,
            'country' => $user->country
        ];
        
        // Send the webhook
        require_once(__DIR__ . '/../lib.php');
        return local_webhooks_send_webhook('user_created', $data);
    }
    
    /**
     * Observer for user updated event
     *
     * @param \core\event\user_updated $event The event
     * @return bool Success/failure
     */
    public static function user_updated(\core\event\user_updated $event) {
        global $DB;
        
        // Get the user data
        $userid = $event->objectid;
        $user = $DB->get_record('user', ['id' => $userid]);
        
        if (!$user) {
            return false;
        }
        
        // Prepare data for webhook
        $data = [
            'userId' => $userid,
            'username' => $user->username,
            'firstName' => $user->firstname,
            'lastName' => $user->lastname,
            'email' => $user->email,
            'phone' => $user->phone1,
            'city' => $user->city,
            'country' => $user->country
        ];
        
        // Send the webhook
        require_once(__DIR__ . '/../lib.php');
        return local_webhooks_send_webhook('user_updated', $data);
    }
    
    /**
     * Observer for user deleted event
     *
     * @param \core\event\user_deleted $event The event
     * @return bool Success/failure
     */
    public static function user_deleted(\core\event\user_deleted $event) {
        // For deleted users, we only have the user ID
        $userid = $event->objectid;
        
        // Prepare data for webhook
        $data = [
            'userId' => $userid
        ];
        
        // Send the webhook
        require_once(__DIR__ . '/../lib.php');
        return local_webhooks_send_webhook('user_deleted', $data);
    }
    
    /**
     * Observer for course completed event
     *
     * @param \core\event\course_completed $event The event
     * @return bool Success/failure
     */
    public static function course_completed(\core\event\course_completed $event) {
        global $DB;
        
        $userid = $event->relateduserid;
        $courseid = $event->courseid;
        
        // Get course information
        $course = $DB->get_record('course', ['id' => $courseid]);
        
        if (!$course) {
            return false;
        }
        
        // Prepare data for webhook
        $data = [
            'userId' => $userid,
            'courseId' => $courseid,
            'courseName' => $course->fullname,
            'completionDate' => $event->timecreated
        ];
        
        // Send the webhook
        require_once(__DIR__ . '/../lib.php');
        return local_webhooks_send_webhook('course_completed', $data);
    }
    
    /**
     * Observer for module completion updated event
     *
     * @param \core\event\course_module_completion_updated $event The event
     * @return bool Success/failure
     */
    public static function module_completed(\core\event\course_module_completion_updated $event) {
        global $DB;
        
        $userid = $event->relateduserid;
        $cmid = $event->contextinstanceid;
        
        // Get module information
        $cm = get_coursemodule_from_id('', $cmid);
        
        if (!$cm) {
            return false;
        }
        
        // Only send the webhook if the module is completed
        $completion = $event->get_record_snapshot('course_modules_completion', $event->objectid);
        if ($completion->completionstate != COMPLETION_COMPLETE && 
            $completion->completionstate != COMPLETION_COMPLETE_PASS) {
            return true; // Not completed, but no error
        }
        
        // Prepare data for webhook
        $data = [
            'userId' => $userid,
            'courseId' => $cm->course,
            'moduleId' => $cmid,
            'moduleName' => $cm->name,
            'moduleType' => $cm->modname,
            'completionDate' => $event->timecreated
        ];
        
        // Send the webhook
        require_once(__DIR__ . '/../lib.php');
        return local_webhooks_send_webhook('module_completed', $data);
    }
}
