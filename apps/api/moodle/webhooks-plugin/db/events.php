<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Event observers for webhooks plugin
 *
 * @package    local_webhooks
 * @copyright  2025 EMA Team
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$observers = [
    // User created event
    [
        'eventname' => '\core\event\user_created',
        'callback' => 'local_webhooks_observer::user_created',
    ],
    // User updated event
    [
        'eventname' => '\core\event\user_updated',
        'callback' => 'local_webhooks_observer::user_updated',
    ],
    // User deleted event
    [
        'eventname' => '\core\event\user_deleted',
        'callback' => 'local_webhooks_observer::user_deleted',
    ],
    // Course completed event (for tracking user progress)
    [
        'eventname' => '\core\event\course_completed',
        'callback' => 'local_webhooks_observer::course_completed',
    ],
    // Module completed event (for tracking user progress)
    [
        'eventname' => '\core\event\course_module_completion_updated',
        'callback' => 'local_webhooks_observer::module_completed',
    ],
];
