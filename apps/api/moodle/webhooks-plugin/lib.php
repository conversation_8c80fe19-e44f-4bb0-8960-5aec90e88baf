<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Library functions for webhooks
 *
 * @package    local_webhooks
 * @copyright  2025 EMA Team
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

/**
 * Send webhook event to the configured endpoint
 *
 * @param string $eventName The name of the event that occurred
 * @param array $eventData Data associated with the event
 * @return bool Whether the webhook was sent successfully
 */
function local_webhooks_send_webhook($eventName, $eventData) {
    global $CFG;
    
    // Default to the webhook relay service
    $webhookUrl = 'http://webhook-relay/moodle/webhook';
    
    // If we have a custom configuration, use that instead
    if (!empty($CFG->webhooks_endpoint)) {
        $webhookUrl = $CFG->webhooks_endpoint;
    }
    
    // Prepare the payload
    $payload = json_encode([
        'eventName' => $eventName,
        'timestamp' => time(),
        'data' => $eventData
    ]);
    
    // Set up the curl request
    $ch = curl_init($webhookUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen($payload)
    ]);
    
    // Execute the request
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    // Log the result
    error_log("Webhook sent to $webhookUrl, status code: $httpCode, response: $result");
    
    return $httpCode >= 200 && $httpCode < 300;
}
