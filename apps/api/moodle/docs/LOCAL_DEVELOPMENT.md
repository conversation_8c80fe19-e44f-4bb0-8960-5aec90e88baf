# Local Moodle Development Envir### Manual Setup

If you prefer to set up manually or need more control:

1. **Ensure Docker is running** on your system

2. **Navigate to the moodle directory:**

   ```bash
   cd apps/api/docker/moodle
   ```

3. **Start the Docker Compose environment:**

   ```bash
   docker-compose up -d
   ```

4. **Wait for services to be ready** (usually 15-30 seconds)

5. **Initialize the database** (if first time):

   ```bash
   docker exec moodle-moodle-1 php admin/cli/install_database.php \
     --agree-license \
     --fullname="EMA Moodle" \
     --shortname="EMA" \
     --adminuser=admin \
     --adminpass=admin \
     --adminemail=<EMAIL>
   ```

6. **Configure web services:**

   ```bash
   # Apply SQL configuration
   docker cp ./sql/setup-webservices.sql moodle-mariadb-1:/tmp/
   docker exec moodle-mariadb-1 bash -c "mysql -u moodle -pmoodle_password moodle < /tmp/setup-webservices.sql"

   # Update config.php
   docker exec moodle-moodle-1 php /var/www/html/update-config.php

   # Enable web services
   docker exec moodle-moodle-1 php /var/www/html/enable-webservices.php
   ```

7. **Access Moodle** at http://localhost:8080e explains how to set up and run the Moodle environment locally for development and testing.

## Quick Start

To quickly start the local Moodle environment:

```bash
cd apps/api/docker/moodle
./setup-moodle.sh
```

This script will:

1. Check prerequisites and dependencies
2. Build and start all required services with Docker Compose
3. Initialize the database if needed
4. Configure web services with all necessary fixes
5. Set up webhook integration
6. Wait for Moodle to be accessible
7. Verify the installation
8. Show access information for all services

## Alternative Setup Methods

### Interactive Management

```bash
./moodle-manager.sh
```

Provides a menu-driven interface for setup, management, and troubleshooting.

### Manual Setup

If you prefer to set up manually or need more control:

1. Ensure Docker is running on your system

2. Navigate to the moodle directory:

   ```bash
   cd apps/api/docker/moodle
   ```

3. Start the Docker Compose environment:

   ```bash
   docker-compose up -d
   ```

4. Access Moodle at http://localhost:8080

## Architecture

The local development setup includes:

- **Moodle 4.0+**: Main LMS application running on Apache with PHP 8.1
- **MariaDB 10.11**: Optimized database for Moodle data
- **MailHog**: Email testing service to capture outbound emails
- **Custom Webhook Plugin**: Integration between Moodle and EMA API

### Container Network

- All containers communicate via Docker network
- Moodle connects to database via internal hostname `mariadb`
- Webhook integration connects to host API via `host.docker.internal:3001`

## Access Information

| Service    | URL/Connection        | Credentials              |
| ---------- | --------------------- | ------------------------ |
| Moodle     | http://localhost:8080 | admin / admin            |
| MailHog UI | http://localhost:8025 | No credentials needed    |
| MariaDB    | localhost:3306        | moodle / moodle_password |

## Configuration Files

| File                    | Purpose                                                 |
| ----------------------- | ------------------------------------------------------- |
| `config.php`            | Development Moodle configuration with debugging enabled |
| `config.production.php` | Production Moodle configuration                         |
| `docker-compose.yml`    | Local development container orchestration               |
| `Dockerfile`            | Development container image definition                  |
| `Dockerfile.production` | Production container image definition                   |

## Development Workflow

### Testing the Integration

1. **Start your local EMA API** (should be running on port 3001):

   ```bash
   cd apps/api
   npm run dev
   ```

2. **Perform actions in Moodle** that trigger webhook events:

   - User enrollment in courses
   - Course completion
   - Grade updates
   - Login events

3. **Check your API logs** to confirm webhook reception

### Testing User Synchronization

```bash
# From the API root directory
cd apps/api

# Create test users in EMA and sync to Moodle
node docker/moodle/scripts/create-test-users.js

# Verify API connectivity
node docker/moodle/scripts/verify-moodle-token.js

# Run comprehensive diagnostics
node docker/moodle/scripts/debug-moodle-connection.js
```

### Customizing Configuration

The main configuration files are:

- `config.php` - Development configuration (debugging enabled)
- `config.production.php` - Production configuration

For local development, you shouldn't need to modify these files. The setup scripts handle all necessary configuration automatically.

### Debugging and Logs

Moodle is configured with debugging enabled for development:

- Debug messages are displayed on screen
- Developer debugging is enabled
- Performance information is shown
- All debugging output is visible

**Accessing Container Logs:**

```bash
# View Moodle application logs
docker-compose logs -f moodle

# View database logs
docker-compose logs -f mariadb

# View logs from all services
docker-compose logs -f

# View recent logs (last 50 lines)
docker-compose logs --tail=50
```

**Moodle Debug Information:**

- Error messages appear directly in the web interface
- Debug information is shown at the bottom of pages
- Database queries and performance metrics are displayed

## Troubleshooting

### Quick Fixes

**Use the interactive troubleshooting:**

```bash
./moodle-manager.sh  # Choose option 4: Troubleshoot Web Services
```

**Or run automated verification:**

```bash
./setup-moodle.sh --verify-only
```

### Database Initialization Issues

If you encounter errors about missing database tables:

```bash
# Check if database is properly initialized
docker exec moodle-mariadb-1 mysql -u moodle -pmoodle_password -e "SHOW TABLES IN moodle;" | wc -l

# Should return > 400 (Moodle has many tables)
# If less, run database initialization:
docker exec moodle-moodle-1 php admin/cli/install_database.php \
  --agree-license \
  --fullname="EMA Moodle" \
  --shortname="EMA" \
  --adminuser=admin \
  --adminpass=admin \
  --adminemail=<EMAIL>
```

### Common Issues

1. **500 Internal Server Error**:

   ```bash
   # Check Moodle application logs
   docker-compose logs moodle
   ```

2. **Database Connection Issues**:

   ```bash
   # Verify database is accessible
   docker exec -it moodle-mariadb-1 mysql -u moodle -pmoodle_password -e "SHOW TABLES IN moodle;"

   # Restart database if needed
   docker-compose restart mariadb
   ```

3. **Web Services 403 Forbidden**:

   ```bash
   # Fix web services configuration
   docker exec moodle-moodle-1 php /var/www/html/update-config.php
   docker exec moodle-moodle-1 php /var/www/html/enable-webservices.php
   ```

4. **Permission Issues** (Linux/Unix):
   ```bash
   # Fix container file permissions if needed
   docker exec -it moodle-moodle-1 chmod -R 777 /var/moodledata
   ```

For more detailed troubleshooting, see [TROUBLESHOOTING.md](TROUBLESHOOTING.md).

## Managing the Environment

### Starting and Stopping

```bash
# Start all services (after initial setup)
docker-compose up -d

# Stop services but keep data
docker-compose down

# Stop services and remove all data (full reset)
docker-compose down -v

# Restart specific service
docker-compose restart moodle
docker-compose restart mariadb
```

### Fresh Installation

```bash
# Complete fresh start (removes all data)
./setup-moodle.sh --fresh-install

# Or manually:
docker-compose down -v
docker system prune -f
./setup-moodle.sh
```

### Maintenance Commands

```bash
# View container status
docker-compose ps

# Check resource usage
docker stats

# Clean up unused Docker resources
docker system prune -f

# Update container images
docker-compose pull
docker-compose build --no-cache
```

## Differences from Production

This local setup differs from the AWS production deployment:

| Aspect            | Local Development | AWS Production            |
| ----------------- | ----------------- | ------------------------- |
| **Orchestration** | Docker Compose    | ECS Fargate               |
| **Database**      | MariaDB container | RDS MySQL/MariaDB         |
| **File Storage**  | Container volumes | S3 with ObjectFS          |
| **Load Balancer** | None              | Application Load Balancer |
| **SSL/TLS**       | None (HTTP only)  | ACM certificates          |
| **Caching**       | None              | ElastiCache Redis         |
| **Email**         | MailHog (testing) | SES (real email)          |
| **Scaling**       | Single container  | Auto-scaling              |
| **Monitoring**    | Container logs    | CloudWatch                |
| **Debugging**     | Enabled           | Disabled                  |
| **Security**      | Development mode  | Production hardened       |

## Environment Variables

The local development environment uses these settings in `apps/api/.env`:

```properties
# Local development configuration
MOODLE_URL_BASE="http://localhost:8080"
MOODLE_TOKEN="18745a8b8f98df61fe052159d3f91542"

# Production settings (commented out for local dev)
# MOODLE_URL_BASE="https://moodle.ema-portal.org"
# MOODLE_TOKEN="production-token-here"
```

## Next Steps

1. **Verify Setup**: Run `./setup-moodle.sh --verify-only`
2. **Test Integration**: Create test users with the verification scripts
3. **Development**: Start building your integration with the EMA API
4. **Production**: When ready, see [AWS_DEPLOYMENT.md](AWS_DEPLOYMENT.md) for production deployment

For comprehensive setup instructions, see [SETUP_GUIDE.md](SETUP_GUIDE.md).
For troubleshooting help, see [TROUBLESHOOTING.md](TROUBLESHOOTING.md).

---

**Quick Help**: Use `./moodle-manager.sh` for interactive management and troubleshooting.
