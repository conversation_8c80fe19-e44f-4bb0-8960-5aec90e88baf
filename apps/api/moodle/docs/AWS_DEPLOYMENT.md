# AWS Deployment Guide for Moodle 5.1

This guide covers deploying the Moodle 5.1 environment to AWS using various services for production use.

## Architecture Overview

The production deployment uses the following AWS services:
- **ECS Fargate**: Container orchestration for Moodle application
- **RDS MySQL/MariaDB**: Managed database service
- **ElastiCache Redis**: Session storage and caching
- **Application Load Balancer**: Load balancing and SSL termination
- **S3**: File storage for Moodle data
- **SES**: Email delivery service
- **CloudWatch**: Monitoring and logging
- **Secrets Manager**: Secure storage of sensitive configuration

## Prerequisites

- AWS CLI configured with appropriate permissions
- Docker installed locally
- ECR repository created for Moodle image
- Domain name configured in Route 53 (optional)

## Deployment Steps

### 1. Build and Push Docker Image

```bash
# Build production image
cd apps/api/docker/moodle
docker build -f Dockerfile.production -t moodle-ema:latest .

# Tag for ECR
docker tag moodle-ema:latest YOUR_ECR_REPOSITORY_URI:latest

# Push to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin YOUR_ECR_REPOSITORY_URI
docker push YOUR_ECR_REPOSITORY_URI:latest
```

### 2. Create RDS Database

```bash
# Create RDS MySQL instance
aws rds create-db-instance \
    --db-instance-identifier moodle-db \
    --db-instance-class db.t3.medium \
    --engine mysql \
    --engine-version 8.0.35 \
    --master-username moodleadmin \
    --master-user-password YOUR_DB_PASSWORD \
    --allocated-storage 100 \
    --storage-type gp2 \
    --vpc-security-group-ids sg-XXXXXXXX \
    --db-subnet-group-name default \
    --backup-retention-period 7 \
    --multi-az \
    --storage-encrypted
```

### 3. Create ElastiCache Redis Cluster

```bash
# Create Redis cluster for sessions
aws elasticache create-replication-group \
    --replication-group-id moodle-redis \
    --description "Moodle Redis Cluster" \
    --node-type cache.t3.micro \
    --engine redis \
    --engine-version 7.0 \
    --num-cache-clusters 2 \
    --cache-parameter-group-name default.redis7 \
    --security-group-ids sg-XXXXXXXX \
    --subnet-group-name default
```

### 4. Create S3 Bucket for File Storage

```bash
# Create S3 bucket
aws s3 mb s3://moodle-ema-files --region us-east-1

# Set bucket policy for Moodle access
aws s3api put-bucket-policy \
    --bucket moodle-ema-files \
    --policy file://s3-bucket-policy.json
```

### 5. Store Secrets in AWS Secrets Manager

```bash
# Create database credentials secret
aws secretsmanager create-secret \
    --name moodle/database \
    --description "Moodle database credentials" \
    --secret-string '{"username":"moodleadmin","password":"YOUR_DB_PASSWORD","host":"moodle-db.cluster-XXXXXXXX.us-east-1.rds.amazonaws.com","port":"3306","dbname":"moodle"}'

# Create SMTP credentials secret
aws secretsmanager create-secret \
    --name moodle/smtp \
    --description "Moodle SMTP credentials" \
    --secret-string '{"host":"email-smtp.us-east-1.amazonaws.com","username":"YOUR_SMTP_USERNAME","password":"YOUR_SMTP_PASSWORD"}'
```

### 6. Deploy ECS Service

Use the provided `ecs-task-definition.json` and `ecs-service.json` files:

```bash
# Register task definition
aws ecs register-task-definition --cli-input-json file://ecs-task-definition.json

# Create ECS service
aws ecs create-service --cli-input-json file://ecs-service.json
```

## Environment Variables

The following environment variables are used in production:

### Database Configuration
- `MOODLE_DB_HOST`: RDS endpoint
- `MOODLE_DB_NAME`: Database name (default: moodle)
- `MOODLE_DB_USER`: Database username
- `MOODLE_DB_PASS`: Database password
- `MOODLE_DB_PORT`: Database port (default: 3306)

### Site Configuration
- `MOODLE_WWW_ROOT`: Full URL to Moodle site
- `MOODLE_DATA_ROOT`: Path to moodledata directory
- `MOODLE_WEBHOOK_ENDPOINT`: EMA API webhook endpoint

### Redis Configuration
- `MOODLE_REDIS_HOST`: ElastiCache Redis endpoint
- `MOODLE_REDIS_PORT`: Redis port (default: 6379)
- `MOODLE_REDIS_AUTH`: Redis authentication (if enabled)

### S3 Configuration
- `MOODLE_S3_BUCKET`: S3 bucket name
- `MOODLE_S3_REGION`: AWS region
- `MOODLE_S3_KEY`: AWS access key
- `MOODLE_S3_SECRET`: AWS secret key

### SMTP Configuration
- `MOODLE_SMTP_HOST`: SES SMTP endpoint
- `MOODLE_SMTP_USER`: SMTP username
- `MOODLE_SMTP_PASS`: SMTP password
- `MOODLE_SMTP_SECURE`: Security protocol (tls/ssl)

## Monitoring and Logging

### CloudWatch Logs
- Application logs are sent to CloudWatch log group: `/aws/ecs/moodle`
- Access logs from ALB are stored in S3

### Health Checks
- ECS health check: Uses container health check
- ALB health check: `/login/index.php` endpoint
- Custom health check: `/healthcheck.php` (if implemented)

## Scaling

### Auto Scaling
Configure ECS auto scaling based on:
- CPU utilization (target: 70%)
- Memory utilization (target: 80%)
- Request count per target

### Database Scaling
- Use RDS read replicas for read-heavy workloads
- Monitor database performance metrics

### Caching
- Use ElastiCache for session storage
- Configure Moodle's built-in caching
- Use CloudFront for static assets

## Security Considerations

### Network Security
- Use VPC with private subnets for ECS tasks
- Configure security groups with minimal required access
- Use ALB in public subnets with WAF

### Data Security
- Enable encryption at rest for RDS
- Enable encryption in transit for all connections
- Use IAM roles for service authentication
- Store sensitive data in Secrets Manager

### SSL/TLS
- Use ACM for SSL certificates
- Configure ALB for SSL termination
- Enforce HTTPS redirects

## Backup and Recovery

### Database Backups
- Automated RDS backups (7-day retention)
- Manual snapshots before major updates

### File Backups
- S3 versioning enabled
- Cross-region replication for critical data

### Disaster Recovery
- Multi-AZ deployment for RDS
- ECS tasks distributed across multiple AZs
- Regular DR testing procedures

## Cost Optimization

- Use Spot instances for non-critical tasks
- Implement lifecycle policies for S3 objects
- Monitor and optimize RDS instance sizing
- Use CloudWatch to track costs and usage
