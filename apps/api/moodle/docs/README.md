# EMA Moodle Integration

This directory contains the Docker configuration and integration scripts for setting up a Moodle environment that integrates with the EMA API system. The setup provides user synchronization, webhooks, and comprehensive tooling for both local development and AWS production deployment.

## Quick Start

```bash
# Navigate to the moodle directory
cd apps/api/docker/moodle

# Use the all-in-one setup script (recommended):
./setup-moodle.sh

# Or use the interactive management interface:
./moodle-manager.sh
```

## Directory Structure

```
moodle/
├── docs/                      # Documentation
│   ├── README.md              # This file - main documentation
│   ├── SETUP_GUIDE.md         # Comprehensive setup and integration guide
│   ├── LOCAL_DEVELOPMENT.md   # Local development environment guide
│   ├── AWS_DEPLOYMENT.md      # AWS production deployment instructions
│   └── TROUBLESHOOTING.md     # Common issues and solutions
├── scripts/                   # Management and utility scripts
│   ├── create-test-users.js   # Test user creation
│   ├── verify-moodle-token.js # Token verification
│   └── debug-moodle-connection.js # Connection diagnostics
├── sql/                       # SQL scripts
│   └── setup-webservices.sql  # Web services configuration
├── webhooks-plugin/           # Custom Moodle plugin for webhooks
├── setup-moodle.sh           # ⭐ Main all-in-one setup script
├── moodle-manager.sh          # ⭐ Interactive management interface
├── docker-compose.yml         # Docker configuration
├── Dockerfile                 # Development container definition
├── Dockerfile.production      # Production container definition
├── config.php                 # Development Moodle configuration
├── config.production.php      # Production Moodle configuration
├── enable-webservices.php     # Web services enablement script
└── update-config.php          # Config.php updater script
```

## Features

- **🚀 One-Click Setup**: Complete installation with a single command
- **🔧 Interactive Management**: Easy-to-use management interface
- **🌐 Moodle 4.0+**: Latest stable version with optimized configuration
- **🗄️ MariaDB 10.11**: Optimized database configuration
- **🔗 Webhook Integration**: Custom plugin to send events to EMA API
- **👥 User Synchronization**: Automatic sync between EMA API and Moodle
- **🛠️ Web Services**: Properly configured REST web services with all necessary fixes
- **📧 Email Testing**: Mailhog for testing email functionality
- **🐛 Development Optimized**: Debugging enabled, caching disabled for development
- **☁️ AWS Ready**: Production-ready configuration for AWS deployment

## Main Scripts

### 🚀 setup-moodle.sh (Recommended)

The primary all-in-one setup script that handles complete Moodle installation:

**Features:**

- ✅ Prerequisites checking (Docker, docker-compose, Node.js)
- 🐳 Docker container management with health checks
- 🗄️ Database initialization and configuration
- 🔧 Web services configuration with all necessary fixes
- 🔗 Webhook plugin setup
- ✅ Installation verification and testing
- 📋 Access information display

**Usage:**

```bash
./setup-moodle.sh                 # Normal setup
./setup-moodle.sh --fresh-install # Clean installation (removes all data)
./setup-moodle.sh --verify-only   # Verify existing setup without changes
./setup-moodle.sh --help          # Show detailed help
```

### 🔧 moodle-manager.sh

Interactive management interface providing:

1. 🚀 **Setup/Start Moodle** - Run the setup script
2. 🆕 **Fresh Install** - Complete clean installation
3. ✅ **Verify Installation** - Test all components
4. 🔧 **Troubleshoot Web Services** - Fix common web service issues
5. 👥 **Create Test Users** - Generate test users for development
6. 🔑 **Verify API Token** - Test Moodle API connectivity
7. 🐛 **Debug Connection Issues** - Comprehensive diagnostics
8. 📊 **View Container Status** - Check Docker container health
9. 📖 **Open Documentation** - Quick access to guides
10. 🛑 **Stop Moodle** - Stop all services

**Usage:**

```bash
./moodle-manager.sh  # Launch interactive menu
```

## Access Information

After successful setup, you'll have access to:

| Service      | URL                   | Credentials              |
| ------------ | --------------------- | ------------------------ |
| **Moodle**   | http://localhost:8080 | admin / admin            |
| **Database** | localhost:3306        | moodle / moodle_password |
| **Email UI** | http://localhost:8025 | No credentials needed    |

## Testing and Verification

### Built-in Verification Scripts

All scripts should be run from the **API root directory** (`apps/api`):

```bash
# Verify API token is working
node docker/moodle/scripts/verify-moodle-token.js

# Create test users and verify synchronization
node docker/moodle/scripts/create-test-users.js

# Comprehensive connection diagnostics
node docker/moodle/scripts/debug-moodle-connection.js
```

### Quick Verification

```bash
# Check container status
docker-compose ps

# Test basic connectivity
curl http://localhost:8080

# Verify web services endpoint
curl "http://localhost:8080/webservice/rest/server.php?wstoken=18745a8b8f98df61fe052159d3f91542&wsfunction=core_webservice_get_site_info&moodlewsrestformat=json"
```

## Web Services Integration

The setup automatically configures Moodle web services with all necessary fixes:

- ✅ **REST Protocol**: Enabled and properly configured
- ✅ **Database Configuration**: `webserviceprotocols` set to 'rest'
- ✅ **Config.php Settings**: All required web service settings added
- ✅ **External Service**: 'EMA API Service' with required functions
- ✅ **API Token**: Pre-configured token for admin user
- ✅ **Capabilities**: Proper permissions for user management

### Configured Functions

The EMA API Service includes these functions:

- `core_user_create_users` - Create new users
- `core_user_update_users` - Update existing users
- `core_user_delete_users` - Delete users
- `core_user_get_users_by_field` - Search users
- `core_webservice_get_site_info` - Get site information

## Common Management Commands

```bash
# Start Moodle (if already set up)
docker-compose up -d

# Stop Moodle services
docker-compose down

# Stop and remove all data (full reset)
docker-compose down -v

# View logs
docker-compose logs -f

# Check container status
docker-compose ps

# Restart services
docker-compose restart
```

## Environment Variables

The integration uses these environment variables (configured in `apps/api/.env`):

```properties
# Moodle Configuration
MOODLE_URL_BASE="http://localhost:8080"           # Local development URL
MOODLE_TOKEN="18745a8b8f98df61fe052159d3f91542"   # Pre-configured API token

# Production Moodle (when deployed to AWS)
# MOODLE_URL_BASE="https://moodle.ema-portal.org"
# MOODLE_TOKEN="production-token-here"
```

## Documentation

| Guide                                            | Purpose                                   |
| ------------------------------------------------ | ----------------------------------------- |
| **[SETUP_GUIDE.md](SETUP_GUIDE.md)**             | Comprehensive setup and integration guide |
| **[LOCAL_DEVELOPMENT.md](LOCAL_DEVELOPMENT.md)** | Local development environment details     |
| **[AWS_DEPLOYMENT.md](AWS_DEPLOYMENT.md)**       | Production deployment to AWS              |
| **[TROUBLESHOOTING.md](TROUBLESHOOTING.md)**     | Common issues and solutions               |

## Architecture

### Local Development

- **Docker Compose**: Container orchestration
- **Moodle**: PHP-based learning management system
- **MariaDB**: Database for Moodle data
- **Mailhog**: Email testing interface
- **Custom Plugin**: Webhook integration with EMA API

### Production (AWS)

- **ECS Fargate**: Container orchestration
- **RDS MySQL/MariaDB**: Managed database
- **ElastiCache Redis**: Session storage and caching
- **S3**: File storage with ObjectFS plugin
- **SES**: Email delivery
- **ALB**: Load balancing with SSL termination
- **CloudWatch**: Monitoring and logging

## User Synchronization

The integration provides automatic user synchronization:

- ✅ **Create**: Users created in EMA are automatically created in Moodle
- ✅ **Update**: User updates in EMA are synchronized to Moodle
- ✅ **Delete**: User deletions in EMA are synchronized to Moodle
- ✅ **Webhook Events**: Moodle sends events back to EMA API

The email address serves as the unique identifier between systems.

## Support and Troubleshooting

1. **Use the interactive manager**: `./moodle-manager.sh` provides guided troubleshooting
2. **Check the troubleshooting guide**: See `TROUBLESHOOTING.md` for common issues
3. **Run diagnostics**: Use the debug script for detailed information
4. **Verify setup**: Use `--verify-only` flag to check your installation

## Next Steps

1. **Start with Quick Start**: Run `./setup-moodle.sh` for immediate setup
2. **Read Setup Guide**: See `SETUP_GUIDE.md` for detailed configuration options
3. **Test Integration**: Create test users and verify synchronization
4. **Deploy to Production**: Follow `AWS_DEPLOYMENT.md` for production deployment

---

**Need Help?** Use `./moodle-manager.sh` for interactive assistance or see the troubleshooting guide.
