# Moodle Troubleshooting Guide

This guide covers common issues and their solutions when working with the EMA Moodle integration.

## Quick Diagnostics

### Interactive Troubleshooting

```bash
./moodle-manager.sh  # Choose option 4: Troubleshoot Web Services
```

### Automated Verification

```bash
./setup-moodle.sh --verify-only
```

### Comprehensive Diagnostics

```bash
cd apps/api
node docker/moodle/scripts/debug-moodle-connection.js
```

## Common Issues

### 1. Web Services 403 Forbidden Error

**Symptoms:**

- API calls return 403 Forbidden
- Cannot create/update users via API
- Web service functions are rejected

**Causes:**

- Web services not properly enabled in database
- Config.php missing web service settings
- REST protocol not enabled

**Solutions:**

**Quick Fix:**

```bash
./moodle-manager.sh  # Option 4: Troubleshoot Web Services
```

**Manual Fix:**

```bash
# Update config.php with web services settings
docker exec moodle-moodle-1 php /var/www/html/update-config.php

# Enable web services through Moodle admin functions
docker exec moodle-moodle-1 php /var/www/html/enable-webservices.php

# Verify configuration
curl "http://localhost:8080/webservice/rest/server.php?wstoken=18745a8b8f98df61fe052159d3f91542&wsfunction=core_webservice_get_site_info&moodlewsrestformat=json"
```

**Verification:**

```bash
# Check database configuration
docker exec moodle-mariadb-1 mysql -u moodle -pmoodle_password -e "
SELECT name, value FROM moodle.mdl_config
WHERE name IN ('enablewebservices', 'webserviceprotocols');"

# Should return:
# enablewebservices | 1
# webserviceprotocols | rest
```

### 2. Database Connection Issues

**Symptoms:**

- Moodle shows "Database connection failed"
- Containers start but Moodle is inaccessible
- 500 Internal Server Error

**Causes:**

- MariaDB container not ready
- Database credentials incorrect
- Network connectivity issues

**Solutions:**

**Check Database Status:**

```bash
# Check if database container is running
docker-compose ps mariadb

# Check database logs
docker-compose logs mariadb

# Test database connectivity
docker exec moodle-mariadb-1 mysql -u moodle -pmoodle_password -e "SELECT 1;"
```

**Fix Database Issues:**

```bash
# Restart database
docker-compose restart mariadb

# Wait for database to be ready
sleep 15

# If still failing, full restart
docker-compose down
docker-compose up -d
```

**Verify Database Configuration:**

```bash
# Check if Moodle database exists and has tables
docker exec moodle-mariadb-1 mysql -u moodle -pmoodle_password -e "SHOW TABLES IN moodle;" | wc -l

# Should return a number > 400 (Moodle has many tables)
```

### 3. Container Startup Issues

**Symptoms:**

- Containers fail to start
- Containers show as "unhealthy"
- Port conflicts
- Image build failures

**Causes:**

- Port 8080 already in use
- Docker daemon issues
- Insufficient resources
- Corrupt container images

**Solutions:**

**Check Container Status:**

```bash
# View all container status
docker-compose ps

# Check specific container logs
docker-compose logs moodle
docker-compose logs mariadb
```

**Port Conflicts:**

```bash
# Check what's using port 8080
lsof -i :8080

# Stop conflicting processes or change ports in docker-compose.yml
```

**Fresh Container Start:**

```bash
# Stop and remove everything
docker-compose down -v

# Remove old images
docker system prune -f

# Fresh build and start
docker-compose build --no-cache
docker-compose up -d
```

**Resource Issues:**

```bash
# Check Docker resources
docker system df

# Clean up unused resources
docker system prune -af
```

### 4. API Token Issues

**Symptoms:**

- Token validation fails
- API returns "Invalid token" error
- User synchronization not working

**Causes:**

- Token not created in database
- Token expired
- Token associated with wrong service

**Solutions:**

**Verify Token:**

```bash
# Check if token exists in database
docker exec moodle-mariadb-1 mysql -u moodle -pmoodle_password -e "
SELECT token, userid, externalserviceid, validuntil
FROM moodle.mdl_external_tokens
WHERE token = '18745a8b8f98df61fe052159d3f91542';"
```

**Test Token:**

```bash
# Test token with API call
curl "http://localhost:8080/webservice/rest/server.php?wstoken=18745a8b8f98df61fe052159d3f91542&wsfunction=core_webservice_get_site_info&moodlewsrestformat=json"

# Should return JSON with site information
```

**Regenerate Token:**
If the token is missing or invalid, you'll need to regenerate it manually:

1. Access Moodle at http://localhost:8080 (admin/admin)
2. Go to Site administration → Plugins → Web services → Manage tokens
3. Create new token for admin user and EMA API Service
4. Update `MOODLE_TOKEN` in `apps/api/.env`

### 5. User Synchronization Failures

**Symptoms:**

- Users created in EMA don't appear in Moodle
- User updates not synchronized
- Sync errors in API logs

**Causes:**

- Web services not working
- Invalid user data
- Network connectivity issues
- Missing user fields

**Solutions:**

**Test User Creation:**

```bash
cd apps/api
node docker/moodle/scripts/create-test-users.js
```

**Manual User Test:**

```bash
# Test creating a user via API
curl -X POST "http://localhost:8080/webservice/rest/server.php" \
  -d "wstoken=18745a8b8f98df61fe052159d3f91542" \
  -d "wsfunction=core_user_create_users" \
  -d "moodlewsrestformat=json" \
  -d "users[0][username]=testuser" \
  -d "users[0][password]=Password123!" \
  -d "users[0][firstname]=Test" \
  -d "users[0][lastname]=User" \
  -d "users[0][email]=<EMAIL>"
```

**Check User Sync Logs:**

```bash
# Check API logs for sync errors
cd apps/api
npm run dev  # Check console for Moodle-related errors
```

### 6. Webhook Integration Issues

**Symptoms:**

- Moodle events not reaching EMA API
- Webhook endpoint returning errors
- No webhook data received

**Causes:**

- Webhook plugin not installed
- Incorrect webhook URL
- API endpoint not accessible
- Network connectivity issues

**Solutions:**

**Verify Webhook Plugin:**

```bash
# Check if webhook plugin is installed
docker exec moodle-moodle-1 ls -la /var/www/html/local/webhooks/
```

**Test Webhook Endpoint:**

```bash
# Test if API webhook endpoint is accessible
curl -X POST http://localhost:3001/webhooks/moodle \
  -H "Content-Type: application/json" \
  -d '{"test": "webhook"}'
```

**Check API Logs:**
Start your API in development mode and watch for webhook requests:

```bash
cd apps/api
npm run dev
```

### 7. Performance Issues

**Symptoms:**

- Slow Moodle response times
- Container resource exhaustion
- Database queries taking too long

**Causes:**

- Insufficient container resources
- Database not optimized
- Too much debugging output
- Caching disabled

**Solutions:**

**Check Resource Usage:**

```bash
# Check container resource usage
docker stats

# Check system resources
htop  # or top on macOS
```

**Optimize for Development:**

```bash
# Moodle debugging is enabled by default for development
# For better performance, you can disable some debugging
# Edit config.php to reduce debug output
```

**Database Optimization:**

```bash
# Check database performance
docker exec moodle-mariadb-1 mysql -u moodle -pmoodle_password -e "SHOW PROCESSLIST;"

# Check slow queries
docker exec moodle-mariadb-1 mysql -u moodle -pmoodle_password -e "SHOW VARIABLES LIKE 'slow_query_log';"
```

## Environment-Specific Issues

### macOS Issues

**Docker Desktop Problems:**

- Ensure Docker Desktop is allocated sufficient resources (4GB+ RAM)
- Check that Docker Desktop is using the correct virtualization framework

**Port Binding Issues:**

```bash
# On macOS, sometimes port binding fails
# Try stopping all containers and restarting Docker Desktop
docker-compose down
# Restart Docker Desktop
docker-compose up -d
```

### Windows Issues

**Path Issues:**

- Ensure Docker Desktop is configured to share the drive containing your project
- Check file path format in docker-compose.yml

**WSL2 Integration:**

- Ensure WSL2 backend is enabled in Docker Desktop
- Run commands from within WSL2 if using WSL

### Linux Issues

**Permission Issues:**

```bash
# Fix file permissions if needed
sudo chown -R $USER:$USER apps/api/docker/moodle/
```

**SELinux Issues:**

```bash
# If using SELinux, you may need to set contexts
sudo setsebool -P container_manage_cgroup true
```

## Debug Information Collection

When reporting issues, collect this information:

### System Information

```bash
# System details
uname -a
docker --version
docker-compose --version
node --version

# Docker system info
docker system info
docker system df
```

### Container Information

```bash
# Container status
docker-compose ps

# Container logs (last 100 lines)
docker-compose logs --tail=100

# Container resource usage
docker stats --no-stream
```

### Moodle Configuration

```bash
# Database configuration
docker exec moodle-mariadb-1 mysql -u moodle -pmoodle_password -e "
SELECT name, value FROM moodle.mdl_config
WHERE name LIKE '%webservice%' OR name = 'enablewebservices';"

# External services
docker exec moodle-mariadb-1 mysql -u moodle -pmoodle_password -e "
SELECT name, shortname, enabled FROM moodle.mdl_external_services;"

# Tokens
docker exec moodle-mariadb-1 mysql -u moodle -pmoodle_password -e "
SELECT LEFT(token, 8) as token_preview, userid, externalserviceid
FROM moodle.mdl_external_tokens;"
```

### Network Connectivity

```bash
# Test internal connectivity
docker exec moodle-moodle-1 ping mariadb
docker exec moodle-moodle-1 curl -I http://localhost

# Test external connectivity
curl -I http://localhost:8080
curl "http://localhost:8080/webservice/rest/server.php?wstoken=18745a8b8f98df61fe052159d3f91542&wsfunction=core_webservice_get_site_info&moodlewsrestformat=json"
```

## Recovery Procedures

### Complete Reset

If all else fails, perform a complete reset:

```bash
# Stop and remove everything
cd apps/api/docker/moodle
docker-compose down -v

# Remove all related Docker resources
docker system prune -af

# Fresh installation
./setup-moodle.sh --fresh-install
```

### Partial Reset (Keep Database)

To reset containers but keep database data:

```bash
# Stop containers
docker-compose down

# Remove only container images (keeps volumes)
docker-compose build --no-cache

# Restart
docker-compose up -d
```

### Database Reset Only

To reset only the database:

```bash
# Stop containers
docker-compose down

# Remove only database volume
docker volume rm moodle_mariadb_data

# Restart and reinitialize
docker-compose up -d
./setup-moodle.sh
```

## Prevention

### Regular Maintenance

```bash
# Weekly cleanup
docker system prune -f

# Check for updates
docker-compose pull

# Verify installation
./setup-moodle.sh --verify-only
```

### Monitoring

```bash
# Check container health
docker-compose ps

# Monitor logs
docker-compose logs -f

# Check resource usage
docker stats
```

### Backup

```bash
# Backup database
docker exec moodle-mariadb-1 mysqldump -u moodle -pmoodle_password moodle > moodle_backup.sql

# Backup moodledata (if using persistent volumes)
docker cp moodle-moodle-1:/var/moodledata ./moodledata_backup
```

## Getting Additional Help

1. **Use Interactive Tools:**

   ```bash
   ./moodle-manager.sh  # Interactive troubleshooting
   ```

2. **Run Diagnostics:**

   ```bash
   cd apps/api
   node docker/moodle/scripts/debug-moodle-connection.js
   ```

3. **Check Documentation:**

   - `SETUP_GUIDE.md` - Comprehensive setup guide
   - `LOCAL_DEVELOPMENT.md` - Development environment details
   - `AWS_DEPLOYMENT.md` - Production deployment

4. **Fresh Start:**
   ```bash
   ./setup-moodle.sh --fresh-install
   ```

---

**Remember:** Most issues can be resolved with the interactive troubleshooting menu: `./moodle-manager.sh`
