# Moodle Setup and Integration Guide

This comprehensive guide covers the complete setup, configuration, and integration of Moodle with the EMA API system.

## Table of Contents

1. [Environment Variables](#environment-variables)
2. [Automated Setup (Recommended)](#automated-setup-recommended)
3. [Manual Setup Process](#manual-setup-process)
4. [Web Services Configuration](#web-services-configuration)
5. [User Synchronization](#user-synchronization)
6. [Testing and Verification](#testing-and-verification)
7. [Troubleshooting](#troubleshooting)

## Environment Variables

The API requires two primary environment variables for Moodle integration, configured in `apps/api/.env`:

```properties
# Development (Local Docker)
MOODLE_URL_BASE="http://localhost:8080"
MOODLE_TOKEN="18745a8b8f98df61fe052159d3f91542"

# Production (AWS Deployment)
# MOODLE_URL_BASE="https://moodle.ema-portal.org"
# MOODLE_TOKEN="production-token-here"
```

## Automated Setup (Recommended)

### Quick Start

The simplest way to get Mood<PERSON> running with full EMA integration:

```bash
cd apps/api/docker/moodle
./setup-moodle.sh
```

This single command will:
1. ✅ Check all prerequisites (Docker, docker-compose, Node.js)
2. 🐳 Build and start Docker containers
3. 🗄️ Initialize the Moodle database
4. 🔧 Configure web services with all necessary fixes
5. 🔗 Set up webhook integration
6. ✅ Verify the installation
7. 📋 Display access information

### Setup Options

```bash
./setup-moodle.sh                 # Normal setup
./setup-moodle.sh --fresh-install # Clean installation (removes all existing data)
./setup-moodle.sh --verify-only   # Only verify existing installation
./setup-moodle.sh --help          # Show detailed help
```

### Interactive Management

For ongoing management and troubleshooting:

```bash
./moodle-manager.sh
```

This provides a user-friendly menu with options for:
- Setup and installation
- Verification and testing
- Troubleshooting web services
- Creating test users
- Debugging connection issues
- Managing containers

## Manual Setup Process

If you need to set up manually or understand the individual steps:

### Step 1: Start Docker Services

```bash
cd apps/api/docker/moodle

# Clean start (recommended for first time)
docker-compose down -v
docker-compose build --no-cache
docker-compose up -d
```

### Step 2: Wait for Services

```bash
# Wait for database to be ready (usually 15-30 seconds)
docker exec moodle-mariadb-1 mysql -u moodle -pmoodle_password -e "SELECT 1;" 

# Wait for Moodle web server to be ready
curl http://localhost:8080
```

### Step 3: Initialize Database

```bash
# Initialize Moodle database
docker exec moodle-moodle-1 php admin/cli/install_database.php \
  --agree-license \
  --fullname="EMA Moodle" \
  --shortname="EMA" \
  --adminuser=admin \
  --adminpass=admin \
  --adminemail=<EMAIL>
```

### Step 4: Configure Web Services

```bash
# Apply SQL configuration
docker cp ./sql/setup-webservices.sql moodle-mariadb-1:/tmp/
docker exec moodle-mariadb-1 bash -c "mysql -u moodle -pmoodle_password moodle < /tmp/setup-webservices.sql"

# Update config.php
docker exec moodle-moodle-1 php /var/www/html/update-config.php

# Enable web services
docker exec moodle-moodle-1 php /var/www/html/enable-webservices.php
```

## Web Services Configuration

The automated setup configures web services with these components:

### Database Configuration

The SQL script (`sql/setup-webservices.sql`) configures:

```sql
-- Enable web services
INSERT INTO mdl_config (name, value) VALUES ('enablewebservices', '1') ON DUPLICATE KEY UPDATE value='1';
INSERT INTO mdl_config (name, value) VALUES ('webserviceprotocols', 'rest') ON DUPLICATE KEY UPDATE value='rest';

-- Create EMA API Service
INSERT INTO mdl_external_services (name, enabled, requiredcapability, restrictedusers, shortname, timecreated, timemodified) 
VALUES ('EMA API Service', 1, '', 1, 'ema_api', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- Add required functions
INSERT INTO mdl_external_services_functions (externalserviceid, functionname) VALUES (@service_id, 'core_user_create_users');
INSERT INTO mdl_external_services_functions (externalserviceid, functionname) VALUES (@service_id, 'core_user_update_users');
INSERT INTO mdl_external_services_functions (externalserviceid, functionname) VALUES (@service_id, 'core_user_delete_users');
INSERT INTO mdl_external_services_functions (externalserviceid, functionname) VALUES (@service_id, 'core_user_get_users_by_field');
INSERT INTO mdl_external_services_functions (externalserviceid, functionname) VALUES (@service_id, 'core_webservice_get_site_info');

-- Create API token
INSERT INTO mdl_external_tokens (token, privatetoken, tokentype, userid, externalserviceid, sid, contextid, creatorid, timecreated, validuntil)
VALUES ('18745a8b8f98df61fe052159d3f91542', '', 0, @admin_id, @service_id, NULL, 1, @admin_id, UNIX_TIMESTAMP(), (UNIX_TIMESTAMP() + 31536000));
```

### Config.php Updates

The `update-config.php` script adds these settings:

```php
$CFG->enablewebservices = 1;
$CFG->webserviceprotocols = 'rest';
```

### Manual Web Services Setup

If you need to manually configure web services through the Moodle UI:

#### 1. Enable Web Services
1. Log in as admin at http://localhost:8080 (admin/admin)
2. Go to Site administration → General → Advanced features
3. Check "Enable web services"
4. Save changes

#### 2. Enable REST Protocol
1. Go to Site administration → Plugins → Web services → Manage protocols
2. Enable "REST protocol"

#### 3. Create External Service
1. Go to Site administration → Plugins → Web services → External services
2. Click "Add"
3. Configure:
   - Name: "EMA API Service"
   - Short name: "ema_api"
   - Enabled: ✅
   - Authorized users only: ✅

#### 4. Add Functions to Service
1. Click "Functions" for the EMA API Service
2. Add these functions:
   - `core_user_create_users`
   - `core_user_update_users`
   - `core_user_delete_users`
   - `core_user_get_users_by_field`
   - `core_webservice_get_site_info`

#### 5. Create API Token
1. Go to Site administration → Plugins → Web services → Manage tokens
2. Click "Create token"
3. Select admin user and EMA API Service
4. Copy the generated token to your `.env` file

## User Synchronization

The integration provides automatic bidirectional user synchronization:

### EMA → Moodle
- **Create**: New users in EMA are automatically created in Moodle
- **Update**: User profile changes in EMA are synchronized to Moodle
- **Delete**: User deletions in EMA are synchronized to Moodle

### Moodle → EMA
- **Events**: Moodle sends webhook events to EMA API for:
  - User enrollment in courses
  - Course completion
  - Grade updates
  - Login events

### Synchronization Process

1. **Identifier**: Email address is used as the unique identifier
2. **Conflict Resolution**: EMA data takes precedence in conflicts
3. **Error Handling**: Failed synchronizations are logged and retried
4. **Validation**: All user data is validated before synchronization

## Testing and Verification

### Automated Verification

The setup script includes comprehensive verification:

```bash
./setup-moodle.sh --verify-only
```

This checks:
- ✅ Container health and status
- ✅ Database connectivity and initialization
- ✅ Web services configuration
- ✅ API token functionality
- ✅ Webhook integration

### Manual Testing Scripts

Run these from the API root directory (`apps/api`):

```bash
# Basic connectivity test
node docker/moodle/scripts/verify-moodle-token.js

# Create test users and verify sync
node docker/moodle/scripts/create-test-users.js

# Comprehensive diagnostics
node docker/moodle/scripts/debug-moodle-connection.js
```

### API Endpoint Testing

```bash
# Test web services endpoint
curl "http://localhost:8080/webservice/rest/server.php?wstoken=18745a8b8f98df61fe052159d3f91542&wsfunction=core_webservice_get_site_info&moodlewsrestformat=json"

# Expected response: JSON with site information
```

### Database Verification

```bash
# Check web services configuration
docker exec moodle-mariadb-1 mysql -u moodle -pmoodle_password -e "
SELECT name, value FROM moodle.mdl_config 
WHERE name IN ('enablewebservices', 'webserviceprotocols');"

# Check external services
docker exec moodle-mariadb-1 mysql -u moodle -pmoodle_password -e "
SELECT name, shortname, enabled FROM moodle.mdl_external_services 
WHERE shortname = 'ema_api';"

# Check API tokens
docker exec moodle-mariadb-1 mysql -u moodle -pmoodle_password -e "
SELECT token, LEFT(token, 8) as token_preview, userid, externalserviceid 
FROM moodle.mdl_external_tokens 
WHERE token = '18745a8b8f98df61fe052159d3f91542';"
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Web Services 403 Forbidden

**Symptoms**: API calls return 403 Forbidden error

**Solutions**:
```bash
# Run web services fix
docker exec moodle-moodle-1 php /var/www/html/update-config.php
docker exec moodle-moodle-1 php /var/www/html/enable-webservices.php

# Or use the manager
./moodle-manager.sh  # Choose option 4: Troubleshoot Web Services
```

#### 2. Database Connection Issues

**Symptoms**: Moodle shows database connection errors

**Solutions**:
```bash
# Check database status
docker-compose ps mariadb

# Restart database
docker-compose restart mariadb

# Check database logs
docker-compose logs mariadb
```

#### 3. Container Startup Issues

**Symptoms**: Containers fail to start or are unhealthy

**Solutions**:
```bash
# Check container status
docker-compose ps

# View container logs
docker-compose logs moodle
docker-compose logs mariadb

# Fresh restart
docker-compose down -v
./setup-moodle.sh --fresh-install
```

#### 4. Token Validation Failures

**Symptoms**: API token is rejected

**Solutions**:
```bash
# Verify token in database
docker exec moodle-mariadb-1 mysql -u moodle -pmoodle_password -e "
SELECT token FROM moodle.mdl_external_tokens 
WHERE token = '18745a8b8f98df61fe052159d3f91542';"

# Regenerate token if needed (manual process through Moodle UI)
```

#### 5. User Synchronization Failures

**Symptoms**: Users not syncing between EMA and Moodle

**Solutions**:
```bash
# Test user creation
node docker/moodle/scripts/create-test-users.js

# Check API connectivity
node docker/moodle/scripts/verify-moodle-token.js

# Full diagnostics
node docker/moodle/scripts/debug-moodle-connection.js
```

### Debug Information Collection

If you need to report an issue, collect this information:

```bash
# System information
docker --version
docker-compose --version
node --version

# Container status
docker-compose ps

# Recent logs (last 50 lines)
docker-compose logs --tail=50

# Web services configuration
docker exec moodle-mariadb-1 mysql -u moodle -pmoodle_password -e "
SELECT name, value FROM moodle.mdl_config 
WHERE name LIKE '%webservice%' OR name = 'enablewebservices';"

# Test API connectivity
curl -s "http://localhost:8080/webservice/rest/server.php?wstoken=18745a8b8f98df61fe052159d3f91542&wsfunction=core_webservice_get_site_info&moodlewsrestformat=json" | jq .
```

### Getting Help

1. **Interactive Troubleshooting**: Use `./moodle-manager.sh` option 4
2. **Fresh Start**: Run `./setup-moodle.sh --fresh-install`  
3. **Verification**: Run `./setup-moodle.sh --verify-only`
4. **Detailed Diagnostics**: Run the debug script from API root:
   ```bash
   cd apps/api
   node docker/moodle/scripts/debug-moodle-connection.js
   ```

## Advanced Configuration

### Custom Token Generation

To generate a new API token:

1. Access Moodle admin interface
2. Go to Site administration → Plugins → Web services → Manage tokens
3. Create new token for EMA API Service
4. Update `MOODLE_TOKEN` in `apps/api/.env`

### Webhook Configuration

The webhook plugin is automatically configured to send events to:
```
POST http://host.docker.internal:3001/webhooks/moodle
```

### Performance Tuning

For production environments:
- Use `Dockerfile.production` for optimized builds
- Configure Redis for session storage
- Enable OPcache for PHP
- Use CDN for static assets

## Next Steps

1. **Verify Setup**: Run `./setup-moodle.sh --verify-only`
2. **Create Test Users**: Run the test user creation script
3. **Test Integration**: Verify user synchronization works
4. **Production Deployment**: See `AWS_DEPLOYMENT.md` for production setup

---

For additional help, use the interactive management script: `./moodle-manager.sh`
