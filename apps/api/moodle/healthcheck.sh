#!/bin/bash

# Health check script for Moodle container
# Used by Docker HEALTHCHECK and AWS ECS/ELB health checks

# Check if Apache is running
if ! pgrep apache2 > /dev/null; then
    echo "Apache is not running"
    exit 1
fi

# Check if <PERSON><PERSON><PERSON> responds to HTTP requests
if ! curl -f -s http://localhost/login/index.php > /dev/null; then
    echo "<PERSON><PERSON><PERSON> is not responding to HTTP requests"
    exit 1
fi

# Check database connectivity (if config exists)
if [ -f /var/www/html/config.php ]; then
    # Try to connect to database using Moodle CLI
    if ! php /var/www/html/admin/cli/check_database_schema.php --help > /dev/null 2>&1; then
        echo "Database connection issues"
        exit 1
    fi
fi

echo "Health check passed"
exit 0
