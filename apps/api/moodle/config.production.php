<?php  // Moodle production configuration template for AWS deployment

unset($CFG);
global $CFG;
$CFG = new stdClass();

// Database configuration - Use AWS RDS
$CFG->dbtype    = 'mariadb'; // or 'mysqli' for MySQL
$CFG->dblibrary = 'native';
$CFG->dbhost    = getenv('MOODLE_DB_HOST') ?: 'localhost';
$CFG->dbname    = getenv('MOODLE_DB_NAME') ?: 'moodle';
$CFG->dbuser    = getenv('MOODLE_DB_USER') ?: 'moodle';
$CFG->dbpass    = getenv('MOODLE_DB_PASS') ?: '';
$CFG->prefix    = getenv('MOODLE_DB_PREFIX') ?: 'mdl_';
$CFG->dboptions = array (
    'dbpersist' => 0,
    'dbport' => getenv('MOODLE_DB_PORT') ?: 3306,
    'dbsocket' => '',
    'dbcollation' => 'utf8mb4_unicode_ci',
);

// Site configuration
$CFG->wwwroot   = getenv('MOODLE_WWW_ROOT') ?: 'https://moodle.ema-portal.org';
$CFG->dataroot  = getenv('MOODLE_DATA_ROOT') ?: '/var/moodledata';
$CFG->admin     = 'admin';
$CFG->directorypermissions = 0777;

// Production settings - Disable debugging
$CFG->debug = 0;
$CFG->debugdisplay = 0;
$CFG->debugdeveloper = false;
$CFG->perfdebug = 0;
$CFG->debugpageinfo = false;
$CFG->allowthemechangeonurl = false;
$CFG->passwordpolicy = true;

// Enable web services for API integration
$CFG->enablewebservices = true;
$CFG->enablemobilewebservice = true;

// Performance settings for production
$CFG->cachejs = true;
$CFG->cachetemplates = true;
$CFG->langstringcache = true;

// Email settings - Use AWS SES
$CFG->smtphosts = getenv('MOODLE_SMTP_HOST') ?: '';
$CFG->smtpuser = getenv('MOODLE_SMTP_USER') ?: '';
$CFG->smtppass = getenv('MOODLE_SMTP_PASS') ?: '';
$CFG->smtpsecure = getenv('MOODLE_SMTP_SECURE') ?: 'tls';
$CFG->smtpauth = (bool)getenv('MOODLE_SMTP_AUTH') ?: true;
$CFG->smtpmaxbulk = (int)getenv('MOODLE_SMTP_MAX_BULK') ?: 50;
$CFG->noemailever = false;

// Session configuration for load balancing
$CFG->session_handler_class = '\core\session\database';
$CFG->session_database_acquire_lock_timeout = 120;

// Configure Redis for caching (optional)
if (getenv('MOODLE_REDIS_HOST')) {
    $CFG->session_handler_class = '\core\session\redis';
    $CFG->session_redis_host = getenv('MOODLE_REDIS_HOST');
    $CFG->session_redis_port = getenv('MOODLE_REDIS_PORT') ?: 6379;
    $CFG->session_redis_auth = getenv('MOODLE_REDIS_AUTH') ?: '';
    $CFG->session_redis_database = getenv('MOODLE_REDIS_DB') ?: 0;
    $CFG->session_redis_acquire_lock_timeout = 120;
    $CFG->session_redis_lock_expire = 7200;
}

// File storage configuration for AWS S3 (requires S3 plugin)
if (getenv('MOODLE_S3_BUCKET')) {
    $CFG->alternative_file_system_class = '\tool_objectfs\s3_file_system';
}

// Security settings
$CFG->cookiesecure = true; // Force HTTPS cookies
$CFG->cookiehttponly = true;
$CFG->loginhttps = true;

// Configure webhook endpoint for EMA integration
$CFG->webhooks_endpoint = getenv('MOODLE_WEBHOOK_ENDPOINT') ?: 'https://api.ema-portal.org/moodle/webhook';

// AWS-specific configurations
$CFG->forced_plugin_settings = [
    'tool_objectfs' => [
        'enabletasks' => true,
        'filesystem' => '\tool_objectfs\s3_file_system',
        's3_bucket' => getenv('MOODLE_S3_BUCKET') ?: '',
        's3_region' => getenv('MOODLE_S3_REGION') ?: 'us-east-1',
        's3_key' => getenv('MOODLE_S3_KEY') ?: '',
        's3_secret' => getenv('MOODLE_S3_SECRET') ?: '',
    ]
];

// Load balancer configuration
$CFG->reverseproxy = (bool)getenv('MOODLE_REVERSE_PROXY') ?: false;
if ($CFG->reverseproxy) {
    $CFG->sslproxy = true;
}

require_once(__DIR__ . '/lib/setup.php');

// There is no php closing tag in this file,
// it is intentional because it prevents trailing whitespace problems!
