#!/bin/bash

# ======================================================================
# EMA Moodle Setup Script
# ======================================================================
# This script provides complete installation, configuration, and setup
# of Moodle with web services integration for the EMA system.
#
# Usage: ./setup-moodle.sh [OPTIONS]
# Options:
#   --fresh-install    Clean install (removes existing containers/data)
#   --verify-only      Only verify the installation, don't install
#   --help            Show this help message
# ======================================================================

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
FRESH_INSTALL=false
VERIFY_ONLY=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --fresh-install)
      FRESH_INSTALL=true
      shift
      ;;
    --verify-only)
      VERIFY_ONLY=true
      shift
      ;;
    --help)
      echo "EMA Moodle Setup Script"
      echo ""
      echo "Usage: $0 [OPTIONS]"
      echo ""
      echo "Options:"
      echo "  --fresh-install    Clean install (removes existing containers/data)"
      echo "  --verify-only      Only verify the installation, don't install"
      echo "  --help            Show this help message"
      echo ""
      echo "This script will:"
      echo "  1. Check prerequisites"
      echo "  2. Build and start Moodle containers"
      echo "  3. Initialize the database"
      echo "  4. Configure web services with our fixes"
      echo "  5. Verify the installation"
      echo "  6. Show access information"
      exit 0
      ;;
    *)
      echo "Unknown option $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Function to print section headers
print_header() {
  echo ""
  echo -e "${BLUE}======================================${NC}"
  echo -e "${BLUE}  $1${NC}"
  echo -e "${BLUE}======================================${NC}"
}

# Function to print step info
print_step() {
  echo -e "${YELLOW}➤ $1${NC}"
}

# Function to print success messages
print_success() {
  echo -e "${GREEN}✅ $1${NC}"
}

# Function to print error messages
print_error() {
  echo -e "${RED}❌ $1${NC}"
}

# Function to print info messages
print_info() {
  echo -e "${CYAN}ℹ️  $1${NC}"
}

# Function to check prerequisites
check_prerequisites() {
  print_header "Checking Prerequisites"
  
  # Check if Docker is installed and running
  if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker and try again."
    exit 1
  fi
  
  if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
  fi
  print_success "Docker is running"
  
  # Check if docker-compose is available
  if ! command -v docker-compose &> /dev/null; then
    print_error "docker-compose is not installed. Please install docker-compose and try again."
    exit 1
  fi
  print_success "docker-compose is available"
  
  # Check if we're in the right directory
  if [ ! -f "docker-compose.yml" ]; then
    print_error "docker-compose.yml not found. Please run this script from the moodle directory."
    exit 1
  fi
  print_success "Found docker-compose.yml"
  
  # Check if Node.js is available for running verification scripts
  if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Some verification features may not work."
  else
    print_success "Node.js is available"
  fi
}

# Function to setup webhooks plugin
setup_webhooks_plugin() {
  print_step "Setting up webhooks plugin..."
  
  if [ ! -d "webhooks-plugin/classes" ] || [ ! -d "webhooks-plugin/db" ]; then
    print_step "First time setup detected. Running webhook plugin setup..."
    if [ -f "./setup.sh" ]; then
      bash ./setup.sh
      print_success "Webhook plugin setup complete"
    else
      print_error "setup.sh not found. Webhook plugin may not work correctly."
    fi
  else
    print_success "Webhook plugin already configured"
  fi
}

# Function to start Docker services
start_docker_services() {
  print_header "Starting Docker Services"
  
  if [ "$FRESH_INSTALL" = true ]; then
    print_step "Performing fresh install - removing existing containers and volumes..."
    docker-compose down -v
    docker system prune -f
  else
    print_step "Stopping existing containers..."
    docker-compose down
  fi
  
  print_step "Building Moodle container..."
  docker-compose build --no-cache moodle
  
  print_step "Starting all services..."
  docker-compose up -d
  
  print_success "Docker services started"
  print_info "Services starting in background..."
}

# Function to wait for services to be ready
wait_for_services() {
  print_header "Waiting for Services"
  
  print_step "Waiting for database to be ready..."
  sleep 15
  
  # Wait for database to be accessible
  local max_attempts=30
  local attempt=1
  while [ $attempt -le $max_attempts ]; do
    if docker exec moodle-mariadb-1 mysql -u moodle -pmoodle_password -e "SELECT 1;" > /dev/null 2>&1; then
      break
    fi
    echo -ne "."
    sleep 2
    attempt=$((attempt + 1))
  done
  
  if [ $attempt -gt $max_attempts ]; then
    print_error "Database failed to start within expected time"
    exit 1
  fi
  print_success "Database is ready"
  
  print_step "Waiting for Moodle web server..."
  local moodle_ready=false
  attempt=1
  while [ $attempt -le 60 ]; do
    if curl --output /dev/null --silent --fail -m 5 http://localhost:8080 2>/dev/null; then
      moodle_ready=true
      break
    fi
    echo -ne "."
    sleep 5
    attempt=$((attempt + 1))
  done
  
  if [ "$moodle_ready" = false ]; then
    print_error "Moodle web server failed to start"
    exit 1
  fi
  print_success "Moodle web server is ready"
}

# Function to initialize database
initialize_database() {
  print_header "Database Initialization"
  
  # Check if Moodle database is already initialized
  print_step "Checking database state..."
  DB_TABLES=$(docker exec moodle-mariadb-1 mysql -u moodle -pmoodle_password -e "SHOW TABLES IN moodle;" 2>/dev/null | wc -l)
  
  if [ "$DB_TABLES" -lt "2" ]; then
    print_step "Installing Moodle database..."
    docker exec moodle-moodle-1 php admin/cli/install_database.php \
      --agree-license \
      --fullname="EMA Moodle" \
      --shortname="EMA" \
      --adminuser=admin \
      --adminpass=admin \
      --adminemail=<EMAIL>
    
    print_success "Moodle database installation complete"
  else
    print_success "Moodle database already initialized"
  fi
}

# Function to configure web services
configure_web_services() {
  print_header "Configuring Web Services"
  
  print_step "Setting up initial web services configuration..."
  # Copy and execute SQL script
  docker cp ./sql/setup-webservices.sql moodle-mariadb-1:/tmp/
  docker exec moodle-mariadb-1 bash -c "mysql -u moodle -pmoodle_password moodle < /tmp/setup-webservices.sql"
  print_success "SQL configuration applied"
  
  print_step "Updating config.php with web services settings..."
  docker exec moodle-moodle-1 php /var/www/html/update-config.php
  print_success "Config.php updated"
  
  print_step "Enabling and configuring web services..."
  docker exec moodle-moodle-1 php /var/www/html/enable-webservices.php
  print_success "Web services configuration complete"
}

# Function to verify installation
verify_installation() {
  print_header "Verifying Installation"
  
  # Test basic connectivity
  print_step "Testing basic Moodle connectivity..."
  if curl --output /dev/null --silent --fail -m 10 http://localhost:8080; then
    print_success "Moodle web interface is accessible"
  else
    print_error "Moodle web interface is not accessible"
    return 1
  fi
  
  # Test web services if Node.js is available
  if command -v node &> /dev/null; then
    print_step "Testing web services API..."
    cd "$SCRIPT_DIR"
    if [ -f "scripts/verify-moodle-token.js" ]; then
      # Change to the API directory to run the script with correct context
      cd ../../
      if node docker/moodle/scripts/verify-moodle-token.js > /dev/null 2>&1; then
        print_success "Web services API is working"
      else
        print_error "Web services API test failed"
        print_info "You can manually verify later with: node docker/moodle/scripts/verify-moodle-token.js"
      fi
      cd "$SCRIPT_DIR"
    else
      print_info "Verification script not found, skipping API test"
    fi
  else
    print_info "Node.js not available, skipping web services verification"
  fi
  
  # Check if all containers are running
  print_step "Checking container status..."
  local running_containers=$(docker-compose ps --services --filter "status=running" | wc -l)
  local total_containers=$(docker-compose ps --services | wc -l)
  
  if [ "$running_containers" -eq "$total_containers" ]; then
    print_success "All containers are running ($running_containers/$total_containers)"
  else
    print_error "Some containers are not running ($running_containers/$total_containers)"
    docker-compose ps
  fi
}

# Function to show access information
show_access_info() {
  print_header "Access Information"
  
  echo -e "${GREEN}🎉 Moodle setup complete!${NC}"
  echo ""
  echo -e "${BLUE}Access Points:${NC}"
  echo -e "  ${GREEN}Moodle:${NC}     http://localhost:8080"
  echo -e "  ${GREEN}Mail:${NC}       http://localhost:8025 (MailHog)"
  echo -e "  ${GREEN}Database:${NC}   localhost:3306 (User: moodle, Password: moodle_password)"
  echo ""
  echo -e "${BLUE}Default Credentials:${NC}"
  echo -e "  ${GREEN}Username:${NC}   admin"
  echo -e "  ${GREEN}Password:${NC}   admin"
  echo ""
  echo -e "${BLUE}Available Scripts:${NC}"
  echo -e "  ${GREEN}Test Users:${NC}      ./scripts/create-test-users.js"
  echo -e "  ${GREEN}Verify Token:${NC}    ./scripts/verify-moodle-token.js"
  echo -e "  ${GREEN}Debug Issues:${NC}    ./scripts/debug-moodle-connection.js"
  echo ""
  echo -e "${BLUE}Documentation:${NC}"
  echo -e "  ${GREEN}Integration:${NC}     ./docs/MOODLE_INTEGRATION.md"
  echo -e "  ${GREEN}AWS Deploy:${NC}      ./docs/AWS_DEPLOYMENT.md"
  echo -e "  ${GREEN}Local Dev:${NC}       ./docs/LOCAL_DEVELOPMENT.md"
  echo ""
  echo -e "${BLUE}Management:${NC}"
  echo -e "  ${GREEN}Stop:${NC}            docker-compose down"
  echo -e "  ${GREEN}Restart:${NC}         ./setup-moodle.sh"
  echo -e "  ${GREEN}Fresh Install:${NC}   ./setup-moodle.sh --fresh-install"
  echo -e "  ${GREEN}Verify Only:${NC}     ./setup-moodle.sh --verify-only"
}

# Main execution flow
main() {
  print_header "EMA Moodle Setup"
  print_info "Starting Moodle installation and configuration..."
  
  # Change to script directory
  cd "$SCRIPT_DIR"
  
  check_prerequisites
  
  if [ "$VERIFY_ONLY" = true ]; then
    verify_installation
    show_access_info
    exit 0
  fi
  
  setup_webhooks_plugin
  start_docker_services
  wait_for_services
  initialize_database
  configure_web_services
  verify_installation
  show_access_info
}

# Run main function
main "$@"
