FROM moodlehq/moodle-php-apache:8.1

LABEL maintainer="EMA Development Team"

# Install required packages
USER root
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libzip-dev \
    zip \
    unzip \
    mariadb-client \
    && docker-php-ext-install zip \
    && rm -rf /var/lib/apt/lists/* \
    && mkdir -p /usr/local/etc/php/conf.d \
    && chmod -R 777 /usr/local/etc/php/conf.d

# Download and setup Moodle 5.1
WORKDIR /var/www/html
RUN git clone --depth=1 --branch=MOODLE_404_STABLE https://github.com/moodle/moodle.git . \
    && chown -R www-data:www-data /var/www/html

# Create moodledata directory
RUN mkdir -p /var/moodledata \
    && chown -R www-data:www-data /var/moodledata \
    && chmod 777 /var/moodledata

# Set up the webhook plugin
COPY --chown=www-data:www-data ./webhooks-plugin /var/www/html/local/webhooks

# Enable mod_rewrite
RUN a2enmod rewrite

# Copy custom config template and scripts
COPY ./config.php /var/www/html/config.php
COPY ./enable-webservices.php /var/www/html/enable-webservices.php
COPY ./update-config.php /var/www/html/update-config.php
RUN chown www-data:www-data /var/www/html/config.php \
    /var/www/html/enable-webservices.php \
    /var/www/html/update-config.php

USER www-data

EXPOSE 80
