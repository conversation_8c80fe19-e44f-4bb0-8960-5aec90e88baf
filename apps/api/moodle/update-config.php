<?php
// <PERSON>ript to update Moodle config.php to ensure web services configuration
// To be run inside the Moodle container

define('CLI_SCRIPT', true);
require_once('/var/www/html/config.php');

// Check if webserviceprotocols is defined in config
if (!isset($CFG->webserviceprotocols) || empty($CFG->webserviceprotocols)) {
    echo "Adding webserviceprotocols setting to config.php...\n";
    
    $config_file = '/var/www/html/config.php';
    $config_content = file_get_contents($config_file);
    
    // Look for the enablewebservices setting
    if (strpos($config_content, '$CFG->enablewebservices') !== false) {
        // Add webserviceprotocols right after enablewebservices
        $new_content = preg_replace(
            '/(\$CFG->enablewebservices\s*=\s*.*?;)/', 
            "$1\n\$CFG->webserviceprotocols = 'rest';", 
            $config_content
        );
        
        if ($new_content != $config_content) {
            // Backup the original file
            file_put_contents($config_file.'.bak', $config_content);
            echo "Backup of original config created as config.php.bak\n";
            
            // Update the file
            if (file_put_contents($config_file, $new_content)) {
                echo "Configuration updated successfully!\n";
            } else {
                echo "Error writing to config.php file. Check permissions.\n";
                exit(1);
            }
        } else {
            echo "Could not properly modify config.php - pattern match failed.\n";
            exit(1);
        }
    } else {
        echo "Could not find enablewebservices in config.php. Please add the following line manually:\n";
        echo "\$CFG->webserviceprotocols = 'rest';\n";
        exit(1);
    }
} else {
    echo "webserviceprotocols is already set in config.php: " . $CFG->webserviceprotocols . "\n";
    
    // If set to something other than 'rest', recommend updating
    if ($CFG->webserviceprotocols !== 'rest') {
        echo "WARNING: webserviceprotocols is set to '" . $CFG->webserviceprotocols . "'\n";
        echo "Consider updating it to just 'rest' for reliability.\n";
    }
}

echo "Configuration check complete!\n";
