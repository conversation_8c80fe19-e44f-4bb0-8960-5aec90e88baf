/**
 * <PERSON><PERSON><PERSON> to debug Moodle API connection issues
 * Run with: node docker/moodle/scripts/debug-moodle-connection.js (from API root)
 */

const axios = require('axios');
const path = require('path');

// Load environment variables from the API root directory
require('dotenv').config({ path: path.join(__dirname, '../../../.env') });

/**
 * Debug Moodle API connectivity
 */
async function debugMoodleAPI() {
  console.log('🔍 Debugging Moodle API connection...');

  const moodleBaseUrl = process.env.MOODLE_URL_BASE;
  const moodleToken = process.env.MOODLE_TOKEN;

  console.log(`\nMoodle Configuration:`);
  console.log(`- URL: ${moodleBaseUrl}`);
  console.log(
    `- Token: ${moodleToken ? moodleToken.substring(0, 5) + '***' + moodleToken.substring(moodleToken.length - 3) : 'Not set'}`,
  );

  // Test 1: Check if Moodle site is reachable
  console.log('\nTest 1: Checking if Moodle site is reachable');
  try {
    const siteResponse = await axios.get(`${moodleBaseUrl}/login/index.php`, {
      timeout: 5000,
      validateStatus: () => true,
    });
    console.log(`✅ Moodle site is reachable. Status: ${siteResponse.status}`);
  } catch (error) {
    console.error(`❌ Cannot reach Moodle site: ${error.message}`);
    console.error('  This suggests the Moodle container might not be running.');
    return;
  }

  // Test 2: Check if web services are enabled by attempting a simple API call
  console.log('\nTest 2: Testing basic API functionality - Get site info');
  try {
    const response = await axios.get(
      `${moodleBaseUrl}/webservice/rest/server.php`,
      {
        params: {
          wstoken: moodleToken,
          wsfunction: 'core_webservice_get_site_info',
          moodlewsrestformat: 'json',
        },
        timeout: 5000,
        validateStatus: () => true,
      },
    );

    if (response.status !== 200) {
      console.error(
        `❌ API request failed with status code: ${response.status}`,
      );
      console.error('  Response:', response.data);
    } else if (response.data && response.data.errorcode) {
      console.error(
        `❌ API request returned error: ${response.data.errorcode}`,
      );
      console.error('  Error message:', response.data.message);

      if (response.data.errorcode === 'accessexception') {
        console.error('\n❌ Access denied. Possible issues:');
        console.error('  1. Web services not enabled in Moodle');
        console.error('  2. REST protocol not enabled');
        console.error('  3. Token is invalid or has expired');
        console.error('  4. Token user does not have the right permissions');
        console.error('  5. The external service is not properly configured');
      }
    } else {
      console.log(`✅ Successfully connected to Moodle API`);
      console.log('  Site info:', response.data);
    }
  } catch (error) {
    console.error(`❌ Error connecting to Moodle API: ${error.message}`);
    if (error.response) {
      console.error('  Response status:', error.response.status);
      console.error('  Response data:', error.response.data);
    }
  }

  // Test 3: Check if user functions are available
  console.log('\nTest 3: Testing user API functions');
  try {
    const response = await axios.get(
      `${moodleBaseUrl}/webservice/rest/server.php`,
      {
        params: {
          wstoken: moodleToken,
          wsfunction: 'core_user_get_users_by_field',
          moodlewsrestformat: 'json',
          field: 'email',
          values: ['<EMAIL>'],
        },
        timeout: 5000,
        validateStatus: () => true,
      },
    );

    if (response.status !== 200) {
      console.error(
        `❌ User API request failed with status code: ${response.status}`,
      );
      console.error('  Response:', response.data);
    } else if (response.data && response.data.errorcode) {
      console.error(
        `❌ User API request returned error: ${response.data.errorcode}`,
      );
      console.error('  Error message:', response.data.message);

      if (response.data.errorcode === 'accessexception') {
        console.error(
          '\n❌ Access denied for user functions. Possible issues:',
        );
        console.error(
          '  1. The core_user_get_users_by_field function is not added to the external service',
        );
        console.error(
          '  2. The token user does not have permission to use this function',
        );
      }
    } else {
      console.log(`✅ Successfully accessed user API functions`);
      console.log('  User data:', JSON.stringify(response.data, null, 2));
    }
  } catch (error) {
    console.error(`❌ Error accessing user API: ${error.message}`);
    if (error.response) {
      console.error('  Response status:', error.response.status);
      console.error('  Response data:', error.response.data);
    }
  }

  // Print out detailed configuration steps
  console.log('\n📋 Configuration Checklist:');
  console.log('1. ⚙️  Enable web services in Moodle:');
  console.log(
    '   - Site administration > General > Advanced features > Enable web services',
  );

  console.log('\n2. 🔌 Enable REST protocol:');
  console.log(
    '   - Site administration > Plugins > Web services > Manage protocols > Enable REST',
  );

  console.log('\n3. 🔨 Create external service:');
  console.log(
    '   - Site administration > Plugins > Web services > External services',
  );
  console.log(
    '   - Add a new service named "EMA API Service" with shortname "ema_api"',
  );
  console.log('   - Check "Enabled" and "Authorized users only"');

  console.log('\n4. 📝 Add functions to service:');
  console.log('   - Click on "Functions" for your service');
  console.log('   - Add these functions:');
  console.log('     * core_user_create_users');
  console.log('     * core_user_update_users');
  console.log('     * core_user_delete_users');
  console.log('     * core_user_get_users_by_field');
  console.log('     * core_webservice_get_site_info');

  console.log('\n5. 👤 Set user permissions:');
  console.log('   - Site administration > Users > Permissions > Define roles');
  console.log('   - Add a new role "Moodle API Access"');
  console.log('   - Add capabilities:');
  console.log('     * webservice/rest:use');
  console.log('     * moodle/user:create');
  console.log('     * moodle/user:update');
  console.log('     * moodle/user:delete');
  console.log('     * moodle/user:viewdetails');
  console.log('   - Assign this role to the admin user');

  console.log('\n6. 🔑 Create token:');
  console.log(
    '   - Site administration > Plugins > Web services > Manage tokens',
  );
  console.log('   - Create new token for admin user and "EMA API Service"');

  console.log('\n7. 🔄 Update .env file:');
  console.log('   - Update MOODLE_TOKEN value in your .env file');

  console.log('\n8. ✅ Verify configuration:');
  console.log('   - Run node scripts/verify-moodle-token.js');
}

// Run the script
debugMoodleAPI().catch((error) => {
  console.error('Error:', error);
  process.exit(1);
});
