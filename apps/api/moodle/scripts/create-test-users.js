/**
 * <PERSON><PERSON><PERSON> to test Moodle user synchronization by creating, updating, and deleting test users
 * Run with: node docker/moodle/scripts/create-test-users.js (from API root)
 */

const { PrismaClient } = require('@prisma/client');
const axios = require('axios');
const { faker } = require('@faker-js/faker');
const path = require('path');

// Load environment variables from the API root directory
require('dotenv').config({ path: path.join(__dirname, '../../../.env') });

const prisma = new PrismaClient();

// Store created users for later operations
let createdUsers = [];

// Function to generate a random user
function generateRandomUser() {
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();
  const email = faker.internet.email({ firstName, lastName }).toLowerCase();

  return {
    username: email,
    email,
    firstName,
    lastName,
    passwordHash:
      '$2b$10$Jm9a6ykdcKaYTtB2w3ZXPeNzKVGR4EzPvQpKw4dV0gKnBsSSrSpta', // 'password123' hashed with bcrypt
    phone_mobile: faker.phone.number(),
    address_city: faker.location.city(),
    address_state: faker.location.state({ abbreviated: true }),
    address_postalcode: faker.location.zipCode(),
    phone: faker.phone.number(),
    status: 'Active', // Using the correct enum value from UserStatus
    created_at: new Date(),
    updated_at: new Date(),
    deleted_at: 0, // The schema requires a numeric value for deleted_at
    hasMoodleAccount: false, // Default value for new users
  };
}

// Check if Moodle is running
async function checkMoodleStatus() {
  try {
    const moodleBaseUrl = process.env.MOODLE_URL_BASE;
    const response = await axios.get(`${moodleBaseUrl}/login/index.php`, {
      timeout: 5000,
      validateStatus: () => true,
    });
    return response.status === 200;
  } catch (error) {
    return false;
  }
}

// Create users and sync with Moodle
async function createUsers(count) {
  console.log(`Creating ${count} random users...`);

  createdUsers = [];
  const moodleResults = {
    success: 0,
    failed: 0,
  };

  for (let i = 0; i < count; i++) {
    try {
      const userData = generateRandomUser();
      console.log(`Creating user ${i + 1}/${count}: ${userData.email}`);

      // Create the user in the database
      const createdUser = await prisma.user.create({
        data: userData,
      });

      // Wait a moment between API calls to avoid overwhelming Moodle
      await new Promise((resolve) => setTimeout(resolve, 500));

      console.log(`Created user in database: ${userData.email}`);

      // Make a direct call to Moodle to ensure synchronization
      try {
        const moodleBaseUrl = process.env.MOODLE_URL_BASE;
        const moodleToken = process.env.MOODLE_TOKEN;

        // Check if user exists in Moodle first
        const checkResponse = await axios.get(
          `${moodleBaseUrl}/webservice/rest/server.php`,
          {
            params: {
              wstoken: moodleToken,
              wsfunction: 'core_user_get_users_by_field',
              moodlewsrestformat: 'json',
              field: 'email',
              values: [userData.email],
            },
          },
        );

        if (
          checkResponse.data &&
          Array.isArray(checkResponse.data) &&
          checkResponse.data.length > 0
        ) {
          console.log(`User already exists in Moodle: ${userData.email}`);
          moodleResults.success++;
        } else {
          // Create user in Moodle
          const response = await axios.post(
            `${moodleBaseUrl}/webservice/rest/server.php`,
            null,
            {
              params: {
                wstoken: moodleToken,
                wsfunction: 'core_user_create_users',
                moodlewsrestformat: 'json',
                users: [
                  {
                    username: userData.email.replace('@', '.'),
                    password: 'Password123!',
                    firstname: userData.firstName,
                    lastname: userData.lastName,
                    email: userData.email,
                    phone1: (userData.phone || '').substring(0, 20),
                    city: userData.address_city || '',
                    country: 'US',
                    customfields: [
                      {
                        type: 'state',
                        value: userData.address_state || '',
                      },
                      {
                        type: 'zipcode',
                        value: userData.address_postalcode || '',
                      },
                    ],
                  },
                ],
              },
            },
          );

          if (
            response.data &&
            Array.isArray(response.data) &&
            response.data.length > 0
          ) {
            console.log(`✅ Synced user to Moodle: ${userData.email}`);
            moodleResults.success++;
          } else {
            console.error(
              `❌ Failed to sync user to Moodle: ${userData.email}`,
            );
            console.error(
              'Response data:',
              JSON.stringify(response.data, null, 2),
            );
            moodleResults.failed++;
          }
        }
      } catch (moodleError) {
        console.error(
          `❌ Error syncing to Moodle: ${userData.email}`,
          moodleError.message,
        );

        // Get more detailed error information if available
        if (moodleError.response && moodleError.response.data) {
          console.error(
            'Moodle error details:',
            JSON.stringify(moodleError.response.data, null, 2),
          );

          // Provide guidance based on error type
          if (moodleError.response.status === 403) {
            console.error(
              '\n❌ Access denied (403 Forbidden). Possible issues:',
            );
            console.error('  1. Web services not enabled in Moodle');
            console.error('  2. REST protocol not enabled');
            console.error('  3. Token is invalid or has expired');
            console.error(
              '  4. Required functions not added to the external service',
            );
            console.error(
              '  5. Token user does not have the right permissions',
            );
            console.error('\n  Run the debug script for more information:');
            console.error('  node scripts/debug-moodle-connection.js');
          }
        }

        moodleResults.failed++;
      }

      createdUsers.push(createdUser);
    } catch (error) {
      console.error(`Error creating user ${i + 1}:`, error.message);
    }
  }

  console.log(
    `\nMoodle sync results: ${moodleResults.success} successful, ${moodleResults.failed} failed`,
  );
  return createdUsers;
}

// Delete test users from both systems
async function deleteTestUsers() {
  console.log('\nDeleting test users...');

  for (const user of createdUsers) {
    try {
      console.log(`\nDeleting user: ${user.email}`);

      // Delete from our system first
      await prisma.user.update({
        where: { id: user.id },
        data: { deleted_at: Math.floor(Date.now() / 1000) },
      });
      console.log(`✅ Deleted user from database: ${user.email}`);

      // Delete from Moodle if they have a Moodle account
      if (user.hasMoodleAccount && user.moodleUserId) {
        const response = await axios.post(
          `${process.env.MOODLE_URL_BASE}/webservice/rest/server.php`,
          null,
          {
            params: {
              wstoken: process.env.MOODLE_TOKEN,
              wsfunction: 'core_user_delete_users',
              moodlewsrestformat: 'json',
              userids: [user.moodleUserId],
            },
          },
        );

        if (response.status === 200) {
          console.log(`✅ Deleted user from Moodle: ${user.email}`);
        }
      }
    } catch (error) {
      console.error(`❌ Error deleting user ${user.email}:`, error.message);
      if (error.response?.data) {
        console.error(
          'Moodle error details:',
          JSON.stringify(error.response.data, null, 2),
        );
      }
    }
  }
}

// Update test users in both systems
async function updateTestUsers() {
  console.log('\nUpdating test users...');

  for (const user of createdUsers) {
    try {
      console.log(`\nUpdating user: ${user.email}`);

      // Generate new information
      const newFirstName = faker.person.firstName();
      const newLastName = faker.person.lastName();
      const newPhone = faker.phone.number();

      // Update in our system
      const updatedUser = await prisma.user.update({
        where: { id: user.id },
        data: {
          firstName: newFirstName,
          lastName: newLastName,
          phone: newPhone,
        },
      });
      console.log(`✅ Updated user in database: ${user.email}`);

      // Update in Moodle if they have a Moodle account
      if (user.hasMoodleAccount && user.moodleUserId) {
        const response = await axios.post(
          `${process.env.MOODLE_URL_BASE}/webservice/rest/server.php`,
          null,
          {
            params: {
              wstoken: process.env.MOODLE_TOKEN,
              wsfunction: 'core_user_update_users',
              moodlewsrestformat: 'json',
              users: [
                {
                  id: user.moodleUserId,
                  firstname: newFirstName,
                  lastname: newLastName,
                  phone1: newPhone,
                },
              ],
            },
          },
        );

        if (response.status === 200) {
          console.log(`✅ Updated user in Moodle: ${user.email}`);
        }
      }

      // Update the stored user information
      user.firstName = newFirstName;
      user.lastName = newLastName;
      user.phone = newPhone;
    } catch (error) {
      console.error(`❌ Error updating user ${user.email}:`, error.message);
      if (error.response?.data) {
        console.error(
          'Moodle error details:',
          JSON.stringify(error.response.data, null, 2),
        );
      }
    }
  }
}

// Check if the Moodle token is valid
async function verifyMoodleToken() {
  try {
    const moodleBaseUrl = process.env.MOODLE_URL_BASE;
    const moodleToken = process.env.MOODLE_TOKEN;

    const response = await axios.get(
      `${moodleBaseUrl}/webservice/rest/server.php`,
      {
        params: {
          wstoken: moodleToken,
          wsfunction: 'core_webservice_get_site_info',
          moodlewsrestformat: 'json',
        },
        validateStatus: () => true,
      },
    );

    if (response.status !== 200 || (response.data && response.data.errorcode)) {
      console.error(
        '\n❌ Moodle token is invalid or lacks necessary permissions.',
      );
      console.error('Error details:', response.data || response.statusText);
      return false;
    }

    return true;
  } catch (error) {
    console.error('\n❌ Error verifying Moodle token:', error.message);
    return false;
  }
}

// Main function
async function main() {
  console.log('Checking Moodle connection...');
  const moodleAvailable = await checkMoodleStatus();

  if (!moodleAvailable) {
    console.error(
      '\n❌ Moodle is not available. Make sure the Moodle container is running.',
    );
    console.log('Run the following command to start Moodle:');
    console.log('  cd docker/moodle && ./run-local.sh');
    process.exit(1);
  }

  console.log('✅ Moodle is running!');
  console.log('\nEnvironment Configuration:');
  console.log(`- MOODLE_URL_BASE: ${process.env.MOODLE_URL_BASE}`);
  console.log(
    `- MOODLE_TOKEN: ${process.env.MOODLE_TOKEN.substring(0, 10)}***`,
  );

  // Verify the token before creating users
  const tokenValid = await verifyMoodleToken();
  if (!tokenValid) {
    console.error(
      '\n❌ Moodle token verification failed. Please check your token configuration.',
    );
    console.log('\nFollow these steps to configure the Moodle web service:');
    console.log(
      '1. Refer to the documentation in apps/api/docker/moodle/MOODLE_INTEGRATION.md',
    );
    console.log(
      '2. Or run the token verification script: node scripts/verify-moodle-token.js',
    );
    process.exit(1);
  }

  console.log('✅ Moodle token is valid and working!');

  // Create test users
  const users = await createUsers(5);
  console.log(`\n✅ Successfully created ${users.length} users!`);

  console.log('\nUser emails:');
  users.forEach((user, index) => {
    console.log(`${index + 1}. ${user.email}`);
  });

  // Wait a bit before updates
  console.log('\nWaiting 5 seconds before testing updates...');
  await new Promise((resolve) => setTimeout(resolve, 5000));

  // Update test users
  await updateTestUsers();

  // Wait a bit before deletion
  console.log('\nWaiting 5 seconds before cleanup...');
  await new Promise((resolve) => setTimeout(resolve, 5000));

  // Delete test users
  await deleteTestUsers();

  await prisma.$disconnect();
  console.log('\nTest completed successfully!');
}

// Run the script
main().catch((error) => {
  console.error('Error:', error);
  prisma.$disconnect();
  process.exit(1);
});
