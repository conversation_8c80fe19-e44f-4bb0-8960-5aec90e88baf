/**
 * <PERSON><PERSON><PERSON> to verify Moodle token and web service configuration
 * Run with: node docker/moodle/scripts/verify-moodle-token.js (from API root)
 */

const axios = require('axios');
const path = require('path');

// Load environment variables from the API root directory
require('dotenv').config({ path: path.join(__dirname, '../../../.env') });

async function verifyMoodleToken() {
  console.log('Verifying Moodle token and web service configuration...');

  const moodleBaseUrl = process.env.MOODLE_URL_BASE;
  const moodleToken = process.env.MOODLE_TOKEN;

  console.log(`Using Moodle URL: ${moodleBaseUrl}`);
  console.log(
    `Using token: ${moodleToken.substring(0, 5)}***${moodleToken.substring(moodleToken.length - 3)}`,
  );

  try {
    // Test a simple function that should work with minimal permissions
    const response = await axios.get(
      `${moodleBaseUrl}/webservice/rest/server.php`,
      {
        params: {
          wstoken: moodleToken,
          wsfunction: 'core_webservice_get_site_info',
          moodlewsrestformat: 'json',
        },
      },
    );

    if (response.data && response.data.errorcode) {
      console.error('❌ Error from Moodle:', response.data);
      return false;
    }

    console.log('✅ Token is valid and working!');
    console.log('Site info:', response.data);

    // Now check if core_user functions are available
    console.log('\nChecking available web services...');
    try {
      const functionsResponse = await axios.get(
        `${moodleBaseUrl}/webservice/rest/server.php`,
        {
          params: {
            wstoken: moodleToken,
            wsfunction: 'core_user_get_users_by_field',
            moodlewsrestformat: 'json',
            field: 'email',
            values: ['<EMAIL>'],
          },
        },
      );

      if (functionsResponse.data && functionsResponse.data.errorcode) {
        console.error(
          '\n❌ core_user_get_users_by_field is not enabled:',
          functionsResponse.data,
        );
        console.error(
          '\nYou need to enable the following functions in Site administration > Plugins > Web services > External services:',
        );
        console.error('- core_user_create_users');
        console.error('- core_user_update_users');
        console.error('- core_user_delete_users');
        console.error('- core_user_get_users_by_field');
        return false;
      }

      console.log('✅ core_user functions are enabled and working!');
      return true;
    } catch (error) {
      console.error('\n❌ Error checking core_user functions:', error.message);
      if (error.response && error.response.data) {
        console.error('Error details:', error.response.data);
      }
      console.error(
        '\nYou need to enable the following functions in Site administration > Plugins > Web services > External services:',
      );
      console.error('- core_user_create_users');
      console.error('- core_user_update_users');
      console.error('- core_user_delete_users');
      console.error('- core_user_get_users_by_field');
      return false;
    }
  } catch (error) {
    console.error('❌ Error verifying Moodle token:', error.message);
    if (error.response && error.response.data) {
      console.error('Error details:', error.response.data);
    }
    return false;
  }
}

async function main() {
  const isValid = await verifyMoodleToken();

  if (isValid) {
    console.log('\n✅ Moodle web service configuration is correctly set up!');
  } else {
    console.log('\n❌ Moodle web service configuration needs attention.');
    console.log('\nFollow these steps to configure the web service:');
    console.log('1. Log in to Moodle as administrator (admin/admin)');
    console.log(
      '2. Go to Site administration > Plugins > Web services > External services',
    );
    console.log(
      '3. Create a new service named "EMA API Service" if it doesn\'t exist',
    );
    console.log('4. In the service settings, enable the following functions:');
    console.log('   - core_user_create_users');
    console.log('   - core_user_update_users');
    console.log('   - core_user_delete_users');
    console.log('   - core_user_get_users_by_field');
    console.log(
      '5. Go to Site administration > Plugins > Web services > Manage tokens',
    );
    console.log(
      '6. Create a new token for the admin user for the "EMA API Service"',
    );
    console.log('7. Copy the token to your .env file');
  }
}

// Run the script
main().catch((error) => {
  console.error('Error:', error);
  process.exit(1);
});
