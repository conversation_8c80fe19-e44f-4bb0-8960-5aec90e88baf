#!/bin/bash

# ======================================================================
# EMA Moodle Manager - Interactive Management Interface
# ======================================================================
# Interactive menu for managing the EMA Moodle installation
# ======================================================================

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Function to print the menu
show_menu() {
    clear
    echo -e "${BLUE}======================================${NC}"
    echo -e "${BLUE}     EMA Moodle Management Console     ${NC}"
    echo -e "${BLUE}======================================${NC}"
    echo ""
    echo -e "${YELLOW}Choose an option:${NC}"
    echo ""
    echo -e "${GREEN}1)${NC} 🚀 Setup/Start Moodle (Normal)"
    echo -e "${GREEN}2)${NC} 🆕 Fresh Install (Clean Setup)"
    echo -e "${GREEN}3)${NC} ✅ Verify Installation"
    echo -e "${GREEN}4)${NC} 🔧 Troubleshoot Web Services"
    echo -e "${GREEN}5)${NC} 👥 Create Test Users"
    echo -e "${GREEN}6)${NC} 🔑 Verify API Token"
    echo -e "${GREEN}7)${NC} 🐛 Debug Connection Issues"
    echo -e "${GREEN}8)${NC} 📊 View Container Status"
    echo -e "${GREEN}9)${NC} 📖 Open Documentation"
    echo -e "${GREEN}10)${NC} 🛑 Stop Moodle"
    echo -e "${RED}11)${NC} 🚪 Exit"
    echo ""
    echo -ne "${CYAN}Enter your choice [1-11]: ${NC}"
}

# Function to execute choice
execute_choice() {
    case $1 in
        1)
            echo -e "${YELLOW}Starting Moodle setup...${NC}"
            ./setup-moodle.sh
            ;;
        2)
            echo -e "${YELLOW}Performing fresh installation...${NC}"
            ./setup-moodle.sh --fresh-install
            ;;
        3)
            echo -e "${YELLOW}Verifying installation...${NC}"
            ./setup-moodle.sh --verify-only
            ;;
        4)
            echo -e "${YELLOW}Running web services troubleshooting...${NC}"
            if docker ps | grep -q moodle-moodle-1; then
                echo "Updating config.php..."
                docker exec moodle-moodle-1 php /var/www/html/update-config.php
                echo "Re-enabling web services..."
                docker exec moodle-moodle-1 php /var/www/html/enable-webservices.php
                echo -e "${GREEN}✅ Web services troubleshooting complete${NC}"
            else
                echo -e "${RED}❌ Moodle container is not running. Start Moodle first.${NC}"
            fi
            ;;
        5)
            echo -e "${YELLOW}Creating test users...${NC}"
            if command -v node &> /dev/null; then
                cd ../..
                node docker/moodle/scripts/create-test-users.js
                cd docker/moodle
            else
                echo -e "${RED}❌ Node.js is required for this function${NC}"
            fi
            ;;
        6)
            echo -e "${YELLOW}Verifying API token...${NC}"
            if command -v node &> /dev/null; then
                cd ../..
                node docker/moodle/scripts/verify-moodle-token.js
                cd docker/moodle
            else
                echo -e "${RED}❌ Node.js is required for this function${NC}"
            fi
            ;;
        7)
            echo -e "${YELLOW}Running connection diagnostics...${NC}"
            if command -v node &> /dev/null; then
                cd ../..
                node docker/moodle/scripts/debug-moodle-connection.js
                cd docker/moodle
            else
                echo -e "${RED}❌ Node.js is required for this function${NC}"
            fi
            ;;
        8)
            echo -e "${YELLOW}Container Status:${NC}"
            docker-compose ps
            echo ""
            echo -e "${YELLOW}Resource Usage:${NC}"
            docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}"
            ;;
        9)
            echo -e "${YELLOW}Available Documentation:${NC}"
            echo ""
            echo -e "${GREEN}Integration Guide:${NC} docs/MOODLE_INTEGRATION.md"
            echo -e "${GREEN}AWS Deployment:${NC}   docs/AWS_DEPLOYMENT.md"
            echo -e "${GREEN}Local Development:${NC} docs/LOCAL_DEVELOPMENT.md"
            echo ""
            echo -e "${CYAN}Opening integration guide...${NC}"
            if command -v code &> /dev/null; then
                code docs/MOODLE_INTEGRATION.md
            elif command -v open &> /dev/null; then
                open docs/MOODLE_INTEGRATION.md
            else
                echo "Please open docs/MOODLE_INTEGRATION.md manually"
            fi
            ;;
        10)
            echo -e "${YELLOW}Stopping Moodle services...${NC}"
            docker-compose down
            echo -e "${GREEN}✅ Moodle services stopped${NC}"
            ;;
        11)
            echo -e "${GREEN}Goodbye!${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ Invalid option. Please choose 1-11.${NC}"
            ;;
    esac
}

# Function to pause and wait for user input
pause() {
    echo ""
    echo -ne "${YELLOW}Press any key to continue...${NC}"
    read -n 1 -s
    echo ""
}

# Main loop
main() {
    cd "$SCRIPT_DIR"
    
    while true; do
        show_menu
        read choice
        echo ""
        execute_choice "$choice"
        pause
    done
}

# Run main function
main "$@"