<?php  // Moodle configuration file

unset($CFG);
global $CFG;
$CFG = new stdClass();

$CFG->dbtype    = 'mariadb';
$CFG->dblibrary = 'native';
$CFG->dbhost    = 'mariadb';
$CFG->dbname    = 'moodle';
$CFG->dbuser    = 'moodle';
$CFG->dbpass    = 'moodle_password';
$CFG->prefix    = 'mdl_';
$CFG->dboptions = array (
  'dbpersist' => 0,
  'dbport' => 3306,
  'dbsocket' => '',
  'dbcollation' => 'utf8mb4_unicode_ci',
);

$CFG->wwwroot   = 'http://localhost:8080';
$CFG->dataroot  = '/var/moodledata';
$CFG->admin     = 'admin';

$CFG->directorypermissions = 0777;

// Development settings - Enable debugging as per Moodle 5.1 recommendations
$CFG->debug = (E_ALL | E_STRICT);
$CFG->debugdisplay = 1;
$CFG->debugdeveloper = true;
$CFG->perfdebug = 15;
$CFG->debugpageinfo = true;
$CFG->allowthemechangeonurl = true;
$CFG->passwordpolicy = false;

// Enable web services
$CFG->enablewebservices = true;
$CFG->enablemobilewebservice = true;

// Force web service protocols and enable REST
$CFG->webserviceprotocols = 'rest,soap,xmlrpc';

// Skip setup configuration for web services
$CFG->externalservice = 'ema_api';
$CFG->externalserviceenabled = true;

// Performance settings for development
$CFG->cachejs = false;
$CFG->cachetemplates = false;
$CFG->langstringcache = false;

// Email settings for development
$CFG->smtphosts = 'localhost:1025';
$CFG->smtpuser = '';
$CFG->smtppass = '';
$CFG->smtpsecure = '';
$CFG->smtpauth = false;
$CFG->smtpmaxbulk = 1;
$CFG->noemailever = false;

// Allow installation without email verification
$CFG->registerauth = 'email';

// Configure webhook endpoint for the EMA integration
$CFG->webhooks_endpoint = getenv('MOODLE_WEBHOOK_URL') ?: 'http://webhook-relay/moodle/webhook';

require_once(__DIR__ . '/lib/setup.php');

// There is no php closing tag in this file,
// it is intentional because it prevents trailing whitespace problems!
