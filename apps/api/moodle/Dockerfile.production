FROM moodlehq/moodle-php-apache:8.1

LABEL maintainer="EMA Development Team"
LABEL version="1.0"
LABEL description="Moodle 5.1 for AWS production deployment"

# Install required packages for production
USER root
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libzip-dev \
    zip \
    unzip \
    mariadb-client \
    redis-tools \
    && docker-php-ext-install zip \
    && docker-php-ext-install opcache \
    && rm -rf /var/lib/apt/lists/*

# Configure PHP for production
RUN { \
    echo 'opcache.enable=1'; \
    echo 'opcache.memory_consumption=256'; \
    echo 'opcache.interned_strings_buffer=8'; \
    echo 'opcache.max_accelerated_files=20000'; \
    echo 'opcache.revalidate_freq=2'; \
    echo 'opcache.fast_shutdown=1'; \
    echo 'opcache.enable_cli=1'; \
    echo 'upload_max_filesize=100M'; \
    echo 'post_max_size=100M'; \
    echo 'max_execution_time=300'; \
    echo 'max_input_vars=5000'; \
    echo 'memory_limit=512M'; \
} > /usr/local/etc/php/conf.d/production.ini

# Download and setup Moodle 5.1
WORKDIR /var/www/html
RUN git clone --depth=1 --branch=MOODLE_404_STABLE https://github.com/moodle/moodle.git . \
    && chown -R www-data:www-data /var/www/html

# Create moodledata directory
RUN mkdir -p /var/moodledata \
    && chown -R www-data:www-data /var/moodledata \
    && chmod 777 /var/moodledata

# Set up the webhook plugin
COPY --chown=www-data:www-data ./webhooks-plugin /var/www/html/local/webhooks

# Copy production configuration
COPY --chown=www-data:www-data ./config.production.php /var/www/html/config.php

# Enable mod_rewrite and SSL
RUN a2enmod rewrite ssl headers

# Configure Apache for production
RUN { \
    echo 'ServerTokens Prod'; \
    echo 'ServerSignature Off'; \
    echo 'Header always set X-Content-Type-Options nosniff'; \
    echo 'Header always set X-Frame-Options DENY'; \
    echo 'Header always set X-XSS-Protection "1; mode=block"'; \
    echo 'Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"'; \
    echo 'Header always set Referrer-Policy "strict-origin-when-cross-origin"'; \
} > /etc/apache2/conf-available/security.conf

RUN a2enconf security

# Health check script
COPY ./healthcheck.sh /usr/local/bin/healthcheck.sh
RUN chmod +x /usr/local/bin/healthcheck.sh

# Switch to www-data user for security
USER www-data

# Expose port 80 (SSL termination handled by load balancer)
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /usr/local/bin/healthcheck.sh

# Start Apache
CMD ["apache2-foreground"]
