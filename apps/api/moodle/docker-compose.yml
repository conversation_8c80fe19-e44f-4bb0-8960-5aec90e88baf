services:
  mariadb:
    image: mariadb:10.11
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=moodle
      - MYSQL_USER=moodle
      - MYSQL_PASSWORD=moodle_password
      - MYSQL_CHARSET=utf8mb4
      - MYSQL_COLLATION=utf8mb4_unicode_ci
    volumes:
      - 'mariadb_data:/var/lib/mysql'
    ports:
      - '3306:3306'
    networks:
      - moodle-network
    command: >
      --default-authentication-plugin=mysql_native_password
      --innodb-file-per-table=1
      --max_allowed_packet=64M
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci

  moodle:
    build:
      context: .
      dockerfile: Dockerfile
    depends_on:
      - mariadb
    ports:
      - '8080:80'
    volumes:
      - 'moodle_data:/var/moodledata'
      - 'moodle_code:/var/www/html'
    networks:
      - moodle-network
    environment:
      - APACHE_DOCUMENT_ROOT=/var/www/html
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/admin/cli/check_database_schema.php"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Webhook relay service to forward Moodle webhooks to our local API
  webhook-relay:
    image: nginx:alpine
    ports:
      - '8090:80'
    volumes:
      - './nginx.conf:/etc/nginx/conf.d/default.conf'
    depends_on:
      - moodle
    networks:
      - moodle-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Optional: Mailhog for testing email functionality
  mailhog:
    image: mailhog/mailhog:latest
    ports:
      - '1025:1025'  # SMTP
      - '8025:8025'  # Web UI
    networks:
      - moodle-network

networks:
  moodle-network:
    driver: bridge

volumes:
  mariadb_data:
    driver: local
  moodle_data:
    driver: local
  moodle_code:
    driver: local
