{"family": "moodle-ema", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "2048", "memory": "4096", "executionRoleArn": "arn:aws:iam::ACCOUNT_ID:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::ACCOUNT_ID:role/moodleTaskRole", "containerDefinitions": [{"name": "moodle", "image": "YOUR_ECR_REPOSITORY_URI:latest", "essential": true, "portMappings": [{"containerPort": 80, "protocol": "tcp"}], "environment": [{"name": "MOODLE_WWW_ROOT", "value": "https://moodle.ema-portal.org"}, {"name": "MOODLE_DATA_ROOT", "value": "/var/moodledata"}, {"name": "MOODLE_WEBHOOK_ENDPOINT", "value": "https://api.ema-portal.org/moodle/webhook"}, {"name": "MOODLE_S3_BUCKET", "value": "moodle-ema-files"}, {"name": "MOODLE_S3_REGION", "value": "us-east-1"}, {"name": "MOODLE_SMTP_HOST", "value": "email-smtp.us-east-1.amazonaws.com"}, {"name": "MOODLE_SMTP_SECURE", "value": "tls"}, {"name": "MOODLE_SMTP_AUTH", "value": "true"}, {"name": "MOODLE_REVERSE_PROXY", "value": "true"}], "secrets": [{"name": "MOODLE_DB_HOST", "valueFrom": "arn:aws:secretsmanager:us-east-1:ACCOUNT_ID:secret:moodle/database:host::"}, {"name": "MOODLE_DB_NAME", "valueFrom": "arn:aws:secretsmanager:us-east-1:ACCOUNT_ID:secret:moodle/database:dbname::"}, {"name": "MOODLE_DB_USER", "valueFrom": "arn:aws:secretsmanager:us-east-1:ACCOUNT_ID:secret:moodle/database:username::"}, {"name": "MOODLE_DB_PASS", "valueFrom": "arn:aws:secretsmanager:us-east-1:ACCOUNT_ID:secret:moodle/database:password::"}, {"name": "MOODLE_SMTP_USER", "valueFrom": "arn:aws:secretsmanager:us-east-1:ACCOUNT_ID:secret:moodle/smtp:username::"}, {"name": "MOODLE_SMTP_PASS", "valueFrom": "arn:aws:secretsmanager:us-east-1:ACCOUNT_ID:secret:moodle/smtp:password::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/aws/ecs/moodle", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "/usr/local/bin/healthcheck.sh"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}, "mountPoints": [{"sourceVolume": "moodledata", "containerPath": "/var/moodledata", "readOnly": false}]}], "volumes": [{"name": "moodledata", "efsVolumeConfiguration": {"fileSystemId": "fs-XXXXXXXXX", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-XXXXXXXXX", "iam": "ENABLED"}}}]}