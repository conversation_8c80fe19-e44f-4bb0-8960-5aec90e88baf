-- <PERSON>QL script to set up Moodle web services
-- To be executed in the Moodle database after installation

-- Enable web services and protocols
INSERT INTO mdl_config (name, value) VALUES ('enablewebservices', '1') ON DUPLICATE KEY UPDATE value='1';
INSERT INTO mdl_config (name, value) VALUES ('webserviceprotocols', 'rest') ON DUPLICATE KEY UPDATE value='rest';

-- Create external service
INSERT INTO mdl_external_services (name, enabled, requiredcapability, restrictedusers, shortname, timecreated, timemodified) 
VALUES ('EMA API Service', 1, '', 1, 'ema_api', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- Get the service ID
SET @service_id = LAST_INSERT_ID();

-- Add functions to the service
INSERT INTO mdl_external_services_functions (externalserviceid, functionname) VALUES (@service_id, 'core_user_create_users');
INSERT INTO mdl_external_services_functions (externalserviceid, functionname) VALUES (@service_id, 'core_user_update_users');
INSERT INTO mdl_external_services_functions (externalserviceid, functionname) VALUES (@service_id, 'core_user_delete_users');
INSERT INTO mdl_external_services_functions (externalserviceid, functionname) VALUES (@service_id, 'core_user_get_users_by_field');
INSERT INTO mdl_external_services_functions (externalserviceid, functionname) VALUES (@service_id, 'core_webservice_get_site_info');

-- Get admin user ID
SET @admin_id = (SELECT id FROM mdl_user WHERE username = 'admin' LIMIT 1);

-- Add admin as authorized user for the service
INSERT INTO mdl_external_services_users (externalserviceid, userid, timecreated) VALUES (@service_id, @admin_id, UNIX_TIMESTAMP());

-- Create a token for the admin user
INSERT INTO mdl_external_tokens (token, privatetoken, tokentype, userid, externalserviceid, sid, contextid, creatorid, timecreated, validuntil)
VALUES ('18745a8b8f98df61fe052159d3f91542', '', 0, @admin_id, @service_id, NULL, 1, @admin_id, UNIX_TIMESTAMP(), (UNIX_TIMESTAMP() + 31536000));
