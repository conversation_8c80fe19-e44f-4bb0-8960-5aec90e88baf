server {
    listen 80;
    server_name localhost;

    location / {
        # Forward requests to the local API server
        # host.docker.internal resolves to the host machine from within Docker
        proxy_pass http://host.docker.internal:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Health check endpoint
    location /health {
        return 200 'Webhook relay is running';
        add_header Content-Type text/plain;
    }
}
