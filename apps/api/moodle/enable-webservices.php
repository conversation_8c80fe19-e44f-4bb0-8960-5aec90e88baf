<?php
// <PERSON>ript to enable web services and protocols in Moodle
// To be run inside the Moodle container

define('CLI_SCRIPT', true);
require_once('/var/www/html/config.php');
require_once($CFG->libdir.'/adminlib.php');

try {
    // Enable web services
    set_config('enablewebservices', 1);
    echo "Web services enabled\n";

    // Ensure the webserviceprotocols config exists
    set_config('webserviceprotocols', 'rest');
    echo "Web service protocols set to 'rest'\n";

    // Enable REST protocol via config settings (modern Moodle approach)
    // In modern Moodle, protocols are managed via config settings rather than database tables
    echo "REST protocol enabled via config settings\n";

    // Check for existing service
    $service = $DB->get_record('external_services', ['shortname' => 'ema_api']);

    if (!$service) {
        // Create the external service
        $service = new stdClass();
        $service->name = 'EMA API Service';
        $service->shortname = 'ema_api';
        $service->enabled = 1;
        $service->requiredcapability = '';
        $service->restrictedusers = 1;
        $service->timecreated = time();
        
        $service->id = $DB->insert_record('external_services', $service);
        echo "EMA API Service created\n";
    } else {
        // Enable the service if it exists but is disabled
        if (!$service->enabled) {
            $DB->set_field('external_services', 'enabled', 1, ['id' => $service->id]);
            echo "EMA API Service enabled\n";
        } else {
            echo "EMA API Service already enabled\n";
        }
    }

    // Add required functions to the service
    $functions = [
        'core_user_create_users',
        'core_user_update_users',
        'core_user_delete_users',
        'core_user_get_users_by_field',
        'core_webservice_get_site_info'
    ];

    foreach ($functions as $function) {
        // Check if function already exists in service
        $exists = $DB->record_exists('external_services_functions', 
                                    ['externalserviceid' => $service->id, 
                                     'functionname' => $function]);
        
        if (!$exists) {
            $record = new stdClass();
            $record->externalserviceid = $service->id;
            $record->functionname = $function;
            $DB->insert_record('external_services_functions', $record);
            echo "Added function {$function} to service\n";
        } else {
            echo "Function {$function} already added to service\n";
        }
    }

    // Add capability to admin user
    $adminuser = $DB->get_record('user', ['username' => 'admin']);
    if ($adminuser) {
        // Add admin user as authorized user for the service
        $exists = $DB->record_exists('external_services_users', 
                                   ['externalserviceid' => $service->id, 
                                    'userid' => $adminuser->id]);
        
        if (!$exists) {
            $record = new stdClass();
            $record->externalserviceid = $service->id;
            $record->userid = $adminuser->id;
            $record->timecreated = time();
            $DB->insert_record('external_services_users', $record);
            echo "Added admin user to service\n";
        } else {
            echo "Admin user already added to service\n";
        }

        // Create a token for admin user
        $tokenrecord = $DB->get_record('external_tokens', 
                                      ['externalserviceid' => $service->id, 
                                       'userid' => $adminuser->id,
                                       'tokentype' => EXTERNAL_TOKEN_PERMANENT]);

        if (!$tokenrecord) {
            // Generate a token
            require_once($CFG->dirroot . '/webservice/lib.php');
            $token = external_generate_token(EXTERNAL_TOKEN_PERMANENT, $service->id, 
                                            $adminuser->id, context_system::instance());
            echo "Created token: {$token}\n";
        } else {
            echo "Token already exists: {$tokenrecord->token}\n";
        }
    }

    // Update the roles and capabilities
    // Get system context
    $syscontext = context_system::instance();

    // Check if webservice role exists
    $roleid = $DB->get_field('role', 'id', ['shortname' => 'moodleapi']);

    if (!$roleid) {
        // Create a new role
        $role = create_role('Moodle API Access', 'moodleapi', 'Role for Moodle API access', 'manager');
        echo "Created new role: Moodle API Access\n";
        
        // Assign needed capabilities
        $capabilities = [
            'webservice/rest:use',
            'moodle/user:create',
            'moodle/user:update',
            'moodle/user:delete',
            'moodle/user:viewdetails'
        ];
        
        foreach ($capabilities as $capability) {
            assign_capability($capability, CAP_ALLOW, $role, $syscontext->id, true);
            echo "Assigned capability {$capability} to role\n";
        }
        
        // Assign role to admin user
        if ($adminuser) {
            role_assign($role, $adminuser->id, $syscontext->id);
            echo "Assigned role to admin user\n";
        }
    }

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
    exit(1);
}

echo "Web services configuration completed successfully!\n";
