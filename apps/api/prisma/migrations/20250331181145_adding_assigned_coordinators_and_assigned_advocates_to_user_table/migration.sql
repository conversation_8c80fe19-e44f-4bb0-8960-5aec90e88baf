-- CreateTable
CREATE TABLE "_AdvocateToCoordinator" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_AdvocateToCoordinator_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_AdvocateToCoordinator_B_index" ON "_AdvocateToCoordinator"("B");

-- AddForeignKey
ALTER TABLE "_AdvocateToCoordinator" ADD CONSTRAINT "_AdvocateToCoordinator_A_fkey" FOREIGN KEY ("A") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AdvocateToCoordinator" ADD CONSTRAINT "_AdvocateToCoordinator_B_fkey" FOREIGN KEY ("B") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
