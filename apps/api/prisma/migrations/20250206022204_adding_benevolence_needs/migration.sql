-- CreateTable
CREATE TABLE "BenevolenceNeed" (
    "id" TEXT NOT NULL,
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,
    "deleted_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "momId" TEXT NOT NULL,
    "amount_c" DOUBLE PRECISION,
    "provided_date_c" TIMESTAMP(3),
    "did_address_need_c" BOOLEAN,
    "financial_amount_gifted_c" DOUBLE PRECISION,
    "financial_amount_requested_c" DOUBLE PRECISION,
    "financial_mom_contribution_c" DOUBLE PRECISION,
    "financial_prevention_plan_c" TEXT,
    "is_urgent_c" BOOLEAN,
    "non_addressal_comment_c" TEXT,
    "notes_c" TEXT,
    "other_is_referral_needed_c" BOOLEAN,
    "pg_fulfillment_method_c" TEXT,
    "physical_good_monetary_value_c" DOUBLE PRECISION,
    "resolved_date_c" TIMESTAMP(3),
    "type_c" TEXT,
    "resolvedByUserId" TEXT,

    CONSTRAINT "BenevolenceNeed_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "BenevolenceNeed" ADD CONSTRAINT "BenevolenceNeed_momId_fkey" FOREIGN KEY ("momId") REFERENCES "Mom"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BenevolenceNeed" ADD CONSTRAINT "BenevolenceNeed_resolvedByUserId_fkey" FOREIGN KEY ("resolvedByUserId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
