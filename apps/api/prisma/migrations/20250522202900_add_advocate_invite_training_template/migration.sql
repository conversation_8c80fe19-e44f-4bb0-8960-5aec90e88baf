-- AlterEnum
BEGIN;
CREATE TYPE "NotificationTemplate_new" AS ENUM ('mom_assigned_to_coordinator', 'new_referral_supervisor', 'new_wellness_assessment_coordinator', 'session_note_rejected', 'mom_ready_for_advocate_assignment', 'benevolence_need_submitted', 'advocate_invite_training');
ALTER TABLE "Notification" ALTER COLUMN "template" TYPE "NotificationTemplate_new" USING ("template"::text::"NotificationTemplate_new");
ALTER TYPE "NotificationTemplate" RENAME TO "NotificationTemplate_old";
ALTER TYPE "NotificationTemplate_new" RENAME TO "NotificationTemplate";
DROP TYPE "NotificationTemplate_old";
COMMIT;
