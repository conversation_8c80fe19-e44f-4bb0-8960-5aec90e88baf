-- AlterTable
ALTER TABLE "ServiceReferral" ADD COLUMN     "benevolence_need_id" TEXT;

-- CreateIndex
CREATE INDEX "ServiceReferral_benevolence_need_id_idx" ON "ServiceReferral"("benevolence_need_id");

-- AddForeignKey
ALTER TABLE "ServiceReferral" ADD CONSTRAINT "ServiceReferral_benevolence_need_id_fkey" FOREIGN KEY ("benevolence_need_id") REFERENCES "BenevolenceNeed"("id") ON DELETE SET NULL ON UPDATE CASCADE;
