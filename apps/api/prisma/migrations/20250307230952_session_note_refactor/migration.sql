/*
  Warnings:

  - You are about to drop the column `covered_in_session_note_id` on the `Lesson` table. All the data in the column will be lost.
  - You are about to drop the column `is_group_session` on the `Session` table. All the data in the column will be lost.
  - You are about to drop the column `covered_lesson_template_id` on the `SessionNote` table. All the data in the column will be lost.
  - You are about to drop the column `track_id` on the `SessionNote` table. All the data in the column will be lost.
  - You are about to drop the `SessionPairing` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "Lesson" DROP CONSTRAINT "Lesson_covered_in_session_note_id_fkey";

-- DropForeignKey
ALTER TABLE "SessionNote" DROP CONSTRAINT "SessionNote_covered_lesson_template_id_fkey";

-- DropForeignKey
ALTER TABLE "SessionNote" DROP CONSTRAINT "SessionNote_track_id_fkey";

-- DropForeignKey
ALTER TABLE "SessionPairing" DROP CONSTRAINT "SessionPairing_pairing_id_fkey";

-- DropForeignKey
ALTER TABLE "SessionPairing" DROP CONSTRAINT "SessionPairing_session_id_fkey";

-- AlterTable
ALTER TABLE "Lesson" DROP COLUMN "covered_in_session_note_id";

-- AlterTable
ALTER TABLE "Session" DROP COLUMN "is_group_session",
ADD COLUMN     "pairing_id" TEXT;

-- AlterTable
ALTER TABLE "SessionNote" DROP COLUMN "covered_lesson_template_id",
DROP COLUMN "track_id",
ADD COLUMN     "covered_lesson_id" TEXT;

-- DropTable
DROP TABLE "SessionPairing";

-- AddForeignKey
ALTER TABLE "SessionNote" ADD CONSTRAINT "SessionNote_covered_lesson_id_fkey" FOREIGN KEY ("covered_lesson_id") REFERENCES "Lesson"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Session" ADD CONSTRAINT "Session_pairing_id_fkey" FOREIGN KEY ("pairing_id") REFERENCES "Pairing"("id") ON DELETE SET NULL ON UPDATE CASCADE;
