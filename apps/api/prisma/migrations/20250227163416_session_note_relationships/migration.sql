/*
  Warnings:

  - You are about to drop the column `completed_lesson_id` on the `Session` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "Session" DROP CONSTRAINT "Session_completed_lesson_id_fkey";

-- DropIndex
DROP INDEX "Session_completed_lesson_id_key";

-- AlterTable
ALTER TABLE "Lesson" ADD COLUMN     "source_lesson_template_id" TEXT;

-- AlterTable
ALTER TABLE "Session" DROP COLUMN "completed_lesson_id";

-- AlterTable
ALTER TABLE "SessionNote" ADD COLUMN     "covered_lesson_id" TEXT,
ADD COLUMN     "lesson_template_id" TEXT,
ADD COLUMN     "track_id" TEXT;

-- AddForeignKey
ALTER TABLE "Lesson" ADD CONSTRAINT "Lesson_source_lesson_template_id_fkey" FOREIGN KEY ("source_lesson_template_id") REFERENCES "LessonTemplate"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SessionNote" ADD CONSTRAINT "SessionNote_track_id_fkey" FOREIGN KEY ("track_id") REFERENCES "Track"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SessionNote" ADD CONSTRAINT "SessionNote_lesson_template_id_fkey" FOREIGN KEY ("lesson_template_id") REFERENCES "LessonTemplate"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SessionNote" ADD CONSTRAINT "SessionNote_covered_lesson_id_fkey" FOREIGN KEY ("covered_lesson_id") REFERENCES "Lesson"("id") ON DELETE SET NULL ON UPDATE CASCADE;
