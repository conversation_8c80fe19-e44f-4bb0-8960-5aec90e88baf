-- CreateTable
CREATE TABLE "CoordinatorNote" (
    "id" TEXT NOT NULL,
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,
    "deleted_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "name" TEXT,
    "description" TEXT NOT NULL,
    "type_c" TEXT NOT NULL,
    "mom_id" TEXT,
    "user_id" TEXT,

    CONSTRAINT "CoordinatorNote_pkey" PRIMARY KEY ("id")
);

-- AddForeign<PERSON>ey
ALTER TABLE "CoordinatorNote" ADD CONSTRAINT "CoordinatorNote_mom_id_fkey" FOREIGN KEY ("mom_id") REFERENCES "Mom"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CoordinatorNote" ADD CONSTRAINT "CoordinatorNote_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
