-- CreateTable
CREATE TABLE "ServiceReferral" (
    "id" TEXT NOT NULL,
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,
    "deleted_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" TEXT,
    "created_by_name" TEXT,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "service" TEXT NOT NULL,
    "outcome" TEXT NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "provider" TEXT NOT NULL,
    "mom_id" TEXT NOT NULL,

    CONSTRAINT "ServiceReferral_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ServiceReferral_mom_id_idx" ON "ServiceReferral"("mom_id");

-- AddForeignKey
ALTER TABLE "ServiceReferral" ADD CONSTRAINT "ServiceReferral_mom_id_fkey" 
    FOREIGN KEY ("mom_id") REFERENCES "Mom"("id") ON DELETE RESTRICT ON UPDATE CASCADE;