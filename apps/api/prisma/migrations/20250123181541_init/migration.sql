-- CreateTable
CREATE TABLE "Referral" (
    "id" TEXT NOT NULL,
    "primary_address_postalcode" TEXT,
    "first_name" TEXT,
    "last_name" TEXT,
    "status" TEXT,
    "sub_status_c" TEXT,
    "phone_other" TEXT,
    "currently_pregnant_c" TEXT,
    "number_of_children_c" INTEGER,
    "need_details_c" TEXT,
    "what_else_c" TEXT,
    "caregiver_type_c" TEXT,
    "gender_c" TEXT,
    "preferred_contact_method_c" TEXT,
    "email1" TEXT,
    "referral_type_c" TEXT,
    "supports_court_order_c" BOOLEAN,
    "service_selected_c" TEXT,
    "created_by_name" TEXT,
    "account_name" TEXT,
    "assigned_user_name" TEXT,
    "affiliate_id" TEXT,
    "agency_id" TEXT,
    "consent_obtained_c" BOOLEAN,
    "referring_contact_first_name_c" TEXT,
    "referring_contact_last_name_c" TEXT,
    "referring_contact_email_c" TEXT,
    "referring_contact_phone_c" TEXT,
    "converted" TEXT,
    "assigned_user_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Referral_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Mom" (
    "id" TEXT NOT NULL,
    "first_name" TEXT,
    "last_name" TEXT,
    "prospect_status" TEXT,
    "status" TEXT,
    "sub_status_c" TEXT,
    "phone_other" TEXT,
    "currently_pregnant_c" TEXT,
    "number_of_children_c" INTEGER,
    "need_details_c" TEXT,
    "what_else_c" TEXT,
    "caregiver_type_c" TEXT,
    "gender_c" TEXT,
    "preferred_contact_method_c" TEXT,
    "email1" TEXT,
    "referral_type_c" TEXT,
    "supports_court_order_c" BOOLEAN,
    "service_selected_c" TEXT,
    "created_by_name" TEXT,
    "account_name" TEXT,
    "assigned_user_name" TEXT,
    "affiliate_id" TEXT,
    "agency_id" TEXT,
    "consent_obtained_c" BOOLEAN,
    "referring_contact_first_name_c" TEXT,
    "referring_contact_last_name_c" TEXT,
    "referring_contact_email_c" TEXT,
    "referring_contact_phone_c" TEXT,
    "converted" TEXT,
    "birthdate" TIMESTAMP(3),
    "discharge_reason_c" TEXT,
    "closed_date_c" TIMESTAMP(3),
    "language_preference_c" TEXT,
    "language_notes_c" TEXT,
    "languages_c" TEXT,
    "primary_address_street" TEXT,
    "primary_address_city" TEXT,
    "primary_address_state" TEXT,
    "primary_address_postalcode" TEXT,
    "primary_address_country" TEXT,
    "primary_address_county_c" TEXT,
    "connected_benevolance_c" BOOLEAN,
    "connected_childcare_c" BOOLEAN,
    "connected_closet_c" BOOLEAN,
    "connected_education_c" BOOLEAN,
    "connected_health_c" BOOLEAN,
    "connected_housing_c" BOOLEAN,
    "connected_legal_c" BOOLEAN,
    "connected_mental_health_c" BOOLEAN,
    "connected_substance_c" BOOLEAN,
    "photo" TEXT,
    "date_entered" TEXT,
    "address_access_c" TEXT,
    "assigned_user_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Mom_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Affiliate" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "billing_address_street" TEXT,
    "billing_address_street_2" TEXT,
    "billing_address_street_3" TEXT,
    "billing_address_street_4" TEXT,
    "billing_address_city" TEXT,
    "billing_address_state" TEXT,
    "billing_address_postalcode" TEXT,
    "billing_address_country" TEXT,
    "phone_office" TEXT,
    "website" TEXT,
    "email1" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Affiliate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Agency" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "agency_name" TEXT NOT NULL,
    "agency_phone" TEXT,
    "address" TEXT,
    "address_city" TEXT,
    "address_state" TEXT,
    "address_postalcode" TEXT,
    "contact_first_name" TEXT NOT NULL,
    "contact_last_name" TEXT NOT NULL,
    "contact_email" TEXT,
    "contact_phone" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Agency_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Document" (
    "id" TEXT NOT NULL,
    "document_name" TEXT,
    "filecontents" TEXT,
    "filename" TEXT,
    "mimeType" TEXT,
    "description" TEXT,
    "external_url_c" TEXT,
    "subcategory_id" TEXT,
    "mom_id" TEXT,
    "referral_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Document_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WellnessAssessment" (
    "id" TEXT NOT NULL,
    "cc_affordability" TEXT,
    "cc_backup_care" TEXT,
    "cc_childcare_safety" BOOLEAN,
    "cc_ed_concerns" BOOLEAN,
    "cc_health_ins" TEXT,
    "cc_med_access" TEXT,
    "cc_notes" TEXT,
    "cc_reliable_care" TEXT,
    "cc_school" TEXT,
    "cc_school_enrollment" TEXT,
    "cc_score" TEXT,
    "cc_special_ed" BOOLEAN,
    "cc_special_med" TEXT,
    "completed_ahead" BOOLEAN,
    "completed_date" TIMESTAMP(3),
    "cw_active_involvement" TEXT,
    "cw_allegations" TEXT,
    "cw_date_of_involvement" TIMESTAMP(3),
    "cw_fp_goal" TEXT,
    "cw_fp_impact" TEXT,
    "cw_home_status" TEXT,
    "cw_involvement_as_child" TEXT,
    "cw_involvement_as_mom" BOOLEAN,
    "cw_maltreatment_type" TEXT,
    "cw_notes" TEXT,
    "cw_score" TEXT,
    "date_entered" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "date_modified" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN,
    "description" TEXT,
    "emp_afford_food" TEXT,
    "emp_challenges" TEXT,
    "emp_challenges_score" TEXT,
    "emp_concrete_pfs_total" TEXT,
    "emp_diff_manage_bills" TEXT,
    "emp_difficulty" TEXT,
    "emp_duration_current" TEXT,
    "emp_emergency_funds" TEXT,
    "emp_fin_notes" TEXT,
    "emp_fin_struggles" TEXT,
    "emp_fin_struggles_score" TEXT,
    "emp_highest_ed" TEXT,
    "emp_notes" TEXT,
    "emp_score" TEXT,
    "emp_status" TEXT,
    "emp_support_needed" TEXT,
    "emp_support_received" TEXT,
    "emp_work_eligibility" TEXT,
    "fah_child_behavior" TEXT,
    "fah_communication" TEXT,
    "fah_discipline" TEXT,
    "fah_emotions" TEXT,
    "fah_fr_pfs_summary" TEXT,
    "fah_notes" TEXT,
    "fah_nurture_pfs_summary" TEXT,
    "fah_positive_outlook" TEXT,
    "fah_power_struggles" TEXT,
    "fah_score" TEXT,
    "fah_traditions" TEXT,
    "faith_attendance" TEXT,
    "faith_belief_self" TEXT,
    "faith_fulfillment" TEXT,
    "faith_goodness" TEXT,
    "faith_happiness" TEXT,
    "faith_id" TEXT,
    "faith_notes" TEXT,
    "faith_overall_sat" TEXT,
    "faith_purpose" TEXT,
    "faith_purpose_summary" TEXT,
    "faith_purpose_summary_score" TEXT,
    "faith_sacrifice" TEXT,
    "faith_sat_summary" TEXT,
    "faith_sat_summary_score" TEXT,
    "faith_support" BOOLEAN,
    "faith_virtue_summary" TEXT,
    "faith_virtue_summary_score" TEXT,
    "home_category" TEXT,
    "home_name_on_lease" TEXT,
    "home_notes" TEXT,
    "home_perc_toward" TEXT,
    "home_recent_homeless" TEXT,
    "home_risk_in_home" BOOLEAN,
    "home_safe" BOOLEAN,
    "home_score" TEXT,
    "home_security_concerns" TEXT,
    "home_type" TEXT,
    "home_voucher" TEXT,
    "legal_current" TEXT,
    "legal_notes" TEXT,
    "legal_plan_childcare" TEXT,
    "legal_rep" TEXT,
    "legal_rep_access" TEXT,
    "meeting_method" TEXT,
    "name" TEXT,
    "notes_care_of_children" TEXT,
    "rel_dynamics" TEXT,
    "rel_length" TEXT,
    "rel_notes" TEXT,
    "rel_resolve_arguments" TEXT,
    "rel_score" TEXT,
    "rel_status" TEXT,
    "rel_tension" TEXT,
    "rel_wast_sf" TEXT,
    "soc_frs_content_with_rels" TEXT,
    "soc_frs_rel_sat" TEXT,
    "soc_frs_summary" TEXT,
    "soc_frs_summary_score" TEXT,
    "soc_notes" TEXT,
    "soc_pfs_emergency_contact" TEXT,
    "soc_pfs_summary_score" TEXT,
    "soc_pfs_support_goals" TEXT,
    "soc_pfs_supportive_advice" TEXT,
    "soc_pfs_supportive_rels" TEXT,
    "soc_pfs_trusted_network_score" TEXT,
    "soc_score" TEXT,
    "soc_trusted_network" TEXT,
    "staff_name_c" TEXT,
    "start_date_c" TIMESTAMP(3),
    "subs_notes" TEXT,
    "subs_recency" TEXT,
    "subs_support_needed" TEXT,
    "subs_treatment_history" BOOLEAN,
    "trnprt_access" TEXT,
    "trnprt_affordable" TEXT,
    "trnprt_license" TEXT,
    "trnprt_notes" TEXT,
    "trnprt_private_safety" TEXT,
    "trnprt_score" TEXT,
    "trnprt_seat_access" BOOLEAN,
    "well_counseling_interest" TEXT,
    "well_counseling_past" TEXT,
    "well_health_summary" TEXT,
    "well_health_summary_score" TEXT,
    "well_mental_health" TEXT,
    "well_notes" TEXT,
    "well_phq_q1" TEXT,
    "well_phq_q2" TEXT,
    "well_phq_q3" TEXT,
    "well_phq_q4" TEXT,
    "well_phq_q5" TEXT,
    "well_phq_q6" TEXT,
    "well_phq_q7" TEXT,
    "well_phq_q8" TEXT,
    "well_phq_q9" TEXT,
    "well_phq_total" TEXT,
    "well_phq_total_score" TEXT,
    "well_phys_health" TEXT,
    "well_score" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "WellnessAssessment_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Referral" ADD CONSTRAINT "Referral_affiliate_id_fkey" FOREIGN KEY ("affiliate_id") REFERENCES "Affiliate"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Referral" ADD CONSTRAINT "Referral_agency_id_fkey" FOREIGN KEY ("agency_id") REFERENCES "Agency"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Mom" ADD CONSTRAINT "Mom_affiliate_id_fkey" FOREIGN KEY ("affiliate_id") REFERENCES "Affiliate"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Mom" ADD CONSTRAINT "Mom_agency_id_fkey" FOREIGN KEY ("agency_id") REFERENCES "Agency"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_mom_id_fkey" FOREIGN KEY ("mom_id") REFERENCES "Mom"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_referral_id_fkey" FOREIGN KEY ("referral_id") REFERENCES "Referral"("id") ON DELETE SET NULL ON UPDATE CASCADE;
