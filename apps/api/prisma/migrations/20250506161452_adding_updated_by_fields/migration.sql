-- AlterTable
ALTER TABLE "AAPIScore" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "ActionItem" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "AdvocateApplication" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "Affiliate" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "AffiliateAgency" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "Agency" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "AuditLog" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "BenevolenceNeed" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "Child" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "ConnectionLog" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "CoordinatorNote" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "Document" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "DocumentTag" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "Event" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "EventRespondent" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "Goal" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "Lesson" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "LessonTemplate" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "Mom" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "Notification" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "Pairing" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "Role" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "ServiceReferral" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "Session" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "SessionGroup" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "SessionNote" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "Tag" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "Track" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "UserRole" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;

-- AlterTable
ALTER TABLE "WellnessAssessment" ADD COLUMN     "updated_by_id" TEXT,
ADD COLUMN     "updated_by_name" TEXT;
