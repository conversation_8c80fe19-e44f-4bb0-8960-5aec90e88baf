-- CreateTable
CREATE TABLE "AffiliateAgency" (
    "id" TEXT NOT NULL,
    "deleted_at" BIGINT NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" TEXT,
    "created_by_name" TEXT,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "affiliate_id" TEXT NOT NULL,
    "agency_id" TEXT NOT NULL,

    CONSTRAINT "AffiliateAgency_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "AffiliateAgency" ADD CONSTRAINT "AffiliateAgency_affiliate_id_fkey" FOREIGN KEY ("affiliate_id") REFERENCES "Affiliate"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AffiliateAgency" ADD CONSTRAINT "AffiliateAgency_agency_id_fkey" FOREIGN KEY ("agency_id") REFERENCES "Agency"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
