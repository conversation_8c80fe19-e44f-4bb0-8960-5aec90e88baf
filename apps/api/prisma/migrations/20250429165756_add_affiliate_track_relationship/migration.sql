-- Migration: Add many-to-many relationship between Affiliate and Track
-- Creates join table and necessary foreign key constraints

-- CreateTable
CREATE TABLE "_AffiliateToTrack" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_AffiliateToTrack_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_AffiliateToTrack_B_index" ON "_AffiliateToTrack"("B");

-- AddForeignKey
ALTER TABLE "_AffiliateToTrack" ADD CONSTRAINT "_AffiliateToTrack_A_fkey" FOREIGN KEY ("A") REFERENCES "Affiliate"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AffiliateToTrack" ADD CONSTRAINT "_AffiliateToTrack_B_fkey" FOREIGN KEY ("B") REFERENCES "Track"("id") ON DELETE CASCADE ON UPDATE CASCADE;
