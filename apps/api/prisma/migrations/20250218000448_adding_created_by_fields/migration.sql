/*
  Warnings:

  - The `type_c` column on the `BenevolenceNeed` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - Made the column `did_address_need_c` on table `BenevolenceNeed` required. This step will fail if there are existing NULL values in that column.
  - Made the column `is_urgent_c` on table `BenevolenceNeed` required. This step will fail if there are existing NULL values in that column.
  - Made the column `other_is_referral_needed_c` on table `BenevolenceNeed` required. This step will fail if there are existing NULL values in that column.
  - Changed the type of `type_c` on the `CoordinatorNote` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "BenevolenceNeedType" AS ENUM ('Financial', 'Physical', 'Other');

-- CreateEnum
CREATE TYPE "CoordinatorNoteType" AS ENUM ('safety_or_concern_update', 'court_update', 'interview_advocate');

-- AlterTable
ALTER TABLE "ActionItem" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT;

-- AlterTable
ALTER TABLE "Affiliate" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT;

-- AlterTable
ALTER TABLE "Agency" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT;

-- AlterTable
ALTER TABLE "BenevolenceNeed" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT,
ALTER COLUMN "did_address_need_c" SET NOT NULL,
ALTER COLUMN "did_address_need_c" SET DEFAULT false,
ALTER COLUMN "is_urgent_c" SET NOT NULL,
ALTER COLUMN "is_urgent_c" SET DEFAULT false,
ALTER COLUMN "other_is_referral_needed_c" SET NOT NULL,
ALTER COLUMN "other_is_referral_needed_c" SET DEFAULT false,
DROP COLUMN "type_c",
ADD COLUMN     "type_c" "BenevolenceNeedType" NOT NULL DEFAULT 'Financial';

-- AlterTable
ALTER TABLE "ConnectionLog" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT;

-- AlterTable
ALTER TABLE "CoordinatorNote" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT,
DROP COLUMN "type_c",
ADD COLUMN     "type_c" "CoordinatorNoteType" NOT NULL;

-- AlterTable
ALTER TABLE "Document" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT;

-- AlterTable
ALTER TABLE "Goal" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT;

-- AlterTable
ALTER TABLE "Group" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT;

-- AlterTable
ALTER TABLE "GroupMoms" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT;

-- AlterTable
ALTER TABLE "Lesson" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT;

-- AlterTable
ALTER TABLE "LessonTemplate" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT;

-- AlterTable
ALTER TABLE "Mom" ADD COLUMN     "created_by_id" TEXT;

-- AlterTable
ALTER TABLE "Pairing" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT;

-- AlterTable
ALTER TABLE "Role" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT;

-- AlterTable
ALTER TABLE "Session" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT;

-- AlterTable
ALTER TABLE "SessionNote" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT;

-- AlterTable
ALTER TABLE "Track" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT;

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT;

-- AlterTable
ALTER TABLE "UserRole" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT;

-- AlterTable
ALTER TABLE "WellnessAssessment" ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "created_by_name" TEXT;
