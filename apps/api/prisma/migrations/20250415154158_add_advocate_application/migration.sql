-- Create<PERSON>num
CREATE TYPE "ApplicationStatus" AS ENUM ('Pending', 'Approved', 'Rejected');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "Interest" AS ENUM ('Advocate', 'Volunteer');

-- CreateEnum
CREATE TYPE "InterviewMeetingType" AS ENUM ('Virtual', 'InPerson');

-- CreateTable
CREATE TABLE "AdvocateApplication" (
    "id" TEXT NOT NULL,
    "deleted_at" BIGINT NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" TEXT,
    "created_by_name" TEXT,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "phone" TEXT NOT NULL,
    "dateOfBirth" TIMESTAMP(3),
    "addressStreet" TEXT NOT NULL,
    "addressCity" TEXT NOT NULL,
    "addressState" TEXT NOT NULL,
    "addressPostalcode" TEXT NOT NULL,
    "referredBy" TEXT,
    "canCompleteBackground" BOOLEAN NOT NULL DEFAULT true,
    "interests" "Interest"[],
    "hasMultiLang" BOOLEAN NOT NULL DEFAULT false,
    "languages" "Language"[],
    "hasExpCrisis" BOOLEAN NOT NULL DEFAULT false,
    "hasExpFoster" BOOLEAN NOT NULL DEFAULT false,
    "hasExpVictims" BOOLEAN NOT NULL DEFAULT false,
    "hasExpWelfare" BOOLEAN NOT NULL DEFAULT false,
    "hasExpChildren" TEXT,
    "personalNote" TEXT,
    "groupPref" TEXT,
    "parentingNote" TEXT,
    "availability" TEXT,
    "interviewDate" TIMESTAMP(3),
    "interviewStartTime" TEXT,
    "interviewEndTime" TEXT,
    "interviewMeetingType" "InterviewMeetingType",
    "interviewLocation" TEXT,
    "status" "ApplicationStatus" NOT NULL DEFAULT 'Pending',
    "reviewNote" TEXT,
    "reviewedBy" TEXT,
    "reviewedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "userId" TEXT,

    CONSTRAINT "AdvocateApplication_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "AdvocateApplication_userId_key" ON "AdvocateApplication"("userId");

-- AddForeignKey
ALTER TABLE "AdvocateApplication" ADD CONSTRAINT "AdvocateApplication_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
