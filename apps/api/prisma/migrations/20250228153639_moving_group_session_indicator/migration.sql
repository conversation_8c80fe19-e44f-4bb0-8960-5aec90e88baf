/*
  Warnings:

  - You are about to drop the column `is_group_session_note` on the `SessionNote` table. All the data in the column will be lost.
  - Made the column `is_group_session` on table `Session` required. This step will fail if there are existing NULL values in that column.

*/
UPDATE "Session"
SET "is_group_session" = false
WHERE "is_group_session" IS NULL;

-- AlterTable
ALTER TABLE "Session" ALTER COLUMN "is_group_session" SET NOT NULL,
ALTER COLUMN "is_group_session" SET DEFAULT false;

-- AlterTable
ALTER TABLE "SessionNote" DROP COLUMN "is_group_session_note";
