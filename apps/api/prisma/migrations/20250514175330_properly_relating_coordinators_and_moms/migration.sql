/*
  Warnings:

  - The values [new_referral_mom] on the enum `NotificationTemplate` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "NotificationTemplate_new" AS ENUM ('mom_assigned_to_coordinator', 'new_referral_supervisor', 'new_wellness_assessment_coordinator');
ALTER TABLE "Notification" ALTER COLUMN "template" TYPE "NotificationTemplate_new" USING ("template"::text::"NotificationTemplate_new");
ALTER TYPE "NotificationTemplate" RENAME TO "NotificationTemplate_old";
ALTER TYPE "NotificationTemplate_new" RENAME TO "NotificationTemplate";
DROP TYPE "NotificationTemplate_old";
COMMIT;

-- AddForeignKey
ALTER TABLE "Mom" ADD CONSTRAINT "Mom_assigned_user_id_fkey" FOREIGN KEY ("assigned_user_id") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
