-- AlterTable
ALTER TABLE "Session" ADD COLUMN     "session_group_id" TEXT;

-- CreateTable
CREATE TABLE "SessionGroup" (
    "id" TEXT NOT NULL,
    "deleted_at" BIGINT NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" TEXT,
    "created_by_name" TEXT,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SessionGroup_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Session" ADD CONSTRAINT "Session_session_group_id_fkey" FOREIGN KEY ("session_group_id") REFERENCES "SessionGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;
