-- Add all photo-related fields to User table
ALTER TABLE "User"
    ADD COLUMN IF NOT EXISTS "photoUrl" TEXT,
    ADD COLUMN IF NOT EXISTS "photoS3FileName" TEXT,
    ADD COLUMN IF NOT EXISTS "thumbnailUrl" TEXT,
    ADD COLUMN IF NOT EXISTS "thumbnailS3FileName" TEXT,
    ADD COLUMN IF NOT EXISTS "iconUrl" TEXT,
    ADD COLUMN IF NOT EXISTS "iconS3FileName" TEXT;

-- Add all photo-related fields to Mom table
ALTER TABLE "Mom"
    ADD COLUMN IF NOT EXISTS "photoUrl" TEXT,
    ADD COLUMN IF NOT EXISTS "photoS3FileName" TEXT,
    ADD COLUMN IF NOT EXISTS "thumbnailUrl" TEXT,
    ADD COLUMN IF NOT EXISTS "thumbnailS3FileName" TEXT,
    ADD COLUMN IF NOT EXISTS "iconUrl" TEXT,
    ADD COLUMN IF NOT EXISTS "iconS3FileName" TEXT;

-- Try to drop the legacy photo column if it exists
ALTER TABLE "Mom" DROP COLUMN IF EXISTS "photo";

-- Remove old profile picture fields from User table
ALTER TABLE "User"
    DROP COLUMN IF EXISTS "profilePicExternalUrl",
    DROP COLUMN IF EXISTS "profilePicMimeType";
