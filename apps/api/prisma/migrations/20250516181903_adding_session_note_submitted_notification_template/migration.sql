/*
  Warnings:

  - The values [new_referral_mom] on the enum `NotificationTemplate` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "NotificationTemplate_new" AS ENUM ('mom_assigned_to_coordinator', 'new_referral_supervisor', 'session_note_submitted');
ALTER TABLE "Notification" ALTER COLUMN "template" TYPE "NotificationTemplate_new" USING ("template"::text::"NotificationTemplate_new");
ALTER TYPE "NotificationTemplate" RENAME TO "NotificationTemplate_old";
ALTER TYPE "NotificationTemplate_new" RENAME TO "NotificationTemplate";
DROP TYPE "NotificationTemplate_old";
COMMIT;
