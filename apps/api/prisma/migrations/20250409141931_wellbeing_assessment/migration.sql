/*
  Warnings:

  - You are about to drop the column `cc_school` on the `WellnessAssessment` table. All the data in the column will be lost.
  - You are about to drop the column `date_entered` on the `WellnessAssessment` table. All the data in the column will be lost.
  - You are about to drop the column `date_modified` on the `WellnessAssessment` table. All the data in the column will be lost.
  - You are about to drop the column `deleted` on the `WellnessAssessment` table. All the data in the column will be lost.
  - You are about to drop the column `description` on the `WellnessAssessment` table. All the data in the column will be lost.
  - You are about to drop the column `emp_challenges_score` on the `WellnessAssessment` table. All the data in the column will be lost.
  - You are about to drop the column `emp_fin_struggles_score` on the `WellnessAssessment` table. All the data in the column will be lost.
  - You are about to drop the column `faith_purpose_summary_score` on the `WellnessAssessment` table. All the data in the column will be lost.
  - You are about to drop the column `faith_sat_summary_score` on the `WellnessAssessment` table. All the data in the column will be lost.
  - You are about to drop the column `faith_virtue_summary_score` on the `WellnessAssessment` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `WellnessAssessment` table. All the data in the column will be lost.
  - You are about to drop the column `notes_care_of_children` on the `WellnessAssessment` table. All the data in the column will be lost.
  - You are about to drop the column `soc_frs_summary_score` on the `WellnessAssessment` table. All the data in the column will be lost.
  - You are about to drop the column `soc_pfs_trusted_network_score` on the `WellnessAssessment` table. All the data in the column will be lost.
  - You are about to drop the column `well_health_summary_score` on the `WellnessAssessment` table. All the data in the column will be lost.
  - You are about to drop the column `well_phq_total_score` on the `WellnessAssessment` table. All the data in the column will be lost.
  - The `cc_score` column on the `WellnessAssessment` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `cw_score` column on the `WellnessAssessment` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `emp_score` column on the `WellnessAssessment` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `fah_score` column on the `WellnessAssessment` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `home_score` column on the `WellnessAssessment` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `rel_score` column on the `WellnessAssessment` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `soc_score` column on the `WellnessAssessment` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `trnprt_score` column on the `WellnessAssessment` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `well_score` column on the `WellnessAssessment` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- AlterTable
ALTER TABLE "WellnessAssessment" DROP COLUMN "cc_school",
DROP COLUMN "date_entered",
DROP COLUMN "date_modified",
DROP COLUMN "deleted",
DROP COLUMN "description",
DROP COLUMN "emp_challenges_score",
DROP COLUMN "emp_fin_struggles_score",
DROP COLUMN "faith_purpose_summary_score",
DROP COLUMN "faith_sat_summary_score",
DROP COLUMN "faith_virtue_summary_score",
DROP COLUMN "name",
DROP COLUMN "notes_care_of_children",
DROP COLUMN "soc_frs_summary_score",
DROP COLUMN "soc_pfs_trusted_network_score",
DROP COLUMN "well_health_summary_score",
DROP COLUMN "well_phq_total_score",
ADD COLUMN     "faith_score" INTEGER,
ADD COLUMN     "legal_score" INTEGER,
ADD COLUMN     "subs_score" INTEGER,
DROP COLUMN "cc_score",
ADD COLUMN     "cc_score" INTEGER,
DROP COLUMN "cw_score",
ADD COLUMN     "cw_score" INTEGER,
DROP COLUMN "emp_score",
ADD COLUMN     "emp_score" INTEGER,
DROP COLUMN "fah_score",
ADD COLUMN     "fah_score" INTEGER,
DROP COLUMN "home_score",
ADD COLUMN     "home_score" INTEGER,
DROP COLUMN "rel_score",
ADD COLUMN     "rel_score" INTEGER,
DROP COLUMN "soc_score",
ADD COLUMN     "soc_score" INTEGER,
DROP COLUMN "trnprt_score",
ADD COLUMN     "trnprt_score" INTEGER,
DROP COLUMN "well_score",
ADD COLUMN     "well_score" INTEGER;
