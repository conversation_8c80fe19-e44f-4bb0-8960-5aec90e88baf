-- Deleting everything from the wellness assessment table in order to be able to add an non-null mom_id column
DELETE FROM "WellnessAssessment";

/*
  Warnings:

  - The `cw_allegations` column on the `WellnessAssessment` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - Added the required column `mom_id` to the `WellnessAssessment` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "WellnessAssessment" ADD COLUMN     "mom_id" TEXT NOT NULL,
DROP COLUMN "cw_allegations",
ADD COLUMN     "cw_allegations" BOOLEAN;

-- AddForeignKey
ALTER TABLE "WellnessAssessment" ADD CONSTRAINT "WellnessAssessment_mom_id_fkey" FOREIGN KEY ("mom_id") REFERENCES "Mom"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
