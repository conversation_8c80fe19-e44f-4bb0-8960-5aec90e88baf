/*
  Warnings:

  - You are about to drop the column `isDeleted` on the `AAPIScore` table. All the data in the column will be lost.
  - The `deleted_at` column on the `AAPIScore` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `ActionItem` table. All the data in the column will be lost.
  - The `deleted_at` column on the `ActionItem` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `Affiliate` table. All the data in the column will be lost.
  - The `deleted_at` column on the `Affiliate` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `Agency` table. All the data in the column will be lost.
  - The `deleted_at` column on the `Agency` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `BenevolenceNeed` table. All the data in the column will be lost.
  - The `deleted_at` column on the `BenevolenceNeed` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `ConnectionLog` table. All the data in the column will be lost.
  - The `deleted_at` column on the `ConnectionLog` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `CoordinatorNote` table. All the data in the column will be lost.
  - The `deleted_at` column on the `CoordinatorNote` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `Document` table. All the data in the column will be lost.
  - The `deleted_at` column on the `Document` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `Event` table. All the data in the column will be lost.
  - The `deleted_at` column on the `Event` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `EventRespondent` table. All the data in the column will be lost.
  - The `deleted_at` column on the `EventRespondent` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `Goal` table. All the data in the column will be lost.
  - The `deleted_at` column on the `Goal` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `Lesson` table. All the data in the column will be lost.
  - The `deleted_at` column on the `Lesson` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `LessonTemplate` table. All the data in the column will be lost.
  - The `deleted_at` column on the `LessonTemplate` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `Mom` table. All the data in the column will be lost.
  - The `deleted_at` column on the `Mom` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `Notification` table. All the data in the column will be lost.
  - The `deleted_at` column on the `Notification` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `Pairing` table. All the data in the column will be lost.
  - The `deleted_at` column on the `Pairing` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `Role` table. All the data in the column will be lost.
  - The `deleted_at` column on the `Role` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `Session` table. All the data in the column will be lost.
  - The `deleted_at` column on the `Session` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `SessionNote` table. All the data in the column will be lost.
  - The `deleted_at` column on the `SessionNote` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `SessionPairing` table. All the data in the column will be lost.
  - The `deleted_at` column on the `SessionPairing` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `Track` table. All the data in the column will be lost.
  - The `deleted_at` column on the `Track` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `User` table. All the data in the column will be lost.
  - The `deleted_at` column on the `User` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `UserRole` table. All the data in the column will be lost.
  - The `deleted_at` column on the `UserRole` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `isDeleted` on the `WellnessAssessment` table. All the data in the column will be lost.
  - The `deleted_at` column on the `WellnessAssessment` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - A unique constraint covering the columns `[username,deleted_at]` on the table `User` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "User_username_key";

-- AlterTable
ALTER TABLE "AAPIScore" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "ActionItem" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "Affiliate" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "Agency" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "BenevolenceNeed" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "ConnectionLog" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "CoordinatorNote" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "Document" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "Event" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "EventRespondent" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "Goal" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "Lesson" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "LessonTemplate" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "Mom" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "Notification" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "Pairing" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "Role" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "Session" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "SessionNote" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "SessionPairing" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "Track" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "User" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "UserRole" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "WellnessAssessment" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" INTEGER NOT NULL DEFAULT 0;

-- CreateIndex
CREATE UNIQUE INDEX "User_username_deleted_at_key" ON "User"("username", "deleted_at");
