/*
  Warnings:

  - You are about to drop the column `content` on the `Notification` table. All the data in the column will be lost.
  - You are about to drop the column `subject` on the `Notification` table. All the data in the column will be lost.
  - You are about to drop the column `type` on the `Notification` table. All the data in the column will be lost.

*/
-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "NotificationTemplate" ADD VALUE 'new_referral_supervisor';
ALTER TYPE "NotificationTemplate" ADD VALUE 'new_referral_mom';

-- AlterTable
ALTER TABLE "Notification" DROP COLUMN "content",
DROP COLUMN "subject",
DROP COLUMN "type";

-- DropEnum
DROP TYPE "NotificationType";
