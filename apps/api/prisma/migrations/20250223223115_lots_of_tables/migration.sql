/*
  Warnings:

  - You are about to drop the column `status` on the `ActionItem` table. All the data in the column will be lost.
  - You are about to drop the column `user_id` on the `CoordinatorNote` table. All the data in the column will be lost.
  - The `status` column on the `Goal` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `groupId` on the `Pairing` table. All the data in the column will be lost.
  - The `status` column on the `Pairing` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `session_type_c` on the `Session` table. All the data in the column will be lost.
  - The `status` column on the `Session` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `name` on the `Track` table. All the data in the column will be lost.
  - The `communication_preference` column on the `User` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `status` column on the `User` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `advocate_status` column on the `User` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the `Group` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `GroupMoms` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[completed_lesson_id]` on the table `Session` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[session_id]` on the table `SessionNote` will be added. If there are existing duplicate values, this will fail.
  - Changed the type of `contact_method_c` on the `ConnectionLog` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Added the required column `title` to the `Lesson` table without a default value. This is not possible if the table is not empty.
  - Added the required column `title` to the `LessonTemplate` table without a default value. This is not possible if the table is not empty.
  - Added the required column `track_id` to the `LessonTemplate` table without a default value. This is not possible if the table is not empty.
  - Added the required column `name` to the `SessionNote` table without a default value. This is not possible if the table is not empty.
  - Added the required column `session_id` to the `SessionNote` table without a default value. This is not possible if the table is not empty.
  - Added the required column `title` to the `Track` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "AdvocateStatusType" AS ENUM ('Applied', 'Interested', 'Rejected', 'Inactive', 'Active', 'In_Training', 'Awaiting_Pairing');

-- CreateEnum
CREATE TYPE "UserStatusType" AS ENUM ('Active', 'Inactive');

-- CreateEnum
CREATE TYPE "CommunicationPreferenceType" AS ENUM ('text_message', 'email', 'both');

-- CreateEnum
CREATE TYPE "ContactMethodType" AS ENUM ('Call', 'SMS_Text', 'Email', 'In_Person', 'Video');

-- CreateEnum
CREATE TYPE "PairingStatusType" AS ENUM ('waiting_to_be_paired', 'paired', 'pairing_complete');

-- CreateEnum
CREATE TYPE "LanguageType" AS ENUM ('english', 'spanish');

-- CreateEnum
CREATE TYPE "LessonStatusType" AS ENUM ('not_started', 'in_progress', 'completed');

-- CreateEnum
CREATE TYPE "SessionStatusType" AS ENUM ('Planned', 'Held', 'NotHeld');

-- CreateEnum
CREATE TYPE "SessionType" AS ENUM ('Support_Session', 'Track_Session', 'Referral_Session');

-- CreateEnum
CREATE TYPE "AttendanceAndPromptnessType" AS ENUM ('On_Time', 'Late', 'No_Show');

-- CreateEnum
CREATE TYPE "MomEngagementType" AS ENUM ('Full', 'Partial', 'None');

-- CreateEnum
CREATE TYPE "SessionNoteStatusType" AS ENUM ('submitted', 'new', 'rejected', 'approved');

-- CreateEnum
CREATE TYPE "NotificationType" AS ENUM ('sms', 'email');

-- CreateEnum
CREATE TYPE "GoalStatusType" AS ENUM ('NotStarted', 'InProgress', 'Completed');

-- DropForeignKey
ALTER TABLE "CoordinatorNote" DROP CONSTRAINT "CoordinatorNote_user_id_fkey";

-- DropForeignKey
ALTER TABLE "GroupMoms" DROP CONSTRAINT "GroupMoms_groupId_fkey";

-- DropForeignKey
ALTER TABLE "GroupMoms" DROP CONSTRAINT "GroupMoms_momId_fkey";

-- DropForeignKey
ALTER TABLE "Pairing" DROP CONSTRAINT "Pairing_groupId_fkey";

-- AlterTable
ALTER TABLE "ActionItem" DROP COLUMN "status",
ADD COLUMN     "doneDate" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "ConnectionLog" DROP COLUMN "contact_method_c",
ADD COLUMN     "contact_method_c" "ContactMethodType" NOT NULL;

-- AlterTable
ALTER TABLE "CoordinatorNote" DROP COLUMN "user_id",
ADD COLUMN     "advocate_id" TEXT,
ADD COLUMN     "coordinator_id" TEXT;

-- AlterTable
ALTER TABLE "Document" ADD COLUMN     "lesson_id" TEXT,
ADD COLUMN     "lesson_template_id" TEXT;

-- AlterTable
ALTER TABLE "Goal" DROP COLUMN "status",
ADD COLUMN     "status" "GoalStatusType";

-- AlterTable
ALTER TABLE "Lesson" ADD COLUMN     "description" TEXT,
ADD COLUMN     "duration_days" INTEGER,
ADD COLUMN     "order" INTEGER,
ADD COLUMN     "priority" INTEGER,
ADD COLUMN     "status" "LessonStatusType",
ADD COLUMN     "title" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "LessonTemplate" ADD COLUMN     "description" TEXT,
ADD COLUMN     "duration_days" INTEGER,
ADD COLUMN     "order" INTEGER,
ADD COLUMN     "priority" INTEGER,
ADD COLUMN     "title" TEXT NOT NULL,
ADD COLUMN     "track_id" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "Pairing" DROP COLUMN "groupId",
DROP COLUMN "status",
ADD COLUMN     "status" "PairingStatusType";

-- AlterTable
ALTER TABLE "Session" DROP COLUMN "session_type_c",
ADD COLUMN     "completed_lesson_id" TEXT,
ADD COLUMN     "session_type" "SessionType",
DROP COLUMN "status",
ADD COLUMN     "status" "SessionStatusType";

-- AlterTable
ALTER TABLE "SessionNote" ADD COLUMN     "attendance_and_promptness" "AttendanceAndPromptnessType",
ADD COLUMN     "date_submitted_c" TIMESTAMP(3),
ADD COLUMN     "description" TEXT,
ADD COLUMN     "moms_engagement_c" "MomEngagementType",
ADD COLUMN     "name" TEXT NOT NULL,
ADD COLUMN     "new_attempt" BOOLEAN,
ADD COLUMN     "new_attempt_example" TEXT,
ADD COLUMN     "note" TEXT,
ADD COLUMN     "session_id" TEXT NOT NULL,
ADD COLUMN     "status" "SessionNoteStatusType";

-- AlterTable
ALTER TABLE "Track" DROP COLUMN "name",
ADD COLUMN     "connection_description" TEXT,
ADD COLUMN     "language_type" "LanguageType",
ADD COLUMN     "mom_summary" TEXT,
ADD COLUMN     "title" TEXT NOT NULL,
ADD COLUMN     "track_summary" TEXT;

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "address_city" TEXT,
ADD COLUMN     "address_postalcode" TEXT,
ADD COLUMN     "address_state" TEXT,
ADD COLUMN     "address_street" TEXT,
ADD COLUMN     "description" TEXT,
ADD COLUMN     "phone_home" TEXT,
ADD COLUMN     "phone_mobile" TEXT,
ADD COLUMN     "phone_other" TEXT,
ADD COLUMN     "phone_work" TEXT,
DROP COLUMN "communication_preference",
ADD COLUMN     "communication_preference" "CommunicationPreferenceType",
DROP COLUMN "status",
ADD COLUMN     "status" "UserStatusType",
DROP COLUMN "advocate_status",
ADD COLUMN     "advocate_status" "AdvocateStatusType";

-- DropTable
DROP TABLE "Group";

-- DropTable
DROP TABLE "GroupMoms";

-- CreateTable
CREATE TABLE "AAPIScore" (
    "id" TEXT NOT NULL,
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,
    "deleted_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" TEXT,
    "created_by_name" TEXT,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "mom_id" TEXT NOT NULL,
    "constructAPreAssessment" DOUBLE PRECISION,
    "constructAPostAssessment" DOUBLE PRECISION,
    "constructBPreAssessment" DOUBLE PRECISION,
    "constructBPostAssessment" DOUBLE PRECISION,
    "constructCPreAssessment" DOUBLE PRECISION,
    "constructCPostAssessment" DOUBLE PRECISION,
    "constructDPreAssessment" DOUBLE PRECISION,
    "constructDPostAssessment" DOUBLE PRECISION,
    "constructEPreAssessment" DOUBLE PRECISION,
    "constructEPostAssessment" DOUBLE PRECISION,

    CONSTRAINT "AAPIScore_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SessionPairing" (
    "id" TEXT NOT NULL,
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,
    "deleted_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" TEXT,
    "created_by_name" TEXT,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "pairing_id" TEXT NOT NULL,
    "session_id" TEXT NOT NULL,

    CONSTRAINT "SessionPairing_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EventRespondent" (
    "id" TEXT NOT NULL,
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,
    "deleted_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" TEXT,
    "created_by_name" TEXT,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "event_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "hasBeenInvited" BOOLEAN NOT NULL DEFAULT false,
    "didRsvp" BOOLEAN NOT NULL DEFAULT false,
    "didCheckin" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "EventRespondent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Event" (
    "id" TEXT NOT NULL,
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,
    "deleted_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" TEXT,
    "created_by_name" TEXT,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "event_title" TEXT NOT NULL,
    "description" TEXT,
    "start_date" TIMESTAMP(3),
    "end_date" TIMESTAMP(3),
    "location" TEXT,
    "join_url" TEXT,

    CONSTRAINT "Event_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Notification" (
    "id" TEXT NOT NULL,
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,
    "deleted_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" TEXT,
    "created_by_name" TEXT,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "subject" TEXT NOT NULL,
    "content" TEXT,
    "type" "NotificationType" NOT NULL,
    "mom_id" TEXT,
    "recipient_user_id" TEXT,
    "sending_user_id" TEXT,
    "event_id" TEXT,
    "status" TEXT,
    "date_sent" TIMESTAMP(3),
    "date_received" TIMESTAMP(3),
    "date_read" TIMESTAMP(3),
    "date_replied" TIMESTAMP(3),

    CONSTRAINT "Notification_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "AAPIScore_mom_id_key" ON "AAPIScore"("mom_id");

-- CreateIndex
CREATE UNIQUE INDEX "Session_completed_lesson_id_key" ON "Session"("completed_lesson_id");

-- CreateIndex
CREATE UNIQUE INDEX "SessionNote_session_id_key" ON "SessionNote"("session_id");

-- AddForeignKey
ALTER TABLE "AAPIScore" ADD CONSTRAINT "AAPIScore_mom_id_fkey" FOREIGN KEY ("mom_id") REFERENCES "Mom"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LessonTemplate" ADD CONSTRAINT "LessonTemplate_track_id_fkey" FOREIGN KEY ("track_id") REFERENCES "Track"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_lesson_template_id_fkey" FOREIGN KEY ("lesson_template_id") REFERENCES "LessonTemplate"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_lesson_id_fkey" FOREIGN KEY ("lesson_id") REFERENCES "Lesson"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Session" ADD CONSTRAINT "Session_completed_lesson_id_fkey" FOREIGN KEY ("completed_lesson_id") REFERENCES "Lesson"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SessionNote" ADD CONSTRAINT "SessionNote_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "Session"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SessionPairing" ADD CONSTRAINT "SessionPairing_pairing_id_fkey" FOREIGN KEY ("pairing_id") REFERENCES "Pairing"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SessionPairing" ADD CONSTRAINT "SessionPairing_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "Session"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CoordinatorNote" ADD CONSTRAINT "CoordinatorNote_coordinator_id_fkey" FOREIGN KEY ("coordinator_id") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CoordinatorNote" ADD CONSTRAINT "CoordinatorNote_advocate_id_fkey" FOREIGN KEY ("advocate_id") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EventRespondent" ADD CONSTRAINT "EventRespondent_event_id_fkey" FOREIGN KEY ("event_id") REFERENCES "Event"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EventRespondent" ADD CONSTRAINT "EventRespondent_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_mom_id_fkey" FOREIGN KEY ("mom_id") REFERENCES "Mom"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_recipient_user_id_fkey" FOREIGN KEY ("recipient_user_id") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_sending_user_id_fkey" FOREIGN KEY ("sending_user_id") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_event_id_fkey" FOREIGN KEY ("event_id") REFERENCES "Event"("id") ON DELETE SET NULL ON UPDATE CASCADE;
