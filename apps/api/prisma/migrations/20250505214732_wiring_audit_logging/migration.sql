-- Please note: Any time new tables are created, they will need to have the log_audit_changes trigger wired up to them manually in the migration file

CREATE OR REPLACE FUNCTION log_audit_changes() RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'DELETE' THEN
    INSERT INTO "AuditLog" ("id", "table", "action", "data", "updated_at")
    VALUES (gen_random_uuid(), TG_TABLE_NAME, 'Delete', row_to_json(OLD), CURRENT_TIMESTAMP);
    RETURN OLD;
  ELSIF TG_OP = 'UPDATE' THEN
    INSERT INTO "AuditLog" ("id", "table", "action", "data", "updated_at")
    VALUES (gen_random_uuid(), TG_TABLE_NAME, 'Update', row_to_json(NEW), CURRENT_TIMESTAMP);
    RETURN NEW;
  ELSIF TG_OP = 'INSERT' THEN
    INSERT INTO "AuditLog" ("id", "table", "action", "data", "updated_at")
    VALUES (gen_random_uuid(), TG_TABLE_NAME, 'Create', row_to_json(NEW), CURRENT_TIMESTAMP);
    RETURN NEW;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER audit_aapi_scores
AFTER INSERT OR UPDATE OR DELETE ON "AAPIScore"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_action_items
AFTER INSERT OR UPDATE OR DELETE ON "ActionItem"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_advocate_applications
AFTER INSERT OR UPDATE OR DELETE ON "AdvocateApplication"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_affiliates
AFTER INSERT OR UPDATE OR DELETE ON "Affiliate"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_agencies
AFTER INSERT OR UPDATE OR DELETE ON "Agency"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_benevolence_needs
AFTER INSERT OR UPDATE OR DELETE ON "BenevolenceNeed"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_children
AFTER INSERT OR UPDATE OR DELETE ON "Child"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_connection_logs
AFTER INSERT OR UPDATE OR DELETE ON "ConnectionLog"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_coordinator_notes
AFTER INSERT OR UPDATE OR DELETE ON "CoordinatorNote"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_documents
AFTER INSERT OR UPDATE OR DELETE ON "Document"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_event_respondents
AFTER INSERT OR UPDATE OR DELETE ON "EventRespondent"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_events
AFTER INSERT OR UPDATE OR DELETE ON "Event"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_goals
AFTER INSERT OR UPDATE OR DELETE ON "Goal"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_lesson_templates
AFTER INSERT OR UPDATE OR DELETE ON "LessonTemplate"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_lessons
AFTER INSERT OR UPDATE OR DELETE ON "Lesson"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_moms
AFTER INSERT OR UPDATE OR DELETE ON "Mom"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_notifications
AFTER INSERT OR UPDATE OR DELETE ON "Notification"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_pairings
AFTER INSERT OR UPDATE OR DELETE ON "Pairing"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_roles
AFTER INSERT OR UPDATE OR DELETE ON "Role"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_service_referrals
AFTER INSERT OR UPDATE OR DELETE ON "ServiceReferral"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_session_groups
AFTER INSERT OR UPDATE OR DELETE ON "SessionGroup"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_session_notes
AFTER INSERT OR UPDATE OR DELETE ON "SessionNote"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_sessions
AFTER INSERT OR UPDATE OR DELETE ON "Session"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_tracks
AFTER INSERT OR UPDATE OR DELETE ON "Track"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_user_roles
AFTER INSERT OR UPDATE OR DELETE ON "UserRole"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_users
AFTER INSERT OR UPDATE OR DELETE ON "User"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_wellness_assessments
AFTER INSERT OR UPDATE OR DELETE ON "WellnessAssessment"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_tags
AFTER INSERT OR UPDATE OR DELETE ON "Tag"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_document_tags
AFTER INSERT OR UPDATE OR DELETE ON "DocumentTag"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

CREATE OR REPLACE TRIGGER audit_affiliate_agencies
AFTER INSERT OR UPDATE OR DELETE ON "AffiliateAgency"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();

