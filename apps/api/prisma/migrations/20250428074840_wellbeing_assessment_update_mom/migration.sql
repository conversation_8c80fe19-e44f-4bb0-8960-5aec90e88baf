/*
  Warnings:

  - You are about to drop the column `last_name` on the `Child` table. All the data in the column will be lost.
  - Made the column `first_name` on table `Child` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "EventRespondent" DROP CONSTRAINT "EventRespondent_mom_id_fkey";

-- DropForeignKey
ALTER TABLE "EventRespondent" DROP CONSTRAINT "EventRespondent_user_id_fkey";

-- AlterTable
ALTER TABLE "Child" DROP COLUMN "last_name",
ADD COLUMN     "active_child_welfare_involvement" TEXT,
ADD COLUMN     "date_of_child_welfare_involvement" TIMESTAMP(3),
ADD COLUMN     "family_preservation_goal" TEXT,
ADD COLUMN     "family_preservation_impact" TEXT,
ALTER COLUMN "first_name" SET NOT NULL;

-- AlterTable
ALTER TABLE "Mom" ADD COLUMN     "cultural_heritage_c" TEXT,
ADD COLUMN     "emergency_contact_name_c" TEXT,
ADD COLUMN     "emergency_contact_number_c" TEXT,
ADD COLUMN     "emergency_contact_relation_c" TEXT,
ADD COLUMN     "languages" TEXT,
ADD COLUMN     "martial_status" TEXT,
ADD COLUMN     "number_of_children_in_home_c" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "phone_alternate_c" TEXT,
ADD COLUMN     "pregnant_due_date" TIMESTAMP(3),
ADD COLUMN     "primary_address_street_two_c" TEXT,
ADD COLUMN     "race_c" TEXT;

-- AddForeignKey
ALTER TABLE "EventRespondent" ADD CONSTRAINT "EventRespondent_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EventRespondent" ADD CONSTRAINT "EventRespondent_mom_id_fkey" FOREIGN KEY ("mom_id") REFERENCES "Mom"("id") ON DELETE SET NULL ON UPDATE CASCADE;
