-- CreateTable
CREATE TABLE "Child" (
    "id" TEXT NOT NULL,
    "deleted_at" BIGINT NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" TEXT,
    "created_by_name" TEXT,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "first_name" TEXT,
    "last_name" TEXT,
    "birthdate" TIMESTAMP(3),
    "gender" TEXT,
    "lives_with" TEXT,
    "legal_custody_status" TEXT,
    "father_involved" TEXT[],
    "father_involvement" TEXT,
    "additional_info" TEXT,
    "mom_id" TEXT NOT NULL,

    CONSTRAINT "Child_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Child" ADD CONSTRAINT "Child_mom_id_fkey" FOREIGN KEY ("mom_id") REFERENCES "Mom"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Update number_of_children_c field in Mom table
UPDATE "Mom" SET "number_of_children_c" = 0 WHERE "number_of_children_c" IS NULL;
ALTER TABLE "Mom" ALTER COLUMN "number_of_children_c" SET DEFAULT 0;
ALTER TABLE "Mom" ALTER COLUMN "number_of_children_c" SET NOT NULL;
ALTER TABLE "Mom" ADD COLUMN IF NOT EXISTS "children_in_home" INTEGER NOT NULL DEFAULT 0;
