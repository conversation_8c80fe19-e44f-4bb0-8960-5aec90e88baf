/*
  Warnings:

  - Added the required column `template` to the `Notification` table without a default value. This is not possible if the table is not empty.
  - Added the required column `status` to the `Notification` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "NotificationStatus" AS ENUM ('pending', 'processing', 'sent', 'failed');

-- CreateEnum
CREATE TYPE "NotificationTemplate" AS ENUM ('mom_assigned_to_coordinator');

-- DropForeignKey
ALTER TABLE "EventRespondent" DROP CONSTRAINT "EventRespondent_mom_id_fkey";

-- DropForeignKey
ALTER TABLE "EventRespondent" DROP CONSTRAINT "EventRespondent_user_id_fkey";

-- AlterTable
ALTER TABLE "Notification" ADD COLUMN     "failure_reason" TEXT,
ADD COLUMN     "template" "NotificationTemplate" NOT NULL,
DROP COLUMN "status",
ADD COLUMN     "status" "NotificationStatus" NOT NULL;

-- AddForeignKey
ALTER TABLE "EventRespondent" ADD CONSTRAINT "EventRespondent_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EventRespondent" ADD CONSTRAINT "EventRespondent_mom_id_fkey" FOREIGN KEY ("mom_id") REFERENCES "Mom"("id") ON DELETE SET NULL ON UPDATE CASCADE;
