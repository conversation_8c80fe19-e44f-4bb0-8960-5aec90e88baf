ALTER TABLE "Mom" RENAME COLUMN "sub_status_c" TO "referral_sub_status";

CREATE TYPE "ReferralSubStatus" AS ENUM ('unable_to_contact', 'client_declined_services', 'ineligible_for_program', 'duplicate_referral', 'referred_to_alternate_program');
CREATE TYPE "ProspectStatus" AS ENUM ('prospect', 'engaged_in_program', 'did_not_engage_in_program', 'prospect_intake_scheduled');

ALTER TABLE "Mom" DROP COLUMN "prospect_status",
ADD  COLUMN "prospect_status" "ProspectStatus",
DROP COLUMN "referral_sub_status",
ADD  COLUMN "referral_sub_status" "ReferralSubStatus";

ALTER TABLE "Mom" ADD COLUMN     "referred_to_agency_id" TEXT;
ALTER TABLE "Mom" ADD CONSTRAINT "Mom_referred_to_agency_id_fkey" FOREIGN KEY ("referred_to_agency_id") REFERENCES "Agency"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "Mom" ADD COLUMN     "referred_to_agency_reason" TEXT;