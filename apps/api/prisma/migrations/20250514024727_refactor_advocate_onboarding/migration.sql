-- CreateEnum
CREATE TYPE "AdvocateStatus" AS ENUM ('Applied', 'Interested', 'Rejected', 'Inactive', 'Active', 'In_Training', 'Awaiting_Pairing');
CREATE TYPE "UserStatus" AS ENUM ('Active', 'Inactive');
CREATE TYPE "CommunicationPreference" AS ENUM ('text_message', 'email', 'both');
CREATE TYPE "MeetingType" AS ENUM ('Virtual', 'InPerson');

-- DropForeignKey
ALTER TABLE "AdvocateApplication" DROP CONSTRAINT "AdvocateApplication_userId_fkey";

-- AlterTable
ALTER TABLE "Mom" DROP COLUMN "preferred_contact_method_c",
ADD COLUMN     "preferred_contact_method_c" "CommunicationPreference";

-- AlterTable
ALTER TABLE "User" DROP COLUMN "communication_preference",
ADD COLUMN     "communication_preference" "CommunicationPreference",
DROP COLUMN "status",
ADD COLUMN     "status" "UserStatus",
DROP COLUMN "advocate_status",
ADD COLUMN     "advocate_status" "AdvocateStatus";

-- DropTable
DROP TABLE "AdvocateApplication";

-- DropEnum
DROP TYPE "InterviewMeetingType";
DROP TYPE "AdvocateStatusType";
DROP TYPE "ApplicationStatus";
DROP TYPE "CommunicationPreferenceType";
DROP TYPE "UserStatusType";

-- CreateTable
CREATE TABLE "AdvocateOnboarding" (
    "id" TEXT NOT NULL,
    "deleted_at" BIGINT NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" TEXT,
    "created_by_name" TEXT,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" TEXT,
    "updated_by_name" TEXT,
    "referredBy" TEXT,
    "canCompleteBackground" BOOLEAN NOT NULL DEFAULT true,
    "interests" "Interest"[],
    "hasMultiLang" BOOLEAN NOT NULL DEFAULT false,
    "languages" "Language"[],
    "hasExpCrisis" BOOLEAN NOT NULL DEFAULT false,
    "hasExpFoster" BOOLEAN NOT NULL DEFAULT false,
    "hasExpVictims" BOOLEAN NOT NULL DEFAULT false,
    "hasExpWelfare" BOOLEAN NOT NULL DEFAULT false,
    "hasExpChildren" TEXT,
    "personalNote" TEXT,
    "groupPref" TEXT,
    "parentingNote" TEXT,
    "availability" TEXT,
    "interviewDate" TIMESTAMP(3),
    "interviewStartTime" TEXT,
    "interviewEndTime" TEXT,
    "interviewMeetingType" "MeetingType",
    "interviewLocation" TEXT,
    "reviewNote" TEXT,
    "reviewedBy" TEXT,
    "reviewedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "userId" TEXT,
    "trackingToken" TEXT,

    CONSTRAINT "AdvocateOnboarding_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "AdvocateOnboarding_userId_key" ON "AdvocateOnboarding"("userId");
CREATE UNIQUE INDEX "AdvocateOnboarding_trackingToken_key" ON "AdvocateOnboarding"("trackingToken");

-- AddForeignKey
ALTER TABLE "AdvocateOnboarding" ADD CONSTRAINT "AdvocateOnboarding_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Create audit trigger for AdvocateOnboarding
CREATE TRIGGER advocate_onboarding_audit_trigger
AFTER INSERT OR UPDATE OR DELETE ON "AdvocateOnboarding"
FOR EACH ROW EXECUTE FUNCTION log_audit_changes();
