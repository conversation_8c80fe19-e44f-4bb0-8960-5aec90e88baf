/*
  Warnings:

  - You are about to drop the column `isDeleted` on the `ServiceReferral` table. All the data in the column will be lost.
  - The `deleted_at` column on the `ServiceReferral` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - Changed the type of `service` on the `ServiceReferral` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `outcome` on the `ServiceReferral` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "ServiceType" AS ENUM ('benevolence', 'care_communities', 'childcare', 'closet_visit', 'crisis_resources', 'education', 'group_parenting', 'health', 'housing', 'legal', 'mental_health', 'substance', 'therapy');

-- CreateEnum
CREATE TYPE "OutcomeType" AS ENUM ('successful', 'unsuccessful', 'unknown');

-- AlterTable
ALTER TABLE "ServiceReferral" DROP COLUMN "isDeleted",
DROP COLUMN "deleted_at",
ADD COLUMN     "deleted_at" BIGINT NOT NULL DEFAULT 0,
DROP COLUMN "service",
ADD COLUMN     "service" "ServiceType" NOT NULL,
DROP COLUMN "outcome",
ADD COLUMN     "outcome" "OutcomeType" NOT NULL;
