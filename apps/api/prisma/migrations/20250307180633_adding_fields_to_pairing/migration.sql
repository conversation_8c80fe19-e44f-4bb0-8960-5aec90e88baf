-- CreateEnum
CREATE TYPE "TrackStatusType" AS ENUM ('in_program', 'discharged_incomplete', 'complete');

-- CreateEnum
CREATE TYPE "InProgramTrackSubStatusType" AS ENUM ('waiting_to_begin', 'session_in_progress');

-- CreateEnum
CREATE TYPE "DischargeIncompleteSubStatusType" AS ENUM ('track_requirements_unmet', 'client_choice', 'relocated', 'other');

-- Create<PERSON>num
CREATE TYPE "IncompleteReasonSubStatusType" AS ENUM ('no_advocate', 'extended_wait', 'priorities_shifted', 'achieved_outcomes');

-- CreateEnum
CREATE TYPE "CompleteReasonSubStatusType" AS ENUM ('completed_full_track', 'completed_without_support_sessions', 'completed_without_post_assessment');

-- AlterTable
ALTER TABLE "Pairing" ADD COLUMN     "complete_reason_sub_status" "CompleteReasonSubStatusType",
ADD COLUMN     "discharge_incomplete_sub_status" "DischargeIncompleteSubStatusType",
ADD COLUMN     "in_program_track_sub_status" "InProgramTrackSubStatusType",
ADD COLUMN     "incomplete_reason_sub_status" "IncompleteReasonSubStatusType",
ADD COLUMN     "track_status" "TrackStatusType";
