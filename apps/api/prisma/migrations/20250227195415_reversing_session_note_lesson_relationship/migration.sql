/*
  Warnings:

  - You are about to drop the column `covered_lesson_id` on the `SessionNote` table. All the data in the column will be lost.
  - You are about to drop the column `lesson_template_id` on the `SessionNote` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "SessionNote" DROP CONSTRAINT "SessionNote_covered_lesson_id_fkey";

-- DropForeignKey
ALTER TABLE "SessionNote" DROP CONSTRAINT "SessionNote_lesson_template_id_fkey";

-- AlterTable
ALTER TABLE "Lesson" ADD COLUMN     "covered_in_session_note_id" TEXT;

-- AlterTable
ALTER TABLE "SessionNote" DROP COLUMN "covered_lesson_id",
DROP COLUMN "lesson_template_id",
ADD COLUMN     "covered_lesson_template_id" TEXT,
ADD COLUMN     "is_group_session_note" BOOLEAN NOT NULL DEFAULT false;

-- AddForeignKey
ALTER TABLE "Lesson" ADD CONSTRAINT "Lesson_covered_in_session_note_id_fkey" FOREIGN KEY ("covered_in_session_note_id") REFERENCES "SessionNote"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SessionNote" ADD CONSTRAINT "SessionNote_covered_lesson_template_id_fkey" FOREIGN KEY ("covered_lesson_template_id") REFERENCES "LessonTemplate"("id") ON DELETE SET NULL ON UPDATE CASCADE;
