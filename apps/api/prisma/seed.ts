import { seedUsers } from './seeds/users';
import { seedRoles } from './seeds/roles';
import { seedMoms } from './seeds/moms';
import { seedAffiliates } from './seeds/affiliates';
import { seedAgencies } from './seeds/agencies';
import { seedBenevolenceNeeds } from './seeds/benevolence-need';
import { seedTracks } from './seeds/tracks';
import { seedPairings } from './seeds/pairings';
import { seedGoals } from './seeds/goals';
import { seedConnectionLogs } from './seeds/connection-logs';
import { seedActionItems } from './seeds/action-items';
import { seedCoordinatorNotes } from './seeds/coordinator-notes';
import { seedSessions } from './seeds/sessions';
import { seedLessonTemplates } from './seeds/lesson-templates';
import { seedLessons } from './seeds/lessons';
import { seedSessionNotes } from './seeds/session-notes';
import { seedEvents } from './seeds/events';
import { seedRespondents } from './seeds/respondents';
import { seedTags } from './seeds/tags';
import { seedDocumentTags } from './seeds/document-tags';
import { seedChildren } from './seeds/children';
import { seedAdvocateOnboardings } from './seeds/advocate-onboardings';
import { seedWellbeingAssessment } from './seeds/well-being-assessments';
import { seedAssessments } from './seeds/assessments';
import { seedAssessmentConstructs } from './seeds/assessment-constructs';
import { seedEmpoweredParentingAssessmentQuestions } from './seeds/assessment-questions-empowered-parenting';
import { seedResilienceAssessmentQuestions } from './seeds/assessment-questions-resilience';

async function main() {
  await seedAgencies();
  await seedAffiliates();
  await seedRoles();
  await seedUsers();
  await seedMoms();
  await seedWellbeingAssessment();
  await seedChildren();
  await seedConnectionLogs();
  await seedTags();
  await seedTracks();
  await seedLessonTemplates();
  await seedPairings();
  await seedSessions();
  await seedLessons();
  await seedSessionNotes();
  await seedGoals();
  await seedActionItems();
  await seedBenevolenceNeeds();
  await seedCoordinatorNotes();
  await seedEvents();
  await seedRespondents();
  await seedDocumentTags();
  await seedAdvocateOnboardings();
  await seedAssessments();
  await seedAssessmentConstructs();
  await seedEmpoweredParentingAssessmentQuestions();
  await seedResilienceAssessmentQuestions();
}

main();
