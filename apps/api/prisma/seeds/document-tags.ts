import { PrismaClient } from '@prisma/client';
import {
  PARENTING_TAG_ID,
  MENTAL_HEALTH_TAG_ID,
  EDUCATION_TAG_ID,
  FINANCE_TAG_ID,
  WELLNESS_TAG_ID,
} from './tags';

import {
  NURTURING_PARENTING_TRACK_ID,
  EMOTIONAL_WELLNESS_TRACK_ID,
  FINANCIAL_LITERACY_TRACK_ID,
  CHILD_DEVELOPMENT_TRACK_ID,
  STRESS_MANAGEMENT_TRACK_ID,
  EDUCATION_PLANNING_TRACK_ID,
  FAMILY_HEALTH_TRACK_ID,
} from './tracks';

import {
  LESSON_TEMPLATE_1_SPICES_ID,
  LESSON_TEMPLATE_2_FINANCIAL_LITERACY_ID,
  LESSON_TEMPLATE_3_STRESS_MANAGEMENT_ID,
} from './lesson-templates';

const prisma = new PrismaClient();

export async function seedDocumentTags() {
  console.info('Starting document-tag association seeding');

  // Create additional documents for each track with multiple tags
  const tracksWithTags = [
    {
      trackId: NURTURING_PARENTING_TRACK_ID,
      primaryTag: PARENTING_TAG_ID,
      secondaryTags: [EDUCATION_TAG_ID, MENTAL_HEALTH_TAG_ID],
    },
    {
      trackId: EMOTIONAL_WELLNESS_TRACK_ID,
      primaryTag: MENTAL_HEALTH_TAG_ID,
      secondaryTags: [WELLNESS_TAG_ID],
    },
    {
      trackId: FINANCIAL_LITERACY_TRACK_ID,
      primaryTag: FINANCE_TAG_ID,
      secondaryTags: [EDUCATION_TAG_ID],
    },
    {
      trackId: CHILD_DEVELOPMENT_TRACK_ID,
      primaryTag: PARENTING_TAG_ID,
      secondaryTags: [EDUCATION_TAG_ID, WELLNESS_TAG_ID],
    },
    {
      trackId: STRESS_MANAGEMENT_TRACK_ID,
      primaryTag: MENTAL_HEALTH_TAG_ID,
      secondaryTags: [WELLNESS_TAG_ID],
    },
    {
      trackId: EDUCATION_PLANNING_TRACK_ID,
      primaryTag: EDUCATION_TAG_ID,
      secondaryTags: [FINANCE_TAG_ID],
    },
    {
      trackId: FAMILY_HEALTH_TRACK_ID,
      primaryTag: WELLNESS_TAG_ID,
      secondaryTags: [PARENTING_TAG_ID],
    },
  ];

  // For each track, create additional documents with various tag combinations
  for (const trackWithTag of tracksWithTags) {
    const track = await prisma.track.findUnique({
      where: { id: trackWithTag.trackId },
    });

    if (!track) continue;

    // Create documents for this track
    const resourceDocId = `resource-${trackWithTag.trackId}-1`;
    await prisma.document.upsert({
      where: { id: resourceDocId },
      update: {},
      create: {
        id: resourceDocId,
        document_name: `${track.title} Resource Guide`,
        description: `Comprehensive resource guide for the ${track.title} track`,
        filename: `${track.title.toLowerCase().replace(/\s+/g, '-')}-resource-guide.pdf`,
        mimeType: 'application/pdf',
        updated_at: new Date(),
      },
    });

    // Link this document to the track's primary tag
    await prisma.documentTag.upsert({
      where: {
        document_id_tag_id_deleted_at: {
          document_id: resourceDocId,
          tag_id: trackWithTag.primaryTag,
          deleted_at: 0,
        },
      },
      update: {},
      create: {
        id: `doc-tag-${resourceDocId}-${trackWithTag.primaryTag}`,
        document_id: resourceDocId,
        tag_id: trackWithTag.primaryTag,
        deleted_at: 0,
        updated_at: new Date(),
      },
    });

    // Create a second document with secondary tags
    const secondDocId = `resource-${trackWithTag.trackId}-2`;
    await prisma.document.upsert({
      where: { id: secondDocId },
      update: {},
      create: {
        id: secondDocId,
        document_name: `Supplementary Materials for ${track.title}`,
        description: `Additional resources for the ${track.title} track`,
        filename: `${track.title.toLowerCase().replace(/\s+/g, '-')}-supplementary.pdf`,
        mimeType: 'application/pdf',
        updated_at: new Date(),
      },
    });

    // Link second document to secondary tags
    for (const secondaryTag of trackWithTag.secondaryTags) {
      await prisma.documentTag.upsert({
        where: {
          document_id_tag_id_deleted_at: {
            document_id: secondDocId,
            tag_id: secondaryTag,
            deleted_at: 0,
          },
        },
        update: {},
        create: {
          id: `doc-tag-${secondDocId}-${secondaryTag}`,
          document_id: secondDocId,
          tag_id: secondaryTag,
          deleted_at: 0,
          updated_at: new Date(),
        },
      });
    }
  }

  // Create documents for lesson templates with multiple tags
  const lessonTemplatesWithTags = [
    {
      id: LESSON_TEMPLATE_1_SPICES_ID,
      tags: [PARENTING_TAG_ID, MENTAL_HEALTH_TAG_ID],
    },
    {
      id: LESSON_TEMPLATE_2_FINANCIAL_LITERACY_ID,
      tags: [FINANCE_TAG_ID, EDUCATION_TAG_ID],
    },
    {
      id: LESSON_TEMPLATE_3_STRESS_MANAGEMENT_ID,
      tags: [MENTAL_HEALTH_TAG_ID, WELLNESS_TAG_ID],
    },
  ];

  for (const template of lessonTemplatesWithTags) {
    const lessonTemplate = await prisma.lessonTemplate.findUnique({
      where: { id: template.id },
    });

    if (!lessonTemplate) continue;

    // Create a lesson resource document
    const lessonDocId = `lesson-doc-${template.id}`;
    await prisma.document.upsert({
      where: { id: lessonDocId },
      update: {},
      create: {
        id: lessonDocId,
        document_name: `${lessonTemplate.title} Handout`,
        description: `Handout for the ${lessonTemplate.title} lesson`,
        filename: `${lessonTemplate.title.toLowerCase().replace(/\s+/g, '-')}-handout.pdf`,
        mimeType: 'application/pdf',
        lesson_template_id: template.id,
        updated_at: new Date(),
      },
    });

    // Link this document to multiple tags
    for (const tagId of template.tags) {
      await prisma.documentTag.upsert({
        where: {
          document_id_tag_id_deleted_at: {
            document_id: lessonDocId,
            tag_id: tagId,
            deleted_at: 0,
          },
        },
        update: {},
        create: {
          id: `doc-tag-${lessonDocId}-${tagId}`,
          document_id: lessonDocId,
          tag_id: tagId,
          deleted_at: 0,
          updated_at: new Date(),
        },
      });
    }
  }

  // Create some standalone documents with multiple tags
  const standaloneDocuments = [
    {
      id: 'standalone-doc-1',
      name: 'Parenting and Mental Health Guide',
      description:
        'A comprehensive guide linking parenting techniques and mental health',
      filename: 'parenting-mental-health-guide.pdf',
      tags: [PARENTING_TAG_ID, MENTAL_HEALTH_TAG_ID],
    },
    {
      id: 'standalone-doc-2',
      name: 'Financial Education for Families',
      description: 'How to teach children about financial literacy',
      filename: 'financial-education-families.pdf',
      tags: [FINANCE_TAG_ID, EDUCATION_TAG_ID, PARENTING_TAG_ID],
    },
    {
      id: 'standalone-doc-3',
      name: 'Wellness and Stress Management Toolkit',
      description: 'Resources for maintaining wellness and managing stress',
      filename: 'wellness-stress-toolkit.pdf',
      tags: [WELLNESS_TAG_ID, MENTAL_HEALTH_TAG_ID],
    },
  ];

  // Add external links with tags
  const externalLinks = [
    {
      id: 'external-link-1',
      name: 'CDC Parenting Resources',
      description: 'Centers for Disease Control parenting resources and tips',
      external_url: 'https://www.cdc.gov/parents/',
      tags: [PARENTING_TAG_ID, EDUCATION_TAG_ID],
    },
    {
      id: 'external-link-2',
      name: 'Mental Health America',
      description: 'Resources for mental health awareness and support',
      external_url: 'https://mhanational.org/',
      tags: [MENTAL_HEALTH_TAG_ID, WELLNESS_TAG_ID],
    },
    {
      id: 'external-link-3',
      name: 'Consumer Financial Protection Bureau - Financial Education',
      description: 'Government resources for financial education and literacy',
      external_url:
        'https://www.consumerfinance.gov/consumer-tools/educator-tools/',
      tags: [FINANCE_TAG_ID, EDUCATION_TAG_ID],
    },
    {
      id: 'external-link-4',
      name: 'Khan Academy',
      description: 'Free educational resources on multiple topics',
      external_url: 'https://www.khanacademy.org/',
      tags: [EDUCATION_TAG_ID],
    },
    {
      id: 'external-link-5',
      name: 'National Institutes of Health - Wellness Toolkit',
      description: 'Wellness resources from the NIH',
      external_url: 'https://www.nih.gov/health-information',
      tags: [WELLNESS_TAG_ID],
    },
  ];

  for (const doc of standaloneDocuments) {
    await prisma.document.upsert({
      where: { id: doc.id },
      update: {},
      create: {
        id: doc.id,
        document_name: doc.name,
        description: doc.description,
        filename: doc.filename,
        mimeType: 'application/pdf',
        updated_at: new Date(),
      },
    });

    for (const tagId of doc.tags) {
      await prisma.documentTag.upsert({
        where: {
          document_id_tag_id_deleted_at: {
            document_id: doc.id,
            tag_id: tagId,
            deleted_at: 0,
          },
        },
        update: {},
        create: {
          id: `doc-tag-${doc.id}-${tagId}`,
          document_id: doc.id,
          tag_id: tagId,
          deleted_at: 0,
          updated_at: new Date(),
        },
      });
    }
  }
  // Create the external link documents
  for (const link of externalLinks) {
    await prisma.document.upsert({
      where: { id: link.id },
      update: {},
      create: {
        id: link.id,
        document_name: link.name,
        description: link.description,
        external_url_c: link.external_url,
        updated_at: new Date(),
      },
    });

    for (const tagId of link.tags) {
      await prisma.documentTag.upsert({
        where: {
          document_id_tag_id_deleted_at: {
            document_id: link.id,
            tag_id: tagId,
            deleted_at: 0,
          },
        },
        update: {},
        create: {
          id: `doc-tag-${link.id}-${tagId}`,
          document_id: link.id,
          tag_id: tagId,
          deleted_at: 0,
          updated_at: new Date(),
        },
      });
    }
  }

  console.info('Completed document-tag association seeding');
}
