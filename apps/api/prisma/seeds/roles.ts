import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export const ADVOCATE_ROLE_ID = '3c7a2642-8a58-40bd-aac2-fa48321f5664';
export const COORDINATOR_ROLE_ID = '780b4d11-748c-482b-abad-8dd42965c969';
export const SUPERVISOR_ROLE_ID = '4d76a91e-3ebd-46bc-ac99-a720b55953e5';
export const ADMINISTRATOR_ROLE_ID = '33e85104-816c-4266-891b-6ef9b8ae7c15';

export async function seedRoles() {
  await prisma.role.upsert({
    where: { id: ADVOCATE_ROLE_ID },
    update: {},
    create: {
      id: ADVOCATE_ROLE_ID,
      name: 'Advocate',
      description: 'Advocate',
      key: 'advocate',
    },
  });
  console.info('Upserted Advocate Role');

  await prisma.role.upsert({
    where: { id: COORDINATOR_ROLE_ID },
    update: {},
    create: {
      id: COORDINATOR_ROLE_ID,
      name: 'Coordinator',
      description: 'Coordinator',
      key: 'coordinator',
    },
  });
  console.info('Upserted Coordinator Role');

  await prisma.role.upsert({
    where: { id: SUPERVISOR_ROLE_ID },
    update: {},
    create: {
      id: SUPERVISOR_ROLE_ID,
      name: 'Supervisor',
      description: 'Supervisor',
      key: 'supervisor',
    },
  });
  console.info('Upserted Supervisor Role');

  await prisma.role.upsert({
    where: { id: ADMINISTRATOR_ROLE_ID },
    update: {},
    create: {
      id: ADMINISTRATOR_ROLE_ID,
      name: 'Administrator',
      description: 'Administrator',
      key: 'administrator',
    },
  });
  console.info('Upserted Administrator Role');
}
