import { PrismaClient } from '@prisma/client';
import { NURTURING_PARENTING_TRACK_ID } from './tracks';
import { SWATHI_MOM_ID, SAHARA_MOM_ID } from './moms';
import { ABAGIAL_USER_ID } from './users';

const prisma = new PrismaClient();

export const SWATHI_AND_ABAGAIL_PAIRING_ID =
  '9d4db165-b25e-4158-887b-cd15d7de6f2a';

export const SAHARA_AND_ABAGAIL_PAIRING_ID =
  'cf94d09e-ed92-4c14-8522-2342d6ca5df2';

export async function seedPairings() {
  await prisma.pairing.upsert({
    where: { id: SWATHI_AND_ABAGAIL_PAIRING_ID },
    update: {},
    create: {
      id: SWATHI_AND_ABAGAIL_PAIRING_ID,
      name: '<PERSON>wathi and Abagail Pairing',
      description: '<PERSON><PERSON>hi and Abagail Pairing Description',
      momId: SWATHI_MOM_ID,
      advocateUserId: ABAGIAL_USER_ID,
      trackId: NURTURING_PARENTING_TRACK_ID,
      status: 'paired',
    },
  });
  console.info('Upserted Swathi and Abagail Pairing');

  await prisma.pairing.upsert({
    where: { id: SAHARA_AND_ABAGAIL_PAIRING_ID },
    update: {},
    create: {
      id: SAHARA_AND_ABAGAIL_PAIRING_ID,
      name: 'Sahara and Abagail Pairing',
      description: 'Sahara and Abagail Pairing Description',
      momId: SAHARA_MOM_ID,
      advocateUserId: ABAGIAL_USER_ID,
      trackId: NURTURING_PARENTING_TRACK_ID,
      status: 'paired',
    },
  });
  console.info('Upserted Sahara and Abagail Pairing');
}
