import { PrismaClient } from '@prisma/client';
import { TEST_AFFILIATE_ID } from './affiliates';
import { TEST_AGENCY_ID } from './agencies';
import { CAROL_USER_ID } from './users';

const prisma = new PrismaClient();

export const SWATHI_MOM_ID = '040b6810-3912-4240-8794-e940600f8bb4';
export const SAHARA_MOM_ID = '1f31e2fd-4991-4664-b8cb-4b35896460e7';
export const SABRINA_MOM_ID = '7bb21202-93ae-40b3-8078-deabeb573f87';

export async function seedMoms() {
  await prisma.mom.upsert({
    where: { id: SWATHI_MOM_ID },
    update: {},
    create: {
      id: SWATHI_MOM_ID,
      first_name: 'Swathi',
      last_name: '<PERSON>',
      status: 'active',
      prospect_status: 'engaged_in_program',
      phone_other: '+11234567890',
      race_c: 'white',
      languages: 'English',
      cultural_heritage_c: 'I grew up in California',
      martial_status: 'single',
      currently_pregnant_c: 'no',
      number_of_children_c: 2,
      need_details_c: 'None',
      what_else_c: 'None',
      caregiver_type_c: 'biological_mom',
      gender_c: 'female',
      preferred_contact_method_c: 'email',
      email1: '<EMAIL>',
      supports_court_order_c: false,
      affiliate_id: TEST_AFFILIATE_ID,
      agency_id: TEST_AGENCY_ID,
      consent_obtained_c: true,
      referring_contact_first_name_c: 'John',
      referring_contact_last_name_c: 'Doe',
      referring_contact_phone_c: '+11234567890',
      referring_contact_email_c: '<EMAIL>',
      birthdate: new Date(1990, 1, 1),
      assigned_user_id: CAROL_USER_ID,
      primary_address_street: 'Main',
      primary_address_city: 'Los Angeles',
      primary_address_state: 'CA',
      primary_address_postalcode: '90210',
      emergency_contact_name_c: 'Mary',
      emergency_contact_relation_c: 'Smith',
      emergency_contact_number_c: '+11234567890',
    },
  });
  console.info('Upserted Mom Swathi');

  await prisma.mom.upsert({
    where: { id: SAHARA_MOM_ID },
    update: {},
    create: {
      id: SAHARA_MOM_ID,
      first_name: 'Sahara',
      last_name: 'Smith',
      status: 'active',
      prospect_status: 'engaged_in_program',
      phone_other: '+11234567890',
      race_c: 'white',
      languages: 'English',
      cultural_heritage_c: 'I grew up in California',
      martial_status: 'single',
      currently_pregnant_c: 'no',
      number_of_children_c: 2,
      need_details_c: 'None',
      what_else_c: 'None',
      caregiver_type_c: 'biological_mom',
      gender_c: 'female',
      preferred_contact_method_c: 'email',
      email1: '<EMAIL>',
      supports_court_order_c: false,
      affiliate_id: TEST_AFFILIATE_ID,
      agency_id: TEST_AGENCY_ID,
      consent_obtained_c: true,
      referring_contact_first_name_c: 'John',
      referring_contact_last_name_c: 'Doe',
      referring_contact_phone_c: '+11234567890',
      referring_contact_email_c: '<EMAIL>',
      birthdate: new Date(1990, 1, 1),
      assigned_user_id: CAROL_USER_ID,
      primary_address_street: 'Main',
      primary_address_city: 'Los Angeles',
      primary_address_state: 'CA',
      primary_address_postalcode: '90210',
      emergency_contact_name_c: 'Mary',
      emergency_contact_relation_c: 'Smith',
      emergency_contact_number_c: '+11234567890',
    },
  });
  console.info('Upserted Mom Sahara');

  await prisma.mom.upsert({
    where: { id: SABRINA_MOM_ID },
    update: {},
    create: {
      id: SABRINA_MOM_ID,
      first_name: 'Sabrina',
      last_name: 'Smith',
      prospect_status: 'prospect',
      phone_other: '+11234567890',
      race_c: 'white',
      languages: 'English',
      cultural_heritage_c: 'I grew up in California',
      martial_status: 'single',
      currently_pregnant_c: 'no',
      number_of_children_c: 2,
      need_details_c: 'None',
      what_else_c: 'None',
      caregiver_type_c: 'biological_mom',
      gender_c: 'female',
      preferred_contact_method_c: 'email',
      email1: '<EMAIL>',
      supports_court_order_c: false,
      affiliate_id: TEST_AFFILIATE_ID,
      agency_id: TEST_AGENCY_ID,
      consent_obtained_c: true,
      referring_contact_first_name_c: 'John',
      referring_contact_last_name_c: 'Doe',
      referring_contact_phone_c: '+11234567890',
      referring_contact_email_c: '<EMAIL>',
      birthdate: new Date(1990, 1, 1),
      primary_address_street: 'Main',
      primary_address_city: 'Los Angeles',
      primary_address_state: 'CA',
      primary_address_postalcode: '90210',
      emergency_contact_name_c: 'Mary',
      emergency_contact_relation_c: 'Smith',
      emergency_contact_number_c: '+11234567890',
    },
  });
  console.info('Upserted Mom Sabrina');
}
