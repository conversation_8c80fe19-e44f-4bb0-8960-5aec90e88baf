import { PrismaClient, Language } from '@prisma/client';
import { faker } from '@faker-js/faker';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();

export async function seedAdvocateOnboardings() {
  console.info('Seeding advocate onboardings...');

  // Delete all existing advocate onboardings
  console.info('Deleting existing advocate onboardings...');
  await prisma.advocateOnboarding.deleteMany();

  // Create or get the onboarding advocate role
  const onboardingRole = await prisma.role.upsert({
    where: { key: 'onboarding_advocate' },
    update: {},
    create: {
      id: uuidv4(),
      name: 'Onboarding Advocate',
      key: 'onboarding_advocate',
      description: 'Advocate who is in the onboarding process',
      updated_at: new Date(),
    },
  });

  // Create 30 advocate onboardings with associated users
  const onboardings = [];

  for (let i = 0; i < 30; i++) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    const email = faker.internet.email({ firstName, lastName });
    const hasInterview = faker.datatype.boolean(0.6); // 60% chance of having an interview scheduled

    // Create a new user for each advocate
    const user = await prisma.user.create({
      data: {
        id: uuidv4(),
        firstName,
        lastName,
        email,
        phone: faker.phone.number(),
        username: faker.internet.username({ firstName, lastName }),
        passwordHash: faker.internet.password(),
        status: 'Active',
        advocate_status: 'Interested',
        communication_preference: faker.helpers.arrayElement([
          'text_message',
          'email',
          'both',
        ]),
        date_of_birth: faker.datatype.boolean(0.8)
          ? faker.date.birthdate({ min: 18, max: 65, mode: 'age' })
          : null,
        address_street: faker.location.streetAddress(),
        address_city: faker.location.city(),
        address_state: faker.location.state(),
        address_postalcode: faker.location.zipCode(),
        updated_at: new Date(),
        userRoles: {
          createMany: {
            data: [
              {
                id: uuidv4(),
                role_id: onboardingRole.id,
                updated_at: new Date(),
              },
            ],
          },
        },
      },
    });

    const onboarding = {
      // Tracking
      trackingToken: uuidv4(),

      // Referral information
      referredBy: faker.datatype.boolean(0.6) ? faker.person.fullName() : null, // 60% chance of having a referrer
      canCompleteBackground: faker.datatype.boolean(0.95), // 95% true

      // Interest info - More varied interests
      interests: faker.helpers.arrayElements(
        ['Advocate', 'Volunteer'],
        faker.number.int({ min: 1, max: 2 }), // At least one interest
      ),

      // Personal info - More varied experience
      hasMultiLang: faker.datatype.boolean(0.4), // 40% chance of being multilingual
      languages: faker.datatype.boolean(0.4)
        ? faker.helpers.arrayElements(
            Object.values(Language),
            faker.number.int({ min: 1, max: 3 }),
          )
        : [],
      hasExpCrisis: faker.datatype.boolean(0.3),
      hasExpFoster: faker.datatype.boolean(0.25),
      hasExpVictims: faker.datatype.boolean(0.35),
      hasExpWelfare: faker.datatype.boolean(0.2),
      hasExpChildren: faker.datatype.boolean(0.4)
        ? faker.lorem.paragraph()
        : null,
      personalNote: faker.datatype.boolean(0.5)
        ? faker.lorem.paragraph()
        : null,

      // Parenting info
      groupPref: faker.helpers.arrayElement(['None', '0-3', '4-9', '10-18']),
      parentingNote: faker.datatype.boolean(0.3)
        ? faker.lorem.paragraph()
        : null,

      // Availability - More varied options
      availability: faker.helpers.arrayElement([
        'Weekdays',
        'Weekends',
        'Evenings',
        'Flexible',
        'Limited',
        'Weekday Mornings',
        'Weekday Afternoons',
        'Weekend Mornings',
        'Weekend Afternoons',
        null,
      ]),

      // Interview info
      interviewDate: hasInterview ? faker.date.future() : null,
      interviewStartTime: hasInterview
        ? faker.date.future().toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
          })
        : null,
      interviewEndTime: hasInterview
        ? faker.date.future().toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
          })
        : null,
      interviewMeetingType: hasInterview
        ? faker.helpers.arrayElement(['Virtual', 'InPerson'])
        : null,
      interviewLocation: hasInterview
        ? faker.datatype.boolean()
          ? faker.internet.url()
          : faker.location.streetAddress()
        : null,

      // Review info
      reviewNote: faker.datatype.boolean(0.4) ? faker.lorem.paragraph() : null,
      reviewedBy: faker.datatype.boolean(0.4) ? faker.person.fullName() : null,
      reviewedAt: faker.datatype.boolean(0.4) ? faker.date.recent() : null,
      completedAt: faker.datatype.boolean(0.7) ? faker.date.recent() : null,

      // Link to the newly created user
      userId: user.id,
    };

    onboardings.push(onboarding);
  }

  // Create the onboardings in the database
  for (const onboarding of onboardings) {
    await prisma.advocateOnboarding.create({
      data: onboarding,
    });
  }

  console.info(
    `Created ${onboardings.length} advocate onboardings with associated users`,
  );
}
