import { PrismaClient } from '@prisma/client';
import { NURTURING_PARENTING_TRACK_ID } from './tracks';

const prisma = new PrismaClient();

export const LESSON_TEMPLATE_1_SPICES_ID =
  'babe1435-e3af-4ee9-91b7-8325f87ecc86';

export const LESSON_TEMPLATE_2_FINANCIAL_LITERACY_ID =
  '79641b3e-6698-40ef-b7b2-603447bfcc9d';

export const LESSON_TEMPLATE_3_STRESS_MANAGEMENT_ID =
  '7dd16242-e1eb-474c-b1e4-aad62e70c2f8';

export async function seedLessonTemplates() {
  await prisma.lessonTemplate.upsert({
    where: { id: LESSON_TEMPLATE_1_SPICES_ID },
    update: {},
    create: {
      id: LESSON_TEMPLATE_1_SPICES_ID,
      title: 'Nurturing Parenting - E - L1: S.P.I.C.E.S.',
      description: 'Nurturing Parenting - E - L1: S.P.I.C.E.S. Description',
      track_id: NURTURING_PARENTING_TRACK_ID,
    },
  });
  console.info('Upserted Spices Lesson Template 1');

  await prisma.lessonTemplate.upsert({
    where: { id: LESSON_TEMPLATE_2_FINANCIAL_LITERACY_ID },
    update: {},
    create: {
      id: LESSON_TEMPLATE_2_FINANCIAL_LITERACY_ID,
      title: 'Nurturing Parenting - E - L2: Financial Literacy',
      description:
        'Nurturing Parenting - E - L2: Financial Literacy Description',
      track_id: NURTURING_PARENTING_TRACK_ID,
    },
  });
  console.info('Upserted Spices Lesson Template 2');

  await prisma.lessonTemplate.upsert({
    where: { id: LESSON_TEMPLATE_3_STRESS_MANAGEMENT_ID },
    update: {},
    create: {
      id: LESSON_TEMPLATE_3_STRESS_MANAGEMENT_ID,
      title: 'Nurturing Parenting - E - L3: Stress Management',
      description:
        'Nurturing Parenting - E - L3: Stress Management Description',
      track_id: NURTURING_PARENTING_TRACK_ID,
    },
  });
  console.info('Upserted Spices Lesson Template 3');
}
