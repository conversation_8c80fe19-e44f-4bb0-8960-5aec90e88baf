import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function seedEvents() {
  const events = [
    {
      event_title: 'Back to School Bash',
      description:
        'Join us for an engaging in-person training session designed to enhance your skills and knowledge in key areas relevant to your role. Participants will have the opportunity to interact with experienced facilitators, collaborate with peers, and engage in hands-on activities.',
      start_date: new Date('2024-10-15T08:00:00Z'),
      end_date: new Date('2024-10-15T09:00:00Z'),
      location: '350 E Las Olas Blvd\nFort Lauderdale, FL 33301',
      join_url: null,
      max_attendees: 100,
    },
    {
      event_title: 'Virtual Parenting Workshop',
      description:
        'Learn effective parenting strategies and techniques in this interactive virtual workshop. Connect with other parents and share experiences while gaining valuable insights from our expert facilitators.',
      start_date: new Date('2024-11-01T14:00:00Z'),
      end_date: new Date('2024-11-01T15:30:00Z'),
      location: null,
      join_url: 'https://zoom.us/j/123456789',
      max_attendees: 50,
    },
    {
      event_title: 'Community Support Group',
      description:
        'Join our monthly community support group where you can share experiences, challenges, and successes with other parents in a safe and supportive environment.',
      start_date: new Date('2024-11-15T10:00:00Z'),
      end_date: new Date('2024-11-15T11:30:00Z'),
      location: 'Community Center\n123 Main St\nMiami, FL 33101',
      join_url: null,
      max_attendees: 25,
    },
  ];

  for (const event of events) {
    await prisma.event.create({
      data: event,
    });
  }
}
