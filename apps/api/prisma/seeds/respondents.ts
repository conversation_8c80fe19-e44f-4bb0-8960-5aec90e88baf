import { PrismaClient } from '@prisma/client';
import { SWATHI_MOM_ID, SAHARA_MOM_ID, SABRINA_MOM_ID } from './moms';
import { faker } from '@faker-js/faker';

const prisma = new PrismaClient();

export async function seedRespondents() {
  // Delete all existing respondents first
  await prisma.eventRespondent.deleteMany({});
  console.info('Deleted all existing respondents');

  // Get some sample events and users to create respondents for
  const events = await prisma.event.findMany();
  const users = await prisma.user.findMany();

  if (events.length === 0) {
    console.info('No events found to create respondents');
    return;
  }

  if (users.length === 0) {
    console.info('No users found to create respondents');
    return;
  }

  // Create some sample respondents
  const respondents = [];
  for (const event of events) {
    // Create exactly 2 of each type
    const types = ['mom', 'user', 'custom'];
    for (const type of types) {
      for (let i = 0; i < 2; i++) {
        let respondentData: any = {
          event_id: event.id,
          hasBeenInvited: Math.random() > 0.5,
          didRsvp: Math.random() > 0.5,
          didCheckin: Math.random() > 0.5,
          needsTransport: Math.random() > 0.5,
          childrenCount: Math.floor(Math.random() * 4),
          address: '123 Sample Street, City, State 12345',
          phone_number: '555-0123',
        };

        if (type === 'mom') {
          const momIds = [SWATHI_MOM_ID, SAHARA_MOM_ID, SABRINA_MOM_ID];
          const momId = momIds[Math.floor(Math.random() * momIds.length)];
          const mom = await prisma.mom.findUnique({ where: { id: momId } });

          if (!mom) {
            console.info(
              `Mom with ID ${momId} not found, skipping this respondent`,
            );
            continue;
          }

          respondentData = {
            ...respondentData,
            mom_id: momId,
            email: mom.email1 || '<EMAIL>',
          };
        } else if (type === 'user') {
          const user = users[Math.floor(Math.random() * users.length)];
          respondentData = {
            ...respondentData,
            user_id: user.id,
            email: user.email || '<EMAIL>',
          };
        } else {
          const firstName = faker.person.firstName();
          const lastName = faker.person.lastName();
          respondentData = {
            ...respondentData,
            name: `${firstName} ${lastName}`,
            email: faker.internet.email({ firstName, lastName }),
            address: faker.location.streetAddress(),
            phone_number: faker.phone.number({ style: 'national' }),
          };
        }

        respondents.push(respondentData);
      }
    }
  }

  // Create the respondents in the database
  for (const respondent of respondents) {
    try {
      await prisma.eventRespondent.create({
        data: respondent,
      });
    } catch (error) {
      console.error('Failed to create respondent:', error);
      console.error('Respondent data:', respondent);
    }
  }

  console.info(`Created ${respondents.length} event respondents`);
}
