import { PrismaClient } from '@prisma/client';
import {
  RESILIENCE_POST_ASSESSMENT_ID,
  RESILIENCE_PRE_ASSESSMENT_ID,
} from './assessments';

const prisma = new PrismaClient();

export async function seedResilienceAssessmentQuestions() {
  let id = '32364c24-5ccf-4cb4-8d65-f7a5d2d5032a';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 1,
      question:
        'I feel confident reaching out to my support system in times of need.',
      responseType: 'int',
      assessment: {
        connect: {
          id: RESILIENCE_PRE_ASSESSMENT_ID,
        },
      },
    },
  });

  id = '3519a222-606d-4906-9d66-d5dc846c28b8';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 2,
      question:
        'I believe naming and recognizing my own emotions is important for my day-to-day functioning.',
      responseType: 'int',
      assessment: {
        connect: {
          id: RESILIENCE_PRE_ASSESSMENT_ID,
        },
      },
    },
  });

  id = '10286e7e-7f6d-48a8-aa38-bde5232c6dde';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 3,
      question: 'I am able to recognize and manage my stress effectively.',
      responseType: 'int',
      assessment: {
        connect: {
          id: RESILIENCE_PRE_ASSESSMENT_ID,
        },
      },
    },
  });

  id = '88971f0b-cd52-4f74-ab70-49d61bf99fc3';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 4,
      question:
        'I use stress management techniques to calm my emotions during stressful times.',
      responseType: 'int',
      assessment: {
        connect: {
          id: RESILIENCE_PRE_ASSESSMENT_ID,
        },
      },
    },
  });

  id = '3b675961-a1ab-4d4b-a941-7492d4c258f4';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 5,
      question:
        'I feel prepared to maintain stability for my children during moments of high stress.',
      responseType: 'int',
      assessment: {
        connect: {
          id: RESILIENCE_PRE_ASSESSMENT_ID,
        },
      },
    },
  });

  id = '209d4fd8-b94a-479d-9ed8-827ee5786244';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 6,
      question: 'I feel confident in setting and achieving personal goals.',
      responseType: 'int',
      assessment: {
        connect: {
          id: RESILIENCE_PRE_ASSESSMENT_ID,
        },
      },
    },
  });

  // -------------------- POST-ASSESSMENT QUESTIONS ----------------------------

  id = '9bf9ad19-889a-4e89-8daf-5fa83b841fc0';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 1,
      question:
        'I feel more confident reaching out to my support system now than I did before the course.',
      responseType: 'int',
      assessment: {
        connect: {
          id: RESILIENCE_POST_ASSESSMENT_ID,
        },
      },
    },
  });

  id = '4c331a51-a5b3-45cb-8d9a-247ca0907651';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 2,
      question:
        'I believe naming and recognizing my own emotions is important for my day-to-day functioning.',
      responseType: 'int',
      assessment: {
        connect: {
          id: RESILIENCE_POST_ASSESSMENT_ID,
        },
      },
    },
  });

  id = 'f96ab47b-3014-40c0-99b3-52b8d9e3f0fb';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 3,
      question:
        'I am better able to recognize and manage stress after this course.',
      responseType: 'int',
      assessment: {
        connect: {
          id: RESILIENCE_POST_ASSESSMENT_ID,
        },
      },
    },
  });

  id = 'ca75c24c-fa81-492c-9011-dda6c0f5f530';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 4,
      question:
        'I use stress management techniques more consistently now than I did before the course.',
      responseType: 'int',
      assessment: {
        connect: {
          id: RESILIENCE_POST_ASSESSMENT_ID,
        },
      },
    },
  });

  id = '90f001bf-3351-4725-b472-aa7ba7cd6a95';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 5,
      question:
        'I feel more prepared to maintain stability for my children during a crisis than I did before.',
      responseType: 'int',
      assessment: {
        connect: {
          id: RESILIENCE_POST_ASSESSMENT_ID,
        },
      },
    },
  });

  id = '88f87c74-af1b-46a5-a43e-c8e9086c85fd';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 6,
      question:
        'I feel more confident in setting and achieving personal goals than I did before.',
      responseType: 'int',
      assessment: {
        connect: {
          id: RESILIENCE_POST_ASSESSMENT_ID,
        },
      },
    },
  });

  id = 'c30409a3-92fd-410c-8ff3-48b9adf8ea10';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 7,
      question:
        'What, if anything, has changed in how you manage stress as a result of this course?',
      responseType: 'string',
      assessment: {
        connect: {
          id: RESILIENCE_POST_ASSESSMENT_ID,
        },
      },
    },
  });

  id = 'e8d33275-cc69-49f6-a7a6-992c6acddbb7';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 8,
      question:
        'Has your approach to parenting during stressful times changed? If so, how?',
      responseType: 'string',
      assessment: {
        connect: {
          id: RESILIENCE_POST_ASSESSMENT_ID,
        },
      },
    },
  });

  id = '056e56ca-d742-4d1a-b6d4-81fa8b8bdc19';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 9,
      question:
        'What personal goals have you set as a result of participating in this course?',
      responseType: 'string',
      assessment: {
        connect: {
          id: RESILIENCE_POST_ASSESSMENT_ID,
        },
      },
    },
  });

  console.info('Upserted Resilience (Pre & Post) Assessment Questions');
}
