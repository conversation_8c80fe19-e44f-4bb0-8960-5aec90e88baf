import { PrismaClient } from '@prisma/client';
import {
  LESSON_TEMPLATE_1_SPICES_ID,
  LESSON_TEMPLATE_2_FINANCIAL_LITERACY_ID,
  LESSON_TEMPLATE_3_STRESS_MANAGEMENT_ID,
} from './lesson-templates';
import {
  SWATHI_AND_ABAGAIL_PAIRING_ID,
  SAHARA_AND_ABAGAIL_PAIRING_ID,
} from './pairings';

const prisma = new PrismaClient();

export const SWATHI_LESSON_1_SPICES_ID = '16ad1460-185d-48e3-8c50-ec0bda2ab2eb';

export const SWATHI_LESSON_2_FINANCIAL_LITERACY_ID =
  '85137750-a966-48b6-88d4-ac4c4efc2c29';

export const SWATHI_LESSON_3_STRESS_MANAGEMENT_ID =
  '2f971c4e-61db-40b4-ab8d-0a27f103c060';

export const SAHARA_LESSON_1_SPICES_ID = '6e2fe197-3cf6-4d63-85ae-f982b47d98e6';

export const SAHARA_LESSON_2_FINANCIAL_LITERACY_ID =
  '06c80966-24e8-4dc0-bbf4-cddff5c2ab94';

export const SAHARA_LESSON_3_STRESS_MANAGEMENT_ID =
  '53a3df6e-05e6-4652-a768-6f105fa75484';

export async function seedLessons() {
  // Swathi
  await prisma.lesson.upsert({
    where: { id: SWATHI_LESSON_1_SPICES_ID },
    update: {},
    create: {
      id: SWATHI_LESSON_1_SPICES_ID,
      title: 'Nurturing Parenting - E - L1: S.P.I.C.E.S.',
      description: 'Nurturing Parenting - E - L1: S.P.I.C.E.S. Description',
      pairing_id: SWATHI_AND_ABAGAIL_PAIRING_ID,
      source_lesson_template_id: LESSON_TEMPLATE_1_SPICES_ID,
    },
  });
  console.info('Upserted Swathi Spices Lesson');

  await prisma.lesson.upsert({
    where: { id: SWATHI_LESSON_2_FINANCIAL_LITERACY_ID },
    update: {},
    create: {
      id: SWATHI_LESSON_2_FINANCIAL_LITERACY_ID,
      title: 'Nurturing Parenting - E - L2: Financial Literacy',
      description:
        'Nurturing Parenting - E - L2: Financial Literacy Description',
      pairing_id: SWATHI_AND_ABAGAIL_PAIRING_ID,
      source_lesson_template_id: LESSON_TEMPLATE_2_FINANCIAL_LITERACY_ID,
    },
  });
  console.info('Upserted Swathi Financial Literacy Lesson');

  await prisma.lesson.upsert({
    where: { id: SWATHI_LESSON_3_STRESS_MANAGEMENT_ID },
    update: {},
    create: {
      id: SWATHI_LESSON_3_STRESS_MANAGEMENT_ID,
      title: 'Nurturing Parenting - E - L3: Stress Management',
      description:
        'Nurturing Parenting - E - L3: Stress Management Description',
      pairing_id: SWATHI_AND_ABAGAIL_PAIRING_ID,
      source_lesson_template_id: LESSON_TEMPLATE_3_STRESS_MANAGEMENT_ID,
    },
  });
  console.info('Upserted Swathi Stress Management Lesson');

  // Sahara
  await prisma.lesson.upsert({
    where: { id: SAHARA_LESSON_1_SPICES_ID },
    update: {},
    create: {
      id: SAHARA_LESSON_1_SPICES_ID,
      title: 'Nurturing Parenting - E - L1: S.P.I.C.E.S.',
      description: 'Nurturing Parenting - E - L1: S.P.I.C.E.S. Description',
      pairing_id: SAHARA_AND_ABAGAIL_PAIRING_ID,
      source_lesson_template_id: LESSON_TEMPLATE_1_SPICES_ID,
    },
  });
  console.info('Upserted Sahara Spices Lesson');

  await prisma.lesson.upsert({
    where: { id: SAHARA_LESSON_2_FINANCIAL_LITERACY_ID },
    update: {},
    create: {
      id: SAHARA_LESSON_2_FINANCIAL_LITERACY_ID,
      title: 'Nurturing Parenting - E - L2: Financial Literacy',
      description:
        'Nurturing Parenting - E - L2: Financial Literacy Description',
      pairing_id: SAHARA_AND_ABAGAIL_PAIRING_ID,
      source_lesson_template_id: LESSON_TEMPLATE_2_FINANCIAL_LITERACY_ID,
    },
  });
  console.info('Upserted Sahara Financial Literacy Lesson');

  await prisma.lesson.upsert({
    where: { id: SAHARA_LESSON_3_STRESS_MANAGEMENT_ID },
    update: {},
    create: {
      id: SAHARA_LESSON_3_STRESS_MANAGEMENT_ID,
      title: 'Nurturing Parenting - E - L3: Stress Management',
      description:
        'Nurturing Parenting - E - L3: Stress Management Description',
      pairing_id: SAHARA_AND_ABAGAIL_PAIRING_ID,
      source_lesson_template_id: LESSON_TEMPLATE_3_STRESS_MANAGEMENT_ID,
    },
  });
  console.info('Upserted Sahara Stress Management Lesson');
}
