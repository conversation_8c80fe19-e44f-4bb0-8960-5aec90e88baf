import { PrismaClient } from '@prisma/client';
import { SWATHI_MOM_ID } from './moms';
import { ABAGIAL_USER_ID, CAROL_USER_ID } from './users';

const prisma = new PrismaClient();

export async function seedConnectionLogs() {
  const connectionLogId = 'cafa9676-a771-47dc-90c7-05faddbac019';
  await prisma.connectionLog.upsert({
    where: { id: connectionLogId },
    create: {
      id: connectionLogId,
      name: 'Test Call Connection Log',
      contact_method_c: 'Call',
      summary_c: 'Test Call Connection Log Summary',
      is_visible_to_advocates_c: true,
      date_created_c: new Date('2025-01-01'),
      mom_id: SWATHI_MOM_ID,
      user_id: ABAGIAL_USER_ID,
    },
    update: {},
  });
  console.info('Upserted Test Call Connection Log 1');

  const connectionLogId2 = 'c2aa90e6-2be7-40d4-a783-019bd18f2c49';
  await prisma.connectionLog.upsert({
    where: { id: connectionLogId2 },
    create: {
      id: connectionLogId2,
      name: 'Test Email Connection Log',
      contact_method_c: 'Email',
      summary_c: 'Test Email Connection Log Summary',
      is_visible_to_advocates_c: true,
      date_created_c: new Date('2025-01-10'),
      mom_id: SWATHI_MOM_ID,
      user_id: CAROL_USER_ID,
    },
    update: {},
  });
  console.info('Upserted Test Call Connection Log 2');
}
