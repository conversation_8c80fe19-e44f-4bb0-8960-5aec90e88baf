import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export const SELF_REGULATION_ASSESSMENT_CONSTRUCT_ID =
  '310e9777-4410-44a1-8d59-d1407e248806';

export const PARENTAL_CONNECTION_ASSESSMENT_CONSTRUCT_ID =
  'f24339be-1dc6-4a1a-a664-228261ff9cfc';

export const GUIDANCE_AND_DISCIPLINE_ASSESSMENT_CONSTRUCT_ID =
  '9fbff77d-af33-4d74-825a-9c1f47a50fb7';

export const POWER_AND_INDEPENDENCE_ASSESSMENT_CONSTRUCT_ID =
  'faf2744e-5be4-4db8-aac1-32e48511131d';

export const SAFETY_AND_VALUES_ASSESSMENT_CONSTRUCT_ID =
  '88807a7c-b7ee-4a94-83d5-182ff258c179';

export async function seedAssessmentConstructs() {
  await prisma.assessmentConstruct.upsert({
    where: { id: SELF_REGULATION_ASSESSMENT_CONSTRUCT_ID },
    update: {},
    create: {
      id: SELF_REGULATION_ASSESSMENT_CONSTRUCT_ID,
      name: 'Self Regulation',
      order: 1,
    },
  });
  console.info('Upserted Self Regulation Assessment Construct');

  await prisma.assessmentConstruct.upsert({
    where: { id: PARENTAL_CONNECTION_ASSESSMENT_CONSTRUCT_ID },
    update: {},
    create: {
      id: PARENTAL_CONNECTION_ASSESSMENT_CONSTRUCT_ID,
      name: 'Parental Connection',
      order: 2,
    },
  });
  console.info('Upserted Parental Connection Assessment Construct');

  await prisma.assessmentConstruct.upsert({
    where: { id: GUIDANCE_AND_DISCIPLINE_ASSESSMENT_CONSTRUCT_ID },
    update: {},
    create: {
      id: GUIDANCE_AND_DISCIPLINE_ASSESSMENT_CONSTRUCT_ID,
      name: 'Guidance & Discipline',
      order: 3,
    },
  });
  console.info('Upserted Guidance and Discipline Assessment Construct');

  await prisma.assessmentConstruct.upsert({
    where: { id: POWER_AND_INDEPENDENCE_ASSESSMENT_CONSTRUCT_ID },
    update: {},
    create: {
      id: POWER_AND_INDEPENDENCE_ASSESSMENT_CONSTRUCT_ID,
      name: 'Power & Independence',
      order: 4,
    },
  });
  console.info('Upserted Power and Independence Assessment Construct');

  await prisma.assessmentConstruct.upsert({
    where: { id: SAFETY_AND_VALUES_ASSESSMENT_CONSTRUCT_ID },
    update: {},
    create: {
      id: SAFETY_AND_VALUES_ASSESSMENT_CONSTRUCT_ID,
      name: 'Safety & Values',
      order: 5,
    },
  });
  console.info('Upserted Safety and Values Assessment Construct');
}
