import { PrismaClient } from '@prisma/client';
import { SWATHI_MOM_ID } from './moms';
import { faker } from '@faker-js/faker';

const prisma = new PrismaClient();

export async function seedWellbeingAssessment() {
  await prisma.wellnessAssessment.create({
    data: {
      completed_ahead: false,
      meeting_method: 'in_person',
      staff_name_c: 'Carol Coordinator',
      start_date_c: faker.date.between({
        from: new Date(2025, 0, 1),
        to: new Date(2025, 1, 1),
      }),
      completed_date: faker.date.between({
        from: new Date(2025, 2, 1),
        to: new Date(2025, 3, 1),
      }),

      cw_home_status: '0_out_home',
      cw_involvement_as_child: 'no_interactions',
      cw_involvement_as_mom: true,
      cw_allegations: true,
      cw_maltreatment_type: 'neglect,physical_abuse,abandonment',
      cw_active_involvement: '25_supportive_services',
      cw_date_of_involvement: undefined,
      cw_fp_goal: 'prevent_foster_care_placement',
      cw_fp_impact: 'prevented_from_permanent_removal',
      cw_notes: undefined,
      cw_score: undefined,

      cc_reliable_care: '5_informal',
      cc_affordability: 'no_payment_needed',
      cc_childcare_safety: true,
      cc_backup_care: '0_none',
      cc_school_enrollment: 'yes',
      cc_special_ed: true,
      cc_ed_concerns: true,
      cc_health_ins: 'yes',
      cc_special_med: 'yes_and_access',
      cc_med_access: 'yes_with_access',
      cc_notes: undefined,
      cc_score: undefined,

      home_type: '10_permanently_neighbor',
      home_category: 'permanent',
      home_name_on_lease: '5_yes',
      home_security_concerns: '0_yes',
      home_recent_homeless: '0_yes',
      home_voucher: 'yes',
      home_perc_toward: '0_>50%',
      home_safe: true,
      home_risk_in_home: true,
      home_notes: undefined,
      home_score: undefined,

      trnprt_access: '0_limited',
      trnprt_affordable: '0_not_affordable',
      trnprt_seat_access: true,
      trnprt_private_safety: 'safe',
      trnprt_license: '5_yes',
      trnprt_notes: undefined,
      trnprt_score: undefined,

      res_positive_outlook: '2_somewhat',
      res_communication: '2_somewhat',
      res_traditions: '2_somewhat',
      res_fr_pfs_summary: '2_sometimes',
      res_overall_sat: '5',
      res_happiness: '5',
      res_sat_summary: 'medium',
      res_fulfillment: '5',
      res_purpose: '5',
      res_purpose_summary: 'medium',
      res_goodness: '5',
      res_sacrifice: '5',
      res_virtue_summary: 'medium',
      res_belief_self: '5',
      res_support: true,
      res_attendance: 'yes',
      res_notes: undefined,
      res_score: undefined,

      well_gad_q1: '2_more_then_half',
      well_gad_q2: '2_more_then_half',
      well_gad_q3: '2_more_then_half',
      well_gad_q4: '2_more_then_half',
      well_gad_q5: '2_more_then_half',
      well_gad_q6: '2_more_then_half',
      well_gad_q7: '2_more_then_half',
      well_gad_total: '1_moderate',
      well_phq_q1: '2_more_then_half',
      well_phq_q2: '2_more_then_half',
      well_phq_q3: '2_more_then_half',
      well_phq_q4: '2_more_then_half',
      well_phq_q5: '2_more_then_half',
      well_phq_q6: '2_more_then_half',
      well_phq_q7: '2_more_then_half',
      well_phq_q8: '2_more_then_half',
      well_phq_q9: '2_more_then_half',
      well_phq_total: '1_moderate',
      well_health_insurance: true,
      well_medical_care: 'yes_currently',
      well_discouragement: 'not_last_6_months',
      well_discouragement_summary: '5_not_last_6_months',
      well_counseling: 'past_not_now',
      well_counseling_summary: '4_historically',
      well_strategies: 'yes_regularly',
      well_strategies_summary: '10_current',
      well_phys_health: '5',
      well_mental_health: '5',
      well_health_summary: 'medium',
      well_counseling_past: '5_yes_currently',
      well_counseling_interest: '5_yes_currently',
      well_reflections: undefined,
      well_notes: undefined,
      well_score: undefined,

      subs_recency: 'frequently',
      subs_support_needed: 'yes',
      subs_treatment_history: true,
      subs_notes: undefined,
      subs_score: undefined,

      soc_status: 'no_partner',
      soc_length: undefined,
      soc_tension: undefined,
      soc_resolve_arguments: undefined,
      soc_wast_sf: undefined,
      soc_dynamics: 'positively',
      soc_rel_reflections: undefined,
      soc_rel_notes: undefined,
      soc_faith_id: 'christian',
      soc_pfs_supportive_rels: '2_somewhat',
      soc_pfs_supportive_advice: '2_somewhat',
      soc_pfs_support_goals: '2_somewhat',
      soc_pfs_emergency_contact: '2_somewhat',
      soc_trusted_network: '1_money,1_relationships',
      soc_pfs_summary_score: '2_some',
      soc_frs_content_with_rels: '5',
      soc_frs_rel_sat: '5',
      soc_frs_summary: 'medium',
      soc_reflections: undefined,
      soc_notes: undefined,
      soc_score: undefined,

      emp_fin_struggles: '-1_rent,-1_utilities',
      emp_challenges: '-1_no_medical,-1_evicted',
      emp_difficulty: '4_never',
      emp_afford_food: '0_never',
      emp_concrete_pfs_total: 'sometimes',
      emp_support_received: 'ssi,snap',
      emp_support_needed: 'wic,tanf',
      emp_diff_manage_bills: '3_sometimes',
      emp_emergency_funds: '5_always',
      emp_fin_notes: undefined,
      emp_status: '1_>_9_months',
      emp_duration_current: 'none',
      emp_work_eligibility: 'no_history',
      emp_highest_ed: 'some_school',
      emp_notes: undefined,
      emp_score: undefined,

      legal_current: 'no',
      legal_rep: 'no',
      legal_rep_access: 'no',
      legal_plan_childcare: 'no',
      legal_notes: undefined,
      legal_score: undefined,

      naa_child_behavior: '2_somewhat',
      naa_discipline: '2_somewhat',
      naa_power_struggles: '2_somewhat',
      naa_emotions: '2_somewhat',
      naa_nurture_pfs_summary: '2_sometimes',
      naa_notes: undefined,
      naa_score: undefined,

      mom_id: SWATHI_MOM_ID,
    },
  });
  console.info("Created Mom Swathi's Well-Being Assessment");
}
