import { PrismaClient } from '@prisma/client';
import { faker } from '@faker-js/faker';

export enum Gender {
  Male = 'male',
  Female = 'female',
  Not_Specified = 'not_specified',
}

export enum LivesWith {
  Mother = 'mother',
  Father = 'father',
  BothParents = 'both_parents',
  Grandparents = 'grandparents',
  OtherFamily = 'other_family',
  FosterCare = 'foster_care',
  Other = 'other',
  NotSpecified = 'not_specified',
}

export enum LegalCustodyStatus {
  FullCustody = 'full_custody',
  SharedCustody = 'shared_custody',
  NoCustody = 'no_custody',
  TemporaryCustody = 'temporary_custody',
  Other = 'other',
  NotSpecified = 'not_specified',
}

export enum FatherInvolved {
  PhysicalPresence = 'physical_presence',
  FinancialSupport = 'financial_support',
  Childcare = 'childcare',
  EmotionalSupport = 'emotional_support',
  DecisionMaking = 'decision_making',
  NotInvolved = 'not_involved',
  Unknown = 'unknown',
  NotSpecified = 'not_specified',
}

const prisma = new PrismaClient();

const GENDER_OPTIONS = Object.values(Gender).filter(
  (value) => value !== Gender.Not_Specified,
);
const LIVES_WITH_OPTIONS = Object.values(LivesWith).filter(
  (value) => value !== LivesWith.NotSpecified,
);
const CUSTODY_STATUS_OPTIONS = Object.values(LegalCustodyStatus).filter(
  (value) => value !== LegalCustodyStatus.NotSpecified,
);
const FATHER_INVOLVED_OPTIONS = Object.values(FatherInvolved).filter(
  (value) => value !== FatherInvolved.NotSpecified,
);

export async function seedChildren() {
  const moms = await prisma.mom.findMany({
    where: {
      deleted_at: 0,
    },
  });

  for (const mom of moms) {
    // Create at least 3 children per mom
    const numChildren = faker.number.int({ min: 3, max: 5 });

    for (let i = 0; i < numChildren; i++) {
      // Get random involvement options (1 to 3 options)
      const numInvolvementOptions = faker.number.int({ min: 1, max: 3 });
      const fatherInvolvedOptions = faker.helpers.arrayElements(
        FATHER_INVOLVED_OPTIONS,
        numInvolvementOptions,
      );

      // If "not_involved" or "unknown" is selected, it should be the only option
      if (
        fatherInvolvedOptions.includes(FatherInvolved.NotInvolved) ||
        fatherInvolvedOptions.includes(FatherInvolved.Unknown)
      ) {
        const singleOption = fatherInvolvedOptions.includes(
          FatherInvolved.NotInvolved,
        )
          ? [FatherInvolved.NotInvolved]
          : [FatherInvolved.Unknown];

        await prisma.child.create({
          data: {
            first_name: faker.person.firstName(),
            birthdate: faker.date.past({ years: 18 }),
            gender: faker.helpers.arrayElement(GENDER_OPTIONS),
            lives_with: faker.helpers.arrayElement(LIVES_WITH_OPTIONS),
            legal_custody_status: faker.helpers.arrayElement(
              CUSTODY_STATUS_OPTIONS,
            ),
            father_involved: singleOption,
            father_involvement:
              singleOption[0] === FatherInvolved.NotInvolved
                ? "Father is not involved in child's life."
                : 'Unknown father involvement.',
            additional_info: faker.lorem.sentence(),
            active_child_welfare_involvement: '25_supportive_services',
            date_of_child_welfare_involvement: faker.date.past(),
            family_preservation_goal: 'prevent_foster_care_placement',
            family_preservation_impact: 'prevented_from_permanent_removal',
            mom_id: mom.id,
          },
        });
      } else {
        await prisma.child.create({
          data: {
            first_name: faker.person.firstName(),
            birthdate: faker.date.past({ years: 18 }),
            gender: faker.helpers.arrayElement(GENDER_OPTIONS),
            lives_with: faker.helpers.arrayElement(LIVES_WITH_OPTIONS),
            legal_custody_status: faker.helpers.arrayElement(
              CUSTODY_STATUS_OPTIONS,
            ),
            father_involved: fatherInvolvedOptions,
            father_involvement: faker.lorem.paragraph(1),
            additional_info: faker.lorem.sentence(),
            active_child_welfare_involvement: '25_supportive_services',
            date_of_child_welfare_involvement: faker.date.past(),
            family_preservation_goal: 'prevent_foster_care_placement',
            family_preservation_impact: 'prevented_from_permanent_removal',
            mom_id: mom.id,
          },
        });
      }
    }
  }

  console.log('Added Children');
}
