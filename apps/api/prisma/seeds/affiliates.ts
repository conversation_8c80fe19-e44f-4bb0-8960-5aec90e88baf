import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export const TEST_AFFILIATE_ID = '7a38bff0-d9cc-11ef-8f57-1b6d65d2fa20';
export const SECOND_AFFILIATE_ID = '8b49cff1-e0dd-22fg-9g68-2c7m76e3gb31';
export const THIRD_AFFILIATE_ID = '9c5adgg2-f1ee-33gh-0h79-3d8n87f4hc42';

export async function seedAffiliates() {
  // First affiliate
  await prisma.affiliate.upsert({
    where: { id: TEST_AFFILIATE_ID },
    update: {},
    create: {
      id: TEST_AFFILIATE_ID,
      name: 'Test Affiliate',
      description: 'Test Affiliate',
      status: 'Active',
      contact_name: 'Test Contact',
      agency_nickname: 'Test Agency',
      billing_address_street: '1 Melrose Place',
      billing_address_street_2: 'Suite 100',
      billing_address_city: 'Los Angeles',
      billing_address_state: 'CA',
      billing_address_postalcode: '90210',
      billing_address_country: 'US',
      phone_office: '1234567890',
      website: 'https://www.testaffiliate.com',
      email1: '<EMAIL>',
    },
  });
  console.info('Upserted Test Affiliate');

  // Second affiliate
  await prisma.affiliate.upsert({
    where: { id: SECOND_AFFILIATE_ID },
    update: {},
    create: {
      id: SECOND_AFFILIATE_ID,
      name: 'Second Affiliate',
      description: 'Second Affiliate Organization',
      status: 'Active',
      contact_name: 'Second Contact',
      agency_nickname: 'Second Agency',
      billing_address_street: '2 Main Street',
      billing_address_street_2: 'Floor 2',
      billing_address_city: 'New York',
      billing_address_state: 'NY',
      billing_address_postalcode: '10001',
      billing_address_country: 'US',
      phone_office: '2345678901',
      website: 'https://www.secondaffiliate.com',
      email1: '<EMAIL>',
    },
  });
  console.info('Upserted Second Affiliate');

  // Third affiliate
  await prisma.affiliate.upsert({
    where: { id: THIRD_AFFILIATE_ID },
    update: {},
    create: {
      id: THIRD_AFFILIATE_ID,
      name: 'Third Affiliate',
      description: 'Third Affiliate Organization',
      status: 'Active',
      contact_name: 'Third Contact',
      agency_nickname: 'Third Agency',
      billing_address_street: '3 Oak Avenue',
      billing_address_street_2: 'Building C',
      billing_address_city: 'Chicago',
      billing_address_state: 'IL',
      billing_address_postalcode: '60601',
      billing_address_country: 'US',
      phone_office: '3456789012',
      website: 'https://www.thirdaffiliate.com',
      email1: '<EMAIL>',
    },
  });
  console.info('Upserted Third Affiliate');
}
