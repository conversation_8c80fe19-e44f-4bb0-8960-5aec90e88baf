import { PrismaClient } from '@prisma/client';
import {
  ADMINISTRATOR_ROLE_ID,
  ADVOCATE_ROLE_ID,
  COORDINATOR_ROLE_ID,
  SUPERVISOR_ROLE_ID,
} from './roles';
import { TEST_AFFILIATE_ID } from './affiliates';
import { faker } from '@faker-js/faker';

const prisma = new PrismaClient();

async function createUser(
  id: string,
  userData: {
    roleId: string;
    userRoleId: string;
    affiliateId: string;
    username: string;
    email: string;
    firstName: string;
    lastName: string;
    passwordHash: string;
    advocate_status?:
      | 'Active'
      | 'Inactive'
      | 'Applied'
      | 'Interested'
      | 'Rejected'
      | 'In_Training'
      | 'Awaiting_Pairing';
    coordinatorId?: string;
  },
) {
  const { roleId, userRoleId, coordinatorId, ...user } = userData;

  await prisma.user.upsert({
    where: { id },
    update: {
      id,
      ...user,
      // Form builder doesn't support international phone numbers, so we use national format
      // eg 10 digits like ************ -- don't use ****** 491 0562
      phone: faker.phone.number({ style: 'national' }),
      secondary_phone: faker.phone.number({ style: 'national' }),
    },
    create: {
      id,
      ...user,
      ...(coordinatorId
        ? {
            assignedCoordinators: {
              connect: [
                {
                  id: coordinatorId,
                },
              ],
            },
          }
        : {}),
    },
  });
  console.info(`Upserted User ${user.username}`);

  await prisma.userRole.upsert({
    where: { id: userRoleId },
    update: {},
    create: {
      id: userRoleId,
      user_id: id,
      role_id: roleId,
    },
  });
  console.info(`Upserted User Role ${userRoleId}`);
}

export const CAROL_USER_ID = '58544ec4-d693-4d0c-9a66-95b74e879059';
export const SARAH_USER_ID = '7ef69ded-dd01-4bb9-9d28-ef5872995a49';
export const ANDY_USER_ID = 'fa13e723-d887-4381-b25c-5a2ade423bcc';
export const ABAGIAL_USER_ID = '83e54d70-51db-4392-8f6f-65f70ff30880';
export const SAMMY_USER_ID = '4d60905e-d5bf-47c9-a96e-d7f5f3174cce';

// New advocate IDs
export const MICHAEL_USER_ID = 'c3a22e70-5d7b-4982-b5c2-86f7d82a7fc2';
export const EMILY_USER_ID = 'd9f42b1d-17a4-48c3-a8c1-72b9e5e1f3b0';
export const JAMES_USER_ID = 'e8c71f3c-6c9a-4d45-b562-1c4f6d814f2b';
export const OLIVIA_USER_ID = 'f9e87c2a-5b3d-4a12-9e67-8c24f3a9d0e7';
export const ETHAN_USER_ID = 'a1b2c3d4-e5f6-4a7b-8c9d-0e1f2a3b4c5d';

export async function seedUsers() {
  const superSecretPasswordHash =
    '$2b$10$e51llQJGBinJKWbO73UHn.Kis90tMGa1TW1Hw7AKDqODmvDBdrYRC';

  await createUser(CAROL_USER_ID, {
    roleId: COORDINATOR_ROLE_ID,
    userRoleId: 'be5ee1fe-2b68-4d2d-bbdc-eef6bf1ebdd0',
    affiliateId: TEST_AFFILIATE_ID,
    username: 'carol',
    email: '<EMAIL>',
    firstName: 'Carol',
    lastName: 'Coordinator',
    passwordHash: superSecretPasswordHash,
  });

  await createUser(ABAGIAL_USER_ID, {
    roleId: ADVOCATE_ROLE_ID,
    userRoleId: '67098d61-6ec1-4922-a122-0e27ba8cbaad',
    affiliateId: TEST_AFFILIATE_ID,
    username: 'abagail',
    email: '<EMAIL>',
    firstName: 'Abagail',
    lastName: 'Advocate',
    passwordHash: superSecretPasswordHash,
    advocate_status: 'Active',
    coordinatorId: CAROL_USER_ID,
  });

  await createUser(SARAH_USER_ID, {
    roleId: SUPERVISOR_ROLE_ID,
    userRoleId: '534967cf-f2fd-4c0c-ae6d-8a2395e44f01',
    affiliateId: TEST_AFFILIATE_ID,
    username: 'sarah',
    email: '<EMAIL>',
    firstName: 'Sarah',
    lastName: 'Supervisor',
    passwordHash: superSecretPasswordHash,
  });

  await createUser(ANDY_USER_ID, {
    roleId: ADMINISTRATOR_ROLE_ID,
    userRoleId: '50001e0f-ca36-4660-8e10-615a955895e6',
    affiliateId: TEST_AFFILIATE_ID,
    username: 'andy',
    email: '<EMAIL>',
    firstName: 'Andy',
    lastName: 'Administrator',
    passwordHash: superSecretPasswordHash,
  });

  // Make Andy a Supervisor too....otherwise he won't be able to login to the Portal.
  const andySupervisorUserRoleId = 'a4e742d1-be1f-4b57-a491-e31779745c6d';
  await prisma.userRole.upsert({
    where: { id: andySupervisorUserRoleId },
    update: {},
    create: {
      id: andySupervisorUserRoleId,
      user_id: ANDY_USER_ID,
      role_id: SUPERVISOR_ROLE_ID,
    },
  });
  console.info(`Upserted User Role ${andySupervisorUserRoleId}`);

  await createUser(SAMMY_USER_ID, {
    roleId: ADVOCATE_ROLE_ID,
    userRoleId: 'e8c9e80f-1cb7-45b9-9197-67602aa6bae9',
    affiliateId: TEST_AFFILIATE_ID,
    username: 'sammy',
    email: '<EMAIL>',
    firstName: 'Sammy',
    lastName: 'Super-Advocate',
    passwordHash: superSecretPasswordHash,
    advocate_status: 'Active',
  });

  // Make Sammy a Coordinator too....b/c they're a super-advocate.
  const sammyCoordinatorUserRoleId = 'e3bde250-aecd-4dd3-a963-cb4eaae6af66';
  await prisma.userRole.upsert({
    where: { id: sammyCoordinatorUserRoleId },
    update: {},
    create: {
      id: sammyCoordinatorUserRoleId,
      user_id: SAMMY_USER_ID,
      role_id: COORDINATOR_ROLE_ID,
    },
  });
  console.info(`Upserted User Role ${sammyCoordinatorUserRoleId}`);

  // New advocates
  await createUser(MICHAEL_USER_ID, {
    roleId: ADVOCATE_ROLE_ID,
    userRoleId: 'f1a2b3c4-d5e6-4f7g-8h9i-0j1k2l3m4n5o',
    affiliateId: TEST_AFFILIATE_ID,
    username: 'michael',
    email: '<EMAIL>',
    firstName: 'Michael',
    lastName: 'Johnson',
    passwordHash: superSecretPasswordHash,
    advocate_status: 'Active',
  });

  await createUser(EMILY_USER_ID, {
    roleId: ADVOCATE_ROLE_ID,
    userRoleId: 'p6q7r8s9-t0u1-4v2w-3x4y-5z6a7b8c9d0e',
    affiliateId: TEST_AFFILIATE_ID,
    username: 'emily',
    email: '<EMAIL>',
    firstName: 'Emily',
    lastName: 'Williams',
    passwordHash: superSecretPasswordHash,
    advocate_status: 'Active',
  });

  await createUser(JAMES_USER_ID, {
    roleId: ADVOCATE_ROLE_ID,
    userRoleId: 'f1g2h3i4-j5k6-4l7m-8n9o-0p1q2r3s4t5u',
    affiliateId: TEST_AFFILIATE_ID,
    username: 'james',
    email: '<EMAIL>',
    firstName: 'James',
    lastName: 'Brown',
    passwordHash: superSecretPasswordHash,
    advocate_status: 'Active',
  });

  await createUser(OLIVIA_USER_ID, {
    roleId: ADVOCATE_ROLE_ID,
    userRoleId: 'v6w7x8y9-z0a1-4b2c-3d4e-5f6g7h8i9j0k',
    affiliateId: TEST_AFFILIATE_ID,
    username: 'olivia',
    email: '<EMAIL>',
    firstName: 'Olivia',
    lastName: 'Davis',
    passwordHash: superSecretPasswordHash,
    advocate_status: 'In_Training',
  });

  await createUser(ETHAN_USER_ID, {
    roleId: ADVOCATE_ROLE_ID,
    userRoleId: 'l1m2n3o4-p5q6-4r7s-8t9u-0v1w2x3y4z5a',
    affiliateId: TEST_AFFILIATE_ID,
    username: 'ethan',
    email: '<EMAIL>',
    firstName: 'Ethan',
    lastName: 'Miller',
    passwordHash: superSecretPasswordHash,
    advocate_status: 'Awaiting_Pairing',
  });

  // Associate all new advocates with Carol (Coordinator)
  const advocatesToAssign = [
    ABAGIAL_USER_ID,
    MICHAEL_USER_ID,
    EMILY_USER_ID,
    JAMES_USER_ID,
    OLIVIA_USER_ID,
    ETHAN_USER_ID,
  ];

  // Create advocate-to-coordinator relationships
  for (const advocateId of advocatesToAssign) {
    // Connect advocate to coordinator (Carol)
    await prisma.user.update({
      where: { id: advocateId },
      data: {
        assignedCoordinators: {
          connect: { id: CAROL_USER_ID },
        },
      },
    });
    console.info(`Associated advocate ${advocateId} with coordinator Carol`);
  }
}
