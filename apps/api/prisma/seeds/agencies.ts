import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export const TEST_AGENCY_ID = 'd915ae80-d9cb-11ef-8f57-1b6d65d2fa20';

export async function seedAgencies() {
  await prisma.agency.upsert({
    where: { id: TEST_AGENCY_ID },
    update: {},
    create: {
      id: TEST_AGENCY_ID,
      name: 'Test Agency',
      agency_name: 'Test Agency',
      agency_phone: '1234567890',
      address: '1 Melrose Place',
      address_city: 'Los Angeles',
      address_state: 'CA',
      address_postalcode: '90210',
      contact_first_name: '<PERSON>',
      contact_last_name: '<PERSON><PERSON>',
      contact_email: '<EMAIL>',
      contact_phone: '1234567890',
    },
  });
  console.info('Upserted Test Agency');
}
