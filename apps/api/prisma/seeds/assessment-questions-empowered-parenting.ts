import { PrismaClient } from '@prisma/client';
import { EMPOWERED_PARENTING_ASSESSMENT_ID } from './assessments';
import {
  SELF_REGULATION_ASSESSMENT_CONSTRUCT_ID,
  PARENTAL_CONNECTION_ASSESSMENT_CONSTRUCT_ID,
  GUIDANCE_AND_DISCIPLINE_ASSESSMENT_CONSTRUCT_ID,
  POWER_AND_INDEPENDENCE_ASSESSMENT_CONSTRUCT_ID,
  SAFETY_AND_VALUES_ASSESSMENT_CONSTRUCT_ID,
} from './assessment-constructs';

const prisma = new PrismaClient();

export async function seedEmpoweredParentingAssessmentQuestions() {
  let id = 'e2a1c7b2-3e4a-4b8e-9c1d-2f3e4a5b6c7d';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 1,
      question:
        'Parental stress and emotions impact the emotional state and overall well being of a child.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: SELF_REGULATION_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'f4b2d3c1-5e6a-4c8d-8b2e-7a6c5d4e3b2a';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 2,
      question:
        'Naming and recognizing my own emotions is important to my day-to-day functioning.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: SELF_REGULATION_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'a7c8e9d1-2b3a-4c5d-9e8f-1a2b3c4d5e6f';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 3,
      question: 'I am confident in my ability to recognize and manage stress.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: SELF_REGULATION_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'b3d2e1c4-6a7b-4e8c-9d1f-2a3b4c5d6e7f';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 4,
      question:
        'Misbehavior is a common way for my child to undermine my authority and control me.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: SELF_REGULATION_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'c5e6f7a8-1b2c-4d3e-8f9a-3b2c1d4e5f6a';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 5,
      question:
        'I make an effort to co-regulate/calm down with my child by providing a safe and supportive presence during challenging moments.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: SELF_REGULATION_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'd8f9a1b2-3c4d-4e5f-9a8b-4c3d2e1f5a6b';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 6,
      question:
        'In the last month, I have made efforts to repair my relationship and reconnect with my child after a conflict.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: PARENTAL_CONNECTION_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'e1f2a3b4-5c6d-4e7f-8a9b-5d4c3e2f1a6b';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 7,
      question: 'Parents should apologize to children when they are wrong.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: PARENTAL_CONNECTION_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'f3a4b5c6-7d8e-4f9a-1b2c-6e5d4c3b2a1f';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 8,
      question:
        'When my child expresses their emotions, I always show empathy.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: PARENTAL_CONNECTION_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'a2b3c4d5-6e7f-4a8b-9c1d-7f6e5d4c3b2a';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 9,
      question:
        "Rules and boundaries need to be explained to children or they won't follow them",
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: PARENTAL_CONNECTION_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'b4c5d6e7-8f9a-4b1c-2d3e-8a7b6c5d4e3f';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 10,
      question:
        'Childhood attachment is a foundational lesson of trust and growth, impacting individuals throughout their lives.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: PARENTAL_CONNECTION_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'c6d7e8f9-1a2b-4c3d-5e6f-9b8a7c6d5e4f';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 11,
      question:
        'I am confident in my ability to build secure attachment with my child.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: PARENTAL_CONNECTION_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'd7e8f9a1-2b3c-4d5e-6f7a-1c2b3d4e5f6a';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 12,
      question:
        'I engage in daily quality time and shared activities with my child.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: PARENTAL_CONNECTION_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'e8f9a1b2-3c4d-4e5f-6a7b-2d3e4c5b6a7f';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 13,
      question:
        'Attachment and connection has a major impact on my parenting approach.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: PARENTAL_CONNECTION_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'f9a1b2c3-4d5e-4f6a-7b8c-3e4d5c6b7a8f';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 14,
      question:
        'Authentic connection with a child strengthens attachment and fosters emotional, relational, and physical connections, benefitting overall child development.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: PARENTAL_CONNECTION_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'a1b2c3d4-5e6f-4a7b-8c9d-4f5e6d7c8b9a';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 15,
      question:
        'Children communicate needs (emotional, sensory, physical, relational) through behaviors.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: GUIDANCE_AND_DISCIPLINE_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'b2c3d4e5-6f7a-4b8c-9d1e-5a6b7c8d9e1f';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 16,
      question:
        'In the last month, I was able to identify when my child was expressing emotional, sensory, physical, and relational needs.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: GUIDANCE_AND_DISCIPLINE_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'c3d4e5f6-7a8b-4c9d-1e2f-6b7c8d9e1f2a';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 17,
      question:
        'Discipline is more effective than punishment in teaching children valuable life skills.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: GUIDANCE_AND_DISCIPLINE_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'd4e5f6a7-8b9c-4d1e-2f3a-7c8d9e1f2a3b';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 18,
      question:
        'Spanking and time out are the best ways to get my child to change their behavior.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: GUIDANCE_AND_DISCIPLINE_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'e5f6a7b8-9c1d-4e2f-3a4b-8d9e1f2a3b4c';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 19,
      question:
        'I am comfortable sharing power with my child and engaging in respectful negotiations.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: POWER_AND_INDEPENDENCE_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'f6a7b8c9-1d2e-4f3a-5b6c-9e1f2a3b4c5d';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 20,
      question:
        'Children need their parents to be authority figures more than their friends.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: POWER_AND_INDEPENDENCE_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'a7b8c9d1-2e3f-4a5b-6c7d-1f2a3b4c5d6e';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 21,
      question:
        'It is important to allow children to make decisions and learn from their experiences.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: POWER_AND_INDEPENDENCE_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'b8c9d1e2-3f4a-4b5c-6d7e-2a3b4c5d6e7f';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 22,
      question:
        'I communicate expectations, limits, and boundaries in a respectful and consistent manner',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: POWER_AND_INDEPENDENCE_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'c9d1e2f3-4a5b-4c6d-7e8f-3b4c5d6e7f8a';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 23,
      question: 'I am confident in teaching safety to my child',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: SAFETY_AND_VALUES_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'd1e2f3a4-5b6c-4d7e-8f9a-4c5d6e7f8a9b';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 24,
      question:
        'Modeling safe behaviors and relationships in front of my child is an effective way of teaching safety.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: SAFETY_AND_VALUES_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'e2f3a4b5-6c7d-4e8f-9a1b-5d6e7f8a9b1c';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 25,
      question:
        'It is the parents responsibility to protect their children and communicate safety practices.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: SAFETY_AND_VALUES_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'f3a4b5c6-7d8e-4f9a-1b2c-6e5d4c3b2a1f';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 26,
      question:
        'I have taught, or intend to teach, my child to come to me if they ever experience something unsafe or uncomfortable',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: SAFETY_AND_VALUES_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'a4b5c6d7-8e9f-4a1b-2c3d-7f8a9b1c2d3e';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 27,
      question:
        'It would benefit my family to establish "family values" and discuss them regularly.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: SAFETY_AND_VALUES_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'b5c6d7e8-9f1a-4b2c-3d4e-8a9b1c2d3e4f';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 28,
      question:
        'Consistency is important when it comes to maintaining and upholding family values.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: SAFETY_AND_VALUES_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'c6d7e8f9-1a2b-4c3d-5e6f-9b8a7c6d5e4f';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 29,
      question:
        'How a home feels significantly impacts the people living in the home.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: SAFETY_AND_VALUES_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'd7e8f9a1-2b3c-4d5e-6f7a-1c2b3d4e5f6a';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 30,
      question: `Parents play a vital role in modeling and meeting their children's needs to help them develop a strong foundation for life.`,
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: SAFETY_AND_VALUES_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'e8f9a1b2-3c4d-4e5f-6a7b-2d3e4c5b6a7f';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 31,
      question:
        'It is necessary for parents to prioritize their own needs (social, physical, intellectual, creative, emotional, and spiritual) in order to effectively meet the needs of children',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: SAFETY_AND_VALUES_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'f9a1b2c3-4d5e-4f6a-7b8c-3e4d5c6b7a8f';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 32,
      question: `Building trust early in childhood significantly impacts a child's social and relational skills.`,
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: SAFETY_AND_VALUES_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'a1b2c3d4-5e6f-4a7b-8c9d-4f5e6d7c8b9a';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 33,
      question:
        'Meeting basic needs is crucial for overall child development, building upon each step in the process.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: SAFETY_AND_VALUES_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  id = 'b2c3d4e5-6f7a-4b8c-9d1e-5a6b7c8d9e1f';
  await prisma.assessmentQuestion.upsert({
    where: { id: id },
    update: {},
    create: {
      id: id,
      order: 34,
      question:
        'I am confident in my ability to provide both structure and nurture to my child in various situations.',
      responseType: 'int',
      assessment: {
        connect: {
          id: EMPOWERED_PARENTING_ASSESSMENT_ID,
        },
      },
      assessmentConstruct: {
        connect: {
          id: SAFETY_AND_VALUES_ASSESSMENT_CONSTRUCT_ID,
        },
      },
    },
  });

  console.info('Upserted Empowered Parenting Assessment Questions');
}
