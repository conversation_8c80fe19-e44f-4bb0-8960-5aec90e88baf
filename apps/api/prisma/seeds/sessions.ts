import { PrismaClient } from '@prisma/client';
import {
  SWATHI_AND_ABAGAIL_PAIRING_ID,
  SAHARA_AND_ABAGAIL_PAIRING_ID,
} from './pairings';
import { addHours } from 'date-fns';

const prisma = new PrismaClient();

export const SESSION_1_ID = 'bb7e68e5-8709-4c68-a780-c4194ef0183a';

export const SESSION_2_ID = 'e8197400-b8e5-4bf2-a75b-084e71bb2369';

export const SESSION_3_ID = 'e6eed425-2512-42cb-93a0-234809367b74';

export const SESSION_4_ID = '9c5a566f-61e1-466b-ab83-1b266f021eb2';

export async function seedSessions() {
  await prisma.session.upsert({
    where: { id: SESSION_1_ID },
    update: {},
    create: {
      id: SESSION_1_ID,
      name: 'Session One',
      description: 'Session One Description',
      location: 'Coffee shop',
      date_start: addHours(new Date(), -50),
      date_end: addHours(new Date(), -48),
      status: 'Held',
      session_type: 'Support_Session',
      pairing_id: SWATHI_AND_ABAGAIL_PAIRING_ID,
    },
  });
  console.info('Upserted Session 1');

  // Session 2
  await prisma.session.upsert({
    where: { id: SESSION_2_ID },
    update: {},
    create: {
      id: SESSION_2_ID,
      name: 'Session Two',
      description: 'Session Two Description',
      location: 'Community Center',
      date_start: addHours(new Date(), -24),
      date_end: addHours(new Date(), -23),
      status: 'Held',
      session_type: 'Track_Session',
      pairing_id: SAHARA_AND_ABAGAIL_PAIRING_ID,
    },
  });
  console.info('Upserted Session 2');

  // Session 3
  await prisma.session.upsert({
    where: { id: SESSION_3_ID },
    update: {},
    create: {
      id: SESSION_3_ID,
      name: 'Session Three',
      description: 'Session Three Description',
      location: 'Different Coffee shop',
      date_start: addHours(new Date(), 96),
      date_end: addHours(new Date(), 98),
      status: 'Planned',
      session_type: 'Track_Session',
      pairing_id: SWATHI_AND_ABAGAIL_PAIRING_ID,
    },
  });
  console.info('Upserted Session 3');

  // Session 4
  await prisma.session.upsert({
    where: { id: SESSION_4_ID },
    update: {},
    create: {
      id: SESSION_4_ID,
      name: 'Session Four',
      description: 'Session Four Description',
      location: 'Community Center',
      date_start: addHours(new Date(), -24),
      date_end: addHours(new Date(), -23),
      status: 'Held',
      session_type: 'Track_Session',
      pairing_id: SWATHI_AND_ABAGAIL_PAIRING_ID,
    },
  });
  console.info('Upserted Session 4');
}
