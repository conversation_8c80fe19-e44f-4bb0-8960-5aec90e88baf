import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Define tag IDs for reference
export const PARENTING_TAG_ID = 'c7e3d4f5-6a7b-8c9d-0e1f-2a3b4c5d6e7f';
export const MENTAL_HEALTH_TAG_ID = 'd8f9e0a1-2b3c-4d5e-6f7g-8h9i0j1k2l3m';
export const EDUCATION_TAG_ID = 'e4f5g6h7-i8j9-k0l1-m2n3-o4p5q6r7s8t9';
export const FINANCE_TAG_ID = 'f5e4d3c2-b1a9-8765-4321-0fedcba98765';
export const WELLNESS_TAG_ID = 'g6h7i8j9-k0l1-m2n3-o4p5-q6r7s8t9u0v1';

// Create an array of tags to insert
const tags = [
  {
    id: PARENTING_TAG_ID,
    name: 'Parenting',
    description: 'Resources related to parenting skills and child development',
  },
  {
    id: MENTAL_HEALTH_TAG_ID,
    name: 'Mental Health',
    description: 'Resources for mental health support and wellbeing',
  },
  {
    id: EDUCATION_TAG_ID,
    name: 'Education',
    description: 'Educational resources and learning materials',
  },
  {
    id: FINANCE_TAG_ID,
    name: 'Finance',
    description: 'Financial literacy and management resources',
  },
  {
    id: WELLNESS_TAG_ID,
    name: 'Wellness',
    description: 'Resources for physical health and overall wellness',
  },
];

export async function seedTags() {
  // Insert all tags
  for (const tag of tags) {
    // First try to find existing tag with the same name
    const existingTag = await prisma.tag.findFirst({
      where: {
        name: tag.name,
        deleted_at: 0, // Only find non-deleted tags
      },
    });

    if (existingTag) {
      // If a tag with this name exists, update it
      await prisma.tag.update({
        where: { id: existingTag.id },
        data: {
          description: tag.description,
          // Ensure we keep the same id if it already exists
          id: tag.id,
        },
      });
    } else {
      // If no tag with this name exists, create a new one
      await prisma.tag.upsert({
        where: { id: tag.id },
        update: {},
        create: {
          id: tag.id,
          name: tag.name,
          description: tag.description,
          deleted_at: 0, // Explicitly set deleted_at to 0
          updated_at: new Date(), // Add updated_at field
        },
      });
    }
  }

  console.info('Upserted Tags');
}
