import { PrismaClient } from '@prisma/client';
import { SWATHI_MOM_ID } from './moms';
import { CAROL_USER_ID } from './users';

const prisma = new PrismaClient();

export async function seedBenevolenceNeeds() {
  const benevolenceNeedId = '80d2a221-cc3e-4436-94ab-c1be2103d1bd';
  await prisma.benevolenceNeed.upsert({
    where: { id: benevolenceNeedId },
    update: {},
    create: {
      id: benevolenceNeedId,
      name: 'Benevolence Need 1',
      description: 'Benevolence Need 1 Description',
      is_urgent_c: true,
      did_address_need_c: true,
      resolved_date_c: new Date('2025-01-01'),
      notes_c: 'Benevolence Need 1 Notes',
      type_c: 'Other',
      other_is_referral_needed_c: false,
      resolvedByUserId: CAROL_USER_ID,
      momId: SWATHI_MOM_ID,
    },
  });
  console.info('Upserted Benevolence Need 1');

  const benevolenceNeedId2 = 'a40dc14f-b9ce-4c34-913e-42b8f5b51e1c';
  await prisma.benevolenceNeed.upsert({
    where: { id: benevolenceNeedId2 },
    update: {},
    create: {
      id: benevolenceNeedId2,
      name: 'Benevolence Need 2',
      description: 'Benevolence Need 2 Description',
      is_urgent_c: false,
      did_address_need_c: true,
      resolved_date_c: new Date('2025-01-10'),
      notes_c: 'Benevolence Need 2 Notes',
      type_c: 'Financial',
      financial_amount_gifted_c: 100.0,
      financial_amount_requested_c: 200.0,
      financial_mom_contribution_c: 100.0,
      financial_prevention_plan_c:
        'Financial Prevention Plan - Make more money',
      resolvedByUserId: CAROL_USER_ID,
      momId: SWATHI_MOM_ID,
    },
  });
  console.info('Upserted Benevolence Need 1');

  const benevolenceNeedId3 = '1d765f6e-d747-49f1-ace0-d91616192fe5';
  await prisma.benevolenceNeed.upsert({
    where: { id: benevolenceNeedId3 },
    update: {},
    create: {
      id: benevolenceNeedId3,
      name: 'Benevolence Need 3',
      description: 'Benevolence Need 3 Description',
      is_urgent_c: true,
      notes_c: 'Benevolence Need 3 Notes - Mom needs diapers',
      type_c: 'Physical',
      momId: SWATHI_MOM_ID,
    },
  });
  console.info('Upserted Benevolence Need 1');
}
