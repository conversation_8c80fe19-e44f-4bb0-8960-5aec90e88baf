import { PrismaClient } from '@prisma/client';
import {
  PARENTING_TAG_ID,
  MENTAL_HEALTH_TAG_ID,
  EDUCATION_TAG_ID,
  FINANCE_TAG_ID,
  WELLNESS_TAG_ID,
} from './tags';
import { TEST_AFFILIATE_ID } from './affiliates';

const prisma = new PrismaClient();

// Define track IDs for reference
export const NURTURING_PARENTING_TRACK_ID =
  'ed0382bc-c8ee-4cea-8361-7b2c361a1b4a';
export const EMOTIONAL_WELLNESS_TRACK_ID =
  'a1b2c3d4-e5f6-4789-abcd-1234567890ab';
export const FINANCIAL_LITERACY_TRACK_ID =
  'f1e2d3c4-b5a6-4789-abcd-1234567890cd';
export const CHILD_DEVELOPMENT_TRACK_ID =
  'c1d2e3f4-g5h6-4789-abcd-1234567890ef';
export const STRESS_MANAGEMENT_TRACK_ID =
  's1t2r3e4-s5s6-4789-abcd-1234567890gh';
export const EDUCATION_PLANNING_TRACK_ID =
  'e1d2u3c4-a5t6-4789-abcd-1234567890ij';
export const FAMILY_HEALTH_TRACK_ID = 'h1e2a3l4-t5h6-4789-abcd-1234567890kl';

// Define track data
const tracks = [
  {
    id: NURTURING_PARENTING_TRACK_ID,
    title: 'Nurturing Parenting',
    description:
      'Learn effective parenting techniques that nurture healthy development in children',
    tagId: PARENTING_TAG_ID,
  },
  {
    id: EMOTIONAL_WELLNESS_TRACK_ID,
    title: 'Emotional Wellness',
    description:
      'Develop skills to manage emotions and improve mental well-being',
    tagId: MENTAL_HEALTH_TAG_ID,
  },
  {
    id: FINANCIAL_LITERACY_TRACK_ID,
    title: 'Financial Literacy',
    description:
      'Learn budgeting, saving, and financial planning for your family',
    tagId: FINANCE_TAG_ID,
  },
  {
    id: CHILD_DEVELOPMENT_TRACK_ID,
    title: 'Child Development Milestones',
    description:
      'Understand key developmental stages and how to support your child',
    tagId: PARENTING_TAG_ID,
  },
  {
    id: STRESS_MANAGEMENT_TRACK_ID,
    title: 'Stress Management',
    description: 'Learn techniques to reduce stress and prevent burnout',
    tagId: MENTAL_HEALTH_TAG_ID,
  },
  {
    id: EDUCATION_PLANNING_TRACK_ID,
    title: 'Education Planning',
    description:
      "Resources for planning your children's education and setting them up for success",
    tagId: EDUCATION_TAG_ID,
  },
  {
    id: FAMILY_HEALTH_TRACK_ID,
    title: 'Family Health & Nutrition',
    description:
      'Practical guidance on maintaining health and nutrition for the whole family',
    tagId: WELLNESS_TAG_ID,
  },
];

export async function seedTracks() {
  // Insert all tracks
  for (const track of tracks) {
    await prisma.track.upsert({
      where: { id: track.id },
      update: {},
      create: {
        id: track.id,
        title: track.title,
        description: track.description,
        updated_at: new Date(),
        affiliates: {
          connect: {
            id: TEST_AFFILIATE_ID,
          },
        },
      },
    });

    // Create a document with this track and associate it with the tag
    const documentId = `doc-${track.id}`;
    await prisma.document.upsert({
      where: { id: documentId },
      update: {},
      create: {
        id: documentId,
        document_name: `${track.title} Overview`,
        description: `Overview document for the ${track.title} track`,
        filename: `${track.title.toLowerCase().replace(/\s+/g, '-')}-overview.pdf`,
        mimeType: 'application/pdf',
        updated_at: new Date(),
      },
    });

    // Create document tag association
    await prisma.documentTag.upsert({
      where: {
        document_id_tag_id_deleted_at: {
          document_id: documentId,
          tag_id: track.tagId,
          deleted_at: 0,
        },
      },
      update: {},
      create: {
        id: `doc-tag-${documentId}-${track.tagId}`,
        document_id: documentId,
        tag_id: track.tagId,
        deleted_at: 0,
        updated_at: new Date(),
      },
    });
  }

  console.info('Upserted Tracks with Tag Associations');
}
