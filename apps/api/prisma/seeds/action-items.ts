import { PrismaClient } from '@prisma/client';
import { SWATHI_GOAL_1_ID, SWATHI_GOAL_2_ID } from './goals';
import { addDays } from 'date-fns';

const prisma = new PrismaClient();

export async function seedActionItems() {
  const actionItemId = 'fdd67a89-5cb9-4096-9cec-3b9ed9b26bc5';
  await prisma.actionItem.upsert({
    where: { id: actionItemId },
    update: {},
    create: {
      id: actionItemId,
      name: 'Action Item 1',
      description: 'Action Item 1 Description',
      dueDate: addDays(new Date(), -3),
      doneDate: addDays(new Date(), -4),
      goalId: SWATHI_GOAL_1_ID,
    },
  });
  console.info('Upserted Action Item 1');

  const actionItemId2 = 'c640c7ed-4ac7-4c5f-8273-23948b0b490d';
  await prisma.actionItem.upsert({
    where: { id: actionItemId2 },
    update: {},
    create: {
      id: actionItemId2,
      name: 'Action Item 2',
      description: 'Action Item 2 Description',
      dueDate: addDays(new Date(), -7),
      doneDate: addDays(new Date(), -8),
      goalId: SWATHI_GOAL_1_ID,
    },
  });
  console.info('Upserted Action Item 2');

  const actionItemId3 = 'db8ae598-96be-4d0f-9c52-3ef45300f7c1';
  await prisma.actionItem.upsert({
    where: { id: actionItemId3 },
    update: {},
    create: {
      id: actionItemId3,
      name: 'Action Item 3',
      description: 'Action Item 3 Description',
      dueDate: addDays(new Date(), -1),
      goalId: SWATHI_GOAL_2_ID,
    },
  });
  console.info('Upserted Action Item 3');

  const actionItemId4 = 'd4cfcb1a-57e4-4045-8bcd-3ac597d5ef27';
  await prisma.actionItem.upsert({
    where: { id: actionItemId4 },
    update: {},
    create: {
      id: actionItemId4,
      name: 'Action Item 4',
      description: 'Action Item 4 Description',
      dueDate: addDays(new Date(), 7),
      goalId: SWATHI_GOAL_2_ID,
    },
  });
  console.info('Upserted Action Item 4');
}
