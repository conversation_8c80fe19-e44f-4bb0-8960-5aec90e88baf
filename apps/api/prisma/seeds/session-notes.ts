import { PrismaClient } from '@prisma/client';
import {
  SESSION_1_ID,
  SESSION_2_ID,
  SESSION_3_ID,
  SESSION_4_ID,
} from './sessions';
import {
  SAHARA_LESSON_2_FINANCIAL_LITERACY_ID,
  SWATHI_LESSON_1_SPICES_ID,
  SWATHI_LESSON_2_FINANCIAL_LITERACY_ID,
} from './lessons';

const prisma = new PrismaClient();

export const SESSION_NOTE_1_ID = 'e546f8f3-9200-45b0-a6f4-7e5625ff7b50';

export const SESSION_NOTE_2_ID = '9109d155-e32e-4cd0-a4ff-dbbf49bd841c';

export const SESSION_NOTE_3_ID = '66679c39-9261-420e-97f8-f29cc9137410';

export const SESSION_NOTE_4_ID = '11e10194-a063-4e0a-9e7e-f854210aa172';

export async function seedSessionNotes() {
  await prisma.sessionNote.upsert({
    where: { id: SESSION_NOTE_1_ID },
    update: {},
    create: {
      id: SESSION_NOTE_1_ID,
      name: 'Session Note One',
      description: 'Session Note One Description',
      note: 'Session Note One Note',
      status: 'submitted',
      attendance_and_promptness: 'On_Time',
      moms_engagement_c: 'Full',
      new_attempt: false,
      new_attempt_example: 'No new attempt',
      date_submitted_c: new Date(),
      session: {
        connect: {
          id: SESSION_1_ID,
        },
      },
    },
  });
  console.info('Upserted Session Note 1');

  // Session 2
  await prisma.sessionNote.upsert({
    where: { id: SESSION_NOTE_2_ID },
    update: {},
    create: {
      id: SESSION_NOTE_2_ID,
      name: 'Session Note Two',
      description: 'Session Note Two Description',
      note: 'Session Note Two Note',
      status: 'new',
      session: {
        connect: {
          id: SESSION_2_ID,
        },
      },
      covered_lesson: {
        connect: {
          id: SAHARA_LESSON_2_FINANCIAL_LITERACY_ID,
        },
      },
    },
  });
  console.info('Upserted Session Note 2');

  // Session 3
  await prisma.sessionNote.upsert({
    where: { id: SESSION_NOTE_3_ID },
    update: {},
    create: {
      id: SESSION_NOTE_3_ID,
      name: 'Session Note Three',
      description: 'Session Note Three Description',
      note: 'Session Note Three Note',
      status: 'new',
      session: {
        connect: {
          id: SESSION_3_ID,
        },
      },
      covered_lesson: {
        connect: {
          id: SWATHI_LESSON_1_SPICES_ID,
        },
      },
    },
  });
  console.info('Upserted Session Note 3');

  // Session 4
  await prisma.sessionNote.upsert({
    where: { id: SESSION_NOTE_4_ID },
    update: {},
    create: {
      id: SESSION_NOTE_4_ID,
      name: 'Session Note Four',
      description: 'Session Note Four Description',
      note: 'Session Note Four Note',
      status: 'new',
      session: {
        connect: {
          id: SESSION_4_ID,
        },
      },
      covered_lesson: {
        connect: {
          id: SWATHI_LESSON_2_FINANCIAL_LITERACY_ID,
        },
      },
    },
  });
  console.info('Upserted Session Note 4');
}
