import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export const EMPOWERED_PARENTING_ASSESSMENT_ID =
  '1cbdeddf-432e-4ecb-bc50-b038b798d291';

export const RESILIENCE_PRE_ASSESSMENT_ID =
  'cb8398f9-dc29-438c-b8c4-e5025cfbf971';

export const RESILIENCE_POST_ASSESSMENT_ID =
  '8ff2b44b-3348-4c75-bf87-2a9ca3176f26';

export async function seedAssessments() {
  await prisma.assessment.upsert({
    where: { id: EMPOWERED_PARENTING_ASSESSMENT_ID },
    update: {},
    create: {
      id: EMPOWERED_PARENTING_ASSESSMENT_ID,
      name: 'Empowered Parenting',
      order: 1,
    },
  });
  console.info('Upserted Empowered Parenting Assessment');

  await prisma.assessment.upsert({
    where: { id: RESILIENCE_PRE_ASSESSMENT_ID },
    update: {},
    create: {
      id: RESILIENCE_PRE_ASSESSMENT_ID,
      name: 'Resilience Pre-Assessment',
      order: 2,
    },
  });
  console.info('Upserted Resilience Pre-Assessment');

  await prisma.assessment.upsert({
    where: { id: RESILIENCE_POST_ASSESSMENT_ID },
    update: {},
    create: {
      id: RESILIENCE_POST_ASSESSMENT_ID,
      name: 'Resilience Post-Assessment',
      order: 3,
    },
  });
  console.info('Upserted Resilience Post-Assessment');
}
