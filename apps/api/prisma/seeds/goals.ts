import { PrismaClient } from '@prisma/client';
import { SWATHI_MOM_ID } from './moms';
import { addDays } from 'date-fns';
const prisma = new PrismaClient();

export const SWATHI_GOAL_1_ID = '3fa66ba5-3829-453f-b2a7-d38bdeac7505';
export const SWATHI_GOAL_2_ID = 'eeacb408-924c-46e5-ba67-9c90595070ec';

export async function seedGoals() {
  await prisma.goal.upsert({
    where: { id: SWATHI_GOAL_1_ID },
    update: {},
    create: {
      id: SWATHI_GOAL_1_ID,
      name: 'Goal 1',
      description: 'Goal 1 Description',
      dueDate: addDays(new Date(), -3),
      doneDate: addDays(new Date(), -4),
      momId: SWATHI_MOM_ID,
    },
  });
  console.info('Upserted Goal 1');

  await prisma.goal.upsert({
    where: { id: SWATHI_GOAL_2_ID },
    update: {},
    create: {
      id: SWATHI_GOAL_2_ID,
      name: 'Goal 2',
      description: 'Goal 2 Description',
      dueDate: addDays(new Date(), 14),
      momId: SWATHI_MOM_ID,
    },
  });
  console.info('Upserted Goal 2');
}
