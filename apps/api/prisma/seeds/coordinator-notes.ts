import { PrismaClient } from '@prisma/client';
import { SWATHI_MOM_ID } from './moms';
import { CAROL_USER_ID } from './users';

const prisma = new PrismaClient();

export async function seedCoordinatorNotes() {
  const coordinatorNoteId = '056af3dd-c307-4c11-9494-ca30b40f6699';
  await prisma.coordinatorNote.upsert({
    where: { id: coordinatorNoteId },
    create: {
      id: coordinatorNoteId,
      name: 'Test Coordinator Note',
      description: 'Test Coordinator Note Description',
      type_c: 'safety_or_concern_update',
      mom_id: SWATHI_MOM_ID,
      coordinator_id: CAROL_USER_ID,
    },
    update: {},
  });
  console.info('Upserted Test Coordinator Note 1');

  const coordinatorNoteId2 = '25175019-3a9d-4e29-a766-1fa7d4184fc1';
  await prisma.coordinatorNote.upsert({
    where: { id: coordinatorNoteId2 },
    create: {
      id: coordinatorNoteId2,
      name: 'Test Coordinator Note 2',
      description: 'Test Coordinator Note 2 Description',
      type_c: 'court_update',
      mom_id: SWATHI_MOM_ID,
      coordinator_id: CAROL_USER_ID,
      isVisibleToAdvocates: false,
    },
    update: {},
  });
  console.info('Upserted Test Coordinator Note 2');
}
