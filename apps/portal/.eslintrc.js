const { resolve } = require('node:path');
const project = resolve(__dirname, 'tsconfig.json');

module.exports = {
  extends: [
    'plugin:@typescript-eslint/eslint-recommended',
    'plugin:@typescript-eslint/recommended',
    'next/core-web-vitals',
    'next',
    'prettier',
  ],
  rules: {
    'react/function-component-definition': [
      1,
      {
        namedComponents: ['arrow-function', 'function-declaration', 'function-expression'],
        unnamedComponents: 'arrow-function',
      },
    ],
    'import/no-default-export': 0,
    '@typescript-eslint/no-extraneous-class': 0,
    'tsdoc/syntax': 0,
    '@typescript-eslint/no-unused-vars': [
      'warn',
      {
        ignoreRestSiblings: true,
        varsIgnorePattern: '^_',
        argsIgnorePattern: '^_',
        destructuredArrayIgnorePattern: '^_',
        caughtErrorsIgnorePattern: '^_?error$',
      },
    ],
    'react-hooks/exhaustive-deps': 'off',
    '@typescript-eslint/ban-ts-comment': 'off',
  },
  globals: {
    React: true,
    JSX: true,
  },
  root: true,
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project,
  },
  env: {
    node: true,
    browser: true,
  },
  plugins: ['only-warn', 'prettier'],
  settings: {
    'import/resolver': {
      typescript: { project },
    },
  },
  ignorePatterns: [
    // Ignore dotfiles
    '.*.js',
    '*.*.js',
    '*.*.mjs',
    'node_modules/',
    '**/*.css',
    '**/*.ico',
    '**/*.png',
    '**/*.svg',
    '**/*.jpg',
    '**/*.jpeg',
    '**/*.gif',
    '**/*.webp',
  ],
  overrides: [{ files: ['*.js?(x)', '*.ts?(x)'] }],
};
