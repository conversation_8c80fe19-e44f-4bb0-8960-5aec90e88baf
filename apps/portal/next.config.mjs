/** @type {import('next').NextConfig} */
import * as fs from 'fs';
import { globSync } from 'glob';
import path from 'path';

const __dirname = path.resolve();

const nextConfig = {
  webpack: (config, { isServer }) => {
    config.resolve.alias['@'] = path.resolve(process.cwd());
    config.externals.push({
      'node:crypto': 'commonjs crypto',
    });
    config.ignoreWarnings = [
      {
        message: /require\.extensions is not supported by webpack\. Use a loader instead\./,
        module: /handlebars\/lib\/index\.js/,
      },
    ];
    return config;
  },
  env: {
    NEXT_PUBLIC_NODE_ENV: process.env.NODE_ENV,
    JWT_SIGNING_SECRET: process.env.JWT_SIGNING_SECRET,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'ema-document-uploads.s3.amazonaws.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'ema-document-uploads.*.amazonaws.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
};

export default nextConfig;
