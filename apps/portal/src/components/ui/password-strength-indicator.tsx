'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { 
  validatePassword, 
  getPasswordStrengthColor, 
  getPasswordRequirementsList,
  type PasswordValidationResult 
} from '@suiteapi/models/utils/password-validation';

interface PasswordStrengthIndicatorProps {
  password: string;
  showRequirements?: boolean;
  className?: string;
}

export const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({
  password,
  showRequirements = true,
  className,
}) => {
  const validation: PasswordValidationResult = validatePassword(password);
  const requirements = getPasswordRequirementsList();

  if (!password) {
    return null;
  }

  const strengthColor = getPasswordStrengthColor(validation.strength);
  const strengthWidth = `${validation.score}%`;

  return (
    <div className={cn('space-y-2', className)}>
      {/* Strength Bar */}
      <div className="space-y-1">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Password strength</span>
          <span 
            className="font-medium capitalize"
            style={{ color: strengthColor }}
          >
            {validation.strength}
          </span>
        </div>
        <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
          <div
            className="h-full transition-all duration-300 ease-in-out rounded-full"
            style={{
              width: strengthWidth,
              backgroundColor: strengthColor,
            }}
          />
        </div>
      </div>

      {/* Requirements List */}
      {showRequirements && (
        <div className="space-y-1">
          <p className="text-sm font-medium text-gray-700">Password must contain:</p>
          <ul className="space-y-1">
            {requirements.map((requirement, index) => {
              // Check if this specific requirement is met
              const isMet = getRequirementStatus(password, requirement);
              
              return (
                <li
                  key={index}
                  className={cn(
                    'text-xs flex items-center gap-2',
                    isMet ? 'text-green-600' : 'text-gray-500'
                  )}
                >
                  <span className={cn(
                    'w-4 h-4 rounded-full flex items-center justify-center text-xs',
                    isMet 
                      ? 'bg-green-100 text-green-600' 
                      : 'bg-gray-100 text-gray-400'
                  )}>
                    {isMet ? '✓' : '○'}
                  </span>
                  {requirement}
                </li>
              );
            })}
          </ul>
        </div>
      )}

      {/* Validation Errors */}
      {validation.errors.length > 0 && (
        <div className="space-y-1">
          {validation.errors.map((error, index) => (
            <p key={index} className="text-xs text-red-600">
              {error}
            </p>
          ))}
        </div>
      )}
    </div>
  );
};

/**
 * Helper function to check if a specific requirement is met
 */
function getRequirementStatus(password: string, requirement: string): boolean {
  if (requirement.includes('8 characters')) {
    return password.length >= 8;
  }
  if (requirement.includes('uppercase')) {
    return /[A-Z]/.test(password);
  }
  if (requirement.includes('lowercase')) {
    return /[a-z]/.test(password);
  }
  if (requirement.includes('number')) {
    return /\d/.test(password);
  }
  if (requirement.includes('special character')) {
    return /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password);
  }
  return false;
}

export default PasswordStrengthIndicator;
