import DatePicker from '@/components/custom/datepicker';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { type FieldConfig } from '@/types/form-field';
import { Plus, Trash2 } from 'lucide-react';
import React from 'react';

interface Task {
  id: string | null;
  name: string;
  dueDate: Date | null;
}

interface ActionItemListInputProps {
  field: FieldConfig;
  value: Task[];
  onChange: (value: Task[]) => void;
  isDisabled?: boolean;
}

export const ActionItemListInput: React.FC<ActionItemListInputProps> = ({ field, value, onChange, isDisabled }) => {
  const listValues = value || [];
  const minItems = field.minItems || 0;
  const maxItems = field.maxItems || Infinity;

  const handleAddItem = () => {
    if (listValues.length >= maxItems || isDisabled) return;

    const newTask: Task = {
      id: Math.random().toString(36), // this is a temp id for upserting the task
      name: '',
      dueDate: null,
    };
    const newList = [...listValues, newTask];
    onChange(newList);
  };

  const handleRemoveItem = (index: number) => {
    if (listValues.length <= minItems || isDisabled) return;

    const newList = listValues.filter((_, i) => i !== index);
    onChange(newList);
  };

  const handleNameChange = (index: number, newName: string) => {
    const newList = [...listValues];
    newList[index] = { ...newList[index], name: newName };
    onChange(newList);
  };

  const handleDateChange = (index: number, newDate: string) => {
    const newList = [...listValues];
    newList[index] = { ...newList[index], dueDate: new Date(newDate) };
    onChange(newList);
  };

  return (
    <div className='space-y-2'>
      {listValues.map((item, index) => (
        <div
          key={item.id ?? `temp-${index}`} // Fallback key for null id
          className='flex items-center space-x-2'
        >
          <Input
            value={item.name}
            onChange={(e) => handleNameChange(index, e.target.value)}
            placeholder={field.listItemPlaceholder || 'Enter task name'}
            disabled={isDisabled}
            className='max-h-9 flex-1'
          />
          <DatePicker
            disabled={isDisabled}
            date={item.dueDate ? new Date(item.dueDate).toISOString() : ''}
            setDate={(date: string | Date | undefined) => handleDateChange(index, date ? date.toString() : '')}
            className='w-[140px]'
          />
          <Button
            type='button'
            variant='outline' // Ghost variant for minimalism
            size='icon' // Icon-only size
            onClick={() => handleRemoveItem(index)}
            disabled={isDisabled || listValues.length <= minItems}
            className='h-9 w-9 border-solid' // Match input height
          >
            <Trash2 className='h-4 w-4' /> {/* Trash icon */}
          </Button>
        </div>
      ))}
      <Button
        type='button'
        variant='outline'
        size='sm'
        onClick={handleAddItem}
        disabled={isDisabled || listValues.length >= maxItems}
      >
        <Plus className='h-4 w-4' /> {field.addButtonLabel || 'Add Task'}
      </Button>
    </div>
  );
};
