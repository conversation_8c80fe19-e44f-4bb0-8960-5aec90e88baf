'use client';

import { FieldConfig } from '@/types/form-field';
import { X } from 'lucide-react';
import { useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

import { Input } from './input';

export function ComboBox({
  field,
  rhfField,
}: {
  field: FieldConfig;
  rhfField: { value: string; onChange: (value: string) => void };
}) {
  const items = useMemo(
    () => field.options?.map((item) => ({ label: item.label, value: item.value as string })) || [],
    [field],
  );

  const [isOpen, setIsOpen] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [textValue, setTextValue] = useState<string>('');
  const [highlightedIndex, setHighlightedIndex] = useState(-2);
  const debouncedTextValue = useDebounce(textValue, 300);

  const filteredItems = useMemo(() => {
    if (!debouncedTextValue) {
      return items;
    }

    const filteredItems = [];

    const searchRegex = new RegExp(
      debouncedTextValue
        .split('')
        .map((char) => char.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'))
        .join('.*?'),
      'i',
    );

    for (const item of items) {
      const match = item.label.match(searchRegex);
      if (match) {
        const start = match.index ?? Infinity;
        const length = match[0].length;

        const exact = item.label.toLowerCase().includes(debouncedTextValue.toLowerCase()) ? 1 : 0;

        // Lower score is better
        const score = start + length - exact * 10;
        filteredItems.push({ item, score });
      }
    }

    return filteredItems.sort((a, b) => a.score - b.score).map(({ item }) => item);
  }, [items, debouncedTextValue]);

  const hasSearchChars = textValue.trim().length > 0 && !filteredItems.find((f) => f.label === textValue);

  const [dropdownStyles, setDropdownStyles] = useState<React.CSSProperties>({});
  const inputRef = useRef<HTMLInputElement | null>(null);

  useLayoutEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        if (!inputRef.current) {
          return;
        }
        const rect = inputRef.current.getBoundingClientRect();
        setDropdownStyles({
          top: `${rect.bottom + window.scrollY}px`,
          left: `${rect.left + window.scrollX}px`,
          width: `${rect.width}px`,
          position: 'absolute',
          zIndex: 1000,
        });
      }, 100);
    } else {
      setDropdownStyles({ height: 0, border: 'none' });
    }

    return () => {
      setDropdownStyles({ height: 0, border: 'none' });
    };
  }, [isOpen]);

  return (
    <>
      {/* Close trigger #1: Escapes position-fixed on dialog ancestor but goes behind dialog */}
      {isOpen
        ? createPortal(
            <div
              className='z-999 fixed left-0 top-0 h-screen w-screen'
              onClick={(_e) => {
                setIsOpen(false);
              }}
            />,
            document.body,
          )
        : null}

      {/* Close trigger #2: Overlays content inside dialog */}
      {isOpen ? (
        <div
          className='z-999 absolute bottom-0 left-0 right-0 top-0'
          onClick={(_e) => {
            setIsOpen(false);
          }}
        />
      ) : null}

      {/* Combobox */}
      <div className='relative w-full'>
        {/* Free form text input & search filter */}
        <Input
          type='text'
          ref={inputRef}
          value={textValue}
          onChange={(e) => {
            setTextValue(e.target.value);
            setHighlightedIndex(-2);
            setIsDirty(true);
          }}
          placeholder={field.placeholder}
          onFocus={() => {
            setIsOpen(true);
          }}
          onBlur={() => {
            if (isDirty && rhfField.value !== textValue) {
              rhfField.onChange(textValue);
              setIsDirty(false);
            }
          }}
          onKeyDown={(e) => {
            if (e.code === 'ArrowDown' && highlightedIndex < filteredItems.length - 1) {
              if (!hasSearchChars && highlightedIndex === -2) {
                setHighlightedIndex(0);
              } else {
                setHighlightedIndex(highlightedIndex + 1);
              }
            } else if (e.code === 'ArrowUp' && highlightedIndex > -2) {
              if (!hasSearchChars && highlightedIndex === 0) {
                setHighlightedIndex(-2);
              } else {
                setHighlightedIndex(highlightedIndex - 1);
              }
            } else if (e.code === 'Tab') {
              setIsOpen(false);
            } else if (e.code === 'Space' && highlightedIndex >= -1) {
              e.preventDefault();

              if (highlightedIndex === -1) {
                rhfField.onChange(textValue);
              } else {
                const item = filteredItems[highlightedIndex];
                rhfField.onChange(item.value);
                setTextValue(item.label);
              }

              setIsOpen(false);
              setIsDirty(false);
              setHighlightedIndex(-2);
            }
          }}
        />

        {/* Clear button */}
        {rhfField.value ? (
          <button
            type='button'
            onClick={() => {
              setTextValue('');
              setIsDirty(false);
              rhfField.onChange('');
            }}
            className='absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600'
          >
            <X className='h-4 w-4' />
          </button>
        ) : null}

        {/* Options dropdown */}
        {createPortal(
          <div
            style={dropdownStyles}
            className='mt-1 max-h-60 w-full overflow-auto rounded border border-gray-300 bg-white shadow-md'
          >
            <ul className='divide-y divide-gray-100 p-1' onPointerLeave={() => setHighlightedIndex(-2)}>
              {/* Create option */}
              {hasSearchChars ? (
                <li
                  key={'create-option'}
                  onPointerOver={() => {
                    setHighlightedIndex(-1);
                  }}
                  onClick={() => {
                    setIsOpen(false);
                    setIsDirty(false);
                    setHighlightedIndex(-2);
                    setTextValue(textValue);
                    rhfField.onChange(textValue);
                  }}
                  className={`pointer-events-auto cursor-pointer rounded p-2 text-sm ${highlightedIndex === -1 ? 'bg-accent' : ''}`}
                >
                  {field.customOptionLabel || 'Create'} &ldquo;{textValue}&rdquo;
                </li>
              ) : null}

              {/* Filtered items */}
              {filteredItems.map((item, index) => (
                <li
                  key={item.value}
                  onPointerOver={() => {
                    setHighlightedIndex(index);
                  }}
                  className={`pointer-events-auto cursor-pointer rounded p-2 text-sm ${highlightedIndex === index ? 'bg-accent' : ''}`}
                  onClick={() => {
                    setIsOpen(false);
                    setIsDirty(false);
                    setHighlightedIndex(-2);
                    setTextValue(item.label);
                    rhfField.onChange(item.value);
                  }}
                >
                  {item.label}
                </li>
              ))}
            </ul>
          </div>,
          document.body,
        )}
      </div>
    </>
  );
}

function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const timer = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(timer);
  }, [value, delay]);

  return debouncedValue;
}
