import { cn } from '@/lib/utils';
import { type VariantProps, cva } from 'class-variance-authority';
import React from 'react';

const badgeVariants = cva(
  'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  {
    variants: {
      variant: {
        // shadcn/ui default variants
        default: 'bg-primary text-primary-foreground hover:bg-primary/80',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/80',
        outline: 'text-foreground border border-input hover:bg-accent hover:text-accent-foreground',

        // Custom color-coded badge variants
        // Green
        success: 'border border-green-500/50 text-green-700 bg-green-50 hover:bg-green-100',
        // Light Blue
        info1: 'border border-blue-400/50 text-blue-600 bg-blue-50 hover:bg-blue-100',
        // Blue
        info2: 'border border-blue-500/50 text-blue-700 bg-blue-100 hover:bg-blue-200',
        // Yellow/Orange
        warn: 'border border-orange-500/50 text-orange-700 bg-orange-50 hover:bg-orange-100',
        // Red/Pink
        danger: 'border border-pink-500/50 text-pink-700 bg-pink-50 hover:bg-pink-100',
        // Light Grey
        neutral: 'border border-gray-300/50 text-gray-700 bg-gray-50 hover:bg-gray-100',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return <div className={cn(badgeVariants({ variant }), className)} {...props} />;
}

export { Badge, badgeVariants };
