import Image from 'next/image';
import React from 'react';

interface ImageTitleDescriptionHeaderProps {
  title: string;
  description?: string | Array<string>;

  alignment?: 'center' | 'left';
}

const ImageTitleDescriptionHeader = ({
  title,
  description,
  alignment = 'center',
}: ImageTitleDescriptionHeaderProps) => {
  return (
    <div className={`flex flex-col ${alignment === 'center' ? 'items-center' : 'items-start'}`}>
      <Image src='/assets/ema-logo.svg' alt='EMA Logo' width={116} height={36} className='mb-8' />
      <h1 className='mb-2 text-3xl font-bold'>{title}</h1>
      {Array.isArray(description) ? (
        description.map((desc, index) => (
          <p key={index} className={`${alignment === 'center' ? 'text-center' : 'text-left'} mb-2`}>
            {desc}
          </p>
        ))
      ) : (
        <p className={`text-emaTextSecondary ${alignment === 'center' ? 'text-center' : 'text-left'}`}>{description}</p>
      )}
    </div>
  );
};

export default ImageTitleDescriptionHeader;
