import TablePaginationFooter from '@/components/custom/table-pagination-footer';
import { Table, TableBody, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { Fragment, ReactNode, useState } from 'react';

interface GenericTableProps<T> {
  data: T[];
  isLoading: boolean;
  itemsPerPage?: number;
  headers: string[];
  rowRenderer: (item: T) => ReactNode;
  mobileRenderer: (item: T) => ReactNode;
  emptyMessage?: string;
  loadingMessage?: string;
  columnWidths?: number[];
  tableWrapperClassName?: string;
  tableHeaderClassName?: string;
  headerSection?: ReactNode;
  shouldUseCustomRowComponent?: boolean;
  shouldShowPagination?: boolean;
  maxVisiblePages?: number;
}

const GenericTable = <T,>({
  data,
  isLoading,
  itemsPerPage = 8,
  headers,
  mobileRenderer,
  rowRenderer,
  emptyMessage = 'There are currently no items to display.',
  loadingMessage = 'Loading...',
  columnWidths,
  tableWrapperClassName,
  tableHeaderClassName,
  headerSection,
  shouldUseCustomRowComponent = false,
  shouldShowPagination = true,
  maxVisiblePages,
}: GenericTableProps<T>) => {
  const [currentPage, setCurrentPage] = useState(1);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = Array.isArray(data) ? data.slice(indexOfFirstItem, indexOfLastItem) : [];
  const totalPages = data ? Math.ceil(data.length / itemsPerPage) : 0;

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  return (
    <>
      {isLoading ? (
        <p className='py-4 text-center'>{loadingMessage}</p>
      ) : (
        <>
          {headerSection ? <div className='py-4 pl-2 pr-6 md:hidden'>{headerSection}</div> : null}
          {/* Mobile View */}
          <div className='mb-4 flex flex-col gap-3 md:hidden'>
            {currentItems.map((item, index) => (
              <div key={index} className='rounded-2xl bg-emaBgQuaternary p-4'>
                {mobileRenderer(item)}
              </div>
            ))}
          </div>

          {/* Desktop View */}
          <div
            className={cn(
              `mt-6 w-full overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm`,
              tableWrapperClassName,
            )}
          >
            {headerSection ? <div className='hidden px-6 py-4 md:block'>{headerSection}</div> : null}

            <div className='mx-auto max-md:hidden'>
              <div className='overflow-x-auto'>
                <Table className='w-full'>
                  <TableHeader>
                    <TableRow>
                      {headers.map((header, index) => (
                        <TableHead
                          key={index}
                          style={{
                            width: columnWidths ? `${columnWidths[index]}%` : `${100 / headers.length}%`, // Default to equal width if no columnWidths provided
                          }}
                          className={tableHeaderClassName}
                        >
                          {header}
                        </TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {currentItems.map((item, index) => (
                      <Fragment key={index}>
                        {shouldUseCustomRowComponent ? (
                          // When set to `true`, parent component is responsible for wrapping the row in the TableRow component
                          // This enables the parent to add a custom `onClick` callback to the TableRow component, or apply custom styles
                          rowRenderer(item)
                        ) : (
                          <TableRow key={index}>{rowRenderer(item)}</TableRow>
                        )}
                      </Fragment>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
            {shouldShowPagination && (
              <div className='rounded bg-background md:p-4'>
                {currentItems.length > 0 ? (
                  <TablePaginationFooter
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                    maxVisiblePages={maxVisiblePages}
                  />
                ) : (
                  <p className='py-4 text-center'>{emptyMessage}</p>
                )}
              </div>
            )}
          </div>
        </>
      )}
    </>
  );
};

export default GenericTable;
