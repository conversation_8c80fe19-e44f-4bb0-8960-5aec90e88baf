'use client';

import { Checkbox } from '@/components/ui/checkbox';
import React from 'react';

interface CheckboxSingleProps {
  value: boolean;
  onChange: (value: string | boolean) => void;
  description?: string;
}

const CheckboxSingle: React.FC<CheckboxSingleProps> = ({ value, onChange, description }) => (
  <div className='space-y-4'>
    <div className='flex space-x-2'>
      <Checkbox
        checked={value}
        className='mt-0.5'
        onCheckedChange={(checked) => {
          onChange(checked);
        }}
      />
      <label className='text-sm font-medium leading-tight text-emaTextSecondary peer-disabled:cursor-not-allowed peer-disabled:opacity-70'>
        {description}
      </label>
    </div>
  </div>
);

export default CheckboxSingle;
