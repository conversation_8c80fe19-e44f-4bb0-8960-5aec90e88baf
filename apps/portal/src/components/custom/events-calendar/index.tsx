'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { addYears } from 'date-fns';
import React from 'react';
import { useState } from 'react';
import { Caption, CaptionProps } from 'react-day-picker';

import './events-calendar.css';

interface EventsCalendarProps {
  date: Date | undefined;
  setDate: (date: Date | undefined) => void;
  eventDates: Date[];
  className?: string;
  maxDate?: Date;
}

const EventsCalendar: React.FC<EventsCalendarProps> = ({
  date,
  setDate,
  eventDates,
  maxDate = addYears(new Date(), 1), // default to one year from current date
}) => {
  // Need to control month state outside of Calendar component to allow custom "Today" button to navigate to current month
  const [month, setMonth] = useState(new Date());

  return (
    <Calendar
      modifiers={{
        event: eventDates, // Days with events will be shown with dot underneath day
      }}
      modifiersClassNames={{
        event: 'calendar-event-day', // Apply custom styles for dot underneath day to days with events
      }}
      className='flex h-full w-full'
      classNames={{
        months: 'flex w-full flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0 flex-1',
        month: 'space-y-4 w-full flex flex-col',
        table: 'w-full h-full border-collapse space-y-1',
        head_row: '',
        row: 'w-full mt-2',
      }}
      mode='single'
      selected={date}
      month={month}
      onMonthChange={setMonth}
      onSelect={setDate}
      toDate={maxDate}
      initialFocus
      components={{
        Caption: (props: CaptionProps) => {
          return (
            <div>
              <Caption {...props} />
              <div className='flex w-full justify-between gap-3 pt-4'>
                <div className='flex w-2/3 items-center justify-center rounded-md border border-input bg-background'>
                  <span className='text-sm font-medium'>
                    {date?.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' }) ??
                      'Select a date'}
                  </span>
                </div>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => {
                    const today = new Date();
                    setDate(today);
                    setMonth(today);
                  }}
                  className='w-1/3'
                >
                  Today
                </Button>
              </div>
            </div>
          );
        },
      }}
    />
  );
};

export default EventsCalendar;
