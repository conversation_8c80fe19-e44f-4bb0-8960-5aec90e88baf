/* Custom class to apply to days with events - black dot underneath date number */
.calendar-event-day::after {
  content: '';
  display: block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: black;
  position: absolute;
  bottom: 3px;
}

/* Change dot color to white when the date is selected */
.calendar-event-day[aria-selected='true']::after {
  background-color: white;
}

/* if it's an out-of-range day for current month view (e.g. Oct 1 shown on Sept calendar), then keep dot black even when selected */
.calendar-event-day.day-outside::after {
  background-color: black;
}
