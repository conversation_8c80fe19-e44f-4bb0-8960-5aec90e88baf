interface CircularProgressProps {
  progress: number;
}

const CircularProgress: React.FC<CircularProgressProps> = ({ progress }) => {
  const radius = 20;
  const circumference = 2 * Math.PI * radius;

  const isValidProgress = !isNaN(progress) && progress >= 0 && progress <= 100;
  const displayProgress = Math.round(progress);
  const offset = isValidProgress ? circumference - (displayProgress / 100) * circumference : circumference;

  return (
    <div className='relative flex h-14 w-14 items-center justify-center px-3.5'>
      <svg className='absolute left-0 top-0 h-full w-full' viewBox='0 0 48 48'>
        <circle
          className='text-gray-200'
          strokeWidth='4'
          stroke='currentColor'
          fill='transparent'
          r={radius}
          cx='24'
          cy='24'
        />
        {isValidProgress ? (
          <circle
            className='text-green-500 transition-all duration-300 ease-linear'
            strokeWidth='4'
            strokeLinecap='round'
            strokeDasharray={circumference}
            strokeDashoffset={offset}
            stroke='currentColor'
            fill='transparent'
            r={radius}
            cx='24'
            cy='24'
            transform='rotate(-90 24 24)'
          />
        ) : null}
      </svg>
      <span className='text-sm font-medium text-gray-800'>
        {isValidProgress ? `${displayProgress.toString()}%` : 'N/A'}
      </span>
    </div>
  );
};

export default CircularProgress;
