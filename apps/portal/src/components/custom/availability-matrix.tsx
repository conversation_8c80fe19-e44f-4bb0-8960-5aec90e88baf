import {
  type DayAvailability,
  type TimeSlot,
  deserializeAvailability,
  serializeAvailability,
} from '@/app/advocate-application/_components/advocate-application.config';
import { Checkbox } from '@/components/ui/checkbox';
import { type AvailabilityMatrixOptions } from '@/types/availability';
import React from 'react';

interface AvailabilityMatrixProps {
  options: AvailabilityMatrixOptions;
  value: string | DayAvailability;
  onChange: (value: string | DayAvailability) => void;
  disabled?: boolean;
}

export const AvailabilityMatrix: React.FC<AvailabilityMatrixProps> = ({
  options,
  value,
  onChange,
  disabled = false,
}) => {
  // Convert string value to object if needed
  const availabilityData = typeof value === 'string' ? deserializeAvailability(value) : value;

  const handleTimeSlotChange = (day: string, timeSlot: TimeSlot) => {
    const dayKey = day.toLowerCase() as keyof DayAvailability;
    const currentDayValue = availabilityData[dayKey] || [];
    const newValue = { ...availabilityData };

    if (currentDayValue.includes(timeSlot)) {
      newValue[dayKey] = currentDayValue.filter((slot) => slot !== timeSlot);
    } else {
      newValue[dayKey] = [...currentDayValue, timeSlot];
    }

    // Ensure at least one time slot is selected for each day
    if (newValue[dayKey]?.length === 0) {
      delete newValue[dayKey];
    }

    // Always convert to string before calling onChange
    const serializedValue = serializeAvailability(newValue);
    onChange(serializedValue);
  };

  // Validate that at least one day and one time slot is selected
  const isValid = () => {
    const selectedDays = Object.keys(availabilityData) as Array<keyof DayAvailability>;
    if (selectedDays.length === 0) return false;

    const hasSelectedTimeSlot = selectedDays.some((day) => {
      const slots = availabilityData[day];
      return Array.isArray(slots) && slots.length > 0;
    });

    return hasSelectedTimeSlot;
  };

  return (
    <div className='w-full space-y-4'>
      <div className='grid grid-cols-[100px_repeat(auto-fill,minmax(80px,1fr))] gap-2'>
        <div className='col-span-1' /> {/* Empty cell for days header */}
        {options.timeSlots.map((timeSlot) => (
          <div key={timeSlot} className='text-center font-medium'>
            {timeSlot.charAt(0).toUpperCase() + timeSlot.slice(1)}
          </div>
        ))}
      </div>
      {options.days.map((day) => (
        <div key={day} className='grid grid-cols-[100px_repeat(auto-fill,minmax(80px,1fr))] gap-2'>
          <div className='font-medium'>{day}</div>
          {options.timeSlots.map((timeSlot) => (
            <div key={`${day}-${timeSlot}`} className='flex justify-center'>
              <Checkbox
                checked={(availabilityData[day.toLowerCase() as keyof DayAvailability] || []).includes(
                  timeSlot as TimeSlot,
                )}
                onCheckedChange={() => handleTimeSlotChange(day, timeSlot as TimeSlot)}
                disabled={disabled}
              />
            </div>
          ))}
        </div>
      ))}
      {!isValid() && <div className='mt-2 text-sm text-red-500'>Please select at least one day and one time slot</div>}
    </div>
  );
};
