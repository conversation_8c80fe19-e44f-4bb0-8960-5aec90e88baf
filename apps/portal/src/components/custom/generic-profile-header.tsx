import Breadcrumbs from '@/app/portal/_components/navigation/breadcrumbs';
import { CircleUser } from 'lucide-react';
import Image from 'next/image';
import React from 'react';

export const DEFAULT_AVATAR = '/assets/user_generic_avatar.png';

const GenericProfileHeader = ({
  photoUrl,
  firstName,
  lastName,
  role,
  breadcrumbItems,
}: {
  photoUrl: string | null | undefined;
  firstName: string;
  lastName: string;
  role: string | null | undefined;
  breadcrumbItems: { label: string; href: string }[];
}) => {
  return (
    <header className='flex w-full flex-col gap-4 px-8 pt-6 max-md:max-w-full max-md:px-5 max-sm:px-1'>
      <Breadcrumbs items={breadcrumbItems} />
      <div className='flex flex-row items-center gap-2'>
        {photoUrl ? (
          <Image
            src={photoUrl}
            alt='Profile Image'
            width={100}
            height={100}
            className='rounded-full object-cover'
            style={{ height: '100px' }}
          />
        ) : (
          <CircleUser className='h-[75px] w-[75px]' />
        )}
        <div>
          <h1 className='mt-1 self-stretch text-3xl font-semibold leading-10 text-gray-900'>
            {firstName} {lastName}
          </h1>
          <span className='block font-semibold text-black'>{role}</span>
        </div>
      </div>
    </header>
  );
};

export default GenericProfileHeader;
