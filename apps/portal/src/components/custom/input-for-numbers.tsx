import { Input, type InputProps } from '@/components/ui/input';
import type { LucideIcon } from 'lucide-react';
import React from 'react';
import { ChangeEvent, forwardRef } from 'react';

interface InputForNumbersProps extends InputProps {
  icon?: LucideIcon;
}

// Defining a type for our modified event
type NumberInputChangeEvent = Omit<ChangeEvent<HTMLInputElement>, 'target'> & {
  target: Omit<ChangeEvent<HTMLInputElement>['target'], 'value'> & {
    value: string | number;
  };
};

const InputForNumbers = forwardRef<HTMLInputElement, InputForNumbersProps>(
  ({ icon: Icon, className, onChange, ...props }, ref) => {
    const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
      if (onChange) {
        const newEvent: NumberInputChangeEvent = {
          ...e,
          target: {
            ...e.target,
            value: e.target.value === '' ? '' : Number(e.target.value),
          },
        };

        onChange(newEvent as unknown as ChangeEvent<HTMLInputElement>);
      }
    };

    return (
      <div className='relative flex items-center'>
        {Icon ? <Icon className='absolute left-3 h-4 w-4 text-muted-foreground' aria-hidden='true' /> : null}
        <Input
          ref={ref}
          className={`${Icon ? 'pl-9' : ''} ${className}`}
          type='number'
          onChange={handleChange}
          {...props}
        />
      </div>
    );
  },
);

InputForNumbers.displayName = 'InputForNumbers';

export default InputForNumbers;
