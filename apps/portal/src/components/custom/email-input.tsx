import { Mail } from 'lucide-react';
import { InputHTMLAttributes, forwardRef } from 'react';

import InputWithIcon from './input-with-icon';

interface EmailInputProps extends InputHTMLAttributes<HTMLInputElement> {
  className?: string;
}

const EmailInput = forwardRef<HTMLInputElement, EmailInputProps>(({ className, ...props }, ref) => (
  <InputWithIcon type='email' className={className} icon={Mail} {...props} ref={ref} />
));

EmailInput.displayName = 'EmailInput';

export default EmailInput;
