import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

const onboardingSteps = [
  'Submit Interest Form',
  'Training',
  'Complete Application',
  'Background Check',
  'Interview',
  'Onboarding',
];

interface AdvocateOnboardingProcessProps {
  activeStep: number;
}

export function AdvocateOnboardingProcess({ activeStep }: AdvocateOnboardingProcessProps) {
  // Ensure activeStep doesn't exceed the number of steps
  const validActiveStep = Math.min(activeStep, onboardingSteps.length - 1);

  return (
    <Card className={cn('w-full')}>
      <CardHeader>
        <CardTitle>Advocate Onboarding Process</CardTitle>
      </CardHeader>
      <CardContent>
        <div className='relative px-4'>
          {/* Timeline with circles */}
          <div className='relative grid grid-cols-6'>
            {onboardingSteps.map((step, index) => (
              <div key={step} className='relative flex justify-center'>
                <div
                  className={cn(
                    'h-5 w-5 shrink-0 rounded-full border-2 transition-colors',
                    index < validActiveStep ? 'border-[#D35400] bg-[#D35400]' : 'border-[#D35400] bg-transparent',
                  )}
                />
                {index < onboardingSteps.length - 1 && (
                  <div
                    className={cn(
                      'absolute left-[calc(50%+10px)] right-[-50%] top-1/2 h-0.5 -translate-y-1/2 transition-colors',
                      index < validActiveStep ? 'bg-[#D35400]' : 'bg-[#D35400]',
                    )}
                    style={{ width: 'calc(100% - 20px)' }}
                  />
                )}
              </div>
            ))}
          </div>

          {/* Labels */}
          <div className='mt-3 grid grid-cols-6'>
            {onboardingSteps.map((step, index) => (
              <div key={step} className='col-span-1'>
                <span
                  className={cn(
                    'block text-center text-xs transition-colors',
                    index < validActiveStep ? 'text-[#D35400]' : 'text-[#D35400]',
                  )}
                >
                  {step}
                </span>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
