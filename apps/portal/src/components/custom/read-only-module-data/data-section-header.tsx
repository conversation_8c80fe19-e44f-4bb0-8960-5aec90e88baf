interface SectionHeaderProps {
  title: string;
}

export const ReadOnlyDataSectionHeader: React.FC<SectionHeaderProps> = ({ title }): JSX.Element => (
  <section className='flex w-full flex-col text-lg font-medium leading-loose max-sm:max-w-full'>
    <h2 className='gap-2 self-start'>{title}</h2>
    <div className='mt-1 min-h-[1px] w-full border border-solid border-gray-200 max-sm:max-w-full' />
  </section>
);
