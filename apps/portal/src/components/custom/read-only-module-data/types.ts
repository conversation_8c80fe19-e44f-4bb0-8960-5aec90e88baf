export type ListStyleOptions =
  | 'none'
  | 'disc'
  | 'decimal'
  | 'decimal-leading-zero'
  | 'lower-roman'
  | 'upper-roman'
  | 'lower-alpha'
  | 'upper-alpha';

export interface ReadOnlyModuleDataProps {
  headerText?: string;
  title: string;
  sections: {
    sectionTitle: string;
    data: {
      label: string;
      value: string | string[];
      listStyle?: ListStyleOptions;
    }[];
  }[];
}
