import { ReadOnlyDataRow } from './data-row';
import { ReadOnlyDataSectionHeader } from './data-section-header';
import { ReadOnlyModuleDataProps } from './types';

const ReadOnlyModuleData: React.FC<ReadOnlyModuleDataProps> = ({ headerText, title, sections }): JSX.Element => {
  return (
    <main className='flex min-w-[640px] max-w-[784px] flex-col rounded-xl bg-white p-6 text-emaTextSecondary shadow-[0px_1px_3px_rgba(0,0,0,0.1)] max-sm:px-5'>
      {headerText ? (
        <div className='w-full pb-3'>
          <h1 className='text-2xl font-semibold leading-9 text-emaTextPrimary'>{headerText}</h1>
        </div>
      ) : null}
      <h1 className='text-xl'>{title}</h1>
      <article className='mt-1 flex w-full flex-col max-sm:max-w-full'>
        {sections.map((section, index) => (
          <section key={index} className='mt-6 flex w-full flex-col max-sm:max-w-full'>
            <ReadOnlyDataSectionHeader title={section.sectionTitle} />
            <div className='mt-4 flex w-full flex-col text-sm leading-none max-sm:max-w-full'>
              {section.data.map((item, index) => (
                <ReadOnlyDataRow key={index} label={item.label} value={item.value} listStyle={item.listStyle} />
              ))}
            </div>
          </section>
        ))}
      </article>
    </main>
  );
};

export default ReadOnlyModuleData;
