import type { ListStyleOptions } from './types';

interface ReadOnlyDataRowProps {
  label: string;
  value: string | string[];
  listStyle?: ListStyleOptions;
}

export const ReadOnlyDataRow: React.FC<ReadOnlyDataRowProps> = ({ label, value, listStyle = 'disc' }): JSX.Element => {
  const isValueArray = Array.isArray(value);

  return (
    <div className='mt-4 flex w-full flex-row items-start gap-10 leading-none max-sm:max-w-full max-sm:flex-wrap max-sm:gap-2'>
      <div className='min-w-60 max-w-60 font-medium leading-4 max-sm:min-w-full max-sm:max-w-full'>{label}</div>
      {isValueArray ? (
        <div className='flex min-w-[240px] flex-col justify-center'>
          <ul className={`list-${listStyle}`}>
            {value.map((item, index) => (
              <li key={`item-${index}`} className={`leading-5${index > 0 ? 'mt-1' : ''}`}>
                {item}
              </li>
            ))}
          </ul>
        </div>
      ) : (
        <div className='leading-5'>{value}</div>
      )}
    </div>
  );
};
