import debounce from 'lodash/debounce';
import { Phone } from 'lucide-react';
import React, { InputHTMLAttributes, forwardRef } from 'react';
import Input, { Value } from 'react-phone-number-input/input';

import InputWithIcon from './input-with-icon';

interface CustomInputComponentProps extends InputHTMLAttributes<HTMLInputElement> {
  className?: string;
}

interface PhoneInputProps {
  className?: string;
  value: string;
  onChange?: (value: string) => void;
}

// manually manage format for US phone numbers, since react-phone-number-input doesn't limit this out-of-the-box
const MAX_DISPLAY_PHONE_LENGTH = 16; // e.g. ****************
const MAX_VALUE_PHONE_LENGTH = 12; // e.g. +18881234567

const CustomInputComponent = forwardRef<HTMLInputElement, CustomInputComponentProps>(({ className, ...props }, ref) => (
  <InputWithIcon
    type='tel'
    maxLength={MAX_DISPLAY_PHONE_LENGTH}
    className={className}
    icon={Phone}
    {...props}
    ref={ref}
  />
));

CustomInputComponent.displayName = 'CustomInputComponent';

const PhoneInput = React.forwardRef(({ className, value, onChange, ...props }: PhoneInputProps, _ref) => {
  return (
    <Input
      inputComponent={CustomInputComponent}
      country='US'
      className={className}
      value={value}
      /**
       * Adapted from https://github.com/omeralpi/shadcn-phone-input
       *
       * Handles the onChange event.
       *
       * react-phone-number-input might trigger the onChange event as undefined
       * when a valid phone number is not entered. To prevent this,
       * the value is coerced to an empty string.
       *
       * @param {E164Number | undefined} input - The entered input value
       */
      onChange={(input) => {
        // If value exceeds standard US tel. value (e.g. +18881234567),
        // such as when a user pastes a value that is too long, trim the value
        if ((input ?? '').length > MAX_VALUE_PHONE_LENGTH) {
          input = input?.slice(0, MAX_VALUE_PHONE_LENGTH) as Value;
        }

        // debounce the onChange event to prevent race condition when two keys pressed at same time
        if (onChange) debounce(() => onChange(input || ('' as Value)), 1)();
      }}
      {...props}
    />
  );
});

PhoneInput.displayName = 'PhoneInput';

export default PhoneInput;
