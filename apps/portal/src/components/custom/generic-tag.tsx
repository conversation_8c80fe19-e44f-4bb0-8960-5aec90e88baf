const GenericTag = ({ tag, key, color = 'blue' }: { tag: string; key: string; color: string }) => {
  return (
    <span
      key={key}
      // Color styles have to be added like the following due to how TW renders the tags
      className={`m-1 inline-flex w-fit items-center rounded-full border px-2 py-1 text-[10px] font-medium capitalize ${color === 'red' && 'border-red-700 bg-red-50 text-red-700'} ${color === 'blue' && 'border-blue-700 bg-blue-50 text-blue-700'} ${color === 'green' && 'border-green-700 bg-green-50 text-green-700'} ${color === 'orange' && 'border-orange-700 bg-orange-50 text-orange-700'} `}
    >
      {tag}
    </span>
  );
};

export default GenericTag;
