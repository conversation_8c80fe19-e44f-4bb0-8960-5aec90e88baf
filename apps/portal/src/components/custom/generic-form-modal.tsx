import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { FieldConfig, FormValues } from '@/types/form-field';
import { FieldValues, SubmitHandler } from 'react-hook-form';
import * as Yup from 'yup';

const GenericFormModal = ({
  onSubmit,
  defaultValues,
  modalTitle = 'Title',
  description,
  actionButtonText = 'Save',
  trigger,
  fields,
  schema,
  open,
  onOpenChange,
}: {
  onSubmit: SubmitHandler<FieldValues>;
  defaultValues?: FormValues | undefined;
  modalTitle?: string;
  description?: string;
  actionButtonText?: string;
  trigger: React.ReactNode;
  fields: FieldConfig[];
  schema: Yup.ObjectSchema<Yup.AnyObject>;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{modalTitle}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>
        <FormFactory
          fields={fields}
          schema={schema}
          defaultValues={defaultValues}
          onSubmit={onSubmit as SubmitHandler<FieldValues>}
          actionButtonsComponent={
            <DialogFooter className='flex w-full flex-row justify-end gap-2'>
              <Button type='submit' variant='default'>
                {actionButtonText}
              </Button>
              <Button variant='outline' onClick={() => onOpenChange?.(false)}>
                Cancel
              </Button>
            </DialogFooter>
          }
        />
      </DialogContent>
    </Dialog>
  );
};

export default GenericFormModal;
