import { But<PERSON> } from '@/components/ui/button';
import { RadioGroup } from '@/components/ui/radio-group';
import { cn } from '@/lib/utils';
import type { FieldOptions } from '@/types/form-field';

interface ButtonRadioGroupProps {
  options: FieldOptions;
  value: string | boolean;
  onChange: (value: string | boolean) => void;
  name: string;
  disabled: boolean | undefined;
}

const ButtonRadioGroup: React.FC<ButtonRadioGroupProps> = ({ options, value, onChange, name, disabled }) => (
  <RadioGroup name={name} value={String(value)} className='flex flex-wrap gap-2'>
    {options.map((option) => {
      const optionValue = typeof option === 'string' ? option : (option.value as string | boolean);
      const optionValueString = String(optionValue);
      const label = typeof option === 'string' ? option : option.label;
      return (
        <Button
          key={optionValueString}
          type='button'
          variant='outline'
          onClick={() => {
            onChange(optionValue);
          }}
          disabled={disabled}
          value={optionValueString}
          className={cn(
            'rounded-md border-2 px-3 py-2 text-sm font-medium transition-all',
            String(value) === optionValueString
              ? 'border-primary bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground hover:shadow-md'
              : 'border-muted bg-popover hover:bg-slate-100 hover:text-accent-foreground',
            'focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
          )}
        >
          {label}
        </Button>
      );
    })}
  </RadioGroup>
);

export default ButtonRadioGroup;
