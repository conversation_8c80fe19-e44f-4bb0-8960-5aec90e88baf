import { Input, type InputProps } from '@/components/ui/input';
import type { LucideIcon } from 'lucide-react';
import { forwardRef } from 'react';

interface InputWithIconProps extends InputProps {
  icon: LucideIcon;
}

const InputWithIcon = forwardRef<HTMLInputElement, InputWithIconProps>(({ icon: Icon, className, ...props }, ref) => {
  return (
    <div className='relative flex items-center'>
      <Icon className='absolute left-3 h-4 w-4 text-muted-foreground' aria-hidden='true' />
      <Input ref={ref} className={`pl-9 ${className}`} {...props} />
    </div>
  );
});

InputWithIcon.displayName = 'InputWithIcon';

export default InputWithIcon;
