import { FormFactoryRef } from '@/app/portal/types';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { type FieldConfig, type FormActionButtonsConfig, type FormValues } from '@/types/form-field';
import { yupResolver } from '@hookform/resolvers/yup';
import { Turnstile } from '@marsidev/react-turnstile';
import debounce from 'lodash/debounce';
import React from 'react';
import { type FieldValues, type SubmitHandler, useForm } from 'react-hook-form';
import { type AnyObject, type ObjectSchema } from 'yup';

import FormFieldWrapper from './form-field-wrapper';

interface FormFactoryProps {
  fields: FieldConfig[];
  schema: ObjectSchema<AnyObject>;
  onSubmit: SubmitHandler<FieldValues>;
  onCancel?: () => void;
  onBack?: (currentData: FormValues) => void;
  defaultValues?: FormValues;
  actionButtonsConfig?: FormActionButtonsConfig;
  actionButtonsComponent?: React.ReactNode | null;
  submitDisabled?: boolean;
  cloudflareTurnstileCallback?: (token: string) => void;
  turnstileSiteKey?: string;
  isCloudflareTurnstileEnabled?: boolean;
  formWrapperClassName?: string;
  formFieldElClass?: string;
  readOnly?: boolean;
  onChange?: (values: FormValues) => void;
  labelWrapperClassName?: string;
  formFieldSubFieldsWrapperClass?: string;
  renderFormFieldItemClassName?: string;
  stopPropagation?: boolean;
}

const FormFactory = React.forwardRef<FormFactoryRef, FormFactoryProps>(
  (
    {
      fields,
      schema,
      onSubmit,
      onCancel, // cancel without saving current form data
      onBack, // pass current form data (without validation) to allow parent to persist incomplete step data in multi-step form
      defaultValues, // pre-fill form with existing data, such as when user steps back in a multi-step form
      actionButtonsConfig, // Configure button text, back vs cancel, etc.
      actionButtonsComponent, // allows passing action buttons wrapped in specialized helper components, such as shadcn/ui dialog triggers
      submitDisabled, // disable submit button based on condition in parent
      cloudflareTurnstileCallback, // callback to handle Cloudflare Turnstile token
      turnstileSiteKey,
      isCloudflareTurnstileEnabled,
      formWrapperClassName,
      formFieldElClass,
      readOnly = false,
      onChange,
      labelWrapperClassName,
      formFieldSubFieldsWrapperClass,
      renderFormFieldItemClassName,
      stopPropagation = false,
    },
    ref,
  ) => {
    const form = useForm<FormValues>({
      resolver: yupResolver(schema),
      defaultValues: defaultValues ?? {},
    });

    React.useImperativeHandle(ref, () => ({ form }), [form]);

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const handleFieldChange = React.useCallback(
      debounce((values: FormValues) => onChange?.(values), 250),
      [],
    );

    return (
      <Form {...form}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            if (stopPropagation) {
              e.stopPropagation();
            }
            void form.handleSubmit(onSubmit)(e);
          }}
          className='space-y-6'
        >
          {fields.map((field) => (
            <React.Fragment key={field.name}>
              <FormFieldWrapper
                renderFormFieldItemClassName={renderFormFieldItemClassName}
                formFieldElClass={formFieldElClass}
                formWrapperClassName={formWrapperClassName}
                field={field}
                readOnly={readOnly}
                onChange={handleFieldChange}
                labelWrapperClassName={labelWrapperClassName}
                formFieldSubFieldsWrapperClass={formFieldSubFieldsWrapperClass}
              />
              {field.bottomContentHidden && field.bottomContentHidden(form.getValues()) ? null : field.bottomContent}
              {field.bottomDivider ? <div className='border-b border-gray-200' /> : null}
            </React.Fragment>
          ))}
          {isCloudflareTurnstileEnabled && cloudflareTurnstileCallback ? (
            <div className='flex justify-end'>
              <Turnstile
                siteKey={turnstileSiteKey || String(process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY)}
                onSuccess={cloudflareTurnstileCallback}
              />
            </div>
          ) : null}
          {actionButtonsComponent !== undefined ? (
            actionButtonsComponent
          ) : (
            <>
              {actionButtonsConfig ? (
                <div className={`flex ${onCancel || onBack ? 'justify-between' : 'justify-end'}`}>
                  {onCancel ? (
                    <Button type='button' onClick={onCancel} variant='secondary'>
                      {actionButtonsConfig.cancelLabel ?? 'Cancel'}
                    </Button>
                  ) : null}
                  {onBack ? (
                    <Button
                      type='button'
                      onClick={() => {
                        onBack(form.getValues());
                      }}
                      variant='secondary'
                    >
                      {actionButtonsConfig.backLabel ?? 'Back'}
                    </Button>
                  ) : null}
                  <Button type='submit' disabled={submitDisabled}>
                    {actionButtonsConfig.continueLabel}
                  </Button>
                </div>
              ) : (
                <Button type='submit' disabled={submitDisabled}>
                  Submit
                </Button>
              )}
            </>
          )}
        </form>
      </Form>
    );
  },
);

FormFactory.displayName = 'FormFactory';

export default FormFactory;
