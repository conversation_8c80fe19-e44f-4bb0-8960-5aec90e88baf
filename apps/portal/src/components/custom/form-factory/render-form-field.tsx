import { ActionItemListInput } from '@/components/ui/action-item-list';
import { ComboBox } from '@/components/ui/combo-box';
import { FormControl, FormItem, FormLabel } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { getZipData } from '@/lib/utils';
import { type AvailabilityMatrixOptions, type DayAvailability } from '@/types/availability';
import { type AvailabilityMatrixFieldConfig, type FieldConfig, type FieldOptionObj } from '@/types/form-field';
import { debounce } from 'lodash';
import React, { useRef, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { MultiSelect } from 'react-multi-select-component';
import { type Option } from 'react-multi-select-component';

import { AvailabilityMatrix } from '../availability-matrix';
import ButtonRadioGroup from '../button-radio-group';
import CheckboxGroup from '../checkbox-group';
import CheckboxSingle from '../checkbox-single';
import DatePicker from '../datepicker';
import DropdownDatePicker from '../dropdown-datepicker';
import EmailInput from '../email-input';
import FileDropzone from '../file-dropzone';
import InputForNumbers from '../input-for-numbers';
import InputWithIcon from '../input-with-icon';
import PhoneInput from '../phone-input';

// Define Task interface based on action-item-list component
interface Task {
  id: string | null;
  name: string;
  dueDate: Date | null;
}

interface RenderFormFieldProps {
  field: FieldConfig;
  rhfField: {
    value: unknown;
    onChange: (value: unknown) => void;
    onBlur: () => void;
    name: string;
    ref?: React.Ref<unknown>;
  };
  isDisabled?: boolean;
  renderFormFieldItemClassName?: string;
}

export const FormField = ({
  field,
  rhfField,
  isDisabled,
  renderFormFieldItemClassName,
}: RenderFormFieldProps): JSX.Element | null => {
  const { setValue } = useFormContext();
  // Initialize local state and debounce outside the switch statement
  const [localValue, setLocalValue] = useState((rhfField.value as string) || '');
  const debouncedOnChange = useRef(debounce((value) => rhfField.onChange({ target: { value } }), 200)).current;
  // The `handleTextareaChange` function separates immediate UI updates and debounced form state updates.
  // `setLocalValue` ensures the textarea reflects user input instantly for a responsive typing experience.
  // `debouncedOnChange` (wrapped in `useRef` for stability) updates the form state after a 200ms delay,
  //  reducing unnecessary calls to `rhfField.onChange` and improving performance in larger forms.
  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setLocalValue(newValue); // Immediate update for local UI
    debouncedOnChange(newValue); // Debounced update for form
  };

  const getZipDataAndSetCityState = async (e: React.ChangeEvent<HTMLInputElement>): Promise<void> => {
    const zip = e.target.value;

    if (zip.length === 5) {
      try {
        const zipData = await getZipData(zip);
        if (zipData.length) {
          setValue('city', zipData[0].place_name);
          setValue('state', zipData[0].state_abbr);
        }
      } catch {
        /* fail silently, allow user to fill in city/state manually */
      }
    }
  };
  const InputComponent = field.icon ? InputWithIcon : Input;
  switch (field.type) {
    case 'text':
    case 'password':
      return (
        <InputComponent
          icon={field.icon!}
          className='max-h-9'
          type={field.type}
          placeholder={field.placeholder}
          disabled={isDisabled}
          value={(rhfField.value as string) || ''}
          min={field.min}
          max={field.max}
          onChange={rhfField.onChange}
          onBlur={rhfField.onBlur}
          name={rhfField.name}
        />
      );

    case 'number':
      return (
        <InputForNumbers
          icon={field.icon!}
          className='max-h-9'
          type={field.type}
          placeholder={field.placeholder}
          disabled={isDisabled}
          value={(rhfField.value as string) ?? ''}
          onChange={rhfField.onChange}
          onBlur={rhfField.onBlur}
          name={rhfField.name}
          min={field.min}
          max={field.max}
        />
      );

    case 'email':
      return (
        <EmailInput
          className='max-h-9'
          placeholder={field.placeholder}
          disabled={isDisabled}
          value={(rhfField.value as string) || ''}
          onChange={rhfField.onChange}
          onBlur={rhfField.onBlur}
          name={rhfField.name}
        />
      );

    case 'tel':
      return (
        <PhoneInput
          className='max-h-9'
          value={(rhfField.value as string) || ''}
          onChange={(value) => rhfField.onChange(value)}
        />
      );

    case 'textarea':
      return (
        <Textarea
          disabled={isDisabled}
          placeholder={field.placeholder}
          onChange={handleTextareaChange}
          value={localValue as string}
          className={renderFormFieldItemClassName}
        />
      );

    case 'radio':
      return (
        <RadioGroup
          onValueChange={rhfField.onChange}
          value={(rhfField.value as string) || ''}
          disabled={isDisabled}
          className='flex flex-col space-y-1'
        >
          {field.options?.map((option: FieldOptionObj) => (
            <FormItem key={String(option.value)} className='flex items-center space-x-3 space-y-0'>
              <FormControl>
                <RadioGroupItem value={String(option.value)} defaultValue={String(option.value) || undefined} />
              </FormControl>
              <FormLabel className='font-normal'>{option?.label}</FormLabel>
            </FormItem>
          ))}
        </RadioGroup>
      );

    case 'select':
      return (
        <Select
          onValueChange={rhfField.onChange}
          value={rhfField.value as string}
          disabled={isDisabled || !field.options?.length}
        >
          <FormControl>
            <SelectTrigger>
              <SelectValue
                /* Placeholder logic if `dynamicOptionsKey` is present:
                 * - Show "Loading..." when options are undefined (still loading)
                 * - Show "Error fetching options" if options is null (API error)
                 * - Show "No options available" if options array is empty
                 * - Otherwise use the default placeholder
                 */
                placeholder={
                  (field.dynamicOptionsKey &&
                    (field.options === undefined
                      ? 'Loading...'
                      : field.options === null
                        ? 'Error fetching options'
                        : !field.options?.length
                          ? 'No options available'
                          : field.placeholder)) ||
                  field.placeholder
                }
              />
            </SelectTrigger>
          </FormControl>
          <SelectContent>
            {field.options?.map((option: FieldOptionObj) => (
              <SelectItem key={String(option.value)} value={String(option.value)}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      );

    case 'multi-select':
      return (
        <MultiSelect
          options={field.options || []}
          value={(rhfField.value as Option[]) || []}
          onChange={rhfField.onChange}
          labelledBy={field.label || 'Select'}
          disabled={isDisabled}
          hasSelectAll={true}
          disableSearch={false}
          overrideStrings={{
            selectSomeItems: field.placeholder || 'Select All Applicable',
            allItemsAreSelected: field.overrideStrings?.allItemsAreSelected || 'All items are selected',
            selectAll: field.overrideStrings?.selectAll || 'Select All',
            search: field.overrideStrings?.search || 'Search',
          }}
        />
      );

    case 'combo-box': {
      return <ComboBox field={field} rhfField={{ value: String(rhfField.value), onChange: rhfField.onChange }} />;
    }

    case 'checkbox':
      return (
        <CheckboxSingle
          description={field.checkboxLabel}
          value={(rhfField.value as boolean) || false}
          onChange={rhfField.onChange}
          // TODO: add disabled prop to CheckboxSingle
          // disabled={isDisabled}
        />
      );

    case 'checkbox-group':
      return (
        <CheckboxGroup
          options={field.options || []}
          values={(rhfField.value as string[]) || []}
          onChange={rhfField.onChange}
          // TODO: add disabled prop to CheckboxGroup
          // disabled={isDisabled}
          renderFormFieldItemClassName={renderFormFieldItemClassName}
        />
      );

    // Specialized custom field types

    case 'button-radio-group':
      return (
        <ButtonRadioGroup
          options={field.options || []}
          value={rhfField.value as string | boolean}
          onChange={rhfField.onChange}
          name={field.name}
          disabled={isDisabled}
        />
      );

    case 'date':
      return (
        <DatePicker
          disabled={isDisabled}
          date={rhfField.value as string | Date}
          setDate={rhfField.onChange}
          minDate={field.minDate}
          maxDate={field.maxDate}
          placeholder={field.placeholder}
          captionLayout={field.captionLayout}
        />
      );

    case 'availability-matrix': {
      if (field.type !== 'availability-matrix') {
        return null;
      }
      const matrixField = field as unknown as AvailabilityMatrixFieldConfig;
      const matrixOptions: AvailabilityMatrixOptions = {
        days: matrixField.options.days,
        timeSlots: matrixField.options.timeSlots,
      };
      return (
        <AvailabilityMatrix
          options={matrixOptions}
          value={rhfField.value as string | DayAvailability}
          onChange={rhfField.onChange}
          disabled={isDisabled}
        />
      );
    }

    case 'date-dropdown': {
      return (
        <DropdownDatePicker
          disabled={isDisabled}
          date={rhfField.value as string | Date | undefined}
          setDate={rhfField.onChange}
          placeholder={field.placeholder}
          minYears={field.minYears}
          maxYears={field.maxYears}
          fromDate={field.fromDate}
          toDate={field.toDate}
          fromYear={field.fromYear}
          toYear={field.toYear}
          displayFormat={field.displayFormat}
        />
      );
    }

    case 'time':
      return (
        <Input
          className='max-h-9'
          type='time'
          placeholder={field.placeholder}
          disabled={isDisabled}
          value={(rhfField.value as string) || ''}
          onChange={rhfField.onChange}
          onBlur={rhfField.onBlur}
          name={rhfField.name}
        />
      );

    case 'files':
      return (
        <FileDropzone
          name={field.name}
          files={(rhfField.value as { filename: string; filecontents: string }[]) || []}
          maxFiles={field.fileUploadOptions?.maxFiles || 1}
          maxSize={field.fileUploadOptions?.maxSize || 1024 * 1024 * 5}
          accept={field.fileUploadOptions?.accept}
        />
      );

    case 'zip':
      return (
        <Input
          type='text'
          placeholder={field.placeholder}
          disabled={isDisabled}
          value={(rhfField.value as string) || ''}
          onChange={(e) => {
            rhfField.onChange(e);
            getZipDataAndSetCityState(e);
          }}
          onBlur={rhfField.onBlur}
          name={rhfField.name}
        />
      );

    case 'action-item-list':
      return (
        <ActionItemListInput
          field={field}
          value={rhfField.value as Task[]}
          onChange={rhfField.onChange}
          isDisabled={isDisabled}
        />
      );

    case 'summary-select': {
      const option = field.options?.find((option) => option.value === rhfField.value);
      return (
        <div>
          {option ? (
            <span className='text-sm'>{option.label}</span>
          ) : (
            <i className='text-sm'>Calculated based on other inputs</i>
          )}
        </div>
      );
    }

    case 'summary-date': {
      return (
        <div>
          {rhfField.value ? (
            <span className='text-sm'>{new Date(rhfField.value as string | number | Date).toLocaleDateString()}</span>
          ) : (
            <i className='text-sm'>Calculated based on other inputs</i>
          )}
        </div>
      );
    }

    default:
      return null;
  }
};
