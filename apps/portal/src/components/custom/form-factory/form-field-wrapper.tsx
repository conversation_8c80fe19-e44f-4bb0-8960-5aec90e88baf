import { FormControl, FormDescription, FormField, FormLabel, FormMessage } from '@/components/ui/form';
import { cn } from '@/lib/utils';
import { type FieldConfig, type FormValues } from '@/types/form-field';
import React, { useEffect } from 'react';
import { type UseFormReturn, useWatch } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';

import { FormField as RenderFormField } from './render-form-field';

interface FormFieldWrapperProps {
  field: FieldConfig;
  formWrapperClassName?: string;
  formFieldElClass?: string;
  readOnly?: boolean;
  onChange?: (values: FormValues) => void;
  labelWrapperClassName?: string;
  formFieldSubFieldsWrapperClass?: string;
  renderFormFieldItemClassName?: string;
}

const FormFieldWrapper = ({
  field,
  formWrapperClassName,
  formFieldElClass,
  readOnly,
  onChange,
  labelWrapperClassName,
  formFieldSubFieldsWrapperClass,
  renderFormFieldItemClassName,
}: FormFieldWrapperProps): JSX.Element | null => {
  const {
    control,
    setValue,
    unregister,
    formState: { errors },
  } = useFormContext() as UseFormReturn<FormValues>;
  const formValues = useWatch({ control });
  // Logic for fields whose display is configured to be conditional on values from other fields
  const isHidden = field.hidden ? field.hidden(formValues) : false;
  const isDisabled =
    readOnly || (typeof field.disabled === 'function' ? field.disabled(formValues) : Boolean(field.disabled));

  // Add effect to call onChange prop when form values change
  useEffect(() => {
    if (onChange) {
      const timeoutId = setTimeout(() => {
        onChange(formValues);
      }, 0);
      return () => clearTimeout(timeoutId);
    }
  }, [formValues]);

  // support showing error coloration on subfields label if one or both have validation error
  const subFieldsHaveError = field.subFields?.some((subfield) => !!errors[subfield.name]);

  useEffect(() => {
    if (field.subFields) {
      // Make sure the parent field of any subfields is not picked up by react-hook-form
      unregister(field.name);
    }
  }, [field.subFields, field.name, unregister]);

  // field.onChange is a callback that can be used to trigger side effects in the parent when the field value changes (e.g. fetching data from an API)
  useEffect(() => {
    if (field.onChange) {
      field.onChange(formValues);
    }
  }, [formValues[field.name], field.onChange, formValues]);

  // allows setting a default value dynamically - e.g. if select options load from an API response, and one of the values should be set as the default
  useEffect(() => {
    if (field.defaultValue) {
      setValue(field.name, field.defaultValue);
    }
  }, [field.defaultValue, field.name, setValue]);

  if (isHidden) return null;

  // Subfields exist to support UX designs where more than one (generally just 2) fields are rendered side by side in a row, instead of a full-width column
  // There is no effect on the form values data structure (form values object is still flat) - it's purely for UX
  if (field.subFields) {
    return (
      <div className='mt-6 flex w-full flex-wrap items-start gap-4 max-md:max-w-full'>
        {field.label && (
          <>
            <div
              className={`flex w-60 min-w-[200px] max-w-[280px] flex-col items-start text-sm font-semibold leading-5 text-emaTextSecondary ${labelWrapperClassName}`}
            >
              <FormLabel htmlFor={field.name} className={subFieldsHaveError ? 'text-destructive' : ''}>
                {field.label}
              </FormLabel>
            </div>
          </>
        )}
        <div className='flex min-w-[240px] flex-1 shrink basis-0 flex-col text-base leading-6'>
          <div className={cn(`flex flex-col gap-4 lg:flex-row lg:gap-0 lg:space-x-4`, formFieldSubFieldsWrapperClass)}>
            {field.subFields.map((subfield) => (
              <FormField
                key={subfield.name}
                control={control}
                name={subfield.name}
                render={({ field: subRhfField }) => (
                  <div className='flex flex-col lg:flex-1'>
                    {subfield.label && (
                      <div className='pb-3 text-sm font-semibold leading-5 text-emaTextSecondary'>
                        <FormLabel htmlFor={subfield.name}>{subfield.label}</FormLabel>
                      </div>
                    )}
                    <FormControl className='lg:flex-1'>
                      <RenderFormField
                        field={subfield}
                        rhfField={subRhfField}
                        isDisabled={isDisabled}
                        renderFormFieldItemClassName={renderFormFieldItemClassName}
                      />
                    </FormControl>

                    <FormMessage customErrorMessage={subfield.errorMessage} className='mt-1' />
                  </div>
                )}
              />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <FormField
      control={control}
      name={field.name}
      render={({ field: rhfField }) => (
        <>
          {field.heading || field.descriptionToHeading ? (
            <div>
              <div>{field.heading ? <h2 className='mb-2 text-2xl font-semibold'>{field.heading}</h2> : null}</div>
              <div>
                {field.descriptionToHeading ? (
                  <div className='text-emaTextSecondary'>{field.descriptionToHeading}</div>
                ) : null}
              </div>
            </div>
          ) : null}
          <div className={cn(`mt-2 flex w-full flex-wrap items-start gap-2 max-md:max-w-full`, formWrapperClassName)}>
            <div className='flex flex-col'>
              {field.label && (
                <div
                  className={cn(
                    'flex w-60 min-w-[200px] max-w-[280px] flex-col items-start text-sm font-semibold leading-5 text-emaTextSecondary',
                    labelWrapperClassName,
                  )}
                >
                  <FormLabel htmlFor={field.name}>{field.label}</FormLabel>
                  {field.labelDescription ? (
                    <div className='mt-1 text-wrap text-sm font-light text-emaTextSecondary'>
                      {field.labelDescription.split('\n').map((line, index) => (
                        <div className='mb-1' key={index}>
                          {line}
                        </div>
                      ))}
                    </div>
                  ) : null}
                </div>
              )}
            </div>
            <div
              className={cn(`flex min-w-[240px] flex-1 shrink basis-0 flex-col text-base leading-6`, formFieldElClass)}
            >
              <FormControl>
                <RenderFormField
                  field={field}
                  rhfField={rhfField}
                  isDisabled={isDisabled}
                  renderFormFieldItemClassName={renderFormFieldItemClassName}
                />
              </FormControl>
              <FormDescription className='mt-2'>{field.description}</FormDescription>
              <FormMessage customErrorMessage={field.errorMessage} className='mt-1' />
            </div>
          </div>
        </>
      )}
    />
  );
};

export default FormFieldWrapper;
