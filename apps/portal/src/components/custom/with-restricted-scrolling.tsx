'use client';

import { useBodyScrollLock } from '@/hooks/useBodyScrollLock';

interface WithRestrictedScrollingProps {
  children: React.ReactNode;
}

/*
 * This component is a wrapper around any layout that requires restricted scrolling at the body level.
 * It uses the useBodyScrollLock hook to prevent scrolling past the Y overflow on the body element.
 * This is mainly to prevent a bug with the shadcn/ui Select component on two-column dashboard layouts,
 * which creates empty scrollable space below the body content, even if the select dropdown is closed.
 */

const WithRestrictedScrolling = ({ children }: WithRestrictedScrollingProps): React.ReactNode => {
  useBodyScrollLock(true);

  return children;
};

export default WithRestrictedScrolling;
