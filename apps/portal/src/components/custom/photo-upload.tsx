'use client';

import { FileUpload } from '@/components/custom/file-upload';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { usePhotoUpload } from '@/hooks/use-photo-upload';
import Image from 'next/image';
import { ReactNode, useEffect, useState } from 'react';

/**
 * Base type for entities that have photos
 */
interface PhotoEntity {
  id?: string;
  photoUrl?: string;
  photoS3FileName?: string;
  thumbnailUrl?: string;
  thumbnailS3FileName?: string;
}

/**
 * Props for the PhotoUpload component
 */
interface PhotoUploadProps<T extends PhotoEntity = PhotoEntity> {
  /**
   * The ID of the entity (user, mom, etc.) that owns the photo
   */
  id: string | undefined;

  /**
   * The current photo URL of the entity, if any
   */
  photoUrl: string | undefined;

  /**
   * The current thumbnail URL of the entity, if any
   */
  thumbnailUrl?: string | undefined;

  /**
   * Function to call when uploading a new photo
   * @param id - The entity ID
   * @param file - The file to upload
   */
  uploadFunction: (id: string, file: File) => Promise<T>;

  /**
   * Function to call when removing an existing photo
   * @param id - The entity ID
   */
  removeFunction: (id: string) => Promise<T>;

  /**
   * Base query key for React Query cache management
   */
  queryKey: string;

  /**
   * Additional query keys to invalidate when the photo changes
   */
  additionalInvalidations?: string[];

  /**
   * Callback function called after successful upload or removal
   * @param data - The result from the API call
   */
  onSuccess?: (data?: T) => void;

  /**
   * Name of the entity for UI messages
   * Example: "Profile", "Mom", "User"
   */
  entityName: string;

  /**
   * React component to show as a placeholder when no photo exists
   * or when the photo fails to load
   */
  placeholderComponent: ReactNode;
}

/**
 * A reusable component for photo upload, display, and removal.
 *
 * This component provides a complete UI for managing photos:
 * - Displays the current photo if one exists
 * - Shows a placeholder when no photo exists
 * - Handles drag and drop for photo upload
 * - Provides a button to upload photos
 * - Offers a remove button for existing photos
 * - Shows a confirmation dialog before removal
 * - Displays loading states during upload/deletion
 *
 * @param props - The photo upload component properties
 *
 * @example
 * ```tsx
 * <PhotoUpload
 *   id={userId}
 *   photoUrl={userPhoto}
 *   uploadFunction={uploadUserPhoto}
 *   removeFunction={removeUserPhoto}
 *   queryKey="user"
 *   additionalInvalidations={['user-data']}
 *   entityName="Profile"
 *   placeholderComponent={<CircleUserRound className='h-28 w-28 text-muted-foreground' />}
 * />
 * ```
 */
export function PhotoUpload<T extends PhotoEntity = PhotoEntity>({
  id,
  photoUrl,
  thumbnailUrl,
  uploadFunction,
  removeFunction,
  queryKey,
  additionalInvalidations,
  onSuccess,
  entityName,
  placeholderComponent,
}: PhotoUploadProps<T>) {
  // Use the photo upload hook
  const {
    imageUrl,
    isUploading,
    isDeleting,
    imageError,
    showDeleteConfirm,
    setShowDeleteConfirm,
    handleFileSelect,
    executeRemoveImage,
    handleImageError,
  } = usePhotoUpload({
    id,
    photoUrl: thumbnailUrl || photoUrl, // Prefer thumbnail for display
    uploadFunction,
    removeFunction,
    queryKey,
    additionalInvalidations,
    onSuccess,
    entityType: entityName.toLowerCase(),
  });

  const [isLoading, setIsLoading] = useState(true);
  const [hasAttemptedLoad, setHasAttemptedLoad] = useState(false);
  const [isNewImageLoading, setIsNewImageLoading] = useState(false);
  const [oldImageUrl, setOldImageUrl] = useState<string | null>(null);

  // Store the previous image URL when a new upload starts
  useEffect(() => {
    if (isUploading && imageUrl && !oldImageUrl) {
      setOldImageUrl(imageUrl);
    } else if (!isUploading && oldImageUrl) {
      // Clear the old image after a delay to ensure smooth transition
      const timer = setTimeout(() => {
        setOldImageUrl(null);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [isUploading, imageUrl, oldImageUrl]);

  // Handle loading state
  useEffect(() => {
    if (isUploading) {
      // Keep loading during upload operations
      setIsLoading(true);
      setHasAttemptedLoad(false);
      return;
    }

    // Only stop loading when we have the final image or we're sure there's no image
    if (photoUrl) {
      // For existing photos, only show content when the image is actually loaded
      if (imageUrl) {
        setIsLoading(false);
        setHasAttemptedLoad(true);
      } else {
        // Keep loading until image is available
        setIsLoading(true);
        setHasAttemptedLoad(false);
      }
    } else {
      // If we don't have a photo URL, we can show the default state
      setIsLoading(false);
      setHasAttemptedLoad(true);
    }
  }, [photoUrl, imageUrl, isUploading, isDeleting]);

  // Handle new image loading
  useEffect(() => {
    if (isUploading) {
      setIsNewImageLoading(true);
    } else if (imageUrl && !isLoading) {
      // Add a small delay to ensure smooth transition
      const timer = setTimeout(() => {
        setIsNewImageLoading(false);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [isUploading, imageUrl, isLoading]);

  // Complete helper text for upload including quality guidance
  const uploadHelperText = (
    <div className='space-y-1 text-center text-xs text-muted-foreground'>
      <p>JPG, PNG or GIF. Max 5MB.</p>
      <p>For best quality, upload images at least 600x600 pixels</p>
    </div>
  );

  // Helper text for when a photo exists and can be removed
  const removeHelperText = (
    <div className='space-y-2'>
      <div className='text-center text-xs text-muted-foreground'>
        <p>JPG, PNG or GIF. Max 5MB.</p>
        <p className='mt-1'>For best quality, upload images at least 600x600 pixels</p>
      </div>
      <div className='mt-2 flex justify-center'>
        <Button
          variant='outline'
          className='text-xs text-destructive'
          disabled={isDeleting}
          onClick={() => setShowDeleteConfirm(true)}
        >
          {isDeleting ? 'Removing...' : 'Remove Photo'}
        </Button>
      </div>
    </div>
  );

  return (
    <>
      <div className='-mb-4 flex w-full flex-col'>
        <div className='flex w-full flex-col'>
          <div className='w-full rounded-lg'>
            {isLoading || !hasAttemptedLoad ? (
              // Loading skeleton
              <div className='flex w-full flex-col items-center gap-4'>
                <div className='relative flex h-40 w-40 items-center justify-center overflow-hidden rounded-full bg-gray-200'>
                  <div className='h-full w-full animate-pulse rounded-full bg-gray-200' />
                </div>
                <div className='flex flex-col items-center gap-2'>
                  <div className='h-4 w-24 animate-pulse rounded bg-gray-200' />
                  <div className='h-3 w-32 animate-pulse rounded bg-gray-200' />
                </div>
                <p className='text-sm text-muted-foreground'>Loading, please wait...</p>
              </div>
            ) : !photoUrl ? (
              // Upload UI when no photo exists
              <FileUpload
                onFileSelect={handleFileSelect}
                acceptedFileTypes={['image/jpeg', 'image/png', 'image/gif']}
                maxFileSize={5 * 1024 * 1024} // 5MB
                buttonLabel='Upload Photo'
                dropText='Drop image here to upload'
                isUploading={isUploading}
                helperText={uploadHelperText}
                enableDragDrop={true}
                multiple={false}
                dropZoneClassName='w-auto'
                fullWidth={true}
              >
                {placeholderComponent}
              </FileUpload>
            ) : (
              // Replace/remove UI when a photo exists
              <FileUpload
                onFileSelect={handleFileSelect}
                acceptedFileTypes={['image/jpeg', 'image/png', 'image/gif']}
                maxFileSize={5 * 1024 * 1024} // 5MB
                buttonLabel='Change Photo'
                dropText='Drop to replace photo'
                isUploading={isUploading}
                helperText={removeHelperText}
                enableDragDrop={true}
                multiple={false}
                dropZoneClassName='w-auto'
                fullWidth={true}
              >
                <div className='relative h-40 w-40'>
                  {/* Show old image during transition if available */}
                  {oldImageUrl && isUploading && (
                    <div className='absolute inset-0 z-10'>
                      <Image
                        src={oldImageUrl}
                        alt={`Previous ${entityName} Photo`}
                        width={160}
                        height={160}
                        className='h-40 w-40 rounded-full object-cover'
                        priority
                      />
                    </div>
                  )}

                  {/* Current image */}
                  {imageUrl && !imageError ? (
                    <div
                      className={`absolute inset-0 z-20 transition-opacity duration-500 ${
                        oldImageUrl && isUploading ? 'opacity-0' : 'opacity-100'
                      }`}
                    >
                      <Image
                        src={imageUrl}
                        alt={`${entityName} Photo`}
                        width={160}
                        height={160}
                        className='h-40 w-40 rounded-full object-cover'
                        onError={handleImageError}
                        priority
                      />
                    </div>
                  ) : (
                    <div className='absolute inset-0 z-20 flex items-center justify-center'>{placeholderComponent}</div>
                  )}

                  {/* Loading overlay */}
                  {isNewImageLoading && (
                    <div className='absolute inset-0 z-30 flex items-center justify-center rounded-full bg-black/20 transition-opacity duration-500'>
                      <div className='h-8 w-8 animate-spin rounded-full border-4 border-white border-t-transparent' />
                    </div>
                  )}
                </div>
              </FileUpload>
            )}
          </div>
        </div>
      </div>

      {/* Delete confirmation dialog */}
      <Dialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Remove Photo</DialogTitle>
            <DialogDescription>
              Are you sure you want to remove this {entityName.toLowerCase()} photo? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant='outline' onClick={() => setShowDeleteConfirm(false)}>
              Cancel
            </Button>
            <Button variant='destructive' onClick={executeRemoveImage} disabled={isDeleting}>
              {isDeleting ? 'Removing...' : 'Remove Photo'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
