'use client';

import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { ArrowUpFromLine, Upload as UploadIcon } from 'lucide-react';
import { ReactNode, useCallback, useEffect, useRef, useState } from 'react';

import { Button } from '../ui/button';

export interface FileUploadProps {
  /**
   * Function called when file(s) are selected and validated
   */
  onFileSelect: (files: File | File[]) => void | Promise<void>;

  /**
   * Maximum file size in bytes
   * @default 5242880 (5MB)
   */
  maxFileSize?: number;

  /**
   * Array of accepted file types (MIME types)
   * @example ['image/jpeg', 'image/png']
   * @default [] (all files)
   */
  acceptedFileTypes?: string[];

  /**
   * Custom label text for the upload button
   * @default 'Upload File'
   */
  buttonLabel?: string;

  /**
   * Custom helper text displayed below the upload button
   */
  helperText?: ReactNode;

  /**
   * Text to display when dragging a file over the drop zone
   * @default 'Drop file here'
   */
  dropText?: string;

  /**
   * CSS class name for the drop zone container
   */
  dropZoneClassName?: string;

  /**
   * Width of the drop zone
   * @default 'w-48'
   */
  width?: string;

  /**
   * Height of the drop zone
   * @default 'h-48'
   */
  height?: string;

  /**
   * Component to render inside the drop zone
   */
  children?: ReactNode;

  /**
   * Is the upload in progress
   */
  isUploading?: boolean;

  /**
   * Enable or disable drag and drop functionality
   * @default true
   */
  enableDragDrop?: boolean;

  /**
   * Allow multiple file selection
   * @default false
   */
  multiple?: boolean;

  /**
   * Whether the component should take up maximum available width
   * @default false - component will take minimal width needed
   */
  fullWidth?: boolean;
}

/**
 * A reusable file upload component that supports both button selection and drag-and-drop.
 *
 * Features:
 * - File selection via button click
 * - Drag and drop support with visual feedback
 * - File validation (size and type)
 * - Support for single or multiple file uploads
 * - Custom placeholder content
 * - Responsive design with optional full-width mode
 * - Loading state indication
 * - Customizable appearance and text
 *
 * @param props - The FileUpload component properties
 *
 * @example
 * ```tsx
 * <FileUpload
 *   onFileSelect={handleFileSelect}
 *   acceptedFileTypes={['image/jpeg', 'image/png', 'image/gif']}
 *   maxFileSize={5 * 1024 * 1024}
 *   buttonLabel='Upload Photo'
 *   dropText='Drop image here to upload'
 *   isUploading={isUploading}
 *   fullWidth={true}
 * >
 *   <PlaceholderIcon className='h-28 w-28' />
 * </FileUpload>
 */
export function FileUpload({
  onFileSelect,
  maxFileSize = 5 * 1024 * 1024, // 5MB
  acceptedFileTypes = [],
  buttonLabel = 'Upload File',
  helperText,
  dropText = 'Drop file here',
  dropZoneClassName,
  width = 'w-48',
  height = 'h-48',
  children,
  isUploading = false,
  enableDragDrop = true,
  multiple = false,
  fullWidth = false,
}: FileUploadProps) {
  const { toast } = useToast();
  const [isDragging, setIsDragging] = useState(false);
  const dragCounterRef = useRef(0);

  const handleFilesSelect = useCallback(
    async (filesList: FileList | null) => {
      if (!filesList || filesList.length === 0) return;

      const validateFiles = (files: File[]): File[] => {
        const validFiles: File[] = [];
        const invalidSizeFiles: string[] = [];
        const invalidTypeFiles: string[] = [];

        for (const file of files) {
          // Check file size
          if (file.size > maxFileSize) {
            invalidSizeFiles.push(file.name);
            continue;
          }

          // Check file type if specified
          if (acceptedFileTypes.length > 0 && !acceptedFileTypes.includes(file.type)) {
            invalidTypeFiles.push(file.name);
            continue;
          }

          validFiles.push(file);
        }

        // Show toast messages for invalid files
        if (invalidSizeFiles.length > 0) {
          toast({
            title: 'Error',
            description: `${invalidSizeFiles.length > 1 ? 'Files' : 'File'} too large: ${invalidSizeFiles.join(', ')}. Max size: ${maxFileSize / (1024 * 1024)}MB`,
            variant: 'destructive',
          });
        }

        if (invalidTypeFiles.length > 0) {
          toast({
            title: 'Error',
            description: `Invalid file ${invalidTypeFiles.length > 1 ? 'types' : 'type'}: ${invalidTypeFiles.join(', ')}. Accepted: ${acceptedFileTypes.join(', ')}`,
            variant: 'destructive',
          });
        }

        return validFiles;
      };

      const filesArray = Array.from(filesList);
      const validFiles = validateFiles(filesArray);

      if (validFiles.length === 0) return;

      try {
        if (multiple) {
          await onFileSelect(validFiles);
        } else {
          // Only use the first valid file if multiple is false
          await onFileSelect(validFiles[0]);
        }
      } catch (error) {
        toast({
          title: 'Error',
          description: error instanceof Error ? error.message : 'Failed to process file',
          variant: 'destructive',
        });
      }
    },
    [multiple, onFileSelect, toast, maxFileSize, acceptedFileTypes],
  );

  const handleInputChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    await handleFilesSelect(files);
  };

  // Drag and drop handlers
  const handleDragEnter = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      if (!enableDragDrop) return;
      e.preventDefault();
      e.stopPropagation();

      // Increment counter on drag enter
      dragCounterRef.current += 1;
      setIsDragging(true);
    },
    [enableDragDrop],
  );

  const handleDragLeave = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      if (!enableDragDrop) return;
      e.preventDefault();
      e.stopPropagation();

      // Decrement counter on drag leave
      dragCounterRef.current -= 1;

      // Only set dragging to false when counter reaches 0
      // This means the mouse has truly left the component
      if (dragCounterRef.current === 0) {
        setIsDragging(false);
      }
    },
    [enableDragDrop],
  );

  const handleDragOver = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      if (!enableDragDrop) return;
      e.preventDefault();
      e.stopPropagation();
      // No state update needed here to prevent flickering
    },
    [enableDragDrop],
  );

  const handleDrop = useCallback(
    async (e: React.DragEvent<HTMLDivElement>) => {
      if (!enableDragDrop) return;
      e.preventDefault();
      e.stopPropagation();

      // Reset counter and state on drop
      dragCounterRef.current = 0;
      setIsDragging(false);

      const files = e.dataTransfer.files;
      await handleFilesSelect(files);
    },
    [enableDragDrop, handleFilesSelect],
  );

  // Prevent default dragging behavior outside the drop zone
  useEffect(() => {
    if (!enableDragDrop) return;

    const handleDragOver = (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
    };

    const handleDrop = (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
    };

    const handleDragEnd = () => {
      // Reset counter and state when drag operation ends
      dragCounterRef.current = 0;
      setIsDragging(false);
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        // Reset counter and state on Escape key
        dragCounterRef.current = 0;
        setIsDragging(false);
      }
    };

    // Add event listeners to the entire document
    document.addEventListener('dragover', handleDragOver);
    document.addEventListener('drop', handleDrop);
    document.addEventListener('dragend', handleDragEnd);
    document.addEventListener('keydown', handleKeyDown);

    // Clean up
    return () => {
      document.removeEventListener('dragover', handleDragOver);
      document.removeEventListener('drop', handleDrop);
      document.removeEventListener('dragend', handleDragEnd);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [enableDragDrop]);

  return (
    <div
      className={cn(
        'relative flex flex-col items-center gap-2 rounded-lg p-4',
        dropZoneClassName,
        'border-2 border-dashed',
        isDragging && enableDragDrop
          ? 'border-blue-500 bg-blue-50/20'
          : enableDragDrop
            ? 'border-gray-300 transition-colors hover:border-gray-400'
            : 'border-gray-200',
        fullWidth ? 'w-full' : 'max-w-fit',
      )}
      onDragEnter={handleDragEnter}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {enableDragDrop && (
        <div className='absolute -top-3 left-4 flex items-center gap-1 bg-white px-2 text-xs text-gray-500'>
          <UploadIcon className='h-3 w-3' />
          <span>{multiple ? 'Drop Files Here' : 'Drop File Here'}</span>
        </div>
      )}

      {/* Drag overlay - covers the entire component when dragging */}
      {isDragging && enableDragDrop && (
        <div className='absolute inset-0 z-20 flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-blue-500 bg-blue-50 bg-opacity-80'>
          <UploadIcon className='h-12 w-12 text-blue-600' />
          <p className='mt-2 text-sm font-semibold text-blue-700'>
            {dropText || (multiple ? 'Drop files here' : 'Drop file here')}
          </p>
        </div>
      )}

      <div
        className={cn(
          `relative flex items-center justify-center overflow-hidden rounded-full bg-gray-100`,
          fullWidth ? `${width} ${height} max-w-full` : `${width} ${height}`,
          'mx-auto', // Center on mobile
        )}
      >
        {/* Content */}
        {children}
      </div>

      <div className={cn('relative flex flex-col items-center gap-2', fullWidth && 'w-full')}>
        <Button
          variant='secondary'
          asChild
          className={cn('z-10 cursor-pointer', 'px-4 py-2 sm:px-6')} // Better padding on mobile
          disabled={isUploading}
        >
          <label htmlFor='file-upload-input'>{isUploading ? 'Uploading...' : buttonLabel}</label>
        </Button>
        <input
          id='file-upload-input'
          type='file'
          accept={acceptedFileTypes.join(',')}
          className='hidden'
          onChange={handleInputChange}
          disabled={isUploading}
          multiple={multiple}
        />
        {enableDragDrop && (
          <div className='mt-1 flex items-center gap-1 text-xs text-muted-foreground'>
            <ArrowUpFromLine className='hidden h-3 w-3 sm:block' />
            <span className='text-center sm:text-left'>
              {multiple ? 'Drag files here or click button to upload' : 'Drag a file here or click button to upload'}
            </span>
          </div>
        )}
        {helperText && <div className={cn('z-10', fullWidth && 'w-full')}>{helperText}</div>}
      </div>
    </div>
  );
}
