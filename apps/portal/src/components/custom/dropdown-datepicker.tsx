import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn, formatDateForDisplay } from '@/lib/utils';
import { parseISO } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import * as React from 'react';

interface DropdownDatePickerProps {
  date: Date | string | undefined;
  setDate: (date: Date | undefined | string) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  // For age-based selection (e.g., must be 18 years old)
  minYears?: number;
  maxYears?: number;
  // For specific date ranges
  fromDate?: Date;
  toDate?: Date;
  // For year range in the dropdown
  fromYear?: number;
  toYear?: number;
  // Date format
  displayFormat?: string;
}

const DropdownDatePicker: React.FC<DropdownDatePickerProps> = ({
  date,
  setDate,
  className,
  placeholder = 'Select date',
  disabled = false,
  minYears,
  maxYears,
  fromDate,
  toDate,
  fromYear,
  toYear,
  displayFormat = 'MM/dd/yyyy',
}) => {
  // Calculate date limits based on age requirements if provided
  const effectiveFromDate = React.useMemo(() => {
    if (fromDate) return fromDate;
    if (maxYears) {
      const date = new Date();
      date.setFullYear(date.getFullYear() - maxYears);
      return date;
    }
    return undefined;
  }, [fromDate, maxYears]);

  const effectiveToDate = React.useMemo(() => {
    if (toDate) return toDate;
    if (minYears) {
      const date = new Date();
      date.setFullYear(date.getFullYear() - minYears);
      return date;
    }
    return undefined;
  }, [toDate, minYears]);

  // Determine year range for dropdown
  const effectiveFromYear = React.useMemo(() => {
    if (fromYear) return fromYear;
    return effectiveFromDate?.getFullYear() ?? new Date().getFullYear() - 100;
  }, [effectiveFromDate, fromYear]);

  const effectiveToYear = React.useMemo(() => {
    if (toYear) return toYear;
    return effectiveToDate?.getFullYear() ?? new Date().getFullYear();
  }, [effectiveToDate, toYear]);

  // Handle parsing of date values
  const parsedDate = React.useMemo(() => {
    if (!date) return undefined;
    return typeof date === 'string' ? parseISO(date) : date;
  }, [date]);

  // Format date for display using the utility function
  const displayDate = parsedDate ? formatDateForDisplay(parsedDate, displayFormat) : undefined;

  return (
    <Popover modal={true}>
      <PopoverTrigger asChild>
        <Button
          disabled={disabled}
          variant='outline'
          className={cn('w-full justify-start text-left font-normal', !date && 'text-muted-foreground', className)}
        >
          <CalendarIcon className='mr-2 h-4 w-4' />
          {displayDate || <span>{placeholder}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-auto p-0'>
        <Calendar
          mode='single'
          selected={parsedDate}
          onSelect={(selectedDate) => {
            if (selectedDate) {
              setDate(selectedDate.toISOString());
            }
          }}
          // Set date range limits
          fromDate={effectiveFromDate}
          toDate={effectiveToDate}
          // Set year range for dropdown
          fromYear={effectiveFromYear}
          toYear={effectiveToYear}
          // Enable dropdown for month and year selection
          captionLayout='dropdown'
          // Make sure calendar focuses initially for keyboard navigation
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
};

export default DropdownDatePicker;
