import { toast } from '@/hooks/use-toast';
import { downloadDocument } from '@/lib/portal';
import { DownloadCloud, ExternalLink } from 'lucide-react';

import { Button } from '../ui/button';

const RenderLinkOrDocument = ({
  document,
}: {
  document: {
    external_url_c: string | null;
    filename: string | null;
    document_name: string | null;
    id: string | null;
  };
}) => {
  const handleDocumentDownload = async (documentId: string) => {
    try {
      if (!documentId) {
        throw new Error('Document ID is required for download');
      }
      const originalFilename = document.filename || document.document_name || 'document';
      await downloadDocument(documentId, originalFilename);
    } catch (error) {
      console.error('Error downloading file:', error);
      toast({
        title: 'Failed to download document',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive',
      });
    }
  };

  return (
    <>
      {document.external_url_c ? (
        <a
          href={document.external_url_c}
          target='_blank'
          rel='noopener noreferrer'
          className='flex items-center text-base text-blue-600 hover:text-blue-800 hover:underline sm:font-semibold md:text-sm md:font-normal'
        >
          {document.filename || document.document_name || 'Untitled Link'}
          <ExternalLink className='mx-1 h-5 w-5 text-blue-600 hover:text-blue-800' />
        </a>
      ) : (
        <Button
          variant='ghost'
          onClick={() => document.id && handleDocumentDownload(document.id)}
          disabled={!document.id}
          className='mx-0 w-fit px-0 text-left hover:bg-transparent hover:underline'
        >
          <span className='mr-2 block text-left text-base sm:font-semibold md:text-sm md:font-normal'>
            {document.filename || document.document_name || 'Untitled Document'}
          </span>
          <DownloadCloud className='h-6 w-6' />
        </Button>
      )}
    </>
  );
};

export default RenderLinkOrDocument;
