import { Button } from '@/components/ui/button';
import { Drawer, DrawerClose, <PERSON><PERSON><PERSON>ontent, Drawer<PERSON>eader, DrawerTitle } from '@/components/ui/drawer';
import { cn } from '@/lib/utils';
import { X } from 'lucide-react';
import { ReactNode, useEffect, useState } from 'react';

interface ResponsiveDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string | ReactNode;
  headerComponent?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

type DrawerDirection = 'right' | 'top' | 'bottom' | 'left' | undefined;

const ResponsiveDrawer: React.FC<ResponsiveDrawerProps> = ({
  isOpen,
  onClose,
  title,
  headerComponent,
  children,
  className,
}) => {
  const [direction, setDirection] = useState<DrawerDirection>('right');
  const [drawerClassName, setDrawerClassName] = useState('');

  // Responsive drawer direction
  // Opens from the right on desktop screens, and from bottom on mobile screens
  useEffect(() => {
    const updateDirection = (): void => {
      if (window.innerWidth < 640) {
        setDirection('bottom');
        setDrawerClassName('p-6 h-full overflow-y-auto'); // Adding height and overflow for mobile
      } else {
        setDirection('right');
        setDrawerClassName('h-screen top-0 right-0 left-auto mt-0 w-[500px] rounded-none overflow-y-auto'); // Adding overflow for desktop
      }
    };

    updateDirection();

    window.addEventListener('resize', updateDirection);

    // Unmount cleanup
    return () => {
      window.removeEventListener('resize', updateDirection);
    };
  }, []);

  return (
    <Drawer open={isOpen} onClose={onClose} direction={direction}>
      <DrawerContent className={cn(drawerClassName, className)}>
        <div
          className={cn('flex items-center justify-between pt-2 md:pl-2 md:pr-4', {
            'mt-4': direction === 'right',
          })}
        >
          {headerComponent ?? (
            <>
              <DrawerHeader>{title ? <DrawerTitle>{title}</DrawerTitle> : null}</DrawerHeader>
              <DrawerClose asChild>
                <Button variant='ghost' size='icon' onClick={onClose}>
                  <X size={20} />
                </Button>
              </DrawerClose>
            </>
          )}
        </div>

        {children}
      </DrawerContent>
    </Drawer>
  );
};

export default ResponsiveDrawer;
