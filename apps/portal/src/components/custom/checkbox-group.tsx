'use client';

import { Checkbox } from '@/components/ui/checkbox';
import type { FieldOptions } from '@/types/form-field';
import { FC } from 'react';

interface CheckboxGroupProps {
  options: FieldOptions;
  values: string[];
  onChange: (value: string[]) => void;
  renderFormFieldItemClassName?: string;
}

const CheckboxGroup: FC<CheckboxGroupProps> = ({ options, values, onChange, renderFormFieldItemClassName }) => (
  <div className={`space-y-4 ${renderFormFieldItemClassName}`}>
    {options.map((option) => {
      const optionValue = typeof option === 'string' ? option : String(option.value);
      const optionLabel = typeof option === 'string' ? option : option.label;
      const isChecked = values.includes(optionValue);
      return (
        <div key={optionValue} className='flex items-center space-x-2'>
          <Checkbox
            checked={isChecked}
            onCheckedChange={(checked) => {
              if (checked) {
                onChange([...values, optionValue]);
              } else {
                onChange(values.filter((v) => v !== optionValue));
              }
            }}
          />
          <label className='text-sm font-medium leading-tight peer-disabled:cursor-not-allowed peer-disabled:opacity-70'>
            {optionLabel}
          </label>
        </div>
      );
    })}
  </div>
);

export default CheckboxGroup;
