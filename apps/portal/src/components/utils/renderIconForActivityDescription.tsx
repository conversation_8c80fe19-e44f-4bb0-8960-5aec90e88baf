import { CalendarDaysIcon, FlagIcon, HandHelpingIcon, MessageCircleMoreIcon, NotebookTextIcon } from 'lucide-react';

// Using Pascal_Snake_case for the activity descriptions because Support, Track and Referral are coming in this way and we want to match the activity descriptions without doing unnecessary data transformations
type ActivityDescriptionType =
  | 'Connection_Log'
  | 'Benevolence'
  | 'Support_Session'
  | 'Track_Session'
  | 'Coordinator_Note';

const activityDescriptionToRender = (type: ActivityDescriptionType): React.ReactNode => {
  switch (type) {
    case 'Connection_Log':
      return (
        <>
          <MessageCircleMoreIcon className='mr-2 h-4 w-4' />
          Connection Log
        </>
      );
    case 'Benevolence':
      return (
        <>
          <FlagIcon className='mr-2 h-4 w-4' />
          Flagged Need
        </>
      );
    case 'Support_Session':
      return (
        <>
          <HandHelpingIcon className='mr-2 h-4 w-4' />
          Support Session
        </>
      );
    case 'Track_Session':
      return (
        <>
          <CalendarDaysIcon className='mr-2 h-4 w-4' />
          Track Session
        </>
      );
    case 'Coordinator_Note':
      return (
        <>
          <NotebookTextIcon className='mr-2 h-4 w-4' />
          Coordinator Note
        </>
      );
  }
};

export default activityDescriptionToRender;
