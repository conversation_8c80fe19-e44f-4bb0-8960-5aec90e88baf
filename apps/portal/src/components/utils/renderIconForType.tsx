import { Banknote, HandHeart, Mail, MessageSquareText, Phone, ShoppingCart, User, Video } from 'lucide-react';

const renderIconForType = (type: string) => {
  switch (type) {
    case 'SMS_Text':
      return <MessageSquareText />;
    case 'Call':
      return <Phone />;
    case 'Email':
      return <Mail />;
    case 'Video':
      return <Video />;
    case 'In_Person':
      return <User />;
    case 'Financial':
      return <Banknote />;
    case 'Physical Goods':
      return <ShoppingCart />;
    case 'Physical':
      return <ShoppingCart />;
    case 'Other Services':
      return <HandHeart />;
    case 'Other':
      return <HandHeart />;

    default:
      return null;
  }
};

export default renderIconForType;
