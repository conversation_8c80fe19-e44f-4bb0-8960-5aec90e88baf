export const AUTH_COOKIE_NAME = 'x-ema-auth';

// Enables checking dashboard URL role param against defined roles
export const PORTAL_ROLE_PARAMS_MAP: Record<string, string> = Object.freeze({
  admin: 'administrator',
  supervisor: 'supervisor',
  coordinator: 'coordinator',
  advocate: 'advocate',
});

export const languageOptions = [
  { label: 'English', value: 'english' },
  { label: 'Spanish', value: 'spanish' },
  { label: 'Chinese (Mandarin)', value: 'chinese_mandarin' },
  { label: 'Vietnamese', value: 'vietnamese' },
  { label: 'Tagalog', value: 'tagalog' },
  { label: 'Arabic', value: 'arabic' },
  { label: 'Korean', value: 'korean' },
  { label: 'Russian', value: 'russian' },
  { label: 'French', value: 'french' },
  { label: 'Hindi', value: 'hindi' },
  { label: 'Portuguese', value: 'portuguese' },
  { label: 'Bengali', value: 'bengali' },
  { label: 'Urdu', value: 'urdu' },
  { label: 'German', value: 'german' },
  { label: 'Haitian Creole', value: 'haitian_creole' },
  { label: 'Polish', value: 'polish' },
  { label: 'Italian', value: 'italian' },
  { label: 'Japanese', value: 'japanese' },
  { label: 'Persian (Farsi)', value: 'persian_farsi' },
  { label: 'Gujarati', value: 'gujarati' },
  { label: 'Other', value: 'other' },
];

export enum Language {
  english = 'english',
  spanish = 'spanish',
  chinese_mandarin = 'chinese_mandarin',
  vietnamese = 'vietnamese',
  tagalog = 'tagalog',
  arabic = 'arabic',
  korean = 'korean',
  russian = 'russian',
  french = 'french',
  hindi = 'hindi',
  portuguese = 'portuguese',
  bengali = 'bengali',
  urdu = 'urdu',
  german = 'german',
  haitian_creole = 'haitian_creole',
  polish = 'polish',
  italian = 'italian',
  japanese = 'japanese',
  persian_farsi = 'persian_farsi',
  gujarati = 'gujarati',
  other = 'other',
}
