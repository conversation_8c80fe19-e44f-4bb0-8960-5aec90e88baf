import { FileArchiveIcon, FileIcon, FileTextIcon, ImageIcon } from 'lucide-react';

/**
 * Returns an appropriate icon component based on the file extension
 */
export const getFileIcon = (filename: string | null) => {
  if (!filename) return <FileIcon className='h-5 w-5 text-gray-500' />;

  const extension = filename.split('.').pop()?.toLowerCase();

  switch (extension) {
    case 'pdf':
      return <FileTextIcon className='h-6 w-6 text-red-600' />;
    case 'doc':
    case 'docx':
      return <FileTextIcon className='h-6 w-6 text-blue-600' />;
    case 'xls':
    case 'xlsx':
      return <FileTextIcon className='h-6 w-6 text-green-600' />;
    case 'ppt':
    case 'pptx':
      return <FileTextIcon className='h-6 w-6 text-orange-600' />;
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return <ImageIcon className='h-6 w-6 text-purple-600' />;
    case 'zip':
    case 'rar':
      return <FileArchiveIcon className='h-6 w-6 text-amber-600' />;
    case 'txt':
      return <FileTextIcon className='h-6 w-6 text-gray-600' />;
    default:
      return <FileIcon className='h-6 w-6 text-gray-600' />;
  }
};
