import Cookies from 'js-cookie';
import { NextRequest } from 'next/server';

export const COOKIE_KEYS = {
  ACCESS_TOKEN: 'ema_accessToken',
};

export const storeAccessToken = (accessToken: string) => {
  Cookies.set(COOKIE_KEYS.ACCESS_TOKEN, accessToken, {
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
  });
};

export const retrieveAccessToken = (request?: NextRequest) => {
  if (request) {
    return request.cookies.get(COOKIE_KEYS.ACCESS_TOKEN)?.value;
  }

  return Cookies.get(COOKIE_KEYS.ACCESS_TOKEN);
};

export const removeAccessToken = () => {
  Cookies.remove(COOKIE_KEYS.ACCESS_TOKEN);
};
