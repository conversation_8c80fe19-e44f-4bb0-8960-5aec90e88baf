import { FieldValues } from 'react-hook-form';

export const updateUserRequest = (data: FieldValues) => {
  const { languages_c, ...rest } = data as {
    languages_c?: Array<{ value: string }> | null;
    [key: string]: unknown;
  };

  // Only add keys if the property is not null or undefined
  const updateData: Record<string, unknown> = {};
  Object.entries(rest).forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
      updateData[key] = value;
    }
  });

  // format languages correctly
  if (languages_c && languages_c?.length > 0) {
    updateData.languages_c = { set: languages_c.map((lang) => lang.value) };
  }

  // format the dob correctly
  if (updateData?.date_of_birth) {
    updateData.date_of_birth = new Date(updateData.date_of_birth as string);
  }

  return updateData;
};
