import { GetReferralFormDataResponse } from '@/types/schemas/requests-responses/get-referral-form-data';
import { YupSchemas } from '@suiteapi/models';

export const getAffiliatesSelfReferral = async (zip: string, within?: number): Promise<GetReferralFormDataResponse> => {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/v1/affiliate?zip=${zip}${within ? `&within=${within}` : ''}`,
  );
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Error fetching affiliates for self-referral form');
  }

  const locations = (await response.json()) as GetReferralFormDataResponse;
  return locations;
};

export const getAffiliatesCommunityReferral = async (
  zip: string,
  within?: number,
): Promise<GetReferralFormDataResponse> => {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/v1/affiliate?zip=${zip}${within ? `&within=${within}` : ''}`,
  );
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Error fetching affiliates for community referral form');
  }

  const locations = (await response.json()) as GetReferralFormDataResponse;
  return locations;
};

export const postReferral = async (
  data: YupSchemas.CreateSelfReferralRequest | YupSchemas.CreateCommunityReferralRequest,
  referralType: 'self' | 'community',
): Promise<void> => {
  const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/${referralType}-referral`, {
    method: 'POST',
    body: JSON.stringify(data),
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error('Error submitting referral form');
  }
};
