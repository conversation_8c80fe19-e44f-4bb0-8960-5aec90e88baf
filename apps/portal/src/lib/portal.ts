import { CreateSessionReportSchema } from '@/types/schemas/create-session-report';
import { ProjectTaskSchema } from '@/types/schemas/db-project-task';
import { ActionItem, MomActionPlanGoal } from '@/types/schemas/mom-action-plan';
import { PairingResponseSchema, PairingSchema } from '@/types/schemas/pairing';
import { SessionSchema } from '@/types/schemas/session';
import { StartSessionSchema } from '@/types/schemas/start-session';
import { User } from '@/types/schemas/user';
import { YupSchemas } from '@suiteapi/models';

import { getFindFirst, getFindMany, getFindUnique, sfetch } from './sfetch';

// TODO: implement language setting logic
const LANG_CODE = 'en_us';

interface Advocate {
  languages_c: string[];
  [key: string]: string | string[] | number | boolean | null | undefined;
}

export const getMoms = async (): Promise<YupSchemas.MomType[]> => {
  const moms = await getFindMany<YupSchemas.MomType>('mom');

  return moms;
};

export const getMomById = async (id: string): Promise<YupSchemas.MomType> => {
  const mom = await getFindUnique<YupSchemas.MomType>('mom', {
    where: {
      id,
    },
  });

  return mom;
};

export const getActivePairings = async (): Promise<PairingResponseSchema[]> => {
  const pairings = await getFindMany<PairingResponseSchema>('pairing', {
    where: {
      status: YupSchemas.PairingStatusType.PAIRED,
    },
    include: {
      mom: true,
      track: true,
    },
  });

  return pairings;
};

export const getActivePairingByMomId = async (id: string): Promise<PairingSchema | null> => {
  const pairing = await getFindFirst<PairingSchema>('pairing', {
    include: {
      track: true,
      advocateUser: true,
      lessons: true,
    },
    where: {
      momId: id,
      status: 'paired',
    },
  });

  return pairing;
};

export const getActionPlanByMomId = async (id: string): Promise<MomActionPlanGoal[]> => {
  let goals = await getFindMany<MomActionPlanGoal>('goal', {
    include: {
      actionItems: true,
    },
    where: {
      momId: id,
    },
    orderBy: {
      created_at: 'asc',
    },
  });

  goals = goals.map((goal) => ({
    ...goal,
    completed: goal.status === 'completed',
    actionItems: goal.actionItems.map((actionItem: ActionItem) => ({
      ...actionItem,
      completed: actionItem.status === 'completed',
    })),
  }));

  return goals;
};

export const getBenevolenceByMomId = async (id: string): Promise<YupSchemas.BenevolenceSchema[]> => {
  const benevolenceNeeds = await getFindMany<YupSchemas.BenevolenceSchema>('benevolenceNeed', {
    where: {
      momId: id,
    },
  });

  return benevolenceNeeds;
};

export const getSessionNotesListWithActivePairingByMomId = async (id: string): Promise<SessionSchema[]> => {
  const sessions = await getFindMany<SessionSchema>('session', {
    where: {
      pairing: {
        momId: id,
        status: YupSchemas.PairingStatusType.PAIRED,
      },
    },
    orderBy: {
      date_start: 'desc',
    },
  });

  return sessions;
};

export const getSessionNotesListByMomId = async (id: string): Promise<SessionSchema[]> => {
  const sessions = await getFindMany<SessionSchema>('session', {
    where: {
      OR: [{ pairing: { momId: id } }, { mom_id: id }],
    },
    orderBy: {
      date_start: 'desc',
    },
  });

  return sessions;
};

export const fetchSessionReportData = async (
  sessionId: string,
): Promise<{ report: CreateSessionReportSchema; session: SessionSchema; lesson: ProjectTaskSchema }> => {
  const session = await getFindUnique<
    SessionSchema & {
      session_note: CreateSessionReportSchema & {
        covered_lesson: ProjectTaskSchema;
      };
      pairing: {
        mom: YupSchemas.MomType;
      };
    }
  >('session', {
    where: {
      id: sessionId,
    },
    include: {
      session_note: {
        include: {
          covered_lesson: true,
        },
      },
      pairing: {
        include: {
          mom: true,
        },
      },
    },
  });

  return {
    report: session.session_note,
    session: session,
    lesson: session.session_note.covered_lesson,
  } as {
    report: CreateSessionReportSchema;
    session: SessionSchema;
    lesson: ProjectTaskSchema;
  };
};

export const sendTextMessageToContacts = async (_data?: Record<string, unknown>): Promise<void> => {
  /*
   * TODO: implement text message API

  const response = await fetch(`/api/${LANG_CODE}/text-message`, {
    method: 'POST',
    body: JSON.stringify(data),
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error('Error sending text message');
  }
  */

  return new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, 1000);
  });
};

export const getAdvocates = async () => {
  const response = await fetch('/api/advocates');
  if (!response.ok) {
    throw new Error('Failed to fetch advocates');
  }
  const advocates = (await response.json()) as Advocate[];

  return advocates.map((advocate: Advocate) => ({
    ...advocate,
    languages_c: Array.isArray(advocate.languages_c)
      ? advocate.languages_c.map((lang: string) => ({
          label: lang,
          value: lang,
        }))
      : [],
  }));
};

export const updateAdvocateStatus = async (id: string, status: string): Promise<void> => {
  const response = await fetch(`/api/${LANG_CODE}/advocate`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      id: id,
      advocate_status: status,
    }),
  });

  if (!response.ok) {
    throw new Error('Failed to update advocate status');
  }
};

export const getReferralDocuments = async (id: string): Promise<YupSchemas.DocumentSchema[] | undefined> => {
  try {
    const response = await sfetch(`/v1/mom/${id}/documents`);
    if (!response.ok) {
      throw new Error('Error fetching referral document');
    }

    return response.json();
  } catch (error) {
    console.error('Error fetching referral document:', error);
  }
};

export const generateDocumentUrl = (id: string): string => {
  if (!id) {
    throw new Error('Document ID is required to generate the URL');
  }

  return `${process.env.NEXT_PUBLIC_API_URL}/v1/document/${id}`;
};

export const downloadDocument = async (documentId: string, filename: string = 'document'): Promise<void> => {
  try {
    if (!documentId) {
      throw new Error('Document ID is required');
    }

    // Download the actual document
    const url = generateDocumentUrl(documentId);
    if (!url || typeof url !== 'string') {
      throw new Error('Invalid document URL');
    }

    const response = await sfetch(url);
    if (!response.ok) {
      throw new Error('Failed to download file');
    }

    // Create and trigger download with the original filename
    const blob = await response.blob();
    const downloadUrl = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = downloadUrl;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(downloadUrl);
    document.body.removeChild(a);
  } catch (error) {
    console.error('Error downloading document:', error);
    throw error;
  }
};

export const createReferralMeeting = async (data: StartSessionSchema) => {
  try {
    const response = await fetch(`/api/${LANG_CODE}/referral-session`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
    return response.json();
  } catch (error) {
    console.error('Error creating referral meeting:', error);
  }
};

export const getTrackLessonTemplates = async (trackId: string): Promise<YupSchemas.LessonTemplateSchema[]> => {
  const lessonTemplates = await getFindMany<YupSchemas.LessonTemplateSchema>('LessonTemplate', {
    where: {
      track_id: trackId,
    },
    include: {
      documents: true,
    },
  });

  return lessonTemplates;
};

export const uploadMomPhoto = async (momId: string, file: File): Promise<YupSchemas.MomType> => {
  return uploadEntityPhoto('mom', momId, file);
};

export const removeMomPhoto = async (momId: string): Promise<YupSchemas.MomType> => {
  return removeEntityPhoto('mom', momId);
};

export const uploadUserPhoto = async (userId: string, file: File): Promise<User> => {
  return uploadEntityPhoto('user', userId, file);
};

export const removeUserPhoto = async (userId: string): Promise<User> => {
  return removeEntityPhoto('user', userId);
};

/**
 * Optimizes an image for upload by resizing it to maintain quality while keeping a reasonable file size
 * @param file The original image file
 * @param maxWidth Maximum width for the optimized image
 * @param maxHeight Maximum height for the optimized image
 * @param quality JPEG quality (0-1)
 * @returns A promise resolving to the optimized image file
 */
async function optimizeImage(file: File, maxWidth = 1200, maxHeight = 1200, quality = 0.85): Promise<File> {
  return new Promise((resolve, reject) => {
    // Create an image element to load the file
    const img = new Image();
    img.onload = () => {
      // Calculate new dimensions while maintaining aspect ratio
      let width = img.width;
      let height = img.height;

      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width = Math.floor(width * ratio);
        height = Math.floor(height * ratio);
      }

      // Create a canvas element to resize the image
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;

      // Draw the image on the canvas
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        reject(new Error('Could not get canvas context'));
        return;
      }

      ctx.drawImage(img, 0, 0, width, height);

      // Convert canvas to blob
      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error('Failed to create image blob'));
            return;
          }
          // Create a new file from the blob
          const optimizedFile = new File([blob], file.name, {
            type: 'image/jpeg',
            lastModified: Date.now(),
          });
          resolve(optimizedFile);
        },
        'image/jpeg',
        quality,
      );
    };

    img.onerror = () => {
      reject(new Error('Failed to load image for optimization'));
    };

    // Load image from file
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Generic function to upload a photo for any entity type
 * @param entityType The type of entity (e.g., 'mom', 'user')
 * @param entityId The ID of the entity
 * @param file The file to upload
 * @returns Promise resolving to the updated entity
 */
async function uploadEntityPhoto<T>(entityType: string, entityId: string, file: File): Promise<T> {
  const reader = new FileReader();

  // Optimize the image before uploading
  let fileToUpload: File;
  try {
    fileToUpload = await optimizeImage(file);
  } catch (error) {
    console.warn('Image optimization failed, using original file:', error);
    fileToUpload = file;
  }

  return new Promise<T>((resolve, reject) => {
    reader.onload = async () => {
      try {
        const base64Photo = reader.result as string;
        const response = await sfetch(`/v1/${entityType}/${entityId}/photo`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            photo: base64Photo,
            contentType: fileToUpload.type,
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to upload ${entityType} photo`);
        }

        const updatedEntity = await response.json();

        // Ensure we have consistent camelCase properties for photo URLs
        if (!updatedEntity.photoUrl && updatedEntity.photo_url) {
          updatedEntity.photoUrl = updatedEntity.photo_url;
        }

        if (!updatedEntity.photoS3FileName && updatedEntity.photo_s3_file_name) {
          updatedEntity.photoS3FileName = updatedEntity.photo_s3_file_name;
        }

        // Ensure consistent property names for thumbnails as well
        if (!updatedEntity.thumbnailUrl && updatedEntity.thumbnail_url) {
          updatedEntity.thumbnailUrl = updatedEntity.thumbnail_url;
        }

        if (!updatedEntity.thumbnailS3FileName && updatedEntity.thumbnail_s3_file_name) {
          updatedEntity.thumbnailS3FileName = updatedEntity.thumbnail_s3_file_name;
        }

        resolve(updatedEntity);
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };

    reader.readAsDataURL(fileToUpload);
  });
}

/**
 * Generic function to remove a photo from any entity type
 * @param entityType The type of entity (e.g., 'mom', 'user')
 * @param entityId The ID of the entity
 * @returns Promise resolving to the updated entity
 */
async function removeEntityPhoto<T>(entityType: string, entityId: string): Promise<T> {
  const response = await sfetch(`/v1/${entityType}/${entityId}/photo`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    throw new Error(`Failed to remove ${entityType} photo`);
  }

  const updatedEntity = await response.json();
  return updatedEntity;
}
