import { retrieveAccessToken, storeAccessToken } from './accessTokenStorageService';

/*
  sfetch - stands for super-fetch. it's like fetch, but better.
  It's a wrapper around fetch that adds the following functionality:
    1. Automatically uses the base url for our api server. You can override this by providing a full url (starting with http:, https:, ws:, or wss:) as the url parameter.
    2. Automatically grabs the access token and uses it as a bearer token in the Authorization header. This can be overridden by providing your own Authorization header in the sfetch options object.
    3. In the event of a 401 unauthorized response, sfetch will attempt to refresh the access token and try the request again.
*/

// Add this outside the sfetch function to maintain a single refresh promise
let refreshPromise: Promise<string | null> | null = null;

// Helper function to handle token refresh
async function refreshToken(): Promise<string | null> {
  const refreshResp = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/auth/refresh`, {
    method: 'GET',
    credentials: 'include',
  });

  if (refreshResp?.ok) {
    const { access_token } = await refreshResp.json();
    return access_token;
  }
  return null;
}

export async function sfetch(
  url: string,
  optionsPlusSetter?: (RequestInit & { setAccessToken?: (token: string) => void }) | undefined,
) {
  // Prepend the base url if the provided url doesn't start with http:, https:, ws: or wss:
  if (!url.startsWith('http:') && !url.startsWith('https:') && !url.startsWith('ws:') && !url.startsWith('wss:')) {
    url = `${process.env.NEXT_PUBLIC_API_URL}${url.startsWith('/') ? '' : '/'}${url}`;
  }

  // Try to grab access token from cookie.
  let accessToken = null;
  if (typeof window !== 'undefined') {
    accessToken = retrieveAccessToken();
  }

  // Pull setAccessToken out of options if present.
  const optionsWithoutSetter = optionsPlusSetter ?? {};
  const { setAccessToken, ...options } = optionsWithoutSetter;

  // Enhance options with auth token.
  const enhancedOptions = {
    ...options,
    headers: {
      ...(accessToken ? { Authorization: `Bearer ${accessToken}` } : {}),
      ...options.headers,
    },
  };

  const resp = await fetch(url, enhancedOptions);
  if (!resp.ok && resp.status === 401) {
    // Start or join an existing token refresh
    refreshPromise = refreshPromise || refreshToken();

    const newToken = await refreshPromise;
    // Clear the refresh promise for future refresh attempts
    refreshPromise = null;

    if (newToken) {
      if (setAccessToken) {
        setAccessToken(newToken);
      } else {
        storeAccessToken(newToken);
      }

      // Enhance options with new auth token
      const newEnhancedOptions = {
        ...enhancedOptions,
        headers: {
          ...enhancedOptions.headers,
          ...(newToken ? { Authorization: `Bearer ${newToken}` } : {}),
        },
      };

      return await fetch(url, newEnhancedOptions);
    }
  }

  return resp;
}

export async function getFindMany<T>(modelName: string, options?: unknown): Promise<T[]> {
  const finalUrl = options
    ? `/v1/zen/${modelName}/findMany?q=${encodeURIComponent(JSON.stringify(options))}`
    : `/v1/zen/${modelName}/findMany`;
  const resp = await sfetch(finalUrl);
  const data = await resp.json();
  return data.data as T[];
}

export async function getFindUnique<T>(modelName: string, options?: unknown): Promise<T> {
  const finalUrl = options
    ? `/v1/zen/${modelName}/findUnique?q=${encodeURIComponent(JSON.stringify(options))}`
    : `/v1/zen/${modelName}/findUnique`;
  const resp = await sfetch(finalUrl);
  const data = await resp.json();
  return data.data as T;
}

export async function getFindFirst<T>(modelName: string, options?: unknown): Promise<T> {
  const finalUrl = options
    ? `/v1/zen/${modelName}/findFirst?q=${encodeURIComponent(JSON.stringify(options))}`
    : `/v1/zen/${modelName}/findFirst`;
  const resp = await sfetch(finalUrl);
  const data = await resp.json();
  return data.data as T;
}
