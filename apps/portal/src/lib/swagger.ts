'use server';

import { createSwaggerSpec } from 'next-swagger-doc';

import { AUTH_COOKIE_NAME } from './constants';

export const getApiDocs = (): Record<string, unknown> => {
  const spec = createSwaggerSpec({
    apiFolder: 'src/app/api',
    definition: {
      openapi: '3.0.0',
      info: {
        title: 'EMA API',
        version: '1.0',
      },
      components: {
        securitySchemes: {
          CookieAuth: {
            type: 'apiKey',
            in: 'cookie',
            name: AUTH_COOKIE_NAME,
          },
        },
      },
      security: [],
    },
  });
  return spec as Record<string, unknown>;
};
