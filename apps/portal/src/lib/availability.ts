import { type DayAvailability, type DayOfWeek, type TimeSlot } from '@/types/availability';

export function serializeAvailability(availability: DayAvailability): string {
  const slots: string[] = [];

  Object.entries(availability).forEach(([day, timeSlots]) => {
    timeSlots.forEach((timeSlot) => {
      slots.push(`${day}-${timeSlot}`);
    });
  });

  return slots.join(',');
}

export function deserializeAvailability(availabilityString: string): DayAvailability {
  const availability: DayAvailability = {};

  if (!availabilityString) {
    return availability;
  }

  availabilityString.split(',').forEach((slot) => {
    const [day, timeSlot] = slot.split('-') as [DayOfWeek, TimeSlot];
    if (!availability[day]) {
      availability[day] = [];
    }
    availability[day].push(timeSlot);
  });

  return availability;
}
