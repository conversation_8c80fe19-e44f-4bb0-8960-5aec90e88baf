import { type FieldConfig, type FieldType, type FormFieldValue, type FormValues } from '@/types/form-field';
import { dateToDateTimeArray } from '@suiteapi/models';
import { type ClassValue, clsx } from 'clsx';
import { differenceInDays, format, isToday, isTomorrow, isYesterday, parse } from 'date-fns';
import { type EventAttributes, createEvent } from 'ics';
import omitBy from 'lodash/omitBy';
import { twMerge } from 'tailwind-merge';
import { z } from 'zod';

/**
 * Merges Tailwind CSS classes using clsx and tailwind-merge
 * @param inputs Class values to merge
 * @returns Merged class string with conflicts resolved
 */
export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs));
}

/**
 * Format a date for display with a customizable format string
 * @param date The date to format
 * @param formatString The format string to use (defaults to MM/dd/yyyy)
 * @returns The formatted date string
 */
export const formatDateForDisplay = (date: Date, formatString: string = 'MM/dd/yyyy'): string => {
  return format(date, formatString);
};

/**
 * Extracts field names from a FieldConfig array
 * @param fieldConfig Array of field configurations
 * @returns Array of field names, including any nested in row-groups
 */
export const getFieldKeysFromFieldConfig = (fieldConfig: FieldConfig[]): string[] => {
  return fieldConfig
    .map((field) => {
      if (field.type === 'row-group' && field.subFields) {
        return field.subFields.map((subField) => subField.name);
      }
      return field.name;
    })
    .flat();
};

/**
 * Converts a JSON schema to an array of form field configurations
 * @param schema JSON schema definition containing properties and UI props
 * @returns Array of field configurations for rendering form fields
 */
export const mapSchemaToFormFields = (schema: z.ZodObject<z.ZodRawShape>): FieldConfig[] => {
  return Object.keys(schema.shape).map((key) => {
    const field = schema.shape[key] as z.ZodTypeAny;
    const config: FieldConfig = {
      name: key,
      label: field?.description || key,
      type: field?._def?.typeName as FieldType,
      placeholder: field?._def?.placeholder,
      description: field?._def?.description,
    };

    if (
      config.type === 'radio' ||
      config.type === 'button-radio-group' ||
      config.type === 'checkbox-group' ||
      config.type === 'select'
    ) {
      config.options = field?._def?.options;
    }

    if (config.type === 'checkbox') {
      config.checkboxLabel = field?._def?.checkboxLabel;
    }

    return config;
  });
};

/**
 * Extracts validation error messages from a schema
 * @param schema JSON schema containing error message definitions
 * @returns Object with field names as keys and error messages as values
 */
export const getValidationErrorMessageConfig = (schema: z.ZodObject<z.ZodRawShape>) => {
  const errMessages = Object.keys(schema.shape).reduce<Record<string, string>>((acc, key: string) => {
    const field = schema.shape[key];
    if (field?.description) {
      acc[key] = field.description;
    }
    return acc;
  }, {});

  return { errMessages };
};

/**
 * Flattens nested field configurations into a single-level array
 * @param fields Array of field configurations, potentially with nested row-groups
 * @returns Flattened array of all field configurations
 */
export const flattenFormFields = (fields: FieldConfig[]): FieldConfig[] => {
  return fields.reduce((acc: FieldConfig[], field) => {
    if (field.type === 'row-group') {
      acc.push(...flattenFormFields(field.subFields ?? []));
    } else {
      acc.push(field);
    }
    return acc;
  }, []);
};

/**
 * Gets default values for form fields, either from stored data or field definitions
 * @param fields Array of field configurations
 * @param storeData Previously stored form values
 * @returns Object with default values for all fields
 */
export const getDefaultFormValues = (fields: FieldConfig[], storeData: FormValues): Record<string, FormFieldValue> =>
  fields.reduce((acc: Record<string, FormFieldValue>, field) => {
    if (storeData[field.name] !== undefined) acc[field.name] = storeData[field.name];
    else if (field.defaultValue !== undefined) acc[field.name] = field.defaultValue;
    else
      acc[field.name] =
        field.type === 'number' ||
        field.type === 'radio' ||
        field.type === 'button-radio-group' ||
        field.type === 'checkbox-group' ||
        field.type === 'select' ||
        field.type === 'date'
          ? undefined
          : field.type === 'checkbox'
            ? false
            : '';
    return acc;
  }, {});

interface ZipData {
  place_name: string;
  state_abbr: string;
  zip: string;
}

/**
 * Fetches location data for a US postal code
 * @param zip US postal code to look up
 * @returns Promise resolving to an array of location data for the zip code
 * @throws Error if the API request fails
 */
export const getZipData = async (zip: string): Promise<ZipData[]> => {
  const response = await fetch(`/api/postal-code/${zip}`);
  if (!response.ok) {
    throw new Error('Error fetching zip code data');
  }

  const placeData = (await response.json()) as ZipData[];
  return placeData;
};

// *Provisionally* functional time formatter - TODO: more robust date/time formatting
/**
 * Formats a session date and time range into a readable string
 * @param date The session date as a string
 * @param startTime The start time in 24-hour format (HH:mm)
 * @param endTime The end time in 24-hour format (HH:mm)
 * @returns Formatted date and time range string
 */
export const formatSessionDateTime = (date: string, startTime: string, endTime: string): string => {
  const sessionDate = new Date(date);

  // Parse start/end times using current date (just for formatting)
  const startDateTime = parse(startTime, 'HH:mm', sessionDate);
  const endDateTime = parse(endTime, 'HH:mm', sessionDate);

  // Format the actual session date
  const formattedDate = format(sessionDate, 'MMMM d, yyyy');

  // Format the start and end times
  const formattedStartTime = format(startDateTime, 'h:mm');
  const formattedEndTime = format(endDateTime, 'h:mm aa');

  // TODO: accurately manage timezone display based on SuiteCRM data + user's timezone

  // Return the formatted string
  return `${formattedDate}, ${formattedStartTime}-${formattedEndTime}`;
};

/**
 * Date util to show relative time from now if the date is within 3 days of today (or configured threshold)
 * Otherwise, show the full date.
 * For relative dates, the time will be omitted (e.g. "today", "tomorrow", "yesterday", "in 3 days", "3 days ago").
 *
 * @param targetDate - The target date as a string (ISO format).
 * @param thresholdDays - The threshold for showing a relative date string.
 * @param prefixString - A string to prefix the relative date with (e.g. 'due')
 * @returns formatted date string
 */
export const formatRelativeDate = (
  targetDate: string,
  prefixString: string = '',
  thresholdDays: number = 3,
): string => {
  const date = new Date(targetDate);
  const now = new Date();

  const daysDifference = differenceInDays(date, now);
  const absDaysDifference = Math.abs(daysDifference);

  if (absDaysDifference <= thresholdDays) {
    if (isToday(date)) {
      return `${prefixString} today`;
    } else if (isTomorrow(date)) {
      return `${prefixString} tomorrow`;
    } else if (isYesterday(date)) {
      return `${prefixString} yesterday`;
    } else if (daysDifference > 0) {
      return `${prefixString} in ${daysDifference} day${daysDifference > 1 ? 's' : ''}`;
    } else {
      return `${prefixString} ${absDaysDifference} day${absDaysDifference > 1 ? 's' : ''} ago`;
    }
  }

  return format(date, 'MM/dd/yyyy');
};

/**
 * Calculate the progress percentage for a progress bar
 * Allows for a custom start and end percentage to be set,
 * to support "padded" start/end progress bar view
 * @param currentStep The current step number (1-indexed)
 * @param numSteps The total number of steps in the process
 * @param startPercent The percentage progress shown on step 1
 * @param endPercent The percentage progress shown on the final step
 * @returns The calculated progress percentage
 * @example
 * calculateStepProgress(1, 4) // 10
 * calculateStepProgress(2, 4) // 37
 * calculateStepProgress(3, 4) // 63
 * calculateStepProgress(4, 4) // 90
 */
export const calculateStepProgress = (
  currentStep: number,
  numSteps: number,
  startPercent: number = 10,
  endPercent: number = 90,
): number => {
  if (numSteps <= 1 || currentStep < 1 || currentStep > numSteps) {
    return 0;
  }

  const stepIncrement = (endPercent - startPercent) / (numSteps - 1);
  const progress = startPercent + stepIncrement * (currentStep - 1);
  return Math.round(progress);
};

/**
 * Decodes HTML entities in a string
 * @param str String potentially containing HTML entities
 * @returns Decoded string with HTML entities converted to characters
 */
export const decodeHtmlEntities = (str: string | undefined | null): string => {
  if (!str) return '';
  const doc = new DOMParser().parseFromString(str, 'text/html');
  return doc.documentElement.textContent || '';
};

/**
 * Converts underscore-case strings to capitalized, space-separated words
 * @param string Underscore-case string to convert
 * @returns Converted string with each word capitalized and separated by spaces
 * @example
 * convertUnderscoreCase('false_start') // 'False Start'
 */
export const convertUnderscoreCase = (string: string | undefined): string => {
  if (!string) return '';

  return string
    .toLowerCase()
    .split('_')
    .map((s) => s.charAt(0).toUpperCase() + s.slice(1))
    .join(' ');
};

/**
 * Converts SuiteCRM checkbox values to Yes/No labels
 * @param value SuiteCRM checkbox value ('1' or '0')
 * @returns 'Yes' for '1', 'No' for any other value
 */
export const checkboxValueToYesNoLabel = (value?: '1' | '0'): string => (value === '1' ? 'Yes' : 'No');

/**
 * Converts boolean values to Yes/No labels
 * @param value Boolean value to convert
 * @returns 'Yes' for true, 'No' for false
 */
export const trueFalseValueToYesNoLabel = (value?: boolean): string => (value ? 'Yes' : 'No');

/**
 * Generates calendar event data for use with the iCalendar format
 * @param start Start date and time of the event
 * @param end End date and time of the event
 * @param otherData Additional event data (title, description, location, url)
 * @returns Event attributes object conforming to the iCalendar spec
 */
export const generateCalendarEventMappings = (start: Date, end: Date, otherData: unknown): EventAttributes => {
  const { title, description, location, url } = otherData as Record<string, string>;

  const event = {
    start: dateToDateTimeArray(start),
    end: dateToDateTimeArray(end),
    title,
    description,
    location,
    url,
  };

  return omitBy(event, (value) => value === undefined) as unknown as EventAttributes;
};

/**
 * Generates an iCalendar (.ics) file from event data
 * @param fileName Base name for the generated file (without extension)
 * @param event Event attributes conforming to the iCalendar spec
 * @returns Promise resolving to a Blob URL for the generated .ics file
 */
export const generateCalendarEventFile = async (fileName: string, event: EventAttributes): Promise<string> => {
  const file: File = await new Promise((resolve, reject) => {
    createEvent(event, (error, value) => {
      if (error) {
        reject(error);
      }

      resolve(new File([value], `${fileName}.ics`, { type: 'text/calendar' }));
    });
  });

  return URL.createObjectURL(file);
};

/**
 * Gets the public-facing website URL from environment variables
 * @returns Website URL from environment or fallback to localhost
 */
export const getWebsiteUrl = (): string => {
  return process.env.NEXT_PUBLIC_WEBSITE_URL || 'http://localhost:3000';
};

/**
 * Formats a phone number string into a standardized format
 * @param phoneNumber Raw phone number string to format
 * @returns Formatted phone number with appropriate punctuation
 * @example
 * formatPhoneNumber('1234567890') // '(*************'
 * formatPhoneNumber('+11234567890') // '+1 (*************'
 */
export const formatPhoneNumber = (phoneNumber: string | null | undefined): string => {
  if (!phoneNumber) return '';

  // Remove all non-digit characters except plus sign
  const cleaned = phoneNumber.replace(/[^\d+]/g, '');

  // Handle numbers with country code (+1)
  if (cleaned.startsWith('+1')) {
    const match = cleaned.slice(2).match(/^(\d{3})(\d{3})(\d{4})/);
    if (match) {
      return '+1 (' + match[1] + ') ' + match[2] + '-' + match[3];
    }
  }

  // Handle 10-digit phone numbers
  const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
  if (match) {
    return '(' + match[1] + ') ' + match[2] + '-' + match[3];
  }

  // Handle 7-digit phone numbers
  const sevenDigitMatch = cleaned.match(/^(\d{3})(\d{4})$/);
  if (sevenDigitMatch) {
    return sevenDigitMatch[1] + '-' + sevenDigitMatch[2];
  }

  // Return original string if it doesn't match expected formats
  return phoneNumber;
};

/**
 * Formats a time string into a localized time format
 * @param time - Time string in "HH:mm" format
 * @returns Formatted time string in the format "H:MM AM/PM"
 * @example
 * formatTime("14:30") // "2:30 PM"
 */
export function formatTime(time: string): string {
  const [hours, minutes] = time.split(':');
  const date = new Date();
  date.setHours(parseInt(hours, 10));
  date.setMinutes(parseInt(minutes, 10));
  return date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' });
}

/**
 * Generates a Google Calendar link for an event
 * @param date - Event date in YYYY-MM-DD format
 * @param startTime - Start time in HH:mm format
 * @param endTime - End time in HH:mm format
 * @param meetingType - Type of meeting ("Virtual" or "In-person")
 * @param location - Meeting location or virtual meeting link
 * @param title - Title of the event (defaults to "Meeting Invitation")
 * @returns URL for adding the event to Google Calendar
 * @example
 * getCalendarLink(
 *   "2024-03-20",
 *   "14:30",
 *   "15:30",
 *   "Virtual",
 *   "https://meet.google.com/abc-def-ghi",
 *   "Interview with John Doe"
 * )
 */
export function getCalendarLink(
  date: string,
  startTime: string,
  endTime: string,
  meetingType: string,
  location: string,
  title: string = 'Meeting Invitation',
): string {
  try {
    // Validate date format
    if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      throw new Error('Invalid date format. Expected YYYY-MM-DD');
    }

    // Validate time formats
    if (!/^\d{2}:\d{2}$/.test(startTime) || !/^\d{2}:\d{2}$/.test(endTime)) {
      throw new Error('Invalid time format. Expected HH:mm');
    }

    // Parse the date and time components
    const [year, month, day] = date.split('-').map(Number);
    const [startHours, startMinutes] = startTime.split(':').map(Number);
    const [endHours, endMinutes] = endTime.split(':').map(Number);

    // Validate numeric values
    if (
      isNaN(year) ||
      isNaN(month) ||
      isNaN(day) ||
      isNaN(startHours) ||
      isNaN(startMinutes) ||
      isNaN(endHours) ||
      isNaN(endMinutes)
    ) {
      throw new Error('Invalid date or time values');
    }

    // Validate time ranges
    if (startHours < 0 || startHours > 23 || startMinutes < 0 || startMinutes > 59) {
      throw new Error('Invalid start time');
    }
    if (endHours < 0 || endHours > 23 || endMinutes < 0 || endMinutes > 59) {
      throw new Error('Invalid end time');
    }

    // Create Date objects with the correct timezone
    const startDate = new Date(Date.UTC(year, month - 1, day, startHours, startMinutes));
    const endDate = new Date(Date.UTC(year, month - 1, day, endHours, endMinutes));

    // Validate the dates
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      throw new Error('Invalid date');
    }

    // Create the calendar event
    const event = {
      start: [year, month, day, startHours, startMinutes] as [number, number, number, number, number],
      end: [year, month, day, endHours, endMinutes] as [number, number, number, number, number],
      title: title,
      description: `${title} ${meetingType === 'Virtual' ? `\nMeeting Link: ${location}` : `\nLocation: ${location}`}`,
      location: location,
      status: 'CONFIRMED' as const,
      busyStatus: 'BUSY' as const,
      organizer: { name: 'ĒMA', email: '<EMAIL>' },
    };

    // Generate the ICS file content
    const icsContent = [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'PRODID:-//ĒMA//NONSGML v1.0//EN',
      'BEGIN:VEVENT',
      `DTSTART:${formatDateForICS(startDate)}`,
      `DTEND:${formatDateForICS(endDate)}`,
      `SUMMARY:${title}`,
      `DESCRIPTION:${event.description.replace(/\n/g, '\\n')}`,
      `LOCATION:${location}`,
      'STATUS:CONFIRMED',
      'BEGIN:VALARM',
      'TRIGGER:-PT15M',
      'ACTION:DISPLAY',
      'DESCRIPTION:Reminder',
      'END:VALARM',
      'END:VEVENT',
      'END:VCALENDAR',
    ].join('\r\n');

    // Create a blob URL for the ICS file
    const blob = new Blob([icsContent], { type: 'text/calendar;charset=utf-8' });
    return URL.createObjectURL(blob);
  } catch (error) {
    console.error('Error generating calendar link:', error);
    return '#';
  }
}

function formatDateForICS(date: Date): string {
  return date.toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');
}
