'use client';

import { useToast } from '@/hooks/use-toast';
import { sfetch } from '@/lib/sfetch';
import { useQueryClient } from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

/**
 * Props for the usePhotoUpload hook
 */
interface UsePhotoUploadProps<TUploadResult = unknown, TRemoveResult = unknown> {
  /**
   * The ID of the entity (user, mom, etc.) that owns the photo
   */
  id: string | undefined;

  /**
   * The current photo URL of the entity, if any
   */
  photoUrl: string | undefined;

  /**
   * Function to call when uploading a new photo
   * @param id - The entity ID
   * @param file - The file to upload
   */
  uploadFunction: (id: string, file: File) => Promise<TUploadResult>;

  /**
   * Function to call when removing an existing photo
   * @param id - The entity ID
   */
  removeFunction: (id: string) => Promise<TRemoveResult>;

  /**
   * Base query key for React Query cache management
   */
  queryKey: string;

  /**
   * Additional query keys to invalidate when the photo changes
   */
  additionalInvalidations?: string[];

  /**
   * Callback function called after successful upload or removal
   * @param data - The result from the API call
   */
  onSuccess?: (data?: TUploadResult | TRemoveResult) => void;

  /**
   * Callback for when a photo is uploaded with local preview
   * @param previewUrl - The local preview URL of the uploaded photo
   */
  onPhotoUploaded?: (previewUrl: string) => void;

  /**
   * Callback for when a photo is deleted
   */
  onPhotoDeleted?: () => void;

  /**
   * The type of entity (e.g., 'user', 'mom')
   * Used for event dispatching
   */
  entityType?: string;
}

/**
 * A custom hook that handles photo upload, display, and removal functionality.
 *
 * This hook manages the entire lifecycle of photo management including:
 * - Fetching the current photo from S3 via proxy
 * - Uploading new photos
 * - Removing existing photos
 * - Handling loading and error states
 * - Managing the delete confirmation dialog
 * - Cache invalidation for React Query
 *
 * @param props - The photo upload configuration options
 * @returns An object containing state and handlers for photo management
 *
 * @example
 * ```tsx
 * const {
 *   imageUrl,
 *   isUploading,
 *   isDeleting,
 *   imageError,
 *   showDeleteConfirm,
 *   setShowDeleteConfirm,
 *   handleFileSelect,
 *   executeRemoveImage,
 *   handleImageError
 * } = usePhotoUpload({
 *   id: userId,
 *   photoUrl: user.photo_url,
 *   uploadFunction: uploadUserPhoto,
 *   removeFunction: removeUserPhoto,
 *   queryKey: 'user',
 *   additionalInvalidations: ['user-data']
 * });
 * ```
 */
export function usePhotoUpload<TUploadResult = unknown, TRemoveResult = unknown>({
  id,
  photoUrl,
  uploadFunction,
  removeFunction,
  queryKey,
  additionalInvalidations = [],
  onSuccess,
  onPhotoUploaded,
  onPhotoDeleted,
  entityType = 'generic', // Default to 'generic' if not specified
}: UsePhotoUploadProps<TUploadResult, TRemoveResult>) {
  const { toast } = useToast();
  const router = useRouter();
  const queryClient = useQueryClient();
  const [isUploading, setIsUploading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [tempImagePreview, setTempImagePreview] = useState<string | null>(null);
  const [timestamp, setTimestamp] = useState(Date.now());
  const [postUploadLoading, setPostUploadLoading] = useState(false);

  // Fetch the image URL using React Query
  const { data: imageUrl } = useQuery({
    queryKey: ['photo', photoUrl, timestamp],
    queryFn: async () => {
      if (!photoUrl) {
        return null;
      }

      if (tempImagePreview && isUploading) {
        return tempImagePreview;
      }

      const filename = photoUrl.split('/').pop();

      if (!filename) {
        return null;
      }

      try {
        const cacheBuster = `?_t=${timestamp}`;
        const response = await sfetch(`/v1/s3/proxy/${filename}${cacheBuster}`);

        if (!response.ok) {
          throw new Error(`Failed to fetch photo: ${response.status} ${response.statusText}`);
        }

        const blob = await response.blob();
        const objectUrl = URL.createObjectURL(blob);
        return objectUrl;
      } catch (error) {
        setImageError(true);
        return null;
      }
    },
    enabled: !!photoUrl && !isDeleting,
    staleTime: 0,
    gcTime: 0,
    refetchOnMount: 'always',
    refetchOnWindowFocus: 'always',
  });

  // Clean up object URLs when component unmounts or when photoUrl changes
  useEffect(() => {
    return () => {
      if (imageUrl) {
        URL.revokeObjectURL(imageUrl);
      }
    };
  }, [imageUrl]);

  // Clean up temp URLs when unmounting
  useEffect(() => {
    return () => {
      if (tempImagePreview) {
        URL.revokeObjectURL(tempImagePreview);
      }
    };
  }, [tempImagePreview]);

  // Force a full refresh including server components
  const forceFullRefresh = useCallback(() => {
    // Create a new timestamp to bust all caches
    setTimestamp(Date.now());

    // Clear all previous React Query data
    queryClient.clear();

    // Invalidate all related queries
    queryClient.invalidateQueries({ queryKey: [queryKey], refetchType: 'all' });

    // Invalidate additional queries if provided
    if (additionalInvalidations.length > 0) {
      additionalInvalidations.forEach((key) => {
        queryClient.invalidateQueries({ queryKey: [key], refetchType: 'all' });
      });
    }

    // Force a Next.js router refresh to revalidate server components
    router.refresh();
  }, [queryClient, queryKey, additionalInvalidations, router]);

  /**
   * Handles file selection from the upload component
   * @param files - The selected file(s) from the file input or drag-and-drop
   */
  const handleFileSelect = useCallback(
    async (files: File | File[]) => {
      setIsUploading(true);
      setImageError(false);

      try {
        if (!id) {
          throw new Error('ID is required');
        }

        // We only handle single file uploads
        const file = Array.isArray(files) ? files[0] : files;

        // Create and set a temporary preview immediately
        if (tempImagePreview) {
          URL.revokeObjectURL(tempImagePreview);
        }
        const preview = URL.createObjectURL(file);
        setTempImagePreview(preview);

        // Perform the actual upload
        const result = await uploadFunction(id, file);

        // Mark post-upload loading state
        setPostUploadLoading(true);

        // Notify of success
        toast({
          title: 'Success',
          description: 'Photo uploaded successfully',
          variant: 'success',
        });

        // Call the success callback if provided
        if (onSuccess) {
          onSuccess(result);
        }

        // Call the onPhotoUploaded callback if provided
        if (onPhotoUploaded) {
          onPhotoUploaded(preview);
        }

        // Dispatch custom event with entity information
        const photoUpdatedEvent = new CustomEvent('photo-updated', {
          detail: {
            entityType,
            entityId: id,
            action: 'upload',
          },
        });
        window.dispatchEvent(photoUpdatedEvent);

        // Dispatch specific entity photo event if entity type is provided
        if (entityType === 'user') {
          window.dispatchEvent(new Event('user-photo-updated'));
        }

        // Invalidate queries
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        for (const key of additionalInvalidations) {
          queryClient.invalidateQueries({ queryKey: [key] });
        }

        // Force a reactive update to reflect changes
        router.refresh();
      } catch (error) {
        setImageError(true);
        toast({
          title: 'Error',
          description: 'Failed to upload photo. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsUploading(false);

        // Reset post-upload loading state after a delay
        setTimeout(() => {
          setPostUploadLoading(false);
        }, 1000);
      }
    },
    [
      id,
      tempImagePreview,
      uploadFunction,
      onSuccess,
      onPhotoUploaded,
      queryClient,
      queryKey,
      additionalInvalidations,
      toast,
      router,
      entityType, // Add entityType to dependencies
    ],
  );

  /**
   * Executes the photo removal after confirmation
   */
  const executeRemoveImage = async () => {
    if (!id) {
      toast({
        title: 'Error',
        description: 'ID is required',
        variant: 'destructive',
      });
      return;
    }

    setIsDeleting(true);

    // Clear the preview immediately
    if (tempImagePreview) {
      URL.revokeObjectURL(tempImagePreview);
      setTempImagePreview(null);
    }

    try {
      const result = await removeFunction(id);

      toast({
        title: 'Success',
        description: 'Photo removed successfully',
        variant: 'success',
      });

      // Call the success callback if provided
      if (onSuccess) {
        onSuccess(result);
      }

      // Call the onPhotoDeleted callback if provided
      if (onPhotoDeleted) {
        onPhotoDeleted();
      }

      // Dispatch a custom event with entity information
      const photoUpdatedEvent = new CustomEvent('photo-updated', {
        detail: {
          entityType,
          entityId: id,
          action: 'remove',
        },
      });
      window.dispatchEvent(photoUpdatedEvent);

      // Dispatch specific entity photo event if entity type is provided
      if (entityType === 'user') {
        window.dispatchEvent(new Event('user-photo-updated'));
      }

      // Additional client-side refresh
      forceFullRefresh();
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to remove photo',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  /**
   * Handles image loading errors
   */
  const handleImageError = useCallback(() => {
    setImageError(true);
  }, []);

  return {
    imageUrl,
    isUploading: isUploading || postUploadLoading,
    isDeleting,
    imageError,
    showDeleteConfirm,
    setShowDeleteConfirm,
    handleFileSelect,
    executeRemoveImage,
    handleImageError,
    forceRefresh: forceFullRefresh,
    timestamp, // Expose timestamp for consumers
  };
}
