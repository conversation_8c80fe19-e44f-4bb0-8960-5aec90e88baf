'use client';

import { useUserInfo } from '@/context/user-info';
import { useMemo } from 'react';

import { useFindManyUser, useFindUniqueUser } from './generated/user';

export const useMyAdvocates = () => {
  const { userData } = useUserInfo();

  const { data: user } = useFindUniqueUser({
    where: {
      id: userData?.sub,
    },
    include: {
      assignedAdvocates: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
        },
      },
      assignedCoordinators: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
        },
      },
      pairings: {
        select: {
          id: true,
          momId: true,
        },
      },
    },
  });

  // Determine role flags using useMemo to prevent unnecessary recalculations
  const { isSupervisor, isCoordinator, isAdvocateUser } = useMemo(() => {
    const isAdmin = userData?.roles?.includes('administrator') ?? false;
    const isSupervisor = !isAdmin && (userData?.roles?.includes('supervisor') ?? false);
    const isCoordinator = !isAdmin && !isSupervisor && (userData?.roles?.includes('coordinator') ?? false);
    const hasHigherRole = isAdmin || isSupervisor || isCoordinator;
    const isAdvocate = userData?.roles?.includes('advocate') ?? false;

    return {
      isSupervisor,
      isCoordinator,
      isAdvocateUser: !hasHigherRole && isAdvocate,
    };
  }, [userData?.roles]);

  const { data: advocates, isLoading } = useFindManyUser(
    {
      where: {
        // Base condition: must have advocate role
        userRoles: {
          some: {
            role: {
              key: 'advocate',
            },
          },
        },
        // Additional filters based on user role
        AND: [
          // If admin, no additional filters
          // If supervisor filter by affiliateId
          ...(isSupervisor
            ? [
                {
                  affiliateId: {
                    equals: userData?.affiliateId,
                  },
                },
              ]
            : []),
          // If coordinator, only return their assigned advocates
          ...(isCoordinator
            ? [
                {
                  id: {
                    in: user?.assignedAdvocates?.map((adv) => adv.id) ?? [],
                  },
                },
              ]
            : []),
        ],
      },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
        assignedCoordinators: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        pairings: {
          include: {
            mom: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
              },
            },
            track: {
              select: {
                id: true,
                title: true,
              },
            },
          },
        },
      },
    },
    {
      enabled: !isAdvocateUser,
    },
  );

  return {
    advocates,
    isLoading,
  };
};
