import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { useCreatePairing } from '@/hooks/generated';
import { useToast } from '@/hooks/use-toast';
import { getTrackLessonTemplates } from '@/lib/portal';
import { YupSchemas } from '@suiteapi/models';

import { CompleteReasonSubStatusType, IncompleteReasonSubStatusType, TrackStatusType } from './generated/__types';
import { DischargeIncompleteSubStatusType, InProgramTrackSubStatusType } from './generated/__types';

interface CreatePairingData {
  advocateUser: string;
  track: string;
  status: string;
  track_status: string;
  in_program_track_sub_status?: string;
  discharge_incomplete_sub_status?: string;
  incomplete_reason_sub_status?: string;
  complete_reason_sub_status?: string;
  description?: string;
}

export const useCreateMomPairing = (momId: string) => {
  const { toast } = useToast();
  const { mutateAsync: createPairingAsync } = useCreatePairing();
  const { fetchMomProfile } = useMomProfileContext();

  const createMomPairing = async (data: CreatePairingData) => {
    try {
      const lessonTemplates = await getTrackLessonTemplates(data.track);

      await createPairingAsync({
        data: {
          momId,
          advocateUserId: data.advocateUser,
          trackId: data.track,
          status: data.status as YupSchemas.PairingStatusType,
          track_status: data.track_status as TrackStatusType,
          in_program_track_sub_status: data.in_program_track_sub_status as InProgramTrackSubStatusType,
          discharge_incomplete_sub_status: data.discharge_incomplete_sub_status as DischargeIncompleteSubStatusType,
          incomplete_reason_sub_status: data.incomplete_reason_sub_status as IncompleteReasonSubStatusType,
          complete_reason_sub_status: data.complete_reason_sub_status as CompleteReasonSubStatusType,
          name: `Pairing for Mom ${momId}`,
          description: data.description || null,
          lessons: {
            create: lessonTemplates.map((lt) => ({
              title: lt.title || `Lesson for Lesson Template ${lt.id}`,
              description: lt.description || null,
              priority: lt.priority || null,
              order: lt.order || null,
              duration_days: lt.duration_days || null,
              status: YupSchemas.LessonStatus.NOT_STARTED,
              documents: {
                create:
                  lt.documents?.map((d, index) => ({
                    document_name: d.document_name || `Document for Lesson ${lt.title} - ${index + 1}`,
                    filename: d.filename,
                    mimeType: d.mimeType,
                    description: d.description,
                    external_url_c: d.external_url_c,
                    s3_file_name: d.s3_file_name,
                    is_primary_lesson_resource: d.is_primary_lesson_resource,
                    subcategory_id: d.subcategory_id,
                  })) || [],
              },
              source_lesson_template_id: lt.id,
            })),
          },
        },
      });

      await fetchMomProfile(momId);

      toast({
        title: 'Success',
        description: 'The pairing has been successfully created',
        variant: 'success',
      });

      return true;
    } catch (error) {
      console.error('Error creating pairing:', error);
      toast({
        title: 'Error',
        description: 'There was an error creating the pairing. Please try again.',
        variant: 'destructive',
      });

      return false;
    }
  };

  return { createMomPairing };
};
