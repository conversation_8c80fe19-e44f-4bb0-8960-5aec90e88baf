import { useUserInfo } from '@/context/user-info';

import { useFindManyUser } from './generated/user';

export const useSupervisors = () => {
  const { userData } = useUserInfo();
  const { data: supervisors, isLoading } = useFindManyUser({
    where: {
      userRoles: {
        some: {
          role: {
            key: 'supervisor',
          },
        },
      },
      affiliateId: {
        equals: userData?.affiliateId,
      },
    },
  });

  return { supervisors, isLoading };
};
