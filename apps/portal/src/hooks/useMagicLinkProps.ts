import { useCallback, useEffect, useState } from 'react';

export const useMagicLinkProps = () => {
  const [data, setData] = useState<Record<string, string>>({});
  const getFilteredCookiesAsJson = useCallback((prefix = 'x-ema-ml-public-') => {
    const cookies = typeof window !== 'undefined' ? document.cookie : ''; // Get all cookies as a string
    if (!cookies) return {}; // Return an empty object if no cookies are set

    return cookies.split('; ').reduce(
      (acc, cookie) => {
        const [name, ...valueParts] = cookie.split('='); // Split name and value
        const decodedName = decodeURIComponent(name);
        const decodedValue = decodeURIComponent(valueParts.join('='));

        // Check if the name starts with the prefix
        if (decodedName.startsWith(prefix)) {
          // Remove the prefix and add to the result object
          const strippedName = decodedName.slice(prefix.length);
          acc[strippedName] = decodedValue;
        }
        return acc;
      },
      {} as Record<string, string>,
    );
  }, []);
  useEffect(() => {
    setData(getFilteredCookiesAsJson());
  }, [getFilteredCookiesAsJson, setData]);

  return { data };
};
