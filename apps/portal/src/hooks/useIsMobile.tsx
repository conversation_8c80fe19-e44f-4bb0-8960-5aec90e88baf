import { debounce } from 'lodash';
import { useEffect, useState } from 'react';

const useIsMobile = (breakpoint: number = 767): boolean => {
  const [isMobile, setIsMobile] = useState<boolean>(window.innerWidth <= breakpoint);

  useEffect(() => {
    const handleResize = debounce(() => {
      setIsMobile(window.innerWidth <= breakpoint);
    }, 200);

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [breakpoint]);

  return isMobile;
};

export default useIsMobile;
