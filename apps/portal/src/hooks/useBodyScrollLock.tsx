import { useEffect } from 'react';

/**
 * Prevents scrolling beyond the Y overflow on the body element when enabled.
 * This is a workaround for shadcn/ui Select component adding empty space
 * below the body on two-column dashboard layouts.
 * Usage is via the WithRestrictedScrolling component,
 * which wraps any layout that requires restricted scrolling at the body level.
 * @param shouldLock - Whether to lock the body scroll
 */

export const useBodyScrollLock = (shouldLock: boolean) => {
  useEffect(() => {
    if (shouldLock) {
      document.body.style.overflowY = 'hidden';
    } else {
      document.body.style.overflowY = '';
    }

    // Ensure the body's overflowY is reset when the component unmounts
    return () => {
      document.body.style.overflowY = '';
    };
  }, [shouldLock]);
};
