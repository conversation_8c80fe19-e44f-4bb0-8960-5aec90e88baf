import { useMutation } from '@tanstack/react-query';
import { sfetch } from '@/lib/sfetch';

interface MoodlePasswordResponse {
  password: string;
}

export const useMoodlePassword = () => {
  return useMutation({
    mutationFn: async (userId: string): Promise<MoodlePasswordResponse> => {
      const response = await sfetch(`/v1/moodle/user/${userId}/password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to get Moodle password');
      }

      return response.json();
    },
  });
};
