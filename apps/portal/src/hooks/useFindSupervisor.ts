import { User } from '@/types/schemas/user';

import { useFindUniqueUser } from './generated/user';

export const useFindSupervisor = (supervisorId: string) => {
  const { data: supervisor, isLoading } = useFindUniqueUser({
    where: {
      id: supervisorId,
    },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      availability: true,
      advocate_status: true,
      advocate_capacity_for_moms: true,
      address_street: true,
      address_city: true,
      address_state: true,
      address_postalcode: true,
      phone_mobile: true,
      photoUrl: true,
      date_of_birth: true,
      timezone: true,
      communication_preference: true,
      secondary_phone: true,
      home_church: true,
      assignedCoordinators: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
        },
      },
      affiliate: {
        select: {
          name: true,
        },
      },
      userRoles: {
        select: {
          role: true,
        },
      },
      language_preference_c: true,
      languages_c: true,
    },
  });

  return { supervisor, isLoading } as { supervisor: User | undefined; isLoading: boolean };
};
