'use client';

import {
  AGENCY_MODULE_NAME,
  BENEVOLENCE_MODULE_NAME,
  CLIENTS_MODULE_NAME,
  REFERRALS_MODULE_NAME,
  SESSION_REPORTS_MODULE_NAME,
} from '@/app/api/_lib/mappedModuleNameConstants';
import { sfetch } from '@/lib/sfetch';
import { HttpClient } from '@suiteapi/api-client';
import { AnyObjectSchema } from 'yup';

const notImplemented = () => {
  throw new Error('Not implemented');
};

export const useGenericModules = <T>(
  moduleName: string,
  schema: AnyObjectSchema,
  shouldUseAnonymousApi: boolean = false,
) => {
  if (moduleName === AGENCY_MODULE_NAME) {
    return {
      getOne: notImplemented,
      getMany: async () => {
        const response = await sfetch('/v1/agency');
        return (await response.json()) as T[];
      },
      post: notImplemented,
      patch: notImplemented,
      delete: notImplemented,
    };
  }
  if (moduleName === REFERRALS_MODULE_NAME) {
    return {
      getOne: async (id: string) => {
        const response = await sfetch(`/v1/mom/${id}`);
        return (await response.json()) as T;
      },
      getMany: async () => {
        const response = await sfetch('/v1/referral');
        return (await response.json()) as T[];
      },
      post: notImplemented,
      patch: notImplemented,
      delete: notImplemented,
    };
  }
  if (moduleName === SESSION_REPORTS_MODULE_NAME) {
    return {
      getOne: notImplemented,
      getMany: async () => [] as T[],
      post: notImplemented,
      patch: notImplemented,
      delete: notImplemented,
    };
  }
  if (moduleName === CLIENTS_MODULE_NAME) {
    return {
      getOne: notImplemented,
      getMany: async () => [] as T[],
      post: notImplemented,
      patch: notImplemented,
      delete: notImplemented,
    };
  }

  if (moduleName === BENEVOLENCE_MODULE_NAME) {
    return {
      getOne: notImplemented,
      getMany: async () => [] as T[],
      post: notImplemented,
      patch: notImplemented,
      delete: notImplemented,
    };
  }
  // Although this hook will always be used in a client component and have access to window,
  // we still need to handle window potentially being undefined during server-side pre-rendering
  const httpClient =
    typeof window === 'undefined'
      ? null
      : new HttpClient({
          baseUrl: window.location.origin,
        });

  const moduleBaseUrl = shouldUseAnonymousApi ? `/api/anon/en_us/${moduleName}` : `/api/en_us/${moduleName}`;

  const getOne = async (id: string): Promise<T | null> => {
    if (!httpClient) return null;

    const response = await httpClient!.get<T>(`${moduleBaseUrl}/${id}`);
    return response;
  };

  const getMany = async (
    queryOpts?: Record<string, unknown>,
    pagingOpts?: Record<string, unknown>,
  ): Promise<T[] | null> => {
    if (!httpClient) return null;

    if (!queryOpts && !pagingOpts) {
      const response = await httpClient!.get<T[]>(moduleBaseUrl);
      return response;
    }

    let fullUrl = moduleBaseUrl + '?';
    if (queryOpts) {
      fullUrl += `queryOpts=${encodeURIComponent(JSON.stringify(queryOpts))}&`;
    }
    if (pagingOpts) {
      fullUrl += `pagingOpts=${encodeURIComponent(JSON.stringify(pagingOpts))}&`;
    }

    fullUrl = fullUrl.slice(0, -1); // Remove the last '&'

    const response = await httpClient!.get<T[]>(fullUrl);
    return response;
  };

  const post = async (newRecord: T): Promise<T | null> => {
    if (!httpClient) return null;

    const yuppedRecord = schema.validateSync(newRecord, { stripUnknown: true });
    const response = await httpClient!.post<T, T>(moduleBaseUrl, yuppedRecord);
    return response;
  };

  const patch = async (id: string, updatedRecord: Partial<T>): Promise<T | null> => {
    if (!httpClient) return null;

    const yuppedRecord = schema.validateSync(updatedRecord, { stripUnknown: true });
    const response = await httpClient!.patch<Partial<T>, T>(`${moduleBaseUrl}/${id}`, yuppedRecord);
    return response;
  };

  const deleteRecord = async (id: string): Promise<void> => {
    if (!httpClient) return;

    await httpClient!.delete(`${moduleBaseUrl}/${id}`);
  };

  return {
    getOne,
    getMany,
    post,
    patch,
    delete: deleteRecord,
  };
};
