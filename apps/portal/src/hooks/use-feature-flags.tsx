'use client';

import { useSearchParams } from 'next/navigation';
import { useCallback, useMemo } from 'react';

/*
 * This is a fairly naive feature flag implementation but I needed something quick and dirty.
 * To make it better, capture the flags from the url as soon as the app boots and store them in a session cookie
 * Then, only look them up from the session cookie.
 * (Or use React Context if you don't care about ever sharing them with the server/service)
 */
export const useFeatureFlags = () => {
  const searchParams = useSearchParams();
  const featureFlags = useMemo(() => searchParams.get('feat')?.split('|') || [], [searchParams]);

  const isFeatureEnabled = useCallback(
    (feature: string) => {
      return featureFlags.includes(feature);
    },
    [featureFlags],
  );

  return { isFeatureEnabled };
};
