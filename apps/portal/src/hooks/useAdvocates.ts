import { useUserInfo } from '@/context/user-info';

import { useFindManyUser } from './generated/user';

export const useAdvocates = () => {
  const { userData } = useUserInfo();
  const { data: advocates, isLoading } = useFindManyUser({
    where: {
      userRoles: {
        some: {
          role: {
            key: 'advocate',
          },
        },
      },
      affiliateId: {
        equals: userData?.affiliateId,
      },
    },
  });

  return { advocates, isLoading };
};
