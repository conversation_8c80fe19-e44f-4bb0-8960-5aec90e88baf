import { User } from '@/types/schemas/user';

import { useFindUniqueUser } from './generated/user';

export const useFindAdvocate = (advocateId: string) => {
  const { data: advocate, isLoading } = useFindUniqueUser({
    where: {
      id: advocateId,
    },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      availability: true,
      advocate_status: true,
      advocate_capacity_for_moms: true,
      address_street: true,
      address_city: true,
      address_state: true,
      address_postalcode: true,
      phone_mobile: true,
      date_of_birth: true,
      photoUrl: true,
      assignedCoordinators: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
        },
      },
      affiliate: {
        select: {
          name: true,
        },
      },
      userRoles: {
        select: {
          role: true,
        },
      },
      language_preference_c: true,
      languages_c: true,
    },
  });

  return { advocate, isLoading } as { advocate: User | undefined; isLoading: boolean };
};
