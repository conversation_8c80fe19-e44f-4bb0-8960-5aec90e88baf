import { useCreateSession, useCreateSessionGroup } from '@/hooks/generated';
import { getFindMany } from '@/lib/sfetch';
import { FormValues } from '@/types/form-field';
import { LessonSchema } from '@/types/schemas/lesson';
import { PairingOrMomSchema } from '@/types/schemas/pairing';
import { SessionSchema, SessionType } from '@/types/schemas/session';
import { dateFromDateAndTimeFields } from '@suiteapi/models';
import { YupSchemas } from '@suiteapi/models';

interface CreateSingleSessionData {
  coordinatorId: string | undefined;
  data: FormValues;
  pairingOrMom: PairingOrMomSchema;
  setIsSubmitting: (isSubmitting: boolean) => void;
  successCallback: (sessionResponse: SessionSchema) => void;
  setErrorMessage: (errorMessage: string | null) => void;
}

interface CreateMultipleSessionsData {
  coordinatorId: string | undefined;
  data: FormValues;
  pairingsOrMoms: PairingOrMomSchema[];
  setIsSubmitting: (isSubmitting: boolean) => void;
  successCallback: (sessionGroupResponse: { id: string; sessions: SessionSchema[] }) => void;
  setErrorMessage: (errorMessage: string | null) => void;
}

const calculateSessionStartAndEndTimes = (data: FormValues) => {
  const startDateTime = data.start_time
    ? dateFromDateAndTimeFields(String(data.date_start), String(data.start_time))
    : data.date_start
      ? new Date(String(data.date_start))
      : null;

  const endDateTime = data.end_time
    ? dateFromDateAndTimeFields(String(data.date_end ?? data.date_start), String(data.end_time))
    : (data.date_end ?? data.date_start)
      ? new Date(String(data.date_end ?? data.date_start))
      : null;

  return { startDateTime, endDateTime };
};

const getSessionPayload = ({
  data,
  coveredLessonId,
  paringId,
  momId,
  assignedUserId,
}: {
  data: FormValues;
  coveredLessonId: string | null;
  paringId?: string;
  momId?: string;
  assignedUserId?: string;
}) => {
  const { startDateTime, endDateTime } = calculateSessionStartAndEndTimes(data);

  return {
    date_start: startDateTime,
    date_end: endDateTime,
    name: 'Standard Session Name',
    description: 'Standard Session Description',
    location: (data.location ?? null) as string | null,
    join_url: (data.join_url ?? null) as string | null,
    status:
      (endDateTime && endDateTime < new Date()) || (startDateTime && startDateTime < new Date())
        ? YupSchemas.SessionStatusType.HELD
        : YupSchemas.SessionStatusType.PLANNED,
    session_type: data.session_type as SessionType,
    session_note: {
      create: {
        name: 'Standard Session Note Name',
        description: 'Standard Session Note Description',
        status: YupSchemas.SessionNoteStatus.NEW,
        covered_lesson_id: coveredLessonId,
      },
    },
    pairing_id: paringId,
    mom_id: momId,
    assigned_user_id: assignedUserId,
  };
};

export const useCreatePairingSession = () => {
  const { mutateAsync: createSessionAsync } = useCreateSession();
  const { mutateAsync: createSessionGroupAsync } = useCreateSessionGroup();

  const createSingleSession = async ({
    coordinatorId,
    data,
    pairingOrMom,
    setIsSubmitting,
    successCallback,
    setErrorMessage,
  }: CreateSingleSessionData) => {
    setIsSubmitting(true);
    setErrorMessage(null);

    try {
      const payload = getSessionPayload({
        data,
        ...(coordinatorId
          ? {
              coveredLessonId: null,
              momId: pairingOrMom.id,
              assignedUserId: coordinatorId,
            }
          : {
              coveredLessonId: (data.current_lesson ?? null) as string | null, // Here, current_lesson is actually a lesson_id (b/c it's a single session creation).
              paringId: pairingOrMom?.id,
            }),
      });
      const newSession = await createSessionAsync({
        data: payload,
      });

      if (newSession) {
        successCallback({
          ...newSession,
          date_start: newSession.date_start?.toISOString(),
          date_end: newSession.date_end?.toISOString(),
          session_type: newSession.session_type ?? SessionType.Support_Session,
        });
      }
    } catch (error) {
      console.error(error);
      setErrorMessage('There was an error.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const createMultipleSessions = async ({
    coordinatorId,
    data,
    pairingsOrMoms,
    setIsSubmitting,
    successCallback,
    setErrorMessage,
  }: CreateMultipleSessionsData) => {
    setIsSubmitting(true);
    setErrorMessage(null);

    try {
      // Fetching lessons only make sense in the advocate view, not in the coordinator view
      const lessons = coordinatorId
        ? []
        : await getFindMany<LessonSchema>('lesson', {
            where: {
              AND: [
                {
                  pairing_id: {
                    in: pairingsOrMoms.map((pairingOrMom) => pairingOrMom.id),
                  },
                },
                {
                  source_lesson_template_id: data.current_lesson, // Here, current_lesson is actually a LessonTemplate id (b/c it's a group session).
                },
              ],
            },
          });

      const newSessionGroup = await createSessionGroupAsync({
        data: {
          sessions: {
            create: pairingsOrMoms?.map((pairingOrMom) =>
              getSessionPayload({
                data,
                ...(coordinatorId
                  ? {
                      coveredLessonId: null,
                      momId: pairingOrMom.id,
                      assignedUserId: coordinatorId,
                    }
                  : {
                      coveredLessonId: lessons.find((lesson) => lesson.pairing_id === pairingOrMom.id)?.id ?? null,
                      paringId: pairingOrMom.id,
                    }),
              }),
            ),
          },
        },
        include: {
          sessions: {
            include: {
              session_note: {
                include: {
                  covered_lesson: true,
                },
              },
            },
          },
        },
      });

      if (newSessionGroup) {
        successCallback({
          ...newSessionGroup,
          sessions: newSessionGroup.sessions.map((session) => ({
            ...session,
            date_start: session.date_start?.toISOString(),
            date_end: session.date_end?.toISOString(),
            session_type: session.session_type ?? SessionType.Support_Session,
          })),
        });
      }
    } catch (error) {
      console.error(error);
      setErrorMessage('There was an error.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return { createSingleSession, createMultipleSessions };
};
