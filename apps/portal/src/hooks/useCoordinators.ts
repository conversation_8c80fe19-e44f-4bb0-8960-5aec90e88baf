import { useUserInfo } from '@/context/user-info';

import { useFindManyUser } from './generated/user';

export const useCoordinators = () => {
  const { userData } = useUserInfo();
  const { data: coordinators, isLoading } = useFindManyUser({
    where: {
      userRoles: {
        some: {
          role: {
            key: 'coordinator',
          },
        },
      },
      affiliateId: {
        equals: userData?.affiliateId,
      },
    },
  });

  return { coordinators, isLoading };
};
