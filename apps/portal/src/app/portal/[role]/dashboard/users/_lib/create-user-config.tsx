import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

import { ActiveInactiveStatusOptions } from '../../settings/_lib/coordinator-basic-info-form.config';
import { userRoleOptions } from './edit-user-config';

export const createUserConfig = (): FieldConfig[] => [
  {
    name: 'firstName',
    label: 'First Name',
    type: 'text',
    placeholder: 'Enter First Name',
  },
  {
    name: 'lastName',
    label: 'Last Name',
    type: 'text',
    placeholder: 'Enter Last Name',
  },
  {
    name: 'username',
    label: 'Username',
    placeholder: 'Enter username',
    type: 'text',
  },
  {
    name: 'status',
    label: 'Status',
    placeholder: 'Select status',
    type: 'select',
    options: ActiveInactiveStatusOptions,
  },
  {
    name: 'email',
    label: 'Email',
    type: 'text',
    placeholder: 'Email',
  },
  {
    name: 'userRoles',
    label: 'Role(s)',
    type: 'multi-select',
    placeholder: 'Assign Roles',
    options: userRoleOptions,
  },
  {
    name: 'password',
    label: 'Password',
    type: 'password',
    placeholder: 'Enter password',
  },
];

export const createUserSchema = yup.object().shape({
  firstName: yup.string().required('First Name is required'),
  lastName: yup.string().required('Last Name is required'),
  username: yup.string().required('Username is required'),
  status: yup.string().required('Status is required'),
  email: yup.string().optional(),
  roles: yup
    .array()
    .of(
      yup
        .string()
        .oneOf(['advocate', 'coordinator', 'supervisor'], 'Please select a valid role')
        .required('Role is required'),
    ),
});
