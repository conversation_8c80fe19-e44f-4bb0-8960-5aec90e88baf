import FormFactory from '@/components/custom/form-factory';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useUserInfo } from '@/context/user-info';
import { sfetch } from '@/lib/sfetch';
import { User } from '@/types/schemas/user';
import React from 'react';
import { FieldValues, SubmitHandler } from 'react-hook-form';

import { createUserConfig, createUserSchema } from '../_lib/create-user-config';

interface CreateUserModalProps {
  user?: any;
  onSubmit: SubmitHandler<FieldValues>;
  trigger: React.ReactNode;
  defaultValues?: any;
  saveButtonText?: string;
  modalTitle?: string;
  onCreateUser?: (user: User) => void;
}

const CreateUserModal = ({
  user,
  onCreateUser,
  trigger,
  defaultValues,
  saveButtonText = 'Save',
  modalTitle = 'Edit User',
}: Omit<CreateUserModalProps, 'onSubmit'>) => {
  const [open, setOpen] = React.useState(false);
  const { userData } = useUserInfo();

  const handleSubmit: SubmitHandler<FieldValues> = async (data) => {
    const { id, roles, password, ...rest } = data;

    // Transform empty advocate_status to null
    if (rest.advocate_status === '') {
      rest.advocate_status = null;
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const updateData = Object.fromEntries(Object.entries(rest).filter(([_, value]) => value !== null));

    const newRoleKeys = data.userRoles?.map((role: { value: string }) => role.value) || [];

    try {
      await sfetch('/v1/auth/user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...updateData,
          username: data.username,
          email: data.email,
          password: data.password,
          userRoles: {
            create: newRoleKeys.map((roleKey: string) => ({
              role: {
                connect: { key: roleKey },
              },
            })),
          },
          affiliateId: userData?.affiliateId,
        }),
      });

      setOpen(false);
    } catch (error) {
      console.error('Error creating user:', error);
    } finally {
      onCreateUser?.(user);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>

      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>{modalTitle}</DialogTitle>
        </DialogHeader>

        <FormFactory
          fields={createUserConfig()}
          schema={createUserSchema}
          onSubmit={handleSubmit}
          actionButtonsComponent={<Button type='submit'>{saveButtonText}</Button>}
          defaultValues={defaultValues}
        />
      </DialogContent>
    </Dialog>
  );
};

export default CreateUserModal;
