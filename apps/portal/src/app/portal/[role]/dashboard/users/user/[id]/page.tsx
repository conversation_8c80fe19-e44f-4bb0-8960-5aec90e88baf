'use client';

import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { useUpdateUser } from '@/hooks/generated';
import { useFindManyUser, useFindUniqueUser } from '@/hooks/generated/user';
import { useToast } from '@/hooks/use-toast';
import { Language } from '@/lib/constants';
import { User } from '@/types/schemas/user';
import { ArrowLeft, Loader2, PencilIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { FieldValues, SubmitHandler } from 'react-hook-form';

import { mapLanguageCodesToOptions } from '../../../people/_utils/language-utils';
import { editUserFieldsConfig, editUserSchema } from '../../_lib/edit-user-config';

const UserPage = ({ params }: { params: { id: string } }) => {
  const { toast } = useToast();
  const { id } = params;
  const router = useRouter();
  const {
    data: user,
    isLoading,
    refetch,
  } = useFindUniqueUser({
    where: { id },
    include: {
      userRoles: {
        select: {
          role: true,
        },
      },
      assignedCoordinators: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
        },
      },
      assignedAdvocates: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
        },
      },
    },
  });

  const { data: allUsers } = useFindManyUser({
    include: {
      userRoles: {
        select: {
          role: true,
        },
      },
    },
  });

  const listOfAdvocates = allUsers?.filter((user) => user.userRoles?.some((role) => role.role.key === 'advocate'));
  const listOfCoordinators = allUsers?.filter((user) =>
    user.userRoles?.some((role) => role.role.key === 'coordinator'),
  );
  const isAdvocate = user?.userRoles?.some((role) => role.role.key === 'advocate') || false;
  const isCoordinator = user?.userRoles?.some((role) => role.role.key === 'coordinator') || false;
  const [isEditing, setIsEditing] = useState(false);
  const { mutateAsync: updateUser, isPending: isUpdating } = useUpdateUser();
  const [key, setKey] = useState(0);

  const listOfCoordinatorsOptions = listOfCoordinators?.map((coordinator) => ({
    label: `${coordinator.firstName} ${coordinator.lastName}`,
    value: coordinator.id,
  }));

  const listOfAdvocatesOptions = listOfAdvocates?.map((advocate) => ({
    label: `${advocate.firstName} ${advocate.lastName}`,
    value: advocate.id,
  }));

  // Add a loading state check
  if (isLoading) {
    return <div>Loading...</div>;
  }

  // Only create these values after we confirm data is loaded
  const roles =
    user?.userRoles?.map(({ role }) => ({
      label: role.name,
      value: role.key,
    })) ?? [];

  // Make sure user exists before creating formDefaultValues
  const formDefaultValues = user
    ? {
        ...user,
        userRoles: roles,
        // Transform assignedCoordinators to match listOfCoordinatorsOptions format
        assignedCoordinators:
          user.assignedCoordinators?.map((coord) => ({
            label: `${coord.firstName} ${coord.lastName}`,
            value: coord.id,
          })) ?? [],
        assignedAdvocates:
          user.assignedAdvocates?.map((adv) => ({
            label: `${adv.firstName} ${adv.lastName}`,
            value: adv.id,
          })) ?? [],
        languages_c: user.languages_c ? mapLanguageCodesToOptions(user.languages_c) : [],
      }
    : undefined;

  // Add a guard clause to prevent rendering the form without data
  if (!user || !formDefaultValues) {
    return <div>No user data found</div>;
  }

  const handleEditUser = async (userData: User) => {
    try {
      const { id, userRoles, assignedCoordinators, assignedAdvocates, languages_c, ...dirtyData } = userData;

      // First call - handle only userRole changes
      const existingRoles = user.userRoles || [];
      const newRoleKeys = userRoles?.map((r) => r.value) || [];
      // Find UserRole entries to delete (exist in current but not in new)
      const userRolesToDelete = existingRoles.filter((userRole) => !newRoleKeys.includes(userRole.role.name));
      // Find UserRole entries to create (exist in new but not in current)
      const rolesToCreate = newRoleKeys.filter((name) => !existingRoles.some((ur) => ur.role.name === name));

      if (userRolesToDelete.length > 0) {
        await updateUser({
          where: { id: id ?? '' },
          data: {
            userRoles: {
              deleteMany: {
                user_id: {
                  equals: user.id,
                },
              },
            },
          },
        });
      }

      // First clear existing assignments
      await updateUser({
        where: { id: id ?? '' },
        data: {
          assignedCoordinators: { set: [] },
          assignedAdvocates: { set: [] },
        },
      });

      // Then set new assignments if they exist
      if (assignedCoordinators && assignedCoordinators.length > 0) {
        await updateUser({
          where: { id: id ?? '' },
          data: {
            assignedCoordinators: {
              set: assignedCoordinators.map((coord) => ({
                id: typeof coord === 'string' ? coord : coord.id,
              })),
            },
          },
        });
      }

      if (assignedAdvocates && assignedAdvocates.length > 0) {
        await updateUser({
          where: { id: id ?? '' },
          data: {
            assignedAdvocates: {
              set: assignedAdvocates.map((adv) => ({
                id: typeof adv === 'string' ? adv : adv.id,
              })),
            },
          },
        });
      }

      // Second call - handle everything else EXCEPT userRoles
      if (dirtyData.advocate_status === '') {
        dirtyData.advocate_status = null;
      }

      const updateData = Object.fromEntries(Object.entries(dirtyData).filter(([_, value]) => value !== null));

      await updateUser({
        where: { id: id ?? '' },
        data: {
          ...updateData,
          assignedCoordinators: {
            set: assignedCoordinators?.map((coordId) => ({
              id: typeof coordId === 'string' ? coordId : coordId.id,
            })),
          },
          assignedAdvocates: {
            set: assignedAdvocates?.map((advId) => ({
              id: typeof advId === 'string' ? advId : advId.id,
            })),
          },
          languages_c: languages_c ? languages_c.map((lang: { value: string }) => lang.value as Language) : [],
        },
      });

      // If there are new roles to create, do it in a separate operation
      if (rolesToCreate.length > 0) {
        await updateUser({
          where: { id: id ?? '' },
          data: {
            userRoles: {
              create: newRoleKeys.map((roleKey) => ({
                role: {
                  connect: {
                    key: roleKey,
                  },
                },
              })),
            },
          },
        });
      }

      toast({
        title: 'Success',
        description: 'The user information has been successfully updated',
        variant: 'success',
      });
    } catch (error) {
      console.error('Error updating user:', error);
      toast({
        title: 'Error',
        description: 'There was an error updating the user information. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsEditing(false);
    }
  };

  const handleCancelEdit = async () => {
    setIsEditing(false);
    await refetch();
    // Force form re-render with fresh data by updating key
    setKey((prev) => prev + 1);
  };

  return (
    <div className='p-6'>
      <Button onClick={() => router.back()} variant='link' className='mb-4'>
        <ArrowLeft className='mr-2 h-4 w-4' /> Back to Users
      </Button>
      <div className='mb-4 flex flex-row items-center justify-between'>
        <h1 className='mb-6 text-2xl font-bold'>User Management</h1>
        {!isEditing ? (
          <Button onClick={() => setIsEditing(!isEditing)} variant='outline'>
            <PencilIcon className='mr-2 h-4 w-4' />
            Edit User
          </Button>
        ) : null}
      </div>
      <div>
        <div className='rounded-lg border bg-white p-4'>
          <div>
            <h2 className='text-xl font-semibold'>
              {user?.firstName} {user?.lastName}
            </h2>
          </div>

          <div className='max-w-[600px]'>
            {isUpdating ? (
              <div className='flex flex-row gap-2'>
                <Loader2 className='h-4 w-4 animate-spin' />
                <p>Saving...</p>
              </div>
            ) : (
              <FormFactory
                key={key}
                fields={editUserFieldsConfig(
                  isAdvocate,
                  isCoordinator,
                  listOfCoordinatorsOptions ?? [],
                  listOfAdvocatesOptions ?? [],
                )}
                schema={editUserSchema}
                readOnly={!isEditing}
                onSubmit={handleEditUser as SubmitHandler<FieldValues>}
                defaultValues={formDefaultValues}
                actionButtonsComponent={
                  <div className='flex flex-row gap-2'>
                    {!isEditing ? (
                      <Button onClick={() => setIsEditing(!isEditing)} variant='outline'>
                        <PencilIcon className='mr-2 h-4 w-4' />
                        Edit User
                      </Button>
                    ) : (
                      <>
                        {' '}
                        <Button type='submit' disabled={isUpdating}>
                          {isUpdating ? 'Saving...' : 'Save'}
                        </Button>
                        <Button onClick={handleCancelEdit} variant='outline' disabled={isUpdating}>
                          Cancel
                        </Button>
                      </>
                    )}
                  </div>
                }
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserPage;
