import { languageOptions } from '@/lib/constants';
import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

import { ActiveInactiveStatusOptions, timezoneOptions } from '../../settings/_lib/coordinator-basic-info-form.config';

const CommunicationPreferenceEnum = {
  TEXT_MESSAGE: 'text_message',
  EMAIL: 'email',
  BOTH: 'both',
} as const;

const communicationPreferenceOptions = [
  { label: 'Text Message', value: CommunicationPreferenceEnum.TEXT_MESSAGE },
  { label: 'Email', value: CommunicationPreferenceEnum.EMAIL },
  { label: 'Both', value: CommunicationPreferenceEnum.BOTH },
];

const advocateStatusOptions = [
  { label: 'Applied', value: 'Applied' },
  { label: 'Interested', value: 'Interested' },
  { label: 'Rejected', value: 'Rejected' },
  { label: 'Inactive', value: 'Inactive' },
  { label: 'Active', value: 'Active' },
  { label: 'In Training', value: 'In_Training' },
  { label: 'Awaiting Pairing', value: 'Awaiting_Pairing' },
];

export const userRoleOptions = [
  { label: 'Advocate', value: 'advocate' },
  { label: 'Coordinator', value: 'coordinator' },
  { label: 'Supervisor', value: 'supervisor' },
];

export const editUserFieldsConfig = (
  isAdvocate: boolean,
  isCoordinator: boolean,
  listOfCoordinatorsOptions: { label: string; value: string }[],
  listOfAdvocatesOptions: { label: string; value: string }[],
): FieldConfig[] => [
  {
    name: 'email',
    label: 'Email',
    placeholder: 'Enter email address',
    type: 'email',
  },
  {
    name: 'firstName',
    label: 'First Name',
    placeholder: 'Enter first name',
    type: 'text',
  },
  {
    name: 'lastName',
    label: 'Last Name',
    placeholder: 'Enter last name',
    type: 'text',
  },
  {
    name: 'address_street',
    label: 'Address Street',
    placeholder: 'Enter address street',
    type: 'text',
  },
  {
    name: 'address_city',
    label: 'Address City',
    placeholder: 'Enter address city',
    type: 'text',
  },
  {
    name: 'address_state',
    label: 'Address State',
    placeholder: 'Enter address state',
    type: 'text',
  },
  {
    name: 'address_postalcode',
    label: 'Address Postal Code',
    placeholder: 'Enter address postal code',
    type: 'text',
  },
  {
    name: 'phone_mobile',
    label: 'Mobile Phone Number',
    placeholder: 'Enter mobile phone number',
    type: 'tel',
  },
  {
    name: 'secondary_phone',
    label: 'Secondary Phone',
    placeholder: 'Enter secondary phone number',
    type: 'tel',
  },
  {
    name: 'home_church',
    label: 'Home Church',
    placeholder: 'Enter home church',
    type: 'text',
  },
  {
    name: 'secondary_email',
    label: 'Secondary Email',
    placeholder: 'Enter secondary email',
    type: 'email',
  },
  {
    name: 'timezone',
    label: 'Time Zone',
    placeholder: 'Select timezone',
    type: 'select',
    options: timezoneOptions,
  },
  {
    name: 'date_of_birth',
    label: 'Date of Birth',
    placeholder: 'Select date of birth',
    type: 'date',
    captionLayout: 'dropdown',
  },
  {
    name: 'communication_preference',
    label: 'Communication Preference',
    placeholder: 'Select communication preference',
    type: 'select',
    options: communicationPreferenceOptions,
  },
  {
    name: 'advocate_status',
    label: 'Advocate Status',
    placeholder: 'Select advocate status',
    type: 'select',
    options: advocateStatusOptions,
  },
  {
    name: 'sms_message_opt_in',
    label: 'SMS Message Opt-In',
    placeholder: 'Select SMS message opt-in',
    type: 'select',
    options: [
      { label: 'Yes', value: true },
      { label: 'No', value: false },
    ],
  },
  {
    name: 'status',
    label: 'Status',
    placeholder: 'Select status',
    type: 'select',
    options: ActiveInactiveStatusOptions,
  },
  {
    name: 'userRoles',
    label: 'User Roles',
    placeholder: 'Select user roles',
    type: 'multi-select',
    options: userRoleOptions,
  },
  {
    name: 'advocate_capacity_for_moms',
    label: 'Advocate Capacity for Moms',
    placeholder: 'Enter advocate capacity for moms',
    type: 'number',
  },
  {
    name: 'assignedCoordinators',
    label: 'Assigned Coordinators',
    type: 'multi-select',
    hidden: () => !isAdvocate,
    options: listOfCoordinatorsOptions,
  },
  {
    name: 'assignedAdvocates',
    label: 'Assigned Advocates',
    type: 'multi-select',
    hidden: () => !isCoordinator,
    options: listOfAdvocatesOptions,
  },
  {
    name: 'language_preference_c',
    label: 'Language Preference',
    labelDescription: 'The language preference for the user',
    type: 'select',
    options: languageOptions,
  },
  {
    name: 'languages_c',
    label: 'Languages',
    labelDescription: 'The languages the user speaks',
    type: 'multi-select',
    options: languageOptions,
  },
];

export const editUserSchema = yup.object().shape({
  email: yup.string().email('Please enter a valid email address').required('Email is required'),

  firstName: yup.string().required('First name is required').min(2, 'First name must be at least 2 characters'),

  lastName: yup.string().required('Last name is required').min(2, 'Last name must be at least 2 characters'),

  address_street: yup.string().nullable(),

  address_city: yup.string().nullable(),

  address_state: yup.string().nullable(),

  address_postalcode: yup.string().nullable(),
  phone: yup
    .string()
    .matches(/^[0-9+-]+$/, 'Please enter a valid phone number')
    .nullable(),

  secondary_phone: yup
    .string()
    .matches(/^[0-9+-]+$/, 'Please enter a valid phone number')
    .nullable(),

  home_church: yup.string().nullable(),

  secondary_email: yup.string().email('Please enter a valid email address').nullable(),

  timezone: yup.string().nullable(),

  date_of_birth: yup.date().max(new Date(), 'Date of birth cannot be in the future').nullable(),

  communication_preference: yup.string().nullable(),

  advocate_status: yup.string().nullable(),

  sms_message_opt_in: yup.boolean().nullable(),

  status: yup
    .string()
    .oneOf(
      ActiveInactiveStatusOptions.map((status) => status.value),
      'Please select a valid status',
    )
    .nullable(),

  userRoles: yup
    .array()
    .of(
      yup.object().shape({
        label: yup.string().required(),
        value: yup.string().required(),
      }),
    )
    .nullable(),

  advocate_capacity_for_moms: yup.number().min(0, 'Capacity must be a positive number').nullable(),

  assignedCoordinators: yup
    .array()
    .of(yup.mixed().transform((value) => (typeof value === 'string' ? value : value?.value || value?.id)))
    .nullable(),

  assignedAdvocates: yup
    .array()
    .of(yup.mixed().transform((value) => (typeof value === 'string' ? value : value?.value || value?.id)))
    .nullable(),
  language_preference_c: yup.string().nullable(),
  languages_c: yup
    .array()
    .of(
      yup.object().shape({
        label: yup.string().required(),
        value: yup.string().required(),
      }),
    )
    .nullable(),
});
