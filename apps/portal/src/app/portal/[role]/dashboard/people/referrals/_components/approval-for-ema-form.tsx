import FormFactory from '@/components/custom/form-factory';
import { But<PERSON> } from '@/components/ui/button';
import { useUpdateMom } from '@/hooks/generated';
import { Mom } from '@/hooks/generated/__types';
import { FormValues, generateFieldsSchema } from '@/types/form-field';
import { YupSchemas } from '@suiteapi/models';
import { useMemo, useState } from 'react';
import { FieldValues } from 'react-hook-form';

import { approvalForEmaFormFields } from './approval-for-ema-form.config';

const ApprovalForEmaForm = ({ mom }: { mom: Mom }) => {
  const [formValues, setFormValues] = useState<FormValues>(mom);
  const updateMom = useUpdateMom();

  const handleSubmit = (data: FieldValues) => {
    updateMom.mutateAsync({
      data: {
        prospect_status: data.prospect_status,
        referral_sub_status: data.referral_sub_status,
        status_description: data.status_description,
        status:
          data.prospect_status === YupSchemas.ProspectStatus.ENGAGED_IN_PROGRAM
            ? YupSchemas.MomStatus.ACTIVE
            : YupSchemas.MomStatus.INACTIVE,
      },
      where: {
        id: mom.id,
      },
    });
  };

  const approvalForEmaFormSchema = useMemo(
    () => generateFieldsSchema(approvalForEmaFormFields, formValues),
    [formValues],
  );

  return (
    <FormFactory
      fields={approvalForEmaFormFields}
      schema={approvalForEmaFormSchema}
      onSubmit={handleSubmit}
      defaultValues={formValues}
      onChange={setFormValues}
      actionButtonsComponent={
        <Button type='submit' disabled={updateMom.isPending}>
          Save
        </Button>
      }
    />
  );
};

export default ApprovalForEmaForm;
