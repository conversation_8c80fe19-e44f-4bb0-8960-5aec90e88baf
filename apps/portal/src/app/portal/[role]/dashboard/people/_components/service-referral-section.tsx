import React, { useEffect } from 'react';
import { format } from 'date-fns';
import { useFindManyServiceReferral } from '@/hooks/generated';

interface ServiceReferralSectionProps {
  momId: string;
  needCreationDate: Date;
  portalRole?: string;
  onHasReferrals: (hasReferrals: boolean) => void;
}

const ServiceReferralSection: React.FC<ServiceReferralSectionProps> = ({ 
  momId, 
  needCreationDate,
  onHasReferrals
}) => {
  // Fetch service referrals for this mom using the generated hook  
  const { data: serviceReferrals, isLoading, error } = useFindManyServiceReferral({
    where: { 
      mom_id: momId 
    },
    orderBy: {
      created_at: 'desc'
    }
  });

  // Find referrals related to this need (based on creation date proximity)
  const relatedReferrals = React.useMemo(() => {
    if (!serviceReferrals || !needCreationDate) return [];
    
    // Find referrals created within 24 hours of the need
    return serviceReferrals.filter(referral => {
      const referralDate = new Date(referral.created_at);
      const timeDiff = Math.abs(referralDate.getTime() - needCreationDate.getTime());
      const hoursDiff = timeDiff / (1000 * 60 * 60);
      return hoursDiff <= 24;
    });
  }, [serviceReferrals, needCreationDate]);

  // Notify parent component about referrals
  useEffect(() => {
    onHasReferrals(relatedReferrals.length > 0);
  }, [relatedReferrals.length, onHasReferrals]);

  if (isLoading) {
    return (
      <div className="mt-2 border-t pt-4">
        <p className='font-semibold mb-2'>Related Service Referrals:</p>
        <div className="space-y-2">
          <div className="h-20 w-full bg-gray-100 rounded-md animate-pulse"></div>
          <div className="h-20 w-full bg-gray-100 rounded-md animate-pulse"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mt-2 border-t pt-4">
        <div className="p-3 bg-red-50 text-red-800 rounded-md">
          Failed to load service referrals. Please try again later.
        </div>
      </div>
    );
  }

  if (relatedReferrals.length === 0) {
    return null; // Don't show the section if no related referrals
  }

  return (
    <div className="mt-2 border-t pt-4">
      <p className='font-semibold mb-2'>Related Service Referrals:</p>
      {relatedReferrals.map(referral => (
        <div key={referral.id} className="bg-gray-50 p-3 rounded-md mb-2">
          <div className="flex justify-between items-center mb-1">
            <span className="font-medium">{referral.service}</span>
            <span className={`text-xs px-2 py-1 rounded-full ${
              referral.outcome === 'successful' ? 'bg-green-100 text-green-800' : 
              referral.outcome === 'unsuccessful' ? 'bg-red-100 text-red-800' : 
              'bg-gray-100 text-gray-800'
            }`}>
              {referral.outcome.charAt(0).toUpperCase() + referral.outcome.slice(1)}
            </span>
          </div>
          <div className="text-xs text-gray-600 flex flex-col gap-1">
            <div className="flex justify-between">
              <span>Provider:</span>
              <span>{referral.provider}</span>
            </div>
            <div className="flex justify-between">
              <span>Start Date:</span>
              <span>{format(new Date(referral.start_date), 'MMM d, yyyy')}</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ServiceReferralSection;
