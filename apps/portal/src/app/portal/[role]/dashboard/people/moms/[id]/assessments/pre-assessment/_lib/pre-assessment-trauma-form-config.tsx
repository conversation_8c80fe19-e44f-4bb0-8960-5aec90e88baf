import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const yesNoPNAOptions = [
  { label: 'Yes', value: 'yes' },
  { label: 'No', value: 'no' },
  { label: 'Prefer not to answer', value: 'prefer_not_to_answer' },
];

export const preAssessmentTraumaFormSchema = yup.object().shape({
  pre_as_trau_disaster: yup.string().required('Please select an option'),
  pre_as_trau_witness_accident: yup.string().required('Please select an option'),
  pre_as_trau_personal_accident: yup.string().required('Please select an option'),
  pre_as_trau_family_jail: yup.string().required('Please select an option'),
  pre_as_trau_personal_jail: yup.string().required('Please select an option'),
  pre_as_trau_foster_care: yup.string().required('Please select an option'),
  pre_as_trau_parents_divorce: yup.string().required('Please select an option'),
  pre_as_trau_personal_divorce: yup.string().required('Please select an option'),
  pre_as_trau_financial_problems: yup.string().required('Please select an option'),
  pre_as_trau_serious_illness: yup.string().required('Please select an option'),
  pre_as_trau_children_financial_burden: yup.string().required('Please select an option'),
  pre_as_trau_emotional_abuse: yup.string().required('Please select an option'),
  pre_as_trau_physical_neglect: yup.string().required('Please select an option'),
  pre_as_trau_pregnancy_loss: yup.string().required('Please select an option'),
  pre_as_trau_child_separation: yup.string().required('Please select an option'),
  pre_as_trau_child_disability: yup.string().required('Please select an option'),
  pre_as_trau_caregiver_disability: yup.string().required('Please select an option'),
  pre_as_trau_sudden_death: yup.string().required('Please select an option'),
  pre_as_trau_natural_death: yup.string().required('Please select an option'),
  pre_as_trau_family_violence: yup.string().required('Please select an option'),
  pre_as_trau_witness_crime: yup.string().required('Please select an option'),
  pre_as_trau_stranger_attack: yup.string().required('Please select an option'),
  pre_as_trau_known_attack_before_16: yup.string().required('Please select an option'),
  pre_as_trau_known_attack_after_16: yup.string().required('Please select an option'),
  pre_as_trau_sexual_harassment: yup.string().required('Please select an option'),
  pre_as_trau_sexual_coercion_before_16: yup.string().required('Please select an option'),
  pre_as_trau_sexual_coercion_after_16: yup.string().required('Please select an option'),
  pre_as_trau_sexual_assault_before_16: yup.string().required('Please select an option'),
  pre_as_trau_sexual_assault_after_16: yup.string().required('Please select an option'),
  pre_as_trau_other_events: yup.string().required('Please select an option'),
  pre_as_trau_witnessed_effect: yup.string().required('Please select an option'),
});

export const preAssessmentTraumaFormConfig: FieldConfig[] = [
  {
    name: 'pre_as_trau_disaster',
    type: 'select',
    placeholder: 'Select',
    label:
      '1) Have you ever been in a serious disaster (for example, an earthquake, hurricane, large fire, explosion)?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_witness_accident',
    type: 'select',
    placeholder: 'Select',
    label: '2) Have you ever seen a serious accident (for example, a bad car wreck or an on-the-job accident)?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_personal_accident',
    type: 'select',
    placeholder: 'Select',
    label:
      '3) Have you ever had a very serious accident or accident-related injury (for example, a bad car wreck or an on-the-job accident)?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_family_jail',
    type: 'select',
    placeholder: 'Select',
    label: '4) Was a close family member ever sent to jail?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_personal_jail',
    type: 'select',
    placeholder: 'Select',
    label: '5) Have you ever been sent to jail?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_foster_care',
    type: 'select',
    placeholder: 'Select',
    label: '6) Were you ever put in foster care or placed for adoption?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_parents_divorce',
    type: 'select',
    placeholder: 'Select',
    label: '7) Did your parents ever separate or divorce while you were living with them?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_personal_divorce',
    type: 'select',
    placeholder: 'Select',
    label: '8) Have you ever been separated or divorced?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_financial_problems',
    type: 'select',
    placeholder: 'Select',
    label: '9) Have you ever had serious money problems (for example, not enough money for food or place to live)?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_serious_illness',
    type: 'select',
    placeholder: 'Select',
    label:
      '10) Have you ever had a very serious physical or mental illness (for example, cancer, heart attack, serious operation, felt like killing yourself, hospitalized because of nerve problems)?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_children_financial_burden',
    type: 'select',
    placeholder: 'Select',
    label: '11) Having children has been a financial burden.',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_emotional_abuse',
    type: 'select',
    placeholder: 'Select',
    label:
      '12) Have you ever been emotionally abused or neglected (for example, being frequently shamed, embarrassed, ignored, or repeatedly told that you were "no good")?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_physical_neglect',
    type: 'select',
    placeholder: 'Select',
    label:
      '13) Have you ever been physically neglected (for example, not fed, not properly clothed, or left to take care of yourself when you were too young or ill)?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_pregnancy_loss',
    type: 'select',
    placeholder: 'Select',
    label: '14) Have you ever had an abortion or miscarriage (lost your baby)?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_child_separation',
    type: 'select',
    placeholder: 'Select',
    label:
      '15) Have you ever been separated from your child against your will (for example, the loss of custody or visitation or kidnapping)?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_child_disability',
    type: 'select',
    placeholder: 'Select',
    label:
      "16) Has a baby or child of yours ever had a severe physical or mental handicap (for example, mentally retarded, birth defects, can't hear, see, walk)?",
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_caregiver_disability',
    type: 'select',
    placeholder: 'Select',
    label:
      "17) Have you ever been responsible for taking care of someone close to you (not your child) who had a severe physical or mental handicap (for example, cancer, stroke, AIDS, nerve problems, can't hear, see, walk)?",
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_sudden_death',
    type: 'select',
    placeholder: 'Select',
    label:
      '18) Has someone close to you died suddenly or unexpectedly (for example, sudden heart attack, murder or suicide)?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_natural_death',
    type: 'select',
    placeholder: 'Select',
    label: '19) Has someone close to you died (do NOT include those who died suddenly or unexpectedly)?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_family_violence',
    type: 'select',
    placeholder: 'Select',
    label:
      '20) When you were young (before age 16) did you ever see violence between family members (for example, hitting, kicking, slapping, punching)?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_witness_crime',
    type: 'select',
    placeholder: 'Select',
    label: '21) Have you ever seen a robbery, mugging, or attack taking place?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_stranger_attack',
    type: 'select',
    placeholder: 'Select',
    label: '22) Have you ever been robbed, mugged, or physically attacked (not sexually) by someone you did not know?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_known_attack_before_16',
    type: 'select',
    placeholder: 'Select',
    label:
      '23) Before age 16, were you ever abused or physically attacked (not sexually) by someone you knew (for example, a parent, boyfriend, or husband hit, slapped, choked, burned, or beat you up)?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_known_attack_after_16',
    type: 'select',
    placeholder: 'Select',
    label:
      '24) After age 16, were you ever abused or physically attacked (not sexually) by someone you knew (for example, a parent, boyfriend, or husband hit, slapped, choked, burned, or beat you up)?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_sexual_harassment',
    type: 'select',
    placeholder: 'Select',
    label:
      '25) Have you ever been bothered or harassed by sexual remarks, jokes, or demands for sexual favors by someone at work or school (for example, a coworker, a boss, a customer, another student, a teacher)?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_sexual_coercion_before_16',
    type: 'select',
    placeholder: 'Select',
    label:
      "26) Before age 16, were you ever touched or made to touch someone else in a sexual way because he/she forced you in some way or threatened to harm you if you didn't?",
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_sexual_coercion_after_16',
    type: 'select',
    placeholder: 'Select',
    label:
      "27) After age 16, were you ever touched or made to touch someone else in a sexual way because he/she forced you in some way or threatened to harm you if you didn't?",
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_sexual_assault_before_16',
    type: 'select',
    placeholder: 'Select',
    label:
      "28) Before age 16, did you ever have sex (oral, anal, genital) when you didn't want to because someone forced you in some way or threatened to hurt you if you didn't?",
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_sexual_assault_after_16',
    type: 'select',
    placeholder: 'Select',
    label:
      "29) After age 16, did you ever have sex (oral, anal, genital) when you didn't want to because someone forced you in some way or threatened to harm you if you didn't?",
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_other_events',
    type: 'select',
    placeholder: 'Select',
    label: '30) Are there any events we did not include that you would like to mention?',
    options: yesNoPNAOptions,
  },
  {
    name: 'pre_as_trau_witnessed_effect',
    type: 'select',
    placeholder: 'Select',
    label:
      "31) Have any of the events mentioned above ever happened to someone close to you so that even though you didn't see it yourself, you were seriously upset by it?",
    options: yesNoPNAOptions,
  },
];
