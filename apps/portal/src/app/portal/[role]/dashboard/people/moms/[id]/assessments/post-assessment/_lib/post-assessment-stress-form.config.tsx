import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

import { disagreeAgreeScaleOptions } from '../../pre-assessment/_lib/pre-assessment-stress-form.config';

export const postAssessmentStressFormSchema = yup.object().shape({
  post_as_stress_happy_parent: yup.string().required('Please select an option'),
  post_as_stress_do_anything_necessary: yup.string().required('Please select an option'),
  post_as_stress_time_energy: yup.string().required('Please select an option'),
  post_as_stress_doing_enough: yup.string().required('Please select an option'),
  post_as_stress_feel_close: yup.string().required('Please select an option'),
  post_as_stress_enjoy_time: yup.string().required('Please select an option'),
  post_as_stress_source_affection: yup.string().required('Please select an option'),
  post_as_stress_optimistic_future: yup.string().required('Please select an option'),
  post_as_major_stress: yup.string().required('Please select an option'),
});

export const postAssessmentStressFormConfig: FieldConfig[] = [
  {
    name: 'post_as_stress_happy_parent',
    type: 'select',
    placeholder: 'Select',
    label: '1) I am happy in my role as a parent.',
    options: disagreeAgreeScaleOptions,
  },
  {
    name: 'post_as_stress_do_anything_necessary',
    type: 'select',
    placeholder: 'Select',
    label:
      "2) There is little or nothing I wouldn't do for my child(ren) if it was necessary for their health or survival.",
    options: disagreeAgreeScaleOptions,
  },
  {
    name: 'post_as_stress_time_energy',
    type: 'select',
    placeholder: 'Select',
    label: '3) Caring for my child(ren) sometimes takes more time and energy than I have to give.',
    options: disagreeAgreeScaleOptions,
  },
  {
    name: 'post_as_stress_doing_enough',
    type: 'select',
    placeholder: 'Select',
    label: '4) I sometimes worry whether I am doing enough for my child(ren).',
    options: disagreeAgreeScaleOptions,
  },
  {
    name: 'post_as_stress_feel_close',
    type: 'select',
    placeholder: 'Select',
    label: '5) I feel close to my child(ren).',
    options: disagreeAgreeScaleOptions,
  },
  {
    name: 'post_as_stress_enjoy_time',
    type: 'select',
    placeholder: 'Select',
    label: '6) I enjoy spending time with my child(ren).',
    options: disagreeAgreeScaleOptions,
  },
  {
    name: 'post_as_stress_source_affection',
    type: 'select',
    placeholder: 'Select',
    label: '7) My child(ren) is (are) an important source of affection for me.',
    options: disagreeAgreeScaleOptions,
  },
  {
    name: 'post_as_stress_optimistic_future',
    type: 'select',
    placeholder: 'Select',
    label: '8) Having children gives me a more certain and optimistic view for the future.',
    options: disagreeAgreeScaleOptions,
  },
  {
    name: 'post_as_major_stress',
    type: 'select',
    placeholder: 'Select',
    label: '9) The major source of stress in my life is my child(ren).',
    options: disagreeAgreeScaleOptions,
  },
];
