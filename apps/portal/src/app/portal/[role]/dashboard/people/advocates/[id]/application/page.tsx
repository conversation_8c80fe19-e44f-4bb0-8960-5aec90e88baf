'use client';

import { useAdvocateProfileContext } from '@/app/portal/context/advocate-profile-context';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useFindFirstAdvocateOnboarding } from '@/hooks/generated/advocate-onboarding';
import { useFindManyCoordinatorNote } from '@/hooks/generated/coordinator-note';
import { useToast } from '@/hooks/use-toast';
import { ArrowLeft, Calendar, Clock } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';

import AdvocateProfileWrapper from '../../../_components/advocates/advocate-profile-wrapper';
import { formatAdvocateStatus, formatSnakeCaseToTitleCase } from '../../../_utils/language-utils';
import AdvocateInterviewReviewModal from '../../_components/advocate-interview-review-modal';

const AdvocateApplicationPage = () => {
  const params = useParams<{ id: string; role: string }>();
  const router = useRouter();
  const { advocateProfile } = useAdvocateProfileContext();
  const { toast } = useToast();

  // Fetch advocate onboarding data
  const { data: onboardingData } = useFindFirstAdvocateOnboarding(
    {
      where: {
        userId: advocateProfile?.id,
      },
    },
    { enabled: !!advocateProfile?.id },
  );

  // Fetch existing coordinator notes to show reviews
  const { data: coordinatorNotes } = useFindManyCoordinatorNote(
    {
      where: {
        advocate_id: params.id,
        type_c: 'interview_advocate',
      },
      include: {
        coordinator: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: {
        created_at: 'desc',
      },
    },
    { enabled: !!params.id },
  );

  const applicationReviews = coordinatorNotes?.filter((note) =>
    note.name?.toLowerCase().includes('interview review: application'),
  );

  // Helper function to handle successful review submission
  const handleReviewSuccess = () => {
    toast({
      title: 'Review Submitted',
      description: 'Your review has been successfully saved.',
      variant: 'success',
    });

    // Refresh the page to show the new review
    router.refresh();
  };

  // Format interview date if available
  const formattedInterviewDate = onboardingData?.interviewDate
    ? new Date(onboardingData.interviewDate).toLocaleDateString()
    : 'Not scheduled';

  return (
    <AdvocateProfileWrapper viewId='interview' advocateId={params.id}>
      <div className='mt-6 flex flex-col gap-4'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center'>
            <Button
              variant='ghost'
              className='mr-2'
              onClick={() => router.push(`/portal/${params.role}/dashboard/people/advocates/${params.id}/interview`)}
            >
              <ArrowLeft className='mr-1 h-4 w-4' /> Back to Interview
            </Button>
            <h1 className='text-xl font-semibold'>Application Review</h1>
          </div>

          <AdvocateInterviewReviewModal
            title='Application Review'
            description="Record your review of this advocate's application"
            successCallback={handleReviewSuccess}
          >
            <Button variant='default'>Add Review</Button>
          </AdvocateInterviewReviewModal>
        </div>{' '}
        {/* Application Header Card */}
        <Card>
          <CardHeader>
            <CardTitle>
              Application for {advocateProfile?.firstName} {advocateProfile?.lastName}
            </CardTitle>
            <CardDescription>
              Submitted on{' '}
              {onboardingData?.created_at
                ? new Date(onboardingData.created_at).toLocaleDateString()
                : new Date().toLocaleDateString()}
              {' • '}
              Status: {formatAdvocateStatus(advocateProfile?.advocate_status)}
            </CardDescription>
          </CardHeader>
        </Card>
        {/* 1. General Information */}
        <Card>
          <CardHeader>
            <CardTitle>General Information</CardTitle>
            <CardDescription>Basic contact information and details</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
              <div className='space-y-2'>
                <div>
                  <span className='text-sm font-medium text-gray-500'>First Name:</span>
                  <p className='text-sm'>{advocateProfile?.firstName || 'Not provided'}</p>
                </div>
                <div>
                  <span className='text-sm font-medium text-gray-500'>Last Name:</span>
                  <p className='text-sm'>{advocateProfile?.lastName || 'Not provided'}</p>
                </div>
                <div>
                  <span className='text-sm font-medium text-gray-500'>Email:</span>
                  <p className='text-sm'>{advocateProfile?.email || 'Not provided'}</p>
                </div>
              </div>
              <div className='space-y-2'>
                <div>
                  <span className='text-sm font-medium text-gray-500'>Phone:</span>
                  <p className='text-sm'>{advocateProfile?.phone || 'Not provided'}</p>
                </div>
                <div>
                  <span className='text-sm font-medium text-gray-500'>Date of Birth:</span>
                  <p className='text-sm'>
                    {advocateProfile?.date_of_birth
                      ? new Date(advocateProfile.date_of_birth).toLocaleDateString()
                      : 'Not provided'}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        {/* 2. Interest */}
        <Card>
          <CardHeader>
            <CardTitle>Interest</CardTitle>
            <CardDescription>In which ways are you interested in volunteering?</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='rounded bg-gray-50 p-3'>
              {onboardingData?.interests?.length ? (
                <ul className='list-disc space-y-1 pl-5 text-sm'>
                  {onboardingData.interests.map((interest, index) => (
                    <li key={index}>{interest}</li>
                  ))}
                </ul>
              ) : (
                <p className='text-sm text-gray-500'>No interests specified</p>
              )}
            </div>

            {/* Also include referral info here as it's related to initial interest */}
            <div className='mt-4'>
              <h4 className='mb-2 text-sm font-medium text-gray-500'>Referral Information</h4>
              <div className='space-y-2 rounded bg-gray-50 p-3'>
                <div>
                  <span className='text-sm font-medium text-gray-500'>Referred By:</span>
                  <p className='text-sm'>{onboardingData?.referredBy || 'Not specified'}</p>
                </div>
                <div>
                  <span className='text-sm font-medium text-gray-500'>Can Complete Background Check:</span>
                  <p className='text-sm'>{onboardingData?.canCompleteBackground ? 'Yes' : 'No'}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        {/* 3. Address Information */}
        <Card>
          <CardHeader>
            <CardTitle>Address Information</CardTitle>
            <CardDescription>Your address details</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
              <div className='space-y-2'>
                <div>
                  <span className='text-sm font-medium text-gray-500'>Street Address:</span>
                  <p className='text-sm'>{advocateProfile?.address_street || 'Not provided'}</p>
                </div>
                <div>
                  <span className='text-sm font-medium text-gray-500'>City:</span>
                  <p className='text-sm'>{advocateProfile?.address_city || 'Not provided'}</p>
                </div>
              </div>
              <div className='space-y-2'>
                <div>
                  <span className='text-sm font-medium text-gray-500'>State:</span>
                  <p className='text-sm'>{advocateProfile?.address_state || 'Not provided'}</p>
                </div>
                <div>
                  <span className='text-sm font-medium text-gray-500'>Postal Code:</span>
                  <p className='text-sm'>{advocateProfile?.address_postalcode || 'Not provided'}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        {/* 4. Experience */}
        <Card>
          <CardHeader>
            <CardTitle>Experience</CardTitle>
            <CardDescription>Your relevant experience and background</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {/* Languages */}
              <div>
                <h4 className='mb-2 text-sm font-medium text-gray-500'>Languages</h4>
                <div className='space-y-2 rounded bg-gray-50 p-3'>
                  <div>
                    <span className='text-sm font-medium text-gray-500'>Multilingual:</span>
                    <p className='text-sm'>{onboardingData?.hasMultiLang ? 'Yes' : 'No'}</p>
                  </div>
                  <div>
                    <span className='text-sm font-medium text-gray-500'>Languages Spoken:</span>
                    <p className='text-sm'>
                      {onboardingData?.languages?.length
                        ? onboardingData.languages.map((language) => formatSnakeCaseToTitleCase(language)).join(', ')
                        : 'None specified'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Professional Experience */}
              <div>
                <h4 className='mb-2 text-sm font-medium text-gray-500'>Professional Experience</h4>
                <div className='space-y-2 rounded bg-gray-50 p-3'>
                  <div>
                    <span className='text-sm font-medium text-gray-500'>Crisis Experience:</span>
                    <p className='text-sm'>{onboardingData?.hasExpCrisis ? 'Yes' : 'No'}</p>
                  </div>
                  <div>
                    <span className='text-sm font-medium text-gray-500'>Foster Care Experience:</span>
                    <p className='text-sm'>{onboardingData?.hasExpFoster ? 'Yes' : 'No'}</p>
                  </div>
                  <div>
                    <span className='text-sm font-medium text-gray-500'>Victim Support Experience:</span>
                    <p className='text-sm'>{onboardingData?.hasExpVictims ? 'Yes' : 'No'}</p>
                  </div>
                  <div>
                    <span className='text-sm font-medium text-gray-500'>Welfare Services Experience:</span>
                    <p className='text-sm'>{onboardingData?.hasExpWelfare ? 'Yes' : 'No'}</p>
                  </div>
                </div>
              </div>

              {/* Experience with Children */}
              <div>
                <h4 className='mb-2 text-sm font-medium text-gray-500'>Experience with Children</h4>
                <div className='rounded bg-gray-50 p-3'>
                  <p className='text-sm'>{onboardingData?.hasExpChildren || 'Not specified'}</p>
                </div>
              </div>

              {/* Additional Notes */}
              <div>
                <h4 className='mb-2 text-sm font-medium text-gray-500'>Additional Notes</h4>
                <div className='rounded bg-gray-50 p-3'>
                  <p className='text-sm'>{onboardingData?.personalNote || 'No additional notes provided'}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        {/* 5. Parenting Group Preferences */}
        <Card>
          <CardHeader>
            <CardTitle>Parenting Group Preferences</CardTitle>
            <CardDescription>Your preferences for working with different groups</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              <div>
                <h4 className='mb-2 text-sm font-medium text-gray-500'>Preferred Age Group</h4>
                <div className='rounded bg-gray-50 p-3'>
                  <p className='text-sm'>{onboardingData?.groupPref || 'No preference specified'}</p>
                </div>
              </div>
              <div>
                <h4 className='mb-2 text-sm font-medium text-gray-500'>Parenting Notes</h4>
                <div className='rounded bg-gray-50 p-3'>
                  <p className='text-sm'>{onboardingData?.parentingNote || 'No parenting notes provided'}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        {/* 6. Availability */}
        <Card>
          <CardHeader>
            <CardTitle>Availability</CardTitle>
            <CardDescription>Your schedule and time commitment</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='rounded bg-gray-50 p-3'>
              <p className='text-sm'>{onboardingData?.availability || 'No availability information provided'}</p>
            </div>
          </CardContent>
        </Card>
        {/* 7. Application Status & Admin Info */}
        <Card>
          <CardHeader>
            <CardTitle>Application Status & Administration</CardTitle>
            <CardDescription>Tracking and administrative information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
              <div className='space-y-2'>
                <div>
                  <span className='text-sm font-medium text-gray-500'>Home Church:</span>
                  <p className='text-sm'>{advocateProfile?.home_church || 'Not provided'}</p>
                </div>
                <div>
                  <span className='text-sm font-medium text-gray-500'>Tracking Token:</span>
                  <p className='font-mono text-sm text-xs'>{onboardingData?.trackingToken || 'Not provided'}</p>
                </div>
              </div>
              <div className='space-y-2'>
                <div>
                  <span className='text-sm font-medium text-gray-500'>Application Status:</span>
                  <p className='text-sm'>{formatAdvocateStatus(advocateProfile?.advocate_status)}</p>
                </div>
                <div>
                  <span className='text-sm font-medium text-gray-500'>Submitted:</span>
                  <p className='text-sm'>
                    {onboardingData?.created_at
                      ? new Date(onboardingData.created_at).toLocaleDateString()
                      : 'Not available'}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        {/* Interview Information */}
        <Card>
          <CardHeader>
            <CardTitle>Interview Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                <div>
                  <h3 className='mb-2 text-sm font-medium text-gray-500'>Interview Schedule</h3>
                  <div className='space-y-2 rounded bg-gray-50 p-3'>
                    <div className='flex items-center'>
                      <Calendar className='mr-2 h-4 w-4 text-gray-500' />
                      <p className='text-sm'>Date: {formattedInterviewDate}</p>
                    </div>
                    <div className='flex items-center'>
                      <Clock className='mr-2 h-4 w-4 text-gray-500' />
                      <p className='text-sm'>
                        Time: {onboardingData?.interviewStartTime || 'Not set'} -{' '}
                        {onboardingData?.interviewEndTime || 'Not set'}
                      </p>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className='mb-2 text-sm font-medium text-gray-500'>Interview Details</h3>
                  <div className='space-y-2 rounded bg-gray-50 p-3'>
                    <p className='text-sm'>
                      Meeting Type:{' '}
                      {formatSnakeCaseToTitleCase(onboardingData?.interviewMeetingType) || 'Not specified'}
                    </p>
                    <p className='text-sm'>Location: {onboardingData?.interviewLocation || 'Not specified'}</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        {/* Previous reviews section */}
        {applicationReviews && applicationReviews.length > 0 && (
          <Card className='mt-4'>
            <CardHeader>
              <CardTitle>Previous Reviews ({applicationReviews.length})</CardTitle>
            </CardHeader>
            <CardContent>
              {applicationReviews.map((review, index) => (
                <div key={review.id} className={`pb-4 ${index < applicationReviews.length - 1 ? 'mb-4 border-b' : ''}`}>
                  <div className='flex items-center justify-between'>
                    <p className='text-sm font-medium'>
                      Reviewed on {new Date(review.created_at).toLocaleDateString()}
                    </p>
                    <p className='text-xs text-gray-500'>
                      by{' '}
                      {review.coordinator?.firstName && review.coordinator?.lastName
                        ? `${review.coordinator.firstName} ${review.coordinator.lastName}`
                        : 'Unknown Reviewer'}
                    </p>
                  </div>
                  <p className='mt-1 text-sm'>{review.description}</p>
                </div>
              ))}
            </CardContent>
          </Card>
        )}
      </div>
    </AdvocateProfileWrapper>
  );
};

export default AdvocateApplicationPage;
