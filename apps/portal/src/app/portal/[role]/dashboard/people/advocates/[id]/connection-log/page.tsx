'use client';

import { usePairedMomsListContext } from '@/app/portal/context/paired-moms-context';
import GenericTable from '@/components/custom/generic-table';
import { Button } from '@/components/ui/button';
import { TableCell } from '@/components/ui/table';
import renderIconForType from '@/components/utils/renderIconForType';
import { useFindManyConnectionLog } from '@/hooks/generated';
import { YupSchemas, formatDateFromString } from '@suiteapi/models';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import AdvocateProfileWrapper from '../../../_components/advocates/advocate-profile-wrapper';
import MomConnectionLogDrawer from '../../../_components/mom-connection-log-drawer';
import { getContactMethodMappings } from '../../../_utils/contact-methods-utils';

const AdvocateConnectionLogPage = () => {
  const { pairedMomsList, fetchPairedMomsList } = usePairedMomsListContext();
  const params = useParams();
  const advocateId = params.id as string;
  const [selectedEntry, setSelectedEntry] = useState<YupSchemas.ConnectionLogWithMinimalMomSchema | null>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  useEffect(() => {
    void fetchPairedMomsList();
  }, [fetchPairedMomsList]);

  // Create a query for each mom's connection logs
  const momIds =
    pairedMomsList?.map((pairing) => pairing.momId).filter((id): id is string => id !== undefined && id !== null) || [];

  const { isLoading: connectionLogsLoading, data: momConnectionLogs } = useFindManyConnectionLog({
    where: {
      mom_id: {
        in: momIds, // This will fetch logs for all moms in the active pairings
      },
    },
    include: {
      mom: {
        select: {
          first_name: true,
          last_name: true,
        },
      },
    },
  });

  const handleRowClick = (log: YupSchemas.ConnectionLogWithMinimalMomSchema) => {
    setSelectedEntry(log);
    setIsDrawerOpen(true);
  };

  const handleCloseDrawer = () => {
    setIsDrawerOpen(false);
  };

  const contactMethodMappings = getContactMethodMappings();
  const renderRow = (log: YupSchemas.ConnectionLogWithMinimalMomSchema) => (
    <>
      <TableCell className='py-4'>
        <div className='flex items-center gap-2 text-sm'>
          {renderIconForType(log.contact_method_c)} {contactMethodMappings[log.contact_method_c]}
        </div>
      </TableCell>
      <TableCell className='py-4'>
        <p className='font-semibold'>
          {log.mom?.first_name} {log.mom?.last_name}
        </p>
        <p className='text-sm font-light text-emaTextQuaternary'>Mom</p>
      </TableCell>
      <TableCell className='whitespace-nowrap py-4'>
        {log.date_created_c ? formatDateFromString(log.date_created_c.toISOString()) : ''}
      </TableCell>
      <TableCell className='break-words py-4'>{log.summary_c}</TableCell>
      <TableCell className='py-4'>
        <Button onClick={() => handleRowClick(log)} variant='outline'>
          View
        </Button>
      </TableCell>
    </>
  );
  const mobileRenderRow = (log: YupSchemas.ConnectionLogWithMinimalMomSchema) => (
    <div className='space-y-2'>
      <div className='flex items-center gap-2 text-sm'>
        {renderIconForType(log.contact_method_c)} {contactMethodMappings[log.contact_method_c]}
      </div>
      <div>{log.summary_c}</div>
    </div>
  );

  return (
    <AdvocateProfileWrapper viewId='connection-log' advocateId={advocateId}>
      <GenericTable
        data={momConnectionLogs ?? []}
        isLoading={connectionLogsLoading}
        headers={['Type', 'Name', 'Date', 'Description', 'Actions']}
        rowRenderer={renderRow}
        mobileRenderer={mobileRenderRow}
        emptyMessage='No connection logs found.'
        columnWidths={[20, 20, 15, 35, 5]}
      />
      <MomConnectionLogDrawer
        isOpen={isDrawerOpen}
        onClose={handleCloseDrawer}
        entry={selectedEntry}
        contactMethodMappings={contactMethodMappings}
      />
    </AdvocateProfileWrapper>
  );
};

export default AdvocateConnectionLogPage;
