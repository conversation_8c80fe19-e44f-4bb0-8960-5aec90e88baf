'use client';

import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { momBasicInfoFormFields, momBasicInfoFormSchema } from '@/app/portal/lib/mom-basic-info-form-config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { useFindUniqueMom, useUpdateMom } from '@/hooks/generated/mom';
import { useToast } from '@/hooks/use-toast';
import { useState } from 'react';
import { FieldValues } from 'react-hook-form';

import MomBasicInfoWrapper from '../../../_components/mom-basic-info-form-wrapper';
import MomProfileWrapper from '../../../_components/mom-profile-wrapper';
import WithMomProfile from '../with-mom-profile';

// Define the shape of the mom data used in the component
interface MomData {
  id: string;
  first_name: string | null;
  last_name: string | null;
  phone_other?: string | null;
  email1?: string | null;
  preferred_contact_method_c?: string | null;
  sms_message_opt_in?: boolean | null;
  languages_c?: string[] | null;
  caregiver_type_c?: string | null;
  birthdate?: string | Date | null;
  [key: string]: unknown; // Allow other properties
}

const MomInfoPage = ({ params }: { params: { id: string } }): React.ReactNode => {
  const { id } = params;
  const { fetchMomProfile } = useMomProfileContext();
  const { toast } = useToast();
  const { mutateAsync: updateMomAsync } = useUpdateMom();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    data: mom,
    isLoading,
    refetch,
  } = useFindUniqueMom({
    where: { id },
  });

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!mom) {
    return <div>No data found</div>;
  }

  const handleSubmit = async (data: FieldValues) => {
    setIsSubmitting(true);

    try {
      await updateMomAsync({
        where: { id },
        data: {
          ...data,
          languages_c: data.languages_c.map((lang: { value: string }) => lang.value),
          birthdate: data.birthdate ? new Date(data.birthdate).toISOString() : null,
        },
      });
      fetchMomProfile(id);
      toast({
        title: 'Success',
        description: 'Basic contact information has been successfully saved',
        variant: 'success',
      });

      refetch();
    } catch (error) {
      toast({
        title: 'Error',
        description: `There was an error saving the contact information. ${error}`,
        variant: 'destructive',
      });

      console.error('Failed to submit:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <WithMomProfile momId={id}>
      <MomProfileWrapper viewId='info' momId={id}>
        <MomBasicInfoWrapper momId={id} currentTab='contact-info'>
          <div className='ml-10 w-full rounded-md border bg-white p-6 max-md:ml-0 md:w-[560px]'>
            <FormFactory
              formWrapperClassName='flex flex-col w-full'
              formFieldElClass='w-full'
              fields={momBasicInfoFormFields}
              schema={momBasicInfoFormSchema}
              onSubmit={handleSubmit}
              defaultValues={mom}
              actionButtonsComponent={
                <div className='my-auto flex w-full min-w-[240px] flex-1 shrink basis-0 items-center justify-end gap-3 self-stretch border-t-[1px] pt-4'>
                  <Button
                    type='submit'
                    className='my-auto flex items-center justify-center gap-1 self-stretch overflow-hidden rounded-lg border-2 border-solid border-white border-opacity-10 bg-slate-800 px-3.5 py-2.5 text-white shadow-sm'
                  >
                    {isSubmitting ? 'Saving...' : 'Save changes'}
                  </Button>
                </div>
              }
            />
          </div>
        </MomBasicInfoWrapper>
      </MomProfileWrapper>
    </WithMomProfile>
  );
};

export default MomInfoPage;
