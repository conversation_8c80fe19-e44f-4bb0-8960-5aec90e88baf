import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import ResponsiveDrawer from '@/components/custom/responsive-drawer';
import { formatDateFromString } from '@suiteapi/models';
import { YupSchemas } from '@suiteapi/models';
import { Eye } from 'lucide-react';

interface CoordinatorNotesDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  entry: YupSchemas.CoordinatorNotesSchema | null;
}

const CoordinatorNotesDrawer = ({ isOpen, onClose, entry }: CoordinatorNotesDrawerProps) => {
  const { momProfile } = useMomProfileContext();
  if (!entry) return null;

  return (
    <ResponsiveDrawer
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className='ml-4 pr-4'>
          <h1 className='mb-1 text-xl font-semibold'>Coordinator Note</h1>
          <p className='text-sm font-normal text-emaTextTertiary'>
            Mom: {momProfile?.first_name} {momProfile?.last_name}
          </p>
        </div>
      }
    >
      <div className='mt-5 flex flex-col gap-5 border-t px-6 pt-5 text-sm text-emaTextSecondary'>
        <p className='text-sm text-emaTextQuaternary'>
          Added: {entry.created_at ? formatDateFromString(entry.created_at.toISOString()) : 'date not available'}
        </p>
        <div>
          <p className='font-semibold text-black'>Note Type: </p>
          <p>
            {entry.type_c === YupSchemas.CoordinatorNoteTypeEnum.SafetyOrConcernUpdate
              ? 'Safety or Concern Update'
              : 'Court Update'}
          </p>
        </div>
        <div>
          <p className='text-bla font-semibold'>Note Details: </p>
          <p>{entry.description}</p>
        </div>
        <p className='flex items-center gap-2'>
          <Eye className='h-6 w-6 text-emaTextTertiary' />
          <span className='text-sm text-emaTextTertiary'>{`Visible to ${entry.type_c === YupSchemas.CoordinatorNoteTypeEnum.CourtUpdate ? 'Advocates,' : ''} Coordinators, Supervisors`}</span>
        </p>
      </div>
    </ResponsiveDrawer>
  );
};

export default CoordinatorNotesDrawer;
