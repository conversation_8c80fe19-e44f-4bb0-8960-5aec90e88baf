'use client';

import { useCoordinatorProfileContext } from '@/app/portal/context/coordinator-profile-context';
import GenericTable from '@/components/custom/generic-table';
import { TableCell } from '@/components/ui/table';
import { PairingStatusType } from '@/hooks/generated/__types';
import { useMyAdvocates } from '@/hooks/useMyAdvocates';
import { AdvocateStatus } from '@/types/schemas/user';
import { format } from 'date-fns';
import { useParams } from 'next/navigation';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { AdvocateStatusBadge } from '../../../_components/advocates-table';
import CoordinatorOverviewDetailsSection from '../../../_components/coordinators/coordinator-overview-details-section';
import CoordinatorProfileWrapper from '../../../_components/coordinators/coordinator-profile-wrapper';

type CoordinatorAdvocatesTableType = {
  created_at: Date | null;
  firstName: string | null;
  lastName: string | null;
  advocate_status: AdvocateStatus | null;
  advocate_capacity_for_moms: number | null;
  pairings: Array<{
    id: string;
    status: PairingStatusType | null;
    mom: {
      first_name: string | null;
      last_name: string | null;
    } | null;
    track: {
      title: string;
    } | null;
  }> | null;
};

const CoordinatorOverviewPage = () => {
  const params = useParams();
  const coordinatorId = params.id as string;
  const { coordinatorProfile, fetchCoordinator } = useCoordinatorProfileContext();
  const [advocateData, setAdvocateData] = useState<CoordinatorAdvocatesTableType[]>([]);

  useEffect(() => {
    if (coordinatorId) {
      fetchCoordinator(coordinatorId);
    }
  }, [coordinatorId, fetchCoordinator]);
  const { advocates, isLoading: isLoadingAdvocates } = useMyAdvocates();

  const filteredAdvocates = useMemo(() => {
    if (!advocates || !coordinatorProfile?.id) return [];

    return advocates.filter((advocate) =>
      advocate.assignedCoordinators?.some((coordinator) => coordinator.id === coordinatorProfile.id),
    );
  }, [advocates, coordinatorProfile?.id]);

  const mappedAdvocateData = useMemo(() => {
    if (!filteredAdvocates) return [];

    return filteredAdvocates.map(
      (advocate): CoordinatorAdvocatesTableType => ({
        created_at: advocate.created_at,
        firstName: advocate.firstName,
        lastName: advocate.lastName,
        advocate_status: advocate.advocate_status,
        advocate_capacity_for_moms: advocate.advocate_capacity_for_moms,
        pairings:
          advocate.pairings?.map((pairing) => ({
            id: pairing.id,
            status: pairing.status,
            mom: pairing.mom
              ? {
                  first_name: pairing.mom.first_name,
                  last_name: pairing.mom.last_name,
                }
              : null,
            track: pairing.track
              ? {
                  title: pairing.track.title,
                }
              : null,
          })) || null,
      }),
    );
  }, [filteredAdvocates]);

  useEffect(() => {
    if (mappedAdvocateData) {
      setAdvocateData(mappedAdvocateData);
    }
  }, [mappedAdvocateData]);

  const formatAdvocateCapacity = (advocate: CoordinatorAdvocatesTableType) => {
    const activePairings = advocate.pairings?.filter((p) => p.status === 'paired') || [];
    return `${activePairings.length} / ${advocate.advocate_capacity_for_moms ?? 'NA'}`;
  };

  const rowRenderer = useCallback(
    (advocate: CoordinatorAdvocatesTableType) => {
      return (
        <>
          <TableCell>
            {advocate.firstName} {advocate.lastName}
          </TableCell>
          <TableCell>{advocate.created_at ? format(advocate.created_at, 'MMM d, yyyy') : ''}</TableCell>
          <TableCell>
            <AdvocateStatusBadge status={advocate.advocate_status as AdvocateStatus} />
          </TableCell>
          <TableCell>{formatAdvocateCapacity(advocate)}</TableCell>
        </>
      );
    },
    [formatAdvocateCapacity],
  );

  const mobileRenderer = useCallback(
    (advocate: CoordinatorAdvocatesTableType) => {
      return (
        <div>
          <p className='text-lg font-semibold'>
            {advocate.firstName} {advocate.lastName}
          </p>
          <div>
            <span className='font-semibold'>Start Date: </span>
            <span>{advocate.created_at ? format(advocate.created_at, 'MMM d, yyyy') : ''}</span>
          </div>
          <div>
            <span className='font-semibold'>Status: </span>
            <AdvocateStatusBadge status={advocate.advocate_status as AdvocateStatus} />
          </div>
          <div>
            <span className='font-semibold'>Moms Supporting: </span>
            <span>{formatAdvocateCapacity(advocate)}</span>
          </div>
        </div>
      );
    },
    [formatAdvocateCapacity],
  );

  return (
    <CoordinatorProfileWrapper viewId='overview'>
      <section className='flex flex-col gap-8 md:flex-row'>
        <div className='md:w-[20%]'>
          <CoordinatorOverviewDetailsSection />
        </div>
        <div className='md:w-[80%]'>
          <GenericTable
            headerSection={<h2 className='text-xl font-semibold'>{coordinatorProfile?.firstName}&apos;s Advocates</h2>}
            data={advocateData}
            rowRenderer={rowRenderer}
            mobileRenderer={mobileRenderer}
            isLoading={isLoadingAdvocates}
            headers={['Name', 'Start Date', 'Status', 'Moms Supporting']}
          />
        </div>
      </section>
    </CoordinatorProfileWrapper>
  );
};

export default CoordinatorOverviewPage;
