'use client';

import { useAssessments } from '@/app/portal/[role]/dashboard/people/moms/[id]/assessments/_hooks/use-assessments';
import { AssessmentsTabsProvider, useAssessmentsTabsContext } from '@/app/portal/context/assessments-tabs-context';
import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import { useRouter } from 'next/navigation';
import React from 'react';

import MomProfileWrapper from '../../../_components/mom-profile-wrapper';
import WithMomProfile from '../with-mom-profile';
import { useDynamicAssessmentTabs } from './_hooks/use-dynamic-assessment-tabs';

const AssessmentsTabs = ({ children, params }: { children: React.ReactNode; params: { id: string } }) => {
  const { assessments, areAssessmentsLoading } = useAssessments();
  const { tabs } = useDynamicAssessmentTabs(assessments);
  const { currentTab, setCurrentTab } = useAssessmentsTabsContext();
  const router = useRouter();
  const { profile } = useDashboardContext();
  const { id } = params;

  const navigateToTab = (tab: string): void => {
    setCurrentTab(tab);
    // Admittedly, this is odd behavior. It's being done b/c the Well-Being assessment (well-being) and
    // the AAPI assessment (assessments) are static tabs. 'assessment' is old UX that
    // was never wired up and can be deleted at some point. Those tabs do not display 'results' but rather
    // the form to fill out themselves; which is why they're not under a 'results' path.
    if (tab === 'well-being' || tab === 'assessments' || tab === 'assessment') {
      router.push(`/portal/${profile.portalRole}/dashboard/people/moms/${id}/assessments/${tab}`);
      return;
    }

    router.push(`/portal/${profile.portalRole}/dashboard/people/moms/${id}/assessments/${tab}/results`);
  };

  if (areAssessmentsLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className='mt-10 flex flex-col'>
      <nav className='mb-4 flex w-full flex-row text-sm font-semibold leading-5 text-emaTextPrimary md:hidden md:w-[280px]'>
        {tabs.map((tab, index) => (
          <div
            key={tab.title}
            onClick={() => navigateToTab(tab.title)}
            className={`cursor-pointer gap-2 px-3 py-2 ${index === 0 ? '' : 'ml-1'} ${
              currentTab === tab.title ? 'border-b-2 border-b-emaBrandSecondary' : 'text-emaTextQuaternary'
            } min-h-[36px]`}
          >
            {tab.navLabel}
          </div>
        ))}
      </nav>
      <div className='flex max-md:flex-col'>
        <nav className='mb-4 hidden w-full flex-col text-sm font-semibold leading-5 text-emaTextPrimary md:mb-0 md:flex md:w-[280px]'>
          {tabs.map((tab, index) => (
            <div
              key={tab.title}
              onClick={() => navigateToTab(tab.title)}
              className={`cursor-pointer gap-2 self-stretch px-3 py-2 ${index === 0 ? '' : 'mt-1'} w-full ${
                currentTab === tab.title ? 'border-l-2 border-l-emaBrandSecondary' : 'text-emaTextQuaternary'
              } min-h-[36px]`}
            >
              {tab.navLabel}
            </div>
          ))}
        </nav>
        {children}
      </div>
    </div>
  );
};

const AssessmentsLayout = ({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { id: string; assessmentId: string };
}): React.ReactNode => {
  const { id } = params;

  return (
    <WithMomProfile momId={id}>
      <MomProfileWrapper viewId='assessments' momId={id}>
        <AssessmentsTabsProvider initialTab={'well-being'}>
          <AssessmentsTabs params={params}>{children}</AssessmentsTabs>
        </AssessmentsTabsProvider>
      </MomProfileWrapper>
    </WithMomProfile>
  );
};

export default AssessmentsLayout;
