import ActionItemsNewMomReferralDrawer from '@/app/portal/_components/dashboard/action-items-tables/action-items-new-mom-referral-drawer';
import { EnhancedMomType } from '@/app/portal/_components/dashboard/action-items-tables/action-items-new-moms-table';
import { MomProfileProvider } from '@/app/portal/context/mom-profile-context';
import GenericTable from '@/components/custom/generic-table';
import { Badge } from '@/components/ui/badge';
import { TableCell, TableRow } from '@/components/ui/table';
import { useUserInfo } from '@/context/user-info';
import { useFindManyMom } from '@/hooks/generated';
import { YupSchemas } from '@suiteapi/models';
import { useState } from 'react';

import { isUserCoordinatorOrAdvocate } from '../_utils/people-utils';

const ReferralsTable = () => {
  const [isReferralDrawerOpen, setIsReferralDrawerOpen] = useState(false);
  const [selectedReferral, setSelectedReferral] = useState<EnhancedMomType | null>(null);
  const { roles } = useUserInfo();

  const {
    data: moms,
    isLoading,
    refetch,
  } = useFindManyMom({
    where: {
      prospect_status: isUserCoordinatorOrAdvocate(roles)
        ? {
            // TODO: https://servant-io.atlassian.net/browse/EMA-1339
            // note: admins who do not have a mom/referral assigned to them will we all moms/referrals on the same table
            in: [YupSchemas.ProspectStatus.PROSPECT, YupSchemas.ProspectStatus.PROSPECT_INTAKE_SCHEDULED],
          }
        : undefined,
    },
  });

  const renderProspectStatus = (status: YupSchemas.ProspectStatusType | undefined | null) => {
    switch (status) {
      case YupSchemas.ProspectStatus.PROSPECT:
        return <Badge variant='outline'>Prospect</Badge>;
      case YupSchemas.ProspectStatus.PROSPECT_INTAKE_SCHEDULED:
        return <Badge variant='outline'>Prospect Intake Scheduled</Badge>;
      default:
        return null;
    }
  };

  const handleRowClicked = (referral: YupSchemas.MomType) => {
    const enhancedReferral: EnhancedMomType = {
      ...referral,
      table_label: 'Referral',
      table_actions_label: 'Review',
    };
    setIsReferralDrawerOpen(true);
    setSelectedReferral(enhancedReferral);
  };

  const renderRow = (referral: YupSchemas.MomType) => {
    return (
      <TableRow className='cursor-pointer' onClick={() => handleRowClicked(referral)}>
        <TableCell>{referral.first_name}</TableCell>
        <TableCell>{referral.last_name}</TableCell>
        <TableCell>
          {/* TODO: https://servant-io.atlassian.net/browse/EMA-1339
            // when it is a admin/supervisor with no moms assigned, it incorrectly pulls back "prospect" status for all moms
            // so we need to check if the user is a admin/supervisor and if so, don't show the prospect status */}
          {isUserCoordinatorOrAdvocate(roles) && referral.prospect_status
            ? renderProspectStatus(referral.prospect_status as YupSchemas.ProspectStatusType)
            : ''}
        </TableCell>
      </TableRow>
    );
  };

  const renderMobileItem = (referral: YupSchemas.MomType) => {
    return (
      <div>
        {referral.first_name} {referral.last_name}
      </div>
    );
  };

  return (
    <MomProfileProvider>
      <GenericTable
        data={(moms ?? []).map((mom) => ({
          ...mom,
          photoUrl: mom.iconUrl || undefined,
          photoS3FileName: mom.iconS3FileName || undefined,
        }))}
        headers={['First Name', 'Last Name', 'Prospect Status']}
        isLoading={isLoading}
        rowRenderer={renderRow}
        mobileRenderer={renderMobileItem}
        shouldUseCustomRowComponent={true}
      />
      <ActionItemsNewMomReferralDrawer
        onScheduleIntakeSuccess={refetch}
        onDenyReferral={() => {
          refetch();
          setIsReferralDrawerOpen(false);
        }}
        isOpen={isReferralDrawerOpen}
        onClose={() => setIsReferralDrawerOpen(false)}
        entry={selectedReferral}
        refreshTable={refetch}
      />
    </MomProfileProvider>
  );
};

export default ReferralsTable;
