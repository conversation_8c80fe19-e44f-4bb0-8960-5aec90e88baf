import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { useCreatePairing, useFindFirstPairing, useFindManyLessonTemplate, useFindManyTrack } from '@/hooks/generated';
import { Mom } from '@/hooks/generated/__types';
import { generateFieldsSchema } from '@/types/form-field';
import { useMemo } from 'react';
import { FieldValues } from 'react-hook-form';

import { getSelectTrackFormFields } from './select-track-form.config';

const SelectTrackForm = ({ mom }: { mom: Mom }) => {
  const paring = useFindFirstPairing({
    where: {
      momId: mom.id,
    },
    orderBy: {
      created_at: 'desc',
    },
  });
  const tracks = useFindManyTrack();
  const lessonTemplates = useFindManyLessonTemplate();
  const createParing = useCreatePairing();

  const handleSubmit = async (data: FieldValues) => {
    const track = tracks.data?.find((track) => track.id === data.track_id);
    if (!track) {
      return;
    }

    const trackLessonTemplates =
      lessonTemplates.data?.filter((lessonTemplate) => lessonTemplate.track_id === track.id) ?? [];

    createParing.mutateAsync({
      data: {
        name: track.title,
        description: track.description,
        momId: mom.id,
        trackId: data.track_id,
        status: 'waiting_to_be_paired',
        lessons: {
          createMany: {
            data: trackLessonTemplates.map((lessonTemplate) => ({
              title: lessonTemplate.title,
              description: lessonTemplate.description,
              priority: lessonTemplate.priority,
              order: lessonTemplate.order,
              duration_days: lessonTemplate.duration_days,
            })),
          },
        },
      },
    });
  };

  const selectTrackFormFields = useMemo(() => getSelectTrackFormFields(tracks.data ?? []), [tracks.data]);
  const selectATrackFormSchema = useMemo(
    () => generateFieldsSchema(selectTrackFormFields, undefined),
    [selectTrackFormFields],
  );

  const defaultValues = useMemo(
    () => ({
      track_id: paring.data?.trackId,
    }),
    [paring],
  );

  if (paring.isLoading || tracks.isLoading) {
    return 'Loading...';
  }

  return (
    <FormFactory
      fields={selectTrackFormFields}
      schema={selectATrackFormSchema}
      onSubmit={handleSubmit}
      defaultValues={defaultValues}
      actionButtonsComponent={
        <Button type='submit' disabled={createParing.isPending}>
          Save
        </Button>
      }
    />
  );
};

export default SelectTrackForm;
