import { useMomConnectionLogsContext } from '@/app/portal/context/mom-connection-logs-context';
import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { Button } from '@/components/ui/button';
import useIsMobile from '@/hooks/useIsMobile';
import { CalendarPlus, SendIcon } from 'lucide-react';

import MomCreateConnectionLogModal from './mom-create-connection-log-modal';
import MomNameImageHeader from './mom-name-photo-header';
import MomScheduleSessionModal from './mom-schedule-session-modal';

interface MomProfileHeaderProps {
  viewId: string;
}

const MomProfileHeader: React.FC<MomProfileHeaderProps> = ({ viewId }) => {
  const { momProfile, pairingInfo } = useMomProfileContext();
  const { signalUpdateList: signalUpdateConnectionLogsList } = useMomConnectionLogsContext();

  const name = `${momProfile?.first_name} ${momProfile?.last_name}` as string;
  const id = momProfile?.id as string;
  const isMobile = useIsMobile();

  return (
    <div className='mt-5 flex w-full flex-wrap items-start gap-5 max-md:max-w-full'>
      <MomNameImageHeader momName={name} momId={id} />
      <div className='flex min-w-[240px] flex-wrap items-center gap-3 text-sm font-semibold leading-none max-md:max-w-full'>
        <MomScheduleSessionModal
          pairingId={pairingInfo?.id}
          buttonComponent={
            <Button
              type='button'
              variant='ghost'
              className='my-auto flex flex-col items-center justify-center gap-2 self-stretch overflow-hidden rounded-lg border border-emaBgQuaternary bg-white px-3.5 py-2.5 text-slate-600 hover:border-input hover:bg-background md:flex-row md:gap-1 md:border-0 md:bg-none'
            >
              <CalendarPlus className='h-5 w-5' />
              <span className='my-auto self-stretch px-0.5'>Schedule a Meeting</span>
            </Button>
          }
          // TODO: Potentially add success callback to trigger refresh of session list - https://servant-io.atlassian.net/browse/EMA-643
          // suggested implementation (need to set up in session notes context):
          // successCallback={signalUpdateSessionNotesList}
        />
        {/* TODO: Ticket regarding this buttons functionality whenever it is decided: https://servant-io.atlassian.net/browse/EMA-421 */}
        {viewId === 'connection-logs' || viewId === 'session-notes' ? (
          <Button className={`${isMobile ? 'h-full flex-col bg-transparent' : 'flex-row'} bg-white`} variant='outline'>
            <p className={`flex items-center ${isMobile ? 'mb-2' : ''}`}>
              <SendIcon size={16} className={`${isMobile ? 'text-slate-600' : ''} `} />
            </p>
            <p className={`ml-3 block ${isMobile ? 'text-center text-slate-600' : ''} `}>Message</p>
          </Button>
        ) : null}
        <MomCreateConnectionLogModal momId={id} successCallback={signalUpdateConnectionLogsList} />
      </div>
    </div>
  );
};

export default MomProfileHeader;
