'use client';

import { useParams } from 'next/navigation';

import AdvocateProfileWrapper from '../../../_components/advocates/advocate-profile-wrapper';
import DocumentManagement from '../../../_components/document-management';

/**
 * Displays and manages documents related to a specific advocate
 * Uses advocate ID from URL parameters to fetch and filter documents
 */
const AdvocateDocumentsPage = () => {
  const params = useParams();
  const advocateId = params.id as string;

  return (
    <AdvocateProfileWrapper viewId='documents' advocateId={advocateId}>
      <div className='p-6'>
        <DocumentManagement
          documentFetchWhereClause={{ advocate_id: advocateId }}
          documentDataEnhancements={{
            advocate_id: advocateId,
          }}
        />
      </div>
    </AdvocateProfileWrapper>
  );
};

export default AdvocateDocumentsPage;
