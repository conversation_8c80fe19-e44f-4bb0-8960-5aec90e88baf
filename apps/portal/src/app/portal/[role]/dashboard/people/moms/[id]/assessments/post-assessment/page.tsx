'use client';

import type { FormValues } from '@/types/form-field';
import React from 'react';

import AssessmentWrapper from '../_components/assessment-wrapper';
import { postAssessmentFormsConfig } from './_lib/post-assessment-form.config';

interface PostAssessmentProps {
  params: {
    id: string;
  };
}

const PostAssessment = ({ params }: PostAssessmentProps) => {
  const handleSubmit = async (data: FormValues) => {
    await fetch(`/api/moms/${params.id}/post-assessment`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  };

  const fetchInitialData = async () => {
    // TODO: Update this API to whatever it is supposed to be
    const response = await fetch(`/api/moms/${params.id}/post-assessment`);
    if (!response.ok) {
      // If no existing data, return empty object
      if (response.status === 404) return {};
      throw new Error('Failed to fetch post-assessment data');
    }
    return response.json();
  };

  return (
    <AssessmentWrapper
      title='Post-Assessment'
      config={postAssessmentFormsConfig}
      onSubmit={handleSubmit}
      fetchInitialData={fetchInitialData}
    />
  );
};

export default PostAssessment;
