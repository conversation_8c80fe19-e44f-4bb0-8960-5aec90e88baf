'use client';

import { AdvocateProfileProvider } from '@/app/portal/context/advocate-profile-context';
import { PairedMomsListProvider } from '@/app/portal/context/paired-moms-context';
import { useParams } from 'next/navigation';
import React from 'react';

const Layout = ({ children }: { children: React.ReactNode }) => {
  const { id } = useParams();
  return (
    <AdvocateProfileProvider advocateId={id as string}>
      <PairedMomsListProvider>{children}</PairedMomsListProvider>
    </AdvocateProfileProvider>
  );
};

export default Layout;
