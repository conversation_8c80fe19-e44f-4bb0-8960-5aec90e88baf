import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { getCreateSessionFormFields } from '@/app/portal/lib/create-session-schema.config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  Dialog<PERSON>ontent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useCreatePairingSession } from '@/hooks/use-create-session';
import { getFieldKeysFromFieldConfig } from '@/lib/utils';
import { type FormValues } from '@/types/form-field';
import { type SessionSchema, sessionApiFieldSchemas } from '@/types/schemas/session';
import { pick } from 'lodash';
import { FileText } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import * as yup from 'yup';

import { getNextLessonId } from '../_utils/session-utils';

interface MomCreateSessionNotesModalProps {
  buttonComponent?: React.ReactNode;
}

const MomCreateSessionNotesModal: React.FC<MomCreateSessionNotesModalProps> = ({ buttonComponent }) => {
  const router = useRouter();
  const { profile } = useDashboardContext();
  const { pairingInfo, momProfile, lessons } = useMomProfileContext();
  const momID = momProfile?.id || '';
  const pathBase = `/portal/${profile.portalRole}/dashboard/people/moms/${String(momID)}/session-notes`;
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const { createSingleSession } = useCreatePairingSession();

  const onSuccess = (sessionResponse: SessionSchema): void => {
    const newSessionId = sessionResponse.id;
    // Navigate to the new session page after successful creation
    router.push(`${pathBase}/${newSessionId}`);
  };

  const incompleteLessons = lessons?.filter((lesson) => lesson.status !== 'completed');
  const lessonOptions =
    incompleteLessons?.map((lesson) => ({ value: String(lesson.id), label: String(lesson.title) })) || [];
  const nextLessonId = getNextLessonId(incompleteLessons || []);

  const isCoordinator = profile.roles.includes('coordinator');
  const coordinatorId = isCoordinator ? profile.id : undefined;

  const formConfig = getCreateSessionFormFields({ isCoordinator, lessonOptions });
  const fieldKeys = getFieldKeysFromFieldConfig(formConfig);
  const createSessionFormSchema = yup.object().shape(pick(sessionApiFieldSchemas, fieldKeys));

  const defaultValues = nextLessonId
    ? {
        current_lesson: nextLessonId,
      }
    : {};

  const handleSubmit = async (data: FormValues): Promise<void> => {
    await createSingleSession({
      coordinatorId,
      data,
      pairingOrMom: {
        id: isCoordinator ? (momProfile?.id ?? '') : (pairingInfo?.id ?? ''),
        mom: {
          id: momProfile?.id ?? undefined,
          first_name: momProfile?.first_name ?? undefined,
          last_name: momProfile?.last_name ?? '',
          assigned_user_id: momProfile?.assigned_user_id ?? undefined,
        },
      },
      setIsSubmitting,
      successCallback: onSuccess,
      setErrorMessage,
    });
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        {buttonComponent ?? (
          <button
            type='button'
            className='my-auto flex items-center justify-center gap-1 self-stretch overflow-hidden rounded-lg border border-solid border-gray-300 bg-white px-3.5 py-2.5 text-slate-700 shadow-sm'
          >
            <FileText className='h-5 w-5' />
            <span className='my-auto self-stretch px-0.5'>Create Session Notes</span>
          </button>
        )}
      </DialogTrigger>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle>Log a Session</DialogTitle>
        </DialogHeader>

        <div className='space-y-4'>
          <FormFactory
            fields={formConfig}
            schema={createSessionFormSchema}
            onSubmit={handleSubmit}
            defaultValues={defaultValues}
            actionButtonsComponent={
              <DialogFooter className='mt-4 gap-4 sm:justify-end'>
                <DialogClose asChild>
                  <Button type='button' variant='secondary'>
                    Cancel
                  </Button>
                </DialogClose>
                <Button type='submit' variant='default' disabled={isSubmitting}>
                  {isSubmitting ? 'Creating...' : 'Create'}
                </Button>
              </DialogFooter>
            }
          />
        </div>
        {errorMessage ? <div className='text-center text-destructive'>{errorMessage}</div> : null}
      </DialogContent>
    </Dialog>
  );
};

export default MomCreateSessionNotesModal;
