'use client';

import { useUserInfo } from '@/context/user-info';
import { notFound } from 'next/navigation';

import MyPeoplePage from '../_components/my-people-page';

const MyPeopleCoordinatorsPage = (): React.ReactNode => {
  const { isInitialized, isAtLeastCoordinator } = useUserInfo();

  // Advocates who do not also have at least coordinator role cannot see this page
  if (isInitialized && !isAtLeastCoordinator) {
    return notFound();
  }

  return <MyPeoplePage activeTab='coordinators' />;
};

export default MyPeopleCoordinatorsPage;
