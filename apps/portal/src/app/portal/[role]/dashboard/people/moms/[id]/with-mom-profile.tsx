'use client';

import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { usePairedMomsListContext } from '@/app/portal/context/paired-moms-context';
import { useEffect } from 'react';

interface WithMomProfileProps {
  momId: string;
  children: React.ReactNode;
}

/*
 * This component is a wrapper around the mom profile page and its tabs
 * that fetches the mom profile if it's not already in the context
 * It needs to be wrapped around any view that is on its own route,
 * which could lose context if the user navigates directly to it or it's "hard refreshed"
 */

const WithMomProfile = ({ momId, children }: WithMomProfileProps): React.ReactNode => {
  const { momProfile, fetchMomProfile } = useMomProfileContext();
  const { fetchPairedMomsList } = usePairedMomsListContext();
  const shouldFetchMomProfile = !momProfile || momProfile.id !== momId;

  useEffect(() => {
    if (shouldFetchMomProfile) {
      void fetchMomProfile(momId);

      // Needed on mom profile to support alt. mom selection in generic modals for scheduling sessions or sending messages
      void fetchPairedMomsList();
    }
  }, [momId, shouldFetchMomProfile, fetchMomProfile, fetchPairedMomsList]);

  if (shouldFetchMomProfile) {
    // TODO: Replace with loading skeleton
    return <div>Loading...</div>;
  }

  return children;
};

export default WithMomProfile;
