'use client';

import MomConnectionLogsTable from '../../../_components/mom-connection-logs-table';
import MomProfileWrapper from '../../../_components/mom-profile-wrapper';
import WithMomProfile from '../with-mom-profile';

const MomConnectionLogsPage = ({ params }: { params: { id: string } }): React.ReactNode => {
  const { id } = params;

  return (
    <WithMomProfile momId={id}>
      <MomProfileWrapper viewId='connection-logs' momId={id}>
        <MomConnectionLogsTable />
      </MomProfileWrapper>
    </WithMomProfile>
  );
};

export default MomConnectionLogsPage;
