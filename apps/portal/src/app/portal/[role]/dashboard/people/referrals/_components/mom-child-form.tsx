import { FormFactoryRef, WellBeingAssessmentFormData } from '@/app/portal/types';
import FormFactory from '@/components/custom/form-factory';
import { FormValues, generateFieldsSchema } from '@/types/form-field';
import { Dispatch, SetStateAction, useEffect, useMemo, useRef } from 'react';

import { momChildFormFields } from './intake-referral-form.config';

const MomChildForm = ({
  index,
  values,
  setFormData,
}: {
  index: number;
  values: FormValues;
  setFormData: Dispatch<SetStateAction<WellBeingAssessmentFormData>>;
}) => {
  const childFormRef = useRef<FormFactoryRef>(null);
  const childFormFieldsSchema = useMemo(() => generateFieldsSchema(momChildFormFields, values), [values]);

  const handleChildFormChange = (values: FormValues) => {
    setFormData((formData) => ({
      ...formData,
      childForms: [
        ...formData.childForms.slice(0, index),
        { ...formData.childForms[index], values },
        ...formData.childForms.slice(index + 1),
      ],
    }));
  };

  useEffect(() => {
    setFormData((formData) => ({
      ...formData,
      childForms: [
        ...formData.childForms.slice(0, index),
        { ...formData.childForms[index], schema: childFormFieldsSchema, ref: childFormRef },
        ...formData.childForms.slice(index + 1),
      ],
    }));
  }, [index, setFormData, childFormFieldsSchema]);

  return (
    <FormFactory
      ref={childFormRef}
      fields={momChildFormFields}
      schema={childFormFieldsSchema}
      defaultValues={values}
      onSubmit={() => {}}
      actionButtonsComponent={null}
      onChange={handleChildFormChange}
    />
  );
};

export default MomChildForm;
