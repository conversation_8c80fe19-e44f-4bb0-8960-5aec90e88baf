import { languageOptions } from '@/lib/constants';

import { CommunicationPreferenceEnum } from '../../admin/_lib/user-management-edit-user-config';
import { timezoneOptions } from '../../settings/_lib/coordinator-basic-info-form.config';

type InputType = string | { value: string } | null | undefined;

export const formatSnakeCaseToTitleCase = (input: InputType, fallback: string = 'None on file'): string => {
  if (!input) return fallback;

  const str = typeof input === 'string' ? input : (input.value ?? '');
  if (!str) return fallback;

  return str
    .split('_')
    .filter(Boolean)
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

// Map each language code to its corresponding option object
export const mapLanguageCodesToOptions = (languageCodes: string[] | null | undefined) => {
  if (!languageCodes) return [];

  return languageCodes.map((code) => languageOptions.find((opt) => opt.value === code)).filter(Boolean);
};

// Convert timezone code to display name
export const formatTimezone = (timezone: string | null | undefined, fallback = '') => {
  if (!timezone) return fallback;

  const timezoneOption = timezoneOptions.find((opt) => opt.value === timezone);
  return timezoneOption?.label || fallback;
};

export const formatAdvocateStatus = (status: string | null | undefined): string => {
  if (!status) return 'None on file';

  switch (status) {
    case 'Applied':
      return 'Application Submitted';
    case 'Interested':
      return 'Expressed Interest';
    case 'Rejected':
      return 'Not Accepted';
    case 'Inactive':
      return 'Inactive';
    case 'Active':
      return 'Active';
    case 'In_Training':
      return 'In Training';
    case 'Awaiting_Pairing':
      return 'Ready for Pairing';
    default:
      return formatSnakeCaseToTitleCase(status);
  }
};

export const formatCommunicationPreference = (preference: string | null): string => {
  if (!preference) return 'None on file';

  switch (preference.toLowerCase()) {
    case CommunicationPreferenceEnum.BOTH:
      return 'Both Text Message and Email';
    case CommunicationPreferenceEnum.TEXT_MESSAGE:
      return 'Text Message';
    case CommunicationPreferenceEnum.EMAIL:
      return 'Email';
    default:
      return formatSnakeCaseToTitleCase(preference);
  }
};
