import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import { Button } from '@/components/ui/button';
import { useCreateNotification, useFindUniqueSessionNote, useUpdateSessionNote } from '@/hooks/generated';
import { useToast } from '@/hooks/use-toast';
import { YupSchemas } from '@suiteapi/models';
import { CheckCircle, XCircle } from 'lucide-react';
import React from 'react';

const SessionNoteStatuses = ({ sessionNoteId }: { sessionNoteId: string }) => {
  const { profile } = useDashboardContext();

  const role = profile?.portalRole;

  const isNotAdvocate = role !== 'advocate';

  const { data: sessionNote, refetch } = useFindUniqueSessionNote({
    where: {
      id: sessionNoteId,
    },
    include: {
      session: {
        select: {
          pairing: {
            select: {
              mom: {
                select: {
                  first_name: true,
                  last_name: true,
                  assigned_user_id: true,
                },
              },
              advocateUser: {
                select: {
                  id: true,
                },
              },
            },
          },
        },
      },
    },
  });

  const status = sessionNote?.status;

  const createNotification = useCreateNotification();
  const updateSessionNote = useUpdateSessionNote();
  const { toast } = useToast();
  const handleUpdateSessionNoteStatus = (newStatus: YupSchemas.SessionNoteStatus) => {
    const dateSubmitted = sessionNote?.date_submitted_c;
    updateSessionNote.mutate(
      {
        where: {
          id: sessionNoteId,
        },
        data: {
          status: newStatus,
          date_submitted_c: newStatus === YupSchemas.SessionNoteStatus.SUBMITTED ? new Date() : dateSubmitted, // update the date_submitted_c if the status is submitted, otherwise maintain the current date_submitted_c
        },
      },
      {
        onError: (error) => {
          console.error(error);
          toast({
            title: 'Error Submitting Session Note',
            description: 'An error occurred while submitting the session note. Please try again later.',
            variant: 'destructive',
          });
        },
        onSuccess: () => {
          refetch();

          if (newStatus === YupSchemas.SessionNoteStatus.SUBMITTED) {
            createNotification.mutate({
              data: {
                status: YupSchemas.NotificationStatus.PENDING,
                template: YupSchemas.NotificationTemplate.SESSION_NOTE_SUBMITTED,
                template_params: {
                  momName: `${sessionNote?.session?.pairing?.mom?.first_name} ${sessionNote?.session?.pairing?.mom?.last_name}`,
                  submitterName: `${profile?.firstName} ${profile?.lastName}`,
                },
                recipient_user: {
                  connect: {
                    id: sessionNote?.session?.pairing?.mom?.assigned_user_id ?? '',
                  },
                },
              },
            });
          }

          if (newStatus === YupSchemas.SessionNoteStatus.REJECTED) {
            createNotification.mutate({
              data: {
                status: YupSchemas.NotificationStatus.PENDING,
                template: YupSchemas.NotificationTemplate.SESSION_NOTE_REJECTED,
                template_params: {
                  momName: `${sessionNote?.session?.pairing?.mom?.first_name} ${sessionNote?.session?.pairing?.mom?.last_name}`,
                  rejecterName: `${profile?.firstName} ${profile?.lastName}`,
                },
                recipient_user: {
                  connect: {
                    id: sessionNote?.session?.pairing?.advocateUser?.id ?? '',
                  },
                },
              },
            });
          }
        },
      },
    );
  };
  return (
    <div className='mb-4 flex w-full justify-end'>
      <div className='flex flex-col'>
        <p className='mb-4 capitalize'>
          <span className='font-semibold'>Session Note Status:</span>{' '}
          <span
            className={`${
              status === YupSchemas.SessionNoteStatus.APPROVED
                ? 'text-green-700'
                : status === YupSchemas.SessionNoteStatus.REJECTED
                  ? 'text-red-500'
                  : status === YupSchemas.SessionNoteStatus.SUBMITTED || status === YupSchemas.SessionNoteStatus.NEW
                    ? 'text-orange-700'
                    : ''
            } capitalize`}
          >
            {status}
          </span>
        </p>
        {isNotAdvocate ? (
          <>
            <div className='flex justify-end gap-2'>
              {status === YupSchemas.SessionNoteStatus.APPROVED ? null : (
                <Button onClick={() => handleUpdateSessionNoteStatus(YupSchemas.SessionNoteStatus.APPROVED)}>
                  <CheckCircle className='mr-2 h-4 w-4' />
                  Approve
                </Button>
              )}
              {status === YupSchemas.SessionNoteStatus.REJECTED ? null : (
                <Button
                  variant='destructive'
                  onClick={() => handleUpdateSessionNoteStatus(YupSchemas.SessionNoteStatus.REJECTED)}
                >
                  <XCircle className='mr-2 h-4 w-4' />
                  Reject
                </Button>
              )}
            </div>
          </>
        ) : (
          // If the status is approved no need to show the button
          <div className='flex justify-end gap-2'>
            {status === YupSchemas.SessionNoteStatus.NEW ? (
              <Button onClick={() => handleUpdateSessionNoteStatus(YupSchemas.SessionNoteStatus.SUBMITTED)}>
                Submit
              </Button>
            ) : null}
            {status === YupSchemas.SessionNoteStatus.REJECTED ? (
              <Button onClick={() => handleUpdateSessionNoteStatus(YupSchemas.SessionNoteStatus.SUBMITTED)}>
                Resubmit
              </Button>
            ) : null}
          </div>
        )}
      </div>
    </div>
  );
};

export default SessionNoteStatuses;
