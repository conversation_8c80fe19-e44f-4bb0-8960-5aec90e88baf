'use client';

import {
  editAdvocateBasicInfoFormSchema,
  editAdvocateBasicInformation,
} from '@/app/portal/lib/advocates/edit-advocate-basic-information.config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { useUpdateUser } from '@/hooks/generated';
import { useToast } from '@/hooks/use-toast';
import { useFindAdvocate } from '@/hooks/useFindAdvocate';
import { updateUserRequest } from '@/lib/updateUserRequest';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import { FieldValues } from 'react-hook-form';

import AdvocateBasicInfoWrapper from '../../../_components/advocates/advocate-basic-info-wrapper';
import AdvocateProfileWrapper from '../../../_components/advocates/advocate-profile-wrapper';

const AdvocateBasicInfoPage = () => {
  const params = useParams();
  const advocateId = params.id as string;
  const { toast } = useToast();
  const { advocate, isLoading } = useFindAdvocate(advocateId);
  const { mutateAsync: updateUser } = useUpdateUser();
  const [isSubmitting, setIsSubmitting] = useState(false);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!advocate) {
    return <div>No data found</div>;
  }

  // Extract field names from the schema to create default values
  const fields = Object.keys(editAdvocateBasicInfoFormSchema.fields);

  // Create default values based on the advocate data
  // Only include fields that exist in the advocate object
  // and are defined in the schema for the form
  // otherwise errors will ensue from extra fields that are not scoped
  const defaultValues = fields.reduce(
    (acc, field) => {
      if (advocate[field as keyof typeof advocate] !== undefined) {
        acc[field] = advocate[field as keyof typeof advocate];
      }
      return acc;
    },
    {} as Record<string, unknown>,
  );

  const handleSubmit = async (data: FieldValues) => {
    setIsSubmitting(true);

    try {
      await updateUser({
        where: { id: advocateId },
        data: updateUserRequest(data),
      });
      toast({
        title: 'Success',
        description: 'Advocate basic information updated successfully.',
      });
      setIsSubmitting(false);
    } catch (error) {
      console.error('Error updating advocate:', error);
      toast({
        title: 'Error',
        description: 'Failed to update advocate information. Please try again.',
        variant: 'destructive',
      });
      setIsSubmitting(false);
    }
  };

  return (
    <AdvocateProfileWrapper viewId='basic-info' advocateId={advocateId}>
      <AdvocateBasicInfoWrapper advocateId={advocateId} currentTab='basic-info'>
        <div className='max-w-[600px] gap-4 rounded-lg border border-emaBorderSecondary bg-white p-4'>
          <FormFactory
            formWrapperClassName='flex flex-col w-full'
            formFieldElClass='w-full'
            fields={editAdvocateBasicInformation}
            schema={editAdvocateBasicInfoFormSchema}
            defaultValues={defaultValues}
            onSubmit={handleSubmit}
            actionButtonsComponent={
              <div className='flex justify-end gap-2'>
                <Button variant='outline'>Cancel</Button>
                <Button variant='default' type='submit' disabled={isSubmitting}>
                  {isSubmitting ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            }
          />
        </div>
      </AdvocateBasicInfoWrapper>
    </AdvocateProfileWrapper>
  );
};

export default AdvocateBasicInfoPage;
