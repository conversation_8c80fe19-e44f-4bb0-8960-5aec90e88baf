import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const frequencyOptions = [
  { label: 'Never', value: 'never' },
  { label: 'Rarely', value: 'rarely' },
  { label: 'Sometimes', value: 'sometimes' },
  { label: 'Often', value: 'often' },
  { label: 'Always', value: 'always' },
];

export const preAssessmentFinancialSecurityFormSchema = yup.object().shape({
  pre_as_fs_unable_to_pay: yup
    .array()
    .of(
      yup.object().shape({
        value: yup
          .string()
          .oneOf(
            [
              'rent_mortgage',
              'utilities',
              'groceries',
              'childcare',
              'medical',
              'household_items',
              'transportation',
              'paid_all',
            ],
            'Invalid financial struggle option',
          ),
        label: yup.string(),
      }),
    )
    .optional(),
  pre_as_fs_past_year: yup
    .array()
    .of(
      yup.object().shape({
        value: yup
          .string()
          .oneOf(
            ['delayed_medical', 'evicted', 'shelter', 'moved_in', 'lost_transportation', 'unemployed', 'none'],
            'Invalid financial struggle option',
          ),
        label: yup.string(),
      }),
    )
    .optional(),
  pre_as_fs_trouble_affording: yup.string().required('Please select an option'),
  pre_as_fs_afford_food: yup.string().required('Please select an option'),
});

export const preAssessmentFinancialSecurityFormConfig: FieldConfig[] = [
  {
    name: 'pre_as_fs_unable_to_pay',
    type: 'multi-select',
    placeholder: 'Select All Applicable',
    label: '1) In the past month, were you unable to pay for:',
    labelDescription: 'Check all that apply',
    options: [
      { label: 'Rent or Mortgage', value: 'rent_mortgage' },
      { label: 'Utilities or bills (electricity, gas/heat, cell phone etc.)', value: 'utilities' },
      { label: 'Groceries/food (including baby formula, diapers)', value: 'groceries' },
      { label: 'Childcare/daycare', value: 'childcare' },
      { label: 'Medicine, medical expenses, or co-pays', value: 'medical' },
      { label: 'Basic household or personal hygiene items', value: 'household_items' },
      { label: 'Transportation (including gas, bus passes, shared rides)', value: 'transportation' },
      { label: 'I was able to pay for all of these', value: 'paid_all' },
    ],
  },
  {
    name: 'pre_as_fs_past_year',
    type: 'multi-select',
    placeholder: 'Select All Applicable',
    label: '2) In the past year, have you:',
    labelDescription: 'Check all that apply',
    options: [
      { label: 'A. Delayed or not gotten medical or dental care', value: 'delayed_medical' },
      { label: 'B. Been evicted from your home or apartment', value: 'evicted' },
      { label: 'C. Live at a shelter, in a hotel/motel, in an abandoned building, or in a vehicle', value: 'shelter' },
      {
        label:
          'D. Moved in with other people, even temporarily, because you could not afford to pay rent/mortgage/bills',
        value: 'moved_in',
      },
      {
        label: 'E. Lost access to your regular transportation (e.g. vehicle totaled or repossessed)',
        value: 'lost_transportation',
      },
      { label: 'F. Been unemployed when you needed & wanted a job', value: 'unemployed' },
      { label: 'G. None of these apply to me', value: 'none' },
    ],
  },
  {
    name: 'pre_as_fs_trouble_affording',
    type: 'select',
    placeholder: 'Select',
    label: '3) I have trouble affording what I need each month.',
    options: frequencyOptions,
  },
  {
    name: 'pre_as_fs_afford_food',
    type: 'select',
    placeholder: 'Select',
    label: '4) I am able to afford the food I want to feed my family.',
    options: frequencyOptions,
  },
];
