'use client';

import { useSupervisorProfileContext } from '@/app/portal/context/supervisor-profile-context';
import { formatPhoneNumber } from '@/lib/utils';
import { format } from 'date-fns';
import { Phone } from 'lucide-react';
import { Mail } from 'lucide-react';
import { CircleUser } from 'lucide-react';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import React from 'react';

import DetailsItem from '../../../_components/details-item';
import SupervisorProfileWrapper from '../../../_components/supervisors/supervisor-profile-wrapper';
import {
  formatCommunicationPreference,
  formatSnakeCaseToTitleCase,
  formatTimezone,
} from '../../../_utils/language-utils';

const SupervisorOverviewPage = () => {
  const params = useParams();
  const supervisorId = params.id as string;
  const { supervisorProfile, fetchSupervisor } = useSupervisorProfileContext();

  React.useEffect(() => {
    if (supervisorId) {
      fetchSupervisor(supervisorId);
    }
  }, []);

  return (
    <SupervisorProfileWrapper viewId='overview'>
      <section className='grid grid-cols-1 gap-8 md:grid-cols-2'>
        <div>
          <DetailsItem label={'First Name'} value={supervisorProfile?.firstName} />
          <DetailsItem label={'Last Name'} value={supervisorProfile?.lastName} />
          <DetailsItem
            label={'Profile Photo'}
            value={
              supervisorProfile?.photoUrl ? (
                <Image
                  src={supervisorProfile?.photoUrl}
                  alt='Profile Image'
                  width={75}
                  height={75}
                  className='h-full w-full rounded-full object-cover'
                  style={{ width: '75px', height: '75px' }}
                />
              ) : (
                <CircleUser />
              )
            }
          />
          <DetailsItem label={'Home Church'} value={supervisorProfile?.home_church ?? 'None on file'} />
          <DetailsItem
            label={'Email'}
            icon={<Mail className='h-4 w-4' />}
            value={supervisorProfile?.email ?? 'None on file'}
          />
          <DetailsItem
            label={'Phone'}
            icon={<Phone className='h-4 w-4' />}
            value={
              supervisorProfile?.phone_mobile ? formatPhoneNumber(supervisorProfile?.phone_mobile) : 'None on file'
            }
          />
          <DetailsItem
            label={'Secondary Phone'}
            icon={<Phone className='h-4 w-4' />}
            value={
              supervisorProfile?.secondary_phone
                ? formatPhoneNumber(supervisorProfile?.secondary_phone)
                : 'None on file'
            }
          />
        </div>
        <div>
          <DetailsItem
            label={'Communication Preference'}
            value={
              supervisorProfile?.communication_preference
                ? formatCommunicationPreference(supervisorProfile?.communication_preference)
                : 'None on file'
            }
          />
          <DetailsItem
            label={'Birthday'}
            value={
              supervisorProfile?.date_of_birth
                ? format(supervisorProfile.date_of_birth, 'MMMM d, yyyy')
                : 'None on file'
            }
          />
          <DetailsItem
            label={'Timezone'}
            value={supervisorProfile?.timezone ? formatTimezone(supervisorProfile?.timezone) : 'None on file'}
          />
          <DetailsItem label={'ĒMA Location'} value={supervisorProfile?.affiliate?.name ?? 'Unassigned'} />
          <DetailsItem label={'Availability'} value={supervisorProfile?.availability ?? 'None on file'} />
          <DetailsItem
            label={'Language Preference'}
            value={formatSnakeCaseToTitleCase(supervisorProfile?.language_preference_c) ?? 'None on file'}
          />
          <DetailsItem
            label={'Languages'}
            value={
              supervisorProfile?.languages_c?.length
                ? supervisorProfile.languages_c.map((language) => formatSnakeCaseToTitleCase(language)).join(', ')
                : 'None on file'
            }
          />
        </div>
      </section>
    </SupervisorProfileWrapper>
  );
};

export default SupervisorOverviewPage;
