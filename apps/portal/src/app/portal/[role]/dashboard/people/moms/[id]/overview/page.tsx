'use client';

import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { MomSessionNotesProvider } from '@/app/portal/context/mom-session-notes-context';
import { useFindFirstAffiliate, useFindFirstPairing } from '@/hooks/generated';
import { useFindManyChild } from '@/hooks/generated/child';
import { Phone, SquareUserRound } from 'lucide-react';
import { useParams } from 'next/navigation';
import React from 'react';

import DetailsItem from '../../../_components/details-item';
import MomFlaggedNeedsSection from '../_components/flagged-needs-section';
import OverviewActivityOverview from '../_components/overview-activity-overview';

const MomOverviewPage = (): React.ReactNode => {
  const { momProfile } = useMomProfileContext();
  const params = useParams();
  const id = params.id as string;

  const { data: pairing } = useFindFirstPairing({
    where: {
      momId: id,
      status: 'paired',
    },
    select: {
      id: true,
      status: true,
      advocateUser: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
        },
      },
    },
  });

  const defaultAdvocateUser = {
    advocateUser: {
      firstName: 'Not',
      lastName: 'Assigned',
      id: 0,
    },
  };

  const { advocateUser: advocate } = pairing || defaultAdvocateUser;
  let affiliate;

  if (momProfile?.affiliate_id) {
    const result = useFindFirstAffiliate({
      where: {
        id: momProfile?.affiliate_id,
      },
    });
    affiliate = result.data;
  }

  // Fetch children for this mom
  const { data: children = [] } = useFindManyChild({
    where: {
      mom_id: id,
      deleted_at: 0,
    },
  });

  return (
    <MomSessionNotesProvider>
      <div className='flex gap-4 max-md:flex-col'>
        <div className='flex min-w-[240px] max-w-[280px] flex-col'>
          <div className='mt-4'>
            {/* TODO: Update Ema location and advocate and children to moms page overview */}

            <DetailsItem
              label={'ĒMA Location: '}
              value={affiliate?.name ?? 'Not assigned'}
              tooltip='Need to transfer to another location? Discuss with your affiliate support manager for transfer options.'
            />
            <DetailsItem
              icon={<SquareUserRound className='h-5 w-5' />}
              label={'Advocate'}
              value={`${advocate?.firstName} ${advocate?.lastName}`}
            />
            {momProfile?.phone_other ? (
              <DetailsItem
                icon={<Phone className='h-5 w-5' />}
                label='Phone Number'
                value={String(momProfile.phone_other)}
              />
            ) : null}
            {momProfile?.email1 ? <DetailsItem label={'Email'} value={String(momProfile.email1)} /> : null}
            <DetailsItem
              label={'Children'}
              value={
                <div>
                  {children.length > 0 ? (
                    children.map((child: { id: string; first_name: string; birthdate: Date | null }) => (
                      <p key={child.id}>
                        {child.first_name}
                        {child.birthdate
                          ? `, age ${Math.floor((Date.now() - child.birthdate.getTime()) / (1000 * 60 * 60 * 24 * 365))}`
                          : ''}
                      </p>
                    ))
                  ) : (
                    <p>No children added yet</p>
                  )}
                </div>
              }
            />
          </div>
          <MomFlaggedNeedsSection />
        </div>

        <OverviewActivityOverview />
      </div>
    </MomSessionNotesProvider>
  );
};

export default MomOverviewPage;
