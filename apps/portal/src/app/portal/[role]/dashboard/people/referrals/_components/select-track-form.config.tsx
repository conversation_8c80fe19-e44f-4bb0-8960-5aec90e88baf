import { StandardFieldConfig } from '@/types/form-field';
import * as yup from 'yup';

import { Track } from '../../../../../../../hooks/generated/__types';

export const getSelectTrackFormFields = (tracks: Track[]): StandardFieldConfig[] => [
  {
    name: 'track_id',
    label: 'Track',
    labelDescription: 'Select a track',
    type: 'select',
    placeholder: 'Select',
    options: tracks.map((track) => ({ label: track.title, value: track.id })),
    schema: yup
      .string()
      .required()
      .oneOf(tracks.map((track) => track.id)),
  },
];
