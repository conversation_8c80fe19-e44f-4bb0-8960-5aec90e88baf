import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { getBenevolenceNeedsFormFields } from '@/app/portal/lib/benevolence-need-drawer-schema.config';
import { FormFactoryRef } from '@/app/portal/types';
import FormFactory from '@/components/custom/form-factory';
import ResponsiveDrawer from '@/components/custom/responsive-drawer';
import { Button } from '@/components/ui/button';
import { useUpdateBenevolenceNeed } from '@/hooks/generated/benevolence-need';
import { useToast } from '@/hooks/use-toast';
import { trueFalseValueToYesNoLabel } from '@/lib/utils';
import { FormValues } from '@/types/form-field';
import { formatDateAndTime } from '@suiteapi/models';
import { YupSchemas } from '@suiteapi/models';
import { AlertCircle } from 'lucide-react';
import React from 'react';

import BenevolenceNeedReferralModal from './benevolence-need-referral-modal';
import ServiceReferralSection from './service-referral-section';

interface BenevolenceNeedDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  entry: YupSchemas.BenevolenceSchema | null;
}

const BenevolenceNeedDrawer = ({ isOpen, onClose, entry }: BenevolenceNeedDrawerProps) => {
  const { toast } = useToast();
  const { momProfile } = useMomProfileContext();
  const { profile } = useDashboardContext();
  const updateNeedMutation = useUpdateBenevolenceNeed();
  const formRef = React.useRef<FormFactoryRef>(null);
  const isNeedComplete = entry?.resolved_date_c ? true : false;
  const [isReferralNeeded, setIsReferralNeeded] = React.useState(entry?.other_is_referral_needed_c === true);
  const [hasRelatedReferrals, setHasRelatedReferrals] = React.useState(false);

  // Update isReferralNeeded when entry changes
  React.useEffect(() => {
    setIsReferralNeeded(entry?.other_is_referral_needed_c === true);
  }, [entry?.other_is_referral_needed_c]);

  React.useEffect(() => {
    const form = formRef.current?.form;
    if (!form || !isOpen) return; // Add isOpen check to prevent multiple rerenders

    // registration to when the drawer opens
    form.register('provided_date_c', {
      onChange: () => {
        const value = form.getValues('provided_date_c');
        if (value) {
          form.clearErrors('provided_date_c');
        }
      },
    });
  }, [isOpen]);

  const saveNeedChanges = async (formData: FormValues): Promise<void> => {
    try {
      const formattedData = {
        ...formData,
        provided_date_c: formData.provided_date_c
          ? new Date(formData.provided_date_c as string).toISOString()
          : formData.provided_date_c,
      };

      // If momId or mom came through in the formData, remove them.
      // We don't need to update them and they're breaking the update statement beow.
      const { mom, momId, ...dataToUpdate } = formattedData as {
        mom?: unknown;
        momId?: string;
        [key: string]: unknown;
      };

      await updateNeedMutation.mutateAsync({
        where: { id: entry?.id as string },
        data: {
          ...dataToUpdate,
          is_urgent_c: entry?.is_urgent_c ?? undefined,
          provided_date_c: dataToUpdate.provided_date_c as string,
        },
      });

      toast({
        title: 'Success',
        description: 'The need changes have been successfully saved',
        variant: 'success',
      });

      // Close the drawer after successful save
      onClose();
    } catch (error) {
      console.error('Error saving need changes:', error);
      toast({
        title: 'Error',
        description: 'There was an error saving the need changes. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleMarkComplete = async (formData: FormValues | undefined) => {
    const form = formRef.current?.form;
    if (!form) return;

    // Specifically validate just the provided_date_c field
    const formValues = form.getValues();
    if (!formValues.provided_date_c) {
      form.setError('provided_date_c', {
        type: 'required',
        message: 'Please select when the need was provided',
      });
      return;
    } else {
      // Explicitly clear the error when we have a valid date
      form.clearErrors('provided_date_c');
    }

    const formattedData = {
      ...formData,
      resolved_date_c: new Date().toISOString(),
    };

    // If momId or mom came through in the formData, remove them.
    // We don't need to update them and they're breaking the update statement beow.
    const { mom, momId, ...dataToUpdate } = formattedData as { mom?: unknown; momId?: string; [key: string]: unknown };

    try {
      await updateNeedMutation.mutateAsync({
        where: { id: entry?.id as string },
        data: {
          ...dataToUpdate,
        },
      });

      toast({
        title: 'Success',
        description: 'The need has been successfully marked as complete',
        variant: 'success',
      });

      // Close the drawer after successful completion
      onClose();
    } catch (error) {
      console.error('Error marking need as complete:', error);
      toast({
        title: 'Error',
        description: 'There was an error marking the need as complete. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleFormChange = (values: FormValues) => {
    const form = formRef.current?.form;
    if (!form) return;

    if (values.provided_date_c) {
      form.clearErrors('provided_date_c');
    }
    // Update isReferralNeeded when the form value changes
    if ('other_is_referral_needed_c' in values) {
      setIsReferralNeeded(values.other_is_referral_needed_c === true);
    }
  };

  if (!entry) return null;

  return (
    <ResponsiveDrawer
      onClose={onClose}
      isOpen={isOpen}
      title={<BenevolenceDrawerTile mom={`${momProfile?.first_name} ${momProfile?.last_name}`} />}
    >
      <div className='mt-5 flex flex-col gap-5 border-t px-6 pt-5 text-sm text-emaTextSecondary'>
        <p className='text-sm text-emaTextQuaternary'>
          Added: {entry.created_at ? formatDateAndTime(entry.created_at) : 'date not available'}
        </p>
        <div>
          <p className='font-semibold text-emaTextSecondary'>Need Type: </p>
          <p>{entry.type_c}</p>
        </div>
        <div>
          <p className='font-semibold'>Is it urgent? </p>
          <p>{trueFalseValueToYesNoLabel(entry.is_urgent_c ?? undefined)}</p>
        </div>
        <div>
          <p className='font-semibold'>Context for Need: </p>
          <p>{entry.description}</p>
        </div>

        {/* Service Referral Section */}
        {isOpen && momProfile?.id && entry?.created_at && (
          <ServiceReferralSection
            momId={momProfile.id}
            needCreationDate={new Date(entry.created_at)}
            portalRole={profile?.portalRole}
            onHasReferrals={setHasRelatedReferrals}
          />
        )}
      </div>
      <div className='px-6'>
        <FormFactory
          ref={formRef}
          fields={getBenevolenceNeedsFormFields(
            entry.type_c ?? YupSchemas.BenevolenceNeedsType.Financial.value,
            isNeedComplete,
          )}
          schema={YupSchemas.getBenevolenceFormSchema(entry.type_c ?? YupSchemas.BenevolenceNeedsType.Financial.value)}
          onSubmit={saveNeedChanges}
          formWrapperClassName='flex-col'
          formFieldElClass='w-full'
          defaultValues={entry}
          actionButtonsComponent={
            <div className='mt-2 flex flex-wrap justify-end gap-2 border-t pb-5 pt-4'>
              {isNeedComplete ? (
                <Button type='button' variant='outline' onClick={onClose}>
                  Close
                </Button>
              ) : (
                <>
                  <Button type='submit' variant='outline' className='mr-2'>
                    Save
                  </Button>
                  <Button
                    type='button'
                    onClick={() => handleMarkComplete(formRef.current?.form.getValues())}
                    className='bg-darkGreen'
                  >
                    Mark Need as Completed
                  </Button>
                  {isReferralNeeded && !hasRelatedReferrals ? <BenevolenceNeedReferralModal /> : null}
                </>
              )}
            </div>
          }
          onChange={handleFormChange}
        />
      </div>
    </ResponsiveDrawer>
  );
};

export default BenevolenceNeedDrawer;

const BenevolenceDrawerTile = ({ mom }: { mom: string | undefined }) => (
  <div className='ml-4'>
    <div className='flex flex-row'>
      <div className='mr-4 h-fit rounded-lg border-2 border-emaBorderSecondary p-2 shadow'>
        <AlertCircle />
      </div>
      <div>
        <p className='mb-1 text-xl'>Flagged Need</p>
        <p className='text-sm font-light'>Mom: {mom}</p>
      </div>
    </div>
  </div>
);
