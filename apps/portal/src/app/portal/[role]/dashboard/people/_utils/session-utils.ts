import type { MomActionPlanGoal, MostRecentAndUpcomingSessions } from '@/app/portal/types';
import type { ReadOnlyModuleDataProps } from '@/components/custom/read-only-module-data/types';
import { decodeHtmlEntities, generateCalendarEventFile, generateCalendarEventMappings } from '@/lib/utils';
import { LessonSchema } from '@/types/schemas/lesson';
import type { PairingResponseSchema } from '@/types/schemas/pairing';
import { type SessionSchema, SessionType } from '@/types/schemas/session';
import { formatDateFromString } from '@suiteapi/models';
import { YupSchemas } from '@suiteapi/models';

export const findCurrentPairingByClientId = (
  clientId: string | undefined,
  pairedMomsList: PairingResponseSchema[],
): PairingResponseSchema | undefined => {
  if (!clientId) {
    return undefined;
  }

  return pairedMomsList?.find((pairing) => pairing.mom?.id === clientId);
};

export const handleDownloadCalendarFile = async (
  data: SessionSchema | YupSchemas.ReferralSessionDataType,
  momName: string,
  title?: string,
): Promise<void> => {
  const startDateTime = new Date(String(data.date_start));
  const endDateTime = new Date(String(data.date_end));
  const eventData = generateCalendarEventMappings(startDateTime, endDateTime, {
    title: `${title ? title : 'Session with'} ${momName}`,
    description: SessionType[data.session_type as keyof typeof SessionType] || 'Unknown Session',
    ...(data.location ? { location: decodeHtmlEntities(data.location as string) } : {}),
    ...(data.join_url ? { url: data.join_url } : {}),
  });

  const formattedDate = formatDateFromString(String(data.date_start), 'yyyy-MM-dd');
  const fileName = `Session-${(momName as string).replace(' ', '-')}-${formattedDate}.ics`;
  const eventFileUrl = await generateCalendarEventFile(fileName, eventData);
  const link = document.createElement('a');
  link.href = eventFileUrl;
  link.download = fileName;

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(eventFileUrl);
};

export const findMostRecentAndUpcomingSessions = (events: SessionSchema[]): MostRecentAndUpcomingSessions => {
  const now = new Date();

  return events.reduce(
    (result, event) => {
      const { date_start } = event;
      const startDate = new Date(String(date_start));

      if (isNaN(startDate.getTime())) {
        return result;
      }

      if (startDate < now) {
        const mostRecentStartDate = new Date(result.mostRecentPast?.date_start || 0);
        // Update the most recent past event if it's closer to now
        if (!result.mostRecentPast || startDate > mostRecentStartDate) {
          result.mostRecentPast = event;
        }
      } else {
        const nextUpcomingStartDate = new Date(result.nextUpcoming?.date_start || Infinity);
        // Update the next upcoming event if it's closer to now
        if (!result.nextUpcoming || startDate < nextUpcomingStartDate) {
          result.nextUpcoming = event;
        }
      }

      return result;
    },
    {
      mostRecentPast: undefined as SessionSchema | undefined,
      nextUpcoming: undefined as SessionSchema | undefined,
    },
  );
};

export const getMomIdToPairingIdMapping = (pairedMomsList: PairingResponseSchema[] | null): Record<string, string> => {
  if (!pairedMomsList) {
    return {};
  }

  return (
    pairedMomsList.reduce(
      (acc, pairing) => {
        if (pairing.mom?.id) {
          acc[pairing.mom?.id as string] = pairing.id as string;
        }
        return acc;
      },
      {} as Record<string, string>,
    ) || {}
  );
};

/**
 * Get the `id` of the lesson with the lowest defined `order_number` value.
 * @param lessons - Array of lessons
 * @returns The id of the lesson with the lowest `order_number` value
 * or the first lesson if `order_number` is not defined
 * or `undefined` if array is empty
 * @example
 * const lessons = [
 * { id: 'lesson-3', order_number: 3, ...otherLessonProps },
 * { id: 'lesson-1', order_number: 1 ...otherLessonProps },
 * { id: 'lesson-2', order_number: 2 ...otherLessonProps },
 * ];
 * getNextLessonId(lessons); // should return 'lesson-1'
 */
export const getNextLessonId = (lessons: LessonSchema[]): string | undefined => {
  if (lessons.length === 0) {
    return undefined;
  }

  const nextLessonIndex = lessons.reduce((acc, lesson, index) => {
    const currentOrderNumber = lesson.order;

    if (index === 0 || !currentOrderNumber) {
      return acc;
    }

    const lowestOrderNumber = lessons[acc].order;

    if (!lowestOrderNumber) {
      return index;
    }

    return currentOrderNumber < lowestOrderNumber ? index : acc;
  }, 0);

  return lessons[nextLessonIndex].id;
};

export const buildReadOnlySessionNotesData = (
  actionPlanGoals: MomActionPlanGoal[] | null,
  sessionReport:
    | {
        id: string;
        lesson_id: string;
        session_id: string;
        lesson_status: 'not_started';
        attendance_and_promptness: '' | 'On_Time' | 'Late' | 'No_Show';
        moms_engagement_c: '' | 'Full' | 'Partial' | 'None';
        new_attempt: boolean;
        new_attempt_example: string;
        note: string;
        created_by_name: string;
        client_id_c: string;
        contact_id_c: string;
        date_submitted_c: string;
        covered_lesson_id: string;
        status_c: 'Submitted' | 'Not_Submitted' | 'Rejected' | 'Approved';
      }
    | undefined,
  sessionMeetingData: SessionSchema | undefined,
): ReadOnlyModuleDataProps => {
  const placeholder = '---';

  const thisWeeksSession = [
    {
      label: 'Overall Session Notes',
      value: decodeHtmlEntities(sessionReport?.note ? String(sessionReport.note) : undefined) || placeholder,
    },
    {
      label: 'Current lesson',
      value: sessionMeetingData?.session_note?.covered_lesson?.title
        ? String(sessionMeetingData.session_note.covered_lesson.title)
        : placeholder,
    },
    {
      label: 'Lesson is:',
      value: sessionMeetingData?.status ? String(sessionMeetingData.status) : placeholder,
    },
    {
      label: "Mom's engagement with the lesson:",
      value: sessionReport?.moms_engagement_c ? String(sessionReport.moms_engagement_c) : placeholder,
    },
    {
      label: 'Are there any notes, observations, or concerns that you would like to share?',
      value:
        decodeHtmlEntities(
          sessionReport?.new_attempt_example ? String(sessionReport.new_attempt_example) : undefined,
        ) || placeholder,
    },
    {
      label: 'Attendance & Promptness:',
      value: sessionReport?.attendance_and_promptness
        ? String(sessionReport.attendance_and_promptness).replaceAll('_', ' ')
        : placeholder,
    },
  ];

  const completedGoals = actionPlanGoals?.filter((goal) => goal.actionItems.every((item) => item.doneDate)) || [];
  const incompleteGoals = actionPlanGoals?.filter((goal) => !completedGoals.includes(goal)) || [];

  const goalTasks =
    actionPlanGoals?.reduce(
      (acc, goal) => {
        return [...acc, ...goal.actionItems];
      },
      [] as MomActionPlanGoal['actionItems'],
    ) || [];

  const completedTasks = goalTasks.filter((task) => task.doneDate);
  const incompleteTasks = goalTasks.filter((task) => !completedTasks.includes(task));

  const actionPlanUpdates = [
    {
      label: 'Goals Created:',
      value: (incompleteGoals?.length && incompleteGoals.map((goal) => goal.description)) || placeholder,
    },
    {
      label: 'Goals Completed:',
      value: (completedGoals?.length && completedGoals.map((goal) => goal.description)) || placeholder,
    },
    {
      label: 'Tasks Created:',
      value: (incompleteTasks?.length && incompleteTasks.map((task) => task.name)) || placeholder,
    },
    {
      label: 'Tasks Completed:',
      value: (completedTasks?.length && completedTasks.map((task) => task.name)) || placeholder,
    },
  ];

  const scheduleNextSession = [
    {
      label: 'Date:',
      value: sessionMeetingData?.date_start
        ? formatDateFromString(sessionMeetingData?.date_start, 'MMM d, yyyy h:mm a')
        : placeholder,
    },
    {
      label: 'Meeting Type:',
      value: sessionMeetingData?.session_type
        ? String(sessionMeetingData.session_type).replaceAll('_', ' ')
        : placeholder,
    },
    {
      label: 'Meeting Link:',
      value: sessionMeetingData?.join_url ? String(sessionMeetingData.join_url) : placeholder,
    },
  ];

  const { first_name = '', last_name = '' } = sessionMeetingData?.pairing?.mom || {};
  const momName = `${first_name} ${last_name}`.trim() || '(Name not available)';
  const formattedSessionDate = sessionMeetingData?.date_start
    ? formatDateFromString(sessionMeetingData.date_start, 'MMM d, yyyy')
    : '(Date not available)';
  const sessionType = sessionMeetingData?.session_type
    ? String(sessionMeetingData.session_type).replaceAll('_', ' ')
    : 'Session';

  return {
    headerText: `${sessionType} with ${momName} - ${formattedSessionDate}`,
    title: 'Session Notes',
    sections: [
      {
        sectionTitle: 'Action Plan Updates',
        data: actionPlanUpdates,
      },
      {
        sectionTitle: "This Week's Session",
        data: thisWeeksSession,
      },
      {
        sectionTitle: 'Schedule Next Session',
        data: scheduleNextSession,
      },
    ],
  };
};

export const openExportPage = (momId: string, sessionId?: string) => {
  // If there is no sessionID, the whole list of notes will be loaded
  const printUrl = `/portal/export/session-notes/?momId=${momId}${sessionId ? `&sessionId=${sessionId}` : ''}`;
  window.open(printUrl, '_blank'); // Open the print/PDF export page in a new tab
};
