'use client';

import { SupervisorProfileProvider } from '@/app/portal/context/supervisor-profile-context';
import { useParams } from 'next/navigation';
import React from 'react';

const Layout = ({ children }: { children: React.ReactNode }) => {
  const { id } = useParams<{ id: string }>();
  return <SupervisorProfileProvider supervisorId={id}>{children}</SupervisorProfileProvider>;
};

export default Layout;
