import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { LessonSchema } from '@/types/schemas/lesson';
import { useState } from 'react';

interface ChangeLessonModalProps {
  lessons: LessonSchema[];
  onLessonChange: (lesson: LessonSchema) => void;
}

const ChangeLessonModal: React.FC<ChangeLessonModalProps> = ({ lessons, onLessonChange }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleLessonSelect = (lesson: LessonSchema) => {
    onLessonChange(lesson);
    setIsModalOpen(false);
  };

  return (
    <div>
      <Button
        type='button'
        variant='ghost'
        className='text-sm text-emaTextSecondary underline'
        onClick={() => setIsModalOpen(true)}
      >
        Change Lesson
      </Button>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className='max-w-md'>
          <DialogHeader>
            <DialogTitle>Select a Lesson</DialogTitle>
          </DialogHeader>
          <ul>
            {lessons
              .sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
              .map((lesson) => (
                <li key={lesson.id} className='flex justify-between p-2'>
                  <span>{lesson.title}</span>
                  <Button variant='outline' onClick={() => handleLessonSelect(lesson)}>
                    Select
                  </Button>
                </li>
              ))}
          </ul>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ChangeLessonModal;
