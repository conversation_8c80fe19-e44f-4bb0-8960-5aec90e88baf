'use client';

import { useAdvocateProfileContext } from '@/app/portal/context/advocate-profile-context';
import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import GenericProfileHeader, { DEFAULT_AVATAR } from '@/components/custom/generic-profile-header';
import { useRouter } from 'next/navigation';

import PersonActionTabs from '../person-action-tabs';

interface AdvocateProfileWrapperProps {
  advocateId: string;
  viewId: string;
}

const AdvocateProfileWrapper = ({
  advocateId,
  viewId,
  children,
}: React.PropsWithChildren<AdvocateProfileWrapperProps>): React.ReactNode => {
  const router = useRouter();
  const { profile } = useDashboardContext();
  const { advocateProfile } = useAdvocateProfileContext();

  const navigateToTab = (tab: string): void => {
    const tabRoute = tab === 'overview' ? '/' : `/${tab}`;
    router.push(`/portal/${profile.portalRole}/dashboard/people/advocates/${advocateId}${tabRoute}`);
  };
  const ADVOCATE_PROFILE_TABS = [
    { label: 'Overview', value: 'overview' },
    { label: 'Basic Info', value: 'basic-info' },
    { label: 'Coordinator Notes', value: 'coordinator-notes' },
    { label: 'Interview', value: 'interview' },
    { label: 'Documents', value: 'documents' },
    { label: 'Connection Log', value: 'connection-log' },
    { label: 'Training', value: 'training' },
  ];
  return (
    <div className='flex flex-col overflow-hidden pb-12'>
      <GenericProfileHeader
        photoUrl={advocateProfile?.photoUrl || DEFAULT_AVATAR}
        firstName={advocateProfile?.firstName ?? ''}
        lastName={advocateProfile?.lastName ?? ''}
        role='Advocate'
        breadcrumbItems={[
          { label: 'People', href: '/people' },
          { label: 'Advocates', href: '/people/advocates' },
        ]}
      />
      <main className='mt-8 flex w-full flex-col max-md:max-w-full'>
        <div className='flex w-full flex-col px-8 max-md:max-w-full max-md:px-5 max-sm:px-1'>
          <PersonActionTabs items={ADVOCATE_PROFILE_TABS} activeTab={viewId} onTabChange={navigateToTab} />
          {children}
        </div>
      </main>
    </div>
  );
};

export default AdvocateProfileWrapper;
