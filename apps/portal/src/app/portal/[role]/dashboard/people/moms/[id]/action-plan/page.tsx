'use client';

import React from 'react';

import MomProfileWrapper from '../../../_components/mom-profile-wrapper';
import ActionPlans from '../_components/action-plan';
import WithMomProfile from '../with-mom-profile';

const MomActionPlanPage = ({ params }: { params: { id: string } }): React.ReactNode => {
  const { id } = params;

  return (
    <WithMomProfile momId={id}>
      <MomProfileWrapper viewId='action-plan' momId={id}>
        <ActionPlans momId={id} />
      </MomProfileWrapper>
    </WithMomProfile>
  );
};

export default MomActionPlanPage;
