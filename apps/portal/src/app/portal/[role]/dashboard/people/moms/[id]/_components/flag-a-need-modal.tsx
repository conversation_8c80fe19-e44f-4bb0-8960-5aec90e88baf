'use client';

import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import {
  createBenevolenceNeedFormFields,
  createBenevolenceNeedFormSchema,
} from '@/app/portal/lib/create-benevolence-need.config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { BenevolenceNeedType } from '@/hooks/generated/__types';
import { useCreateBenevolenceNeed } from '@/hooks/generated/benevolence-need';
import { useCreateNotification } from '@/hooks/generated/notification';
import type { FormValues } from '@/types/form-field';
import { YupSchemas } from '@suiteapi/models';
import { CircleAlert } from 'lucide-react';
import { useState } from 'react';

interface CreateFlaggedNeedModalProps {
  momId?: string;
  successCallback?: () => void;
  buttonComponent?: React.ReactNode;
  open?: boolean;
  onClose?: () => void;
}

const CreateFlaggedNeedModal: React.FC<CreateFlaggedNeedModalProps> = ({
  momId,
  successCallback,
  buttonComponent,
  open,
  onClose,
}) => {
  // open state managed by parent is needed to support opening modal from dropdown menu item
  // (normally/ideally, component can manage its own open state if open trigger is a static button on parent)
  const externallyControlledOpenState = typeof open === 'boolean' && typeof onClose === 'function';
  const externalOpenProps = externallyControlledOpenState ? { open } : {};
  const { momProfile } = useMomProfileContext();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [flaggedNeedData, setFlaggedNeedData] = useState<YupSchemas.CreateBenevolenceSchema | null>(null);

  const createBenevolenceNeed = useCreateBenevolenceNeed();
  const createNotification = useCreateNotification();

  const handleCloseAndClearForm = (): void => {
    if (externallyControlledOpenState) {
      onClose?.();
    }

    // prevent brief impression of form view when closing modal after success screen
    setTimeout(() => {
      setIsSuccess(false);
      setFlaggedNeedData(null);
    }, 200);
  };

  const handleSubmit = async (data: FormValues): Promise<void> => {
    setIsSubmitting(true);
    setIsSuccess(false);

    if (!momId) return;

    try {
      const flaggedNeed = await createBenevolenceNeed.mutateAsync(
        {
          data: {
            type_c: String(data.type_c) as BenevolenceNeedType,
            name: `${momProfile?.first_name} ${momProfile?.last_name}`,
            is_urgent_c: Boolean(data.is_urgent_c),
            description: String(data.description),
            mom: {
              connect: {
                id: momId,
              },
            },
          },
        },
        {
          onSuccess: () => {
            createNotification.mutate({
              data: {
                status: YupSchemas.NotificationStatus.PENDING,
                template: YupSchemas.NotificationTemplate.BENEVOLENCE_NEED_SUBMITTED,
                template_params: {
                  momName: `${momProfile?.first_name} ${momProfile?.last_name}`,
                  needUrgency: data.is_urgent_c ? 'urgent' : 'general',
                  needType: String(data.type_c),
                  needSummary: String(data.description),
                },
                recipient_user: {
                  connect: {
                    id: momProfile?.assigned_user_id ?? '',
                  },
                },
              },
            });
            successCallback?.();
            setIsSuccess(true);
          },
        },
      );

      const transformedNeed = flaggedNeed
        ? {
            ...flaggedNeed,
            client_id: momId,
            mom_name: `${momProfile?.first_name} ${momProfile?.last_name}`,
            created_at: flaggedNeed.created_at?.toISOString(),
          }
        : null;

      setFlaggedNeedData(transformedNeed);
    } catch (error) {
      console.error('Error creating flagged need:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog onOpenChange={handleCloseAndClearForm} {...externalOpenProps}>
      <DialogTrigger asChild>
        {externallyControlledOpenState
          ? null
          : (buttonComponent ?? (
              <Button type='button' variant='outline' className='mt-6 max-w-fit gap-3'>
                <CircleAlert className='h-5 w-5' />
                <span className='my-auto self-stretch'>Flag a Need</span>
              </Button>
            ))}
      </DialogTrigger>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle>Create Need</DialogTitle>
          <DialogDescription>{isSuccess ? 'Need created!' : 'Flag a Need'}</DialogDescription>
        </DialogHeader>
        {!isSuccess && (
          <div className='space-y-4'>
            <FormFactory
              fields={createBenevolenceNeedFormFields}
              schema={createBenevolenceNeedFormSchema}
              onSubmit={handleSubmit}
              actionButtonsComponent={
                <DialogFooter className='mt-4 sm:justify-end'>
                  <DialogClose asChild>
                    <Button type='button' variant='secondary'>
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button type='submit' variant='default' disabled={isSubmitting}>
                    {isSubmitting ? 'Saving...' : 'Save'}
                  </Button>
                </DialogFooter>
              }
            />
          </div>
        )}
        {isSuccess && flaggedNeedData ? (
          <>
            <div>Need was successfully flagged.</div>
            <div>Need Type: {flaggedNeedData.type_c}</div>
            <div>Is Urgent: {flaggedNeedData.is_urgent_c ? 'Yes' : 'No'}</div>
            <div>Context for need: {flaggedNeedData.description}</div>
            <DialogFooter className='mt-4 sm:justify-end'>
              <DialogClose asChild>
                <Button type='button' variant='secondary'>
                  Done
                </Button>
              </DialogClose>
            </DialogFooter>
          </>
        ) : null}
      </DialogContent>
    </Dialog>
  );
};

export default CreateFlaggedNeedModal;
