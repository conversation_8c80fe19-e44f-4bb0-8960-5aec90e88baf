import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import ResponsiveDrawer from '@/components/custom/responsive-drawer';
import renderIconForType from '@/components/utils/renderIconForType';
import { decodeHtmlEntities } from '@/lib/utils';
import { formatDateFromString } from '@suiteapi/models';
import { YupSchemas } from '@suiteapi/models';
import { ReactNode } from 'react';

import { isUserAdvocateOnly } from '../_utils/people-utils';

interface MomConnectionLogDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  entry: YupSchemas.ConnectionLogSchema | null;
  contactMethodMappings: { [key: string]: string };
}

const MomConnectionLogDrawer: React.FC<MomConnectionLogDrawerProps> = ({
  isOpen,
  onClose,
  entry,
  contactMethodMappings,
}) => {
  const logType = entry ? contactMethodMappings[entry.contact_method_c] : '';
  const icon = entry ? renderIconForType(entry.contact_method_c) : null;
  const date = entry?.date_created_c?.toLocaleDateString() || '';
  const mom = entry?.name || '';
  const summary = decodeHtmlEntities(entry?.summary_c);
  const contactedBy = entry?.created_by_name || '';
  const isVisibleToAdvocates = entry?.is_visible_to_advocates_c ? 'Yes' : 'No';
  const added = entry ? formatDateFromString(new Date(entry?.date_created_c).toISOString()) : 'date not available';
  const { profile } = useDashboardContext();
  const isAdvocateOnly = isUserAdvocateOnly(profile?.roles);

  return (
    <ResponsiveDrawer
      isOpen={isOpen}
      onClose={onClose}
      title={<DrawerTitle type={logType} icon={icon} date={date} mom={mom} />}
    >
      <div className='mt-2 border-t border-emaBgQuaternary p-6'>
        <p className='mb-2 font-semibold'>Summary</p>
        <p className='mb-4'>{summary}</p>
        <div className='flex flex-col gap-1 text-sm text-emaTextQuaternary'>
          <p>Contacted by: {contactedBy}</p>
          {!isAdvocateOnly ? <p>Visible to Advocates: {isVisibleToAdvocates}</p> : null}
          <p>Added: {added}</p>
        </div>
      </div>
    </ResponsiveDrawer>
  );
};

export default MomConnectionLogDrawer;

interface DrawerTitleProps {
  type: string;
  icon: ReactNode;
  date: string;
  mom: string;
}

const DrawerTitle = (props: DrawerTitleProps) => {
  const { type, icon, date, mom } = props;
  const formattedDate = date ? formatDateFromString(new Date(date).toISOString()) : '';

  return (
    <div>
      <div className='flex flex-row'>
        <div>
          <div className='flex items-center rounded border border-emaBgQuaternary p-2'> {icon}</div>
        </div>
        <div className='ml-4 flex flex-col'>
          <p className='mb-2 text-xl font-semibold'>
            {type} Contact{formattedDate ? ',' : ''} {formattedDate}
          </p>
          <p className='text-left text-sm font-light'>Mom: {mom}</p>
        </div>
      </div>
    </div>
  );
};
