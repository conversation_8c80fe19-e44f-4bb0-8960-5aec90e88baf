import { sfetch } from '@/lib/sfetch';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import Image from 'next/image';
import { useEffect, useState } from 'react';

interface MomNameImageHeaderProps {
  momId: string;
  photoUrl?: string;
  name?: string;
}

export function MomNameImageHeader({ momId, photoUrl, name }: MomNameImageHeaderProps) {
  const [showLoadingState, setShowLoadingState] = useState(false);
  const [loadedImage, setLoadedImage] = useState<string | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const queryClient = useQueryClient();

  // Listen for photo-updated events
  useEffect(() => {
    const handlePhotoUpdated = (event: Event) => {
      // Check if this is a CustomEvent with detail
      const customEvent = event as CustomEvent<{ entityType: string; entityId: string }>;
      if (customEvent.detail) {
        const { entityType, entityId } = customEvent.detail;

        // Only process events for mom entities related to this component
        if (entityType === 'mom' && momId === entityId) {
          // Invalidate the query cache for this image
          queryClient.invalidateQueries({ queryKey: ['mom-photo', momId] });

          // Clear the loaded image to show loading state
          setLoadedImage(null);

          // Force component to re-render
          setRefreshKey((prev) => prev + 1);
        }
      }
    };

    window.addEventListener('photo-updated', handlePhotoUpdated);
    return () => window.removeEventListener('photo-updated', handlePhotoUpdated);
  }, [momId, queryClient]);

  // Use a short delay before showing loading state to prevent flickering
  useEffect(() => {
    let timeout: NodeJS.Timeout;
    if (photoUrl && !loadedImage) {
      // Only show loading state after a short delay to prevent quick flashes
      timeout = setTimeout(() => {
        setShowLoadingState(true);
      }, 100);
    } else {
      setShowLoadingState(false);
    }
    return () => clearTimeout(timeout);
  }, [photoUrl, loadedImage]);

  useQuery({
    queryKey: ['mom-photo', momId, photoUrl, refreshKey],
    queryFn: async () => {
      if (!photoUrl) return null;

      const filename = photoUrl.split('/').pop();
      if (!filename) return null;

      try {
        // Add timestamp to bust cache
        const timestamp = Date.now();
        const response = await sfetch(`/v1/s3/proxy/${filename}?_t=${timestamp}`);
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.status}`);
        }
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        setLoadedImage(url);
        return url;
      } catch (error) {
        console.error('[MomNameImageHeader/info] Error downloading photo:', error);
        throw error;
      }
    },
    enabled: !!photoUrl,
    staleTime: 0,
    refetchOnMount: 'always',
    refetchOnWindowFocus: true,
  });

  // Clean up object URLs when component unmounts or when URL changes
  useEffect(() => {
    return () => {
      if (loadedImage) {
        URL.revokeObjectURL(loadedImage);
      }
    };
  }, [loadedImage]);

  return (
    <div className='flex items-center gap-4'>
      <div className='flex h-16 w-16 items-center justify-center overflow-hidden rounded-full bg-gray-100'>
        {!photoUrl ? (
          // No photo case - show default avatar
          <Image
            src='/assets/mom_generic_avatar.png'
            alt={name || "Mom's photo"}
            width={64}
            height={64}
            className='h-full w-full rounded-full object-cover'
            priority
          />
        ) : loadedImage ? (
          // Loaded photo case - show the actual photo
          <Image
            src={loadedImage}
            alt={name || "Mom's photo"}
            width={64}
            height={64}
            className='h-full w-full rounded-full object-cover'
            priority
          />
        ) : showLoadingState ? (
          // Loading state - show animated pulse
          <div className='h-full w-full animate-pulse rounded-full bg-gray-200' />
        ) : (
          // Brief initial state - show empty container
          <div className='h-full w-full rounded-full bg-gray-100' />
        )}
      </div>
      <h1 className='text-2xl font-bold'>{name}</h1>
    </div>
  );
}
