'use client';

import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useUserInfo } from '@/context/user-info';
import { Plus } from 'lucide-react';
import { useParams, usePathname, useRouter } from 'next/navigation';

import AdvocatesTable from './advocates-table';
import CoordinatorsTable from './coordinators-table';
import MomsTable from './moms-table';
import ReferralsTable from './referrals-table';
import SupervisorsTable from './supervisors-table';

interface MyPeoplePageProps {
  activeTab?: 'advocates' | 'coordinators' | 'moms' | 'supervisors' | 'referrals';
}

const MyPeoplePage: React.FC<MyPeoplePageProps> = ({ activeTab = 'moms' }) => {
  const router = useRouter();
  const params = useParams();
  const pathname = usePathname();
  const baseHref = `/portal/${params.role}/dashboard/people`;
  const { isInitialized, isAtLeastCoordinator } = useUserInfo();
  const { roles } = useUserInfo();
  const isAdmin = roles.includes('administrator');

  const headerText = isAtLeastCoordinator
    ? 'Find your Supervisors, Advocates, Coordinators, and Moms all in one place.'
    : 'Find your Moms all in one place.';

  const handleTabValueChange = (value: string) => {
    const validOptions = ['advocates', 'coordinators', 'moms', 'supervisors', 'referrals'];

    if (validOptions.includes(value)) {
      router.push(`${baseHref}/${value}`);
    }
  };

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <h1 className='text-2xl font-semibold'>My People</h1>
        {isAdmin ? (
          <Button
            onClick={() => {
              const currentPath = pathname;
              const newPath = currentPath.replace('/people', '/admin#users-heading');
              router.push(newPath);
            }}
            className='flex items-center gap-2'
          >
            <Plus className='h-4 w-4' />
            Add New
          </Button>
        ) : null}
      </div>

      {/* Make sure user profile/role data is available before role-based logic is applied */}
      {isInitialized ? (
        <>
          <p className='text-muted-foreground'>{headerText}</p>

          {isAtLeastCoordinator ? (
            <>
              <Tabs defaultValue={activeTab} className='w-full' onValueChange={handleTabValueChange}>
                <TabsList>
                  <TabsTrigger value='supervisors'>Supervisors</TabsTrigger>
                  <TabsTrigger value='advocates'>Advocates</TabsTrigger>
                  <TabsTrigger value='coordinators'>Coordinators</TabsTrigger>
                  <TabsTrigger value='moms'>Moms</TabsTrigger>
                  <TabsTrigger value='referrals'>Referrals</TabsTrigger>
                </TabsList>
              </Tabs>
              <PeopleTableComponent activeTab={activeTab} />
            </>
          ) : (
            <MomsTable />
          )}
        </>
      ) : null}
    </div>
  );
};

export default MyPeoplePage;

const PeopleTableComponent: React.FC<MyPeoplePageProps> = ({ activeTab }) => {
  switch (activeTab) {
    case 'advocates':
      return <AdvocatesTable />;
    case 'coordinators':
      return <CoordinatorsTable />;
    case 'moms':
      return <MomsTable />;
    case 'supervisors':
      return <SupervisorsTable />;
    case 'referrals':
      return <ReferralsTable />;
    default:
      return <AdvocatesTable />;
  }
};
