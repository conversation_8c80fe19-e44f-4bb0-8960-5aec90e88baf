'use client';

import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { useMemo } from 'react';
import { FieldValues } from 'react-hook-form';

import { useCreateMom } from '../../../../../../hooks/generated';
import { useDashboardContext } from '../../../../context/dashboard-context';
import {
  momAddressInfoFormFields,
  momAddressInfoFormSchema,
  momBasicInfoFormFields,
  momBasicInfoFormSchema,
} from '../../../../lib/mom-basic-info-form-config';

const MomAddModal: React.FC = () => {
  const router = useRouter();
  const { toast } = useToast();
  const { profile } = useDashboardContext();
  const createMom = useCreateMom();

  const fields = useMemo(
    () => [...momBasicInfoFormFields, ...momAddressInfoFormFields],
    [momBasicInfoFormFields, momAddressInfoFormFields],
  );

  const schema = useMemo(
    () => momBasicInfoFormSchema.concat(momAddressInfoFormSchema),
    [momBasicInfoFormSchema, momAddressInfoFormSchema],
  );

  const role =
    (profile.roles.includes('supervisor') && 'supervisor') ||
    (profile.roles.includes('coordinator') && 'coordinator') ||
    (profile.roles.includes('advocate') && 'advocate') ||
    '';

  const handleSubmit = async (data: FieldValues) => {
    try {
      const mom = await createMom.mutateAsync({
        data: {
          ...data,
          languages_c: data.languages_c.map((lang: { value: string }) => lang.value),
          birthdate: data.birthdate ? new Date(data.birthdate).toISOString() : null,
          ...((role === 'supervisor' && {
            affiliate_id: profile.affiliateId,
          }) ||
            (role === 'coordinator' && {
              assigned_user_id: profile.id,
            }) ||
            (role === 'advocate' && {
              pairings: {
                create: {
                  name: '',
                  advocateUserId: profile.id,
                },
              },
            })),
        },
      });

      if (role && mom) {
        router.push(`/portal/${role}/dashboard/people/moms/${mom.id}`);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: `There was an error creating a mom. ${error}`,
        variant: 'destructive',
      });

      console.error('Failed to submit:', error);
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant='outline'>Add Mom</Button>
      </DialogTrigger>
      <DialogContent className='max-h-[90dvh] overflow-auto sm:max-w-xl'>
        <DialogHeader>
          <DialogTitle>Add Mom</DialogTitle>
        </DialogHeader>
        <div className='space-y-4'>
          <FormFactory
            formWrapperClassName='flex flex-col w-full'
            formFieldElClass='w-full'
            fields={fields}
            schema={schema}
            onSubmit={handleSubmit}
            actionButtonsComponent={
              <div className='my-auto flex w-full min-w-[240px] flex-1 shrink basis-0 items-center justify-end gap-3 self-stretch border-t-[1px] pt-4'>
                <Button
                  type='submit'
                  className='my-auto flex items-center justify-center gap-1 self-stretch overflow-hidden rounded-lg border-2 border-solid border-white border-opacity-10 bg-slate-800 px-3.5 py-2.5 text-white shadow-sm'
                >
                  {createMom.isPending ? 'Adding mom...' : 'Add mom'}
                </Button>
              </div>
            }
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default MomAddModal;
