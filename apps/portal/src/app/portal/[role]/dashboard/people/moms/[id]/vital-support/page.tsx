'use client';

import BenevolenceTable from '@/app/portal/_components/dashboard/benevolence-table';
import { useMomFlaggedNeedsContext } from '@/app/portal/context/mom-flagged-needs-context';
import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { useEffect } from 'react';

import MomProfileWrapper from '../../../_components/mom-profile-wrapper';

const MomBenevolencePage = ({ params }: { params: { id: string } }) => {
  const { id } = params;
  const { momProfile, fetchMomProfile } = useMomProfileContext();
  const { momFlaggedNeeds, fetchMomFlaggedNeeds } = useMomFlaggedNeedsContext();

  useEffect(() => {
    if (momProfile?.id) {
      fetchMomFlaggedNeeds(String(momProfile.id));
    }
  }, [momProfile, fetchMomFlaggedNeeds]);

  useEffect(() => {
    const fetchProfile = async () => {
      await fetchMomProfile(id);
    };

    if (!momProfile || momProfile.id !== id) {
      fetchProfile();
    }
  }, [id, momProfile, fetchMomProfile]);

  if (!momProfile) {
    return <div>Loading...</div>;
  }

  const handleCloseDrawer = () => {
    // Refetch the mom flagged needs when the drawer is closed
    fetchMomFlaggedNeeds(String(momProfile.id));
  };

  return (
    <MomProfileWrapper viewId='vital-support' momId={id}>
      {/* // TODO: Update with actual benevolence data https://servant-io.atlassian.net/browse/EMA-427 */}
      <BenevolenceTable
        momData={momFlaggedNeeds}
        toggleColumn={'status'}
        title={'Needs History'}
        showFilterButtons={false}
        showExportBtn={true}
        onCloseDrawer={handleCloseDrawer}
        filterOutMetNeeds={false}
      />
    </MomProfileWrapper>
  );
};

export default MomBenevolencePage;
