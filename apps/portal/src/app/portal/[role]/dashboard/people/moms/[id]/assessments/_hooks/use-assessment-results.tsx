import { useFindManyAssessmentResult } from '../../../../../../../../../hooks/generated';

export const useAssessmentResults = ({ momId, assessmentId }: { momId: string; assessmentId: string }) => {
  const { data: assessmentResults, isLoading: areAssessmentResultsLoading } = useFindManyAssessmentResult({
    where: {
      AND: [
        {
          assessmentId: {
            equals: assessmentId,
          },
        },
        {
          momId: {
            equals: momId,
          },
        },
      ],
    },
    orderBy: [
      {
        created_at: 'desc',
      },
      {
        completedAt: 'desc',
      },
    ],
    select: {
      id: true,
      completedAt: true,
      created_at: true,
    },
  });

  return { assessmentResults, areAssessmentResultsLoading };
};
