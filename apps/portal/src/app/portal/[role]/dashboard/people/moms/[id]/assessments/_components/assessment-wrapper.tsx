'use client';

import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { flattenFormFields, getDefaultFormValues } from '@/lib/utils';
import type { FieldConfig, FormValues } from '@/types/form-field';
import { ArrowLeftIcon, CheckIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import * as yup from 'yup';

interface AssessmentConfig {
  steps: {
    [key: string]: {
      title: string;
      fields: FieldConfig[];
      schema: yup.ObjectSchema<yup.AnyObject>;
    };
  };
}

interface AssessmentWrapperProps {
  title: string;
  config: AssessmentConfig;
  onSubmit: (data: FormValues) => Promise<void>;
  fetchInitialData?: () => Promise<FormValues>;
  selfAssessment?: boolean;
}

const AssessmentWrapper = ({ title, config, onSubmit, fetchInitialData, selfAssessment }: AssessmentWrapperProps) => {
  const router = useRouter();
  const [activeSection, setActiveSection] = useState(0);
  const [loading, setLoading] = useState(!!fetchInitialData);
  const [formData, setFormData] = useState<FormValues>({});
  const [successfulSubmission, setSuccessfulSubmission] = useState(false);
  const [readOnly, setReadOnly] = useState(false);

  useEffect(() => {
    const loadInitialData = async () => {
      if (fetchInitialData) {
        try {
          const initialData = await fetchInitialData();
          setFormData(initialData);
          // If the form has been filled out before and there is data, then it should be readonly
          if (initialData && Object.keys(initialData).length > 0) {
            setReadOnly(true);
          }
        } catch (error) {
          console.error('Error loading initial data:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    loadInitialData();
  }, [fetchInitialData, setFormData, setReadOnly, setLoading]);

  const stepKeys = Object.keys(config.steps);
  const sections = stepKeys.map((key) => ({
    id: key,
    label: config.steps[key].title,
  }));

  const handleSaveAndGoToPrevious = (data: FormValues): void => {
    setFormData((prev) => ({ ...prev, ...data }));
    setActiveSection((prev) => prev - 1);
  };

  const handleContinue = async (data: FormValues): Promise<void> => {
    setFormData((prev) => ({ ...prev, ...data }));
    setActiveSection((prev) => prev + 1);
  };

  const handleSubmit = async (data: FormValues): Promise<void> => {
    setFormData((prev) => ({ ...prev, ...data }));
    setLoading(true);

    const allFormData = { ...formData, ...data };

    try {
      await onSubmit(allFormData);
      setSuccessfulSubmission(true);
      toast({
        title: 'Success',
        description: 'The assessment form has been successfully submitted',
        variant: 'success',
      });
    } catch (error) {
      console.error('Error submitting pre-assessment form', error);
      toast({
        title: 'Error',
        description: 'There was an error submitting the assessment form. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const isFirstStep = activeSection === 0;
  const isFinalStep = activeSection === sections.length - 1;

  const currentStepConfig = config.steps[stepKeys[activeSection]];

  const actionButtonsConfig = {
    continueLabel: isFinalStep ? 'Submit' : 'Next',
    backLabel: 'Back',
  };

  return (
    <div className='flex h-full flex-col md:flex-row'>
      {/* Tabs container - horizontal scrolling on mobile, vertical on desktop */}
      <div className='border-b md:w-64 md:border-b-0 md:p-4'>
        <div className='-mb-px overflow-x-auto'>
          <div className='flex min-w-full md:flex-col'>
            {sections.map((section, index) => (
              <div
                key={section.id}
                className={`shrink-0 cursor-pointer px-3 py-2 text-sm md:px-4 md:py-2 ${
                  index === activeSection
                    ? 'border-b-2 border-primary font-medium text-primary md:border-b-0 md:border-l-2'
                    : 'border-b-2 border-transparent text-gray-500 hover:text-gray-700'
                } ${index === 0 ? 'ml-4 md:ml-0' : ''} ${index === sections.length - 1 ? 'mr-4 md:mr-0' : ''}`}
                onClick={() => setActiveSection(index)}
              >
                {section.label}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className='flex-1 p-4 md:p-6'>
        {!selfAssessment ? (
          <Button onClick={() => router.back()} variant='link' className='pl-0'>
            <ArrowLeftIcon className='mr-2 h-4 w-4' /> Back to Assessments
          </Button>
        ) : null}
        <h1 className='mb-4 mt-4 text-xl font-bold md:mb-6 md:mt-6 md:text-2xl'>{title}</h1>

        <div className='rounded-lg border border-gray-200 bg-white p-4'>
          {/* Success State */}
          {successfulSubmission ? (
            <div className='flex h-[300px] items-center justify-center text-center text-lg font-bold'>
              <div className='flex flex-col items-center justify-center'>
                <CheckIcon className='h-24 w-24 gap-4 text-green-500' />
                <p className='mb-4 text-xl font-bold'>You have successfully submitted the {title}</p>
                {!selfAssessment ? <Button onClick={() => router.back()}>Back to Assessments</Button> : null}
              </div>
            </div>
          ) : (
            <>
              {/* Form */}
              {loading ? (
                <div>Loading...</div>
              ) : (
                <FormFactory
                  fields={currentStepConfig.fields}
                  schema={currentStepConfig.schema}
                  onSubmit={isFinalStep ? handleSubmit : handleContinue}
                  onBack={isFirstStep ? undefined : handleSaveAndGoToPrevious}
                  defaultValues={getDefaultFormValues(flattenFormFields(currentStepConfig.fields), formData)}
                  actionButtonsConfig={actionButtonsConfig}
                  readOnly={readOnly}
                  formWrapperClassName='md:grid grid-cols-2 gap-4'
                  labelWrapperClassName='min-w-[90%]'
                />
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default AssessmentWrapper;
