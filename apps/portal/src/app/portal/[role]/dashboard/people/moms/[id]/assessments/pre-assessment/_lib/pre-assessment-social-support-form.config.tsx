import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

import { likeMyLifeOptions } from '../../../../../referrals/_components/intake-referral-form.config';

export const adviceOptions = [
  { label: 'Housing', value: 'housing' },
  { label: 'Education Services', value: 'education_services' },
  { label: 'Mental Health Services', value: 'mental_health_services' },
  { label: 'Health Services', value: 'health_services' },
  { label: 'Childcare', value: 'childcare' },
  { label: 'Substance Use Treatment', value: 'substance_use_treatment' },
  { label: 'Legal Services', value: 'legal_services' },
  { label: 'Closet Visit', value: 'closet_visit' },
  { label: 'Benevolence', value: 'benevolence' },
];

export const preAssessmentSocialSupportFormConfig: FieldConfig[] = [
  {
    name: 'pre_as_ss_people_believe',
    type: 'select',
    placeholder: 'Select',
    label: '1) I have people who believe in me.',
    options: likeMyLifeOptions,
  },
  {
    name: 'pre_as_ss_advice_giver',
    type: 'select',
    placeholder: 'Select',
    label: "2) I have someone in my life who gives me advice, even when it's hard to hear.",
    options: likeMyLifeOptions,
  },
  {
    name: 'pre_as_ss_goal_support',
    type: 'select',
    placeholder: 'Select',
    label: '3) When I am trying to work on achieving a goal, I have friends who will support me.',
    options: likeMyLifeOptions,
  },
  {
    name: 'pre_as_ss_childcare',
    type: 'select',
    placeholder: 'Select',
    label: '4) When I need someone to look after my kids on short notice, I can find someone I trust.',
    options: likeMyLifeOptions,
  },
  {
    name: 'pre_as_ss_trusted_resources',
    type: 'multi-select',
    placeholder: 'Select All Applicable',
    label: '5) I have people I trust to ask for advice about (check all that apply)',
    options: adviceOptions,
  },
];

export const preAssessmentSocialSupportFormSchema = yup.object().shape({
  pre_as_ss_people_believe: yup.string().required('Please select an option'),
  pre_as_ss_advice_giver: yup.string().required('Please select an option'),
  pre_as_ss_goal_support: yup.string().required('Please select an option'),
  pre_as_ss_childcare: yup.string().required('Please select an option'),
  pre_as_ss_trusted_resources: yup
    .array()
    .of(
      yup.object().shape({
        value: yup.string().oneOf(
          adviceOptions.map((option) => option.value),
          'Invalid advice option',
        ),
        label: yup.string(),
      }),
    )
    .optional(),
});
