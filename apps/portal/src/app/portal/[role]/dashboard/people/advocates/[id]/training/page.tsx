'use client';

import GenericTable from '@/components/custom/generic-table';
import { TableCell } from '@/components/ui/table';
import { CheckCircle, CircleX } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useCallback } from 'react';

import AdvocateProfileWrapper from '../../../_components/advocates/advocate-profile-wrapper';

const AdvocateTrainingPage = () => {
  const params = useParams();
  const advocateId = params.id as string;

  const dummyDataTraining = [
    {
      id: '1',
      title: 'Advocate Training Dummy Data',
      status: 'completed',
      updated_at: '2021-01-01',
    },
  ];

  const dummyCertifications = [
    {
      id: '123',
      title: 'Certification 1 Dummy Data',
      updated_at: '2021-01-01',
    },
  ];

  const rowRenderer = useCallback(
    (item: { id: string; title: string; status?: string; updated_at: string }): React.ReactNode => {
      return (
        <>
          <TableCell>{item.title}</TableCell>
          <TableCell>{item.updated_at}</TableCell>
          {item.status ? (
            <TableCell>
              {item.status === 'completed' ? (
                <div className='flex items-center gap-2'>
                  <CheckCircle className='text-green-500' />
                  Yes
                </div>
              ) : (
                <div className='flex items-center gap-2'>
                  <CircleX className='text-red-500' />
                  No
                </div>
              )}
            </TableCell>
          ) : null}
        </>
      );
    },
    [],
  );

  const mobileRenderer = useCallback(
    (item: { id: string; title: string; status?: string; updated_at: string }): React.ReactNode => {
      return (
        <div>
          <div className='text-lg font-semibold'>{item.title}</div>
          <div>{item.updated_at}</div>
          {item.status ? (
            <div className='flex items-center gap-2'>
              <span className='font-semibold'>Completed:</span>
              {item.status === 'completed' ? (
                <div className='flex items-center gap-2'>
                  <CheckCircle className='text-green-500' /> Yes
                </div>
              ) : (
                <div className='flex items-center gap-2'>
                  <CircleX className='text-red-500' /> No
                </div>
              )}
            </div>
          ) : null}
        </div>
      );
    },
    [],
  );

  return (
    <AdvocateProfileWrapper viewId='training' advocateId={advocateId}>
      <div className='flex flex-col gap-2'>
        <GenericTable
          data={dummyDataTraining}
          isLoading={false}
          headers={['Title', 'Date', 'Completed?']}
          rowRenderer={rowRenderer}
          mobileRenderer={mobileRenderer}
          columnWidths={[35, 30, 35]}
          headerSection={<p className='text-lg font-semibold'>Training</p>}
        />
        <GenericTable
          data={dummyCertifications}
          isLoading={false}
          headers={['Title', 'Date', '']}
          rowRenderer={rowRenderer}
          mobileRenderer={mobileRenderer}
          columnWidths={[35, 30, 35]}
          headerSection={<p className='text-lg font-semibold'>Certifications</p>}
        />
      </div>
    </AdvocateProfileWrapper>
  );
};

export default AdvocateTrainingPage;
