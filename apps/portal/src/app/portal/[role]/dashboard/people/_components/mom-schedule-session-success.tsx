import { decodeHtmlEntities } from '@/lib/utils';
import type { PairingOrMomSchema } from '@/types/schemas/pairing';
import { type SessionSchema, SessionType } from '@/types/schemas/session';
import { formatStartEndDateTime } from '@suiteapi/models';
import { Calendar, Link, MapPin, MessagesSquare, User, Users, Video } from 'lucide-react';

interface MomScheduleSessionSuccessProps {
  scheduledSessions: SessionSchema[];
  scheduledPairings: PairingOrMomSchema[];
}

const SessionInfoItem = ({ label, value, icon }: { label: string; value: unknown; icon: JSX.Element }) => (
  <div className='flex min-h-[54px] w-full flex-col justify-center text-sm'>
    <div className='flex min-h-[54px] w-full items-start rounded-md py-2'>
      {icon}
      <div className='flex w-64 min-w-[240px] flex-col pl-4'>
        <div className='flex w-60 max-w-full flex-col'>
          <div className='w-full font-medium leading-none text-zinc-950'>{String(value)}</div>
          <div className='mt-1 w-full whitespace-nowrap leading-none text-zinc-500'>{label}</div>
        </div>
      </div>
    </div>
  </div>
);

const MomScheduleSessionSuccess: React.FC<MomScheduleSessionSuccessProps> = ({
  scheduledSessions,
  scheduledPairings,
}) => {
  const { session_type, date_start, date_end, location, join_url } = scheduledSessions[0];

  // There is no "meeting_type" field in SuiteCRM, so value of `location` or `join_url` determines if it's virtual or in-person
  const isInPerson = !join_url; // default to in-person if join_url is blank

  const momInfo = {
    label: 'Mom(s)',
    value: scheduledPairings.map(({ mom }) => `${mom?.first_name} ${mom?.last_name}`).join(', '),
    icon: <User className='h-6 w-6' />,
  };

  const sessionTypeInfo = {
    label: 'Session Type',
    value: session_type === SessionType.Track_Session ? 'Track Session' : 'Support Session',
    icon: <MessagesSquare className='h-6 w-6' />,
  };

  const dateInfo = {
    label: 'Date',
    value: formatStartEndDateTime(String(date_start), String(date_end)),
    icon: <Calendar className='h-6 w-6' />,
  };

  const meetingTypeInfo = {
    label: 'Meeting Type',
    value: isInPerson ? 'In Person' : 'Virtual',
    icon: isInPerson ? <Users className='h-6 w-6' /> : <Video className='h-6 w-6' />,
  };

  const meetingLocationInfo = {
    label: isInPerson ? 'Meeting Location' : 'Meeting Link',
    value: decodeHtmlEntities(isInPerson ? location : join_url),
    icon: isInPerson ? <MapPin className='h-6 w-6' /> : <Link className='h-6 w-6' />,
  };

  const infoItems = [momInfo, sessionTypeInfo, dateInfo, meetingTypeInfo, meetingLocationInfo];

  return (
    <section className='mt-2 flex flex-col gap-3'>
      {infoItems.map((item) => (
        <SessionInfoItem key={item.label} {...item} />
      ))}
    </section>
  );
};

export default MomScheduleSessionSuccess;
