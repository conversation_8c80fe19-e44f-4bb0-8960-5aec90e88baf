import {
  scheduleNextSessionFormFields,
  scheduleNextSessionSchema,
} from '@/app/portal/lib/mom-session-notes-form.config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';

const SessionNotesScheduleNext: React.FC = () => {
  const defaultValues = {}; // TODO: set up schedule next session default values

  const handleSubmit = (): void => {
    // TODO: API call to persist data in SuiteCRM
    // scheduleSession() etc.
  };

  return (
    <section className='mt-3 flex w-full flex-col rounded-xl border border-solid border-gray-100 bg-white shadow-sm'>
      <div className='flex w-full flex-col px-6 pb-4'>
        <FormFactory
          fields={scheduleNextSessionFormFields}
          schema={scheduleNextSessionSchema}
          onSubmit={handleSubmit}
          defaultValues={defaultValues}
          actionButtonsComponent={
            <div className='my-auto flex w-full min-w-[240px] flex-1 shrink basis-0 items-center justify-end gap-3 self-stretch'>
              <Button
                type='submit'
                className='my-auto flex items-center justify-center gap-1 self-stretch overflow-hidden rounded-lg border-2 border-solid border-white border-opacity-10 bg-slate-800 px-3.5 py-2.5 text-white shadow-sm'
              >
                Save changes
              </Button>
            </div>
          }
        />
      </div>
    </section>
  );
};

export default SessionNotesScheduleNext;
