import type { FieldConfig } from '@/types/form-field';
import type { ObjectSchema } from 'yup';

import {
  preAssessmentDepressionFormConfig,
  preAssessmentDepressionSchema,
} from './pre-assessment-depression-form.config';
import {
  preAssessmentFinancialSecurityFormConfig,
  preAssessmentFinancialSecurityFormSchema,
} from './pre-assessment-financial-security-form.config';
import {
  preAssessmentSocialSupportFormConfig,
  preAssessmentSocialSupportFormSchema,
} from './pre-assessment-social-support-form.config';
import { preAssessmentStressFormConfig, preAssessmentStressFormSchema } from './pre-assessment-stress-form.config';
import { preAssessmentTraumaFormConfig, preAssessmentTraumaFormSchema } from './pre-assessment-trauma-form-config';

// Define the structure of a step
interface PreAssessmentStep {
  title: string;
  fields: FieldConfig[];
  schema: ObjectSchema<Record<string, unknown>>;
}

// Define the structure of the steps object with an index signature
interface PreAssessmentSteps {
  [key: string]: PreAssessmentStep;
}

// Define the structure of the entire config
interface PreAssessmentFormsConfig {
  steps: PreAssessmentSteps;
}

export const preAssessmentFormsConfig: PreAssessmentFormsConfig = {
  steps: {
    stress: {
      title: 'Stress',
      fields: preAssessmentStressFormConfig,
      schema: preAssessmentStressFormSchema,
    },
    trauma: {
      title: 'Trauma',
      fields: preAssessmentTraumaFormConfig,
      schema: preAssessmentTraumaFormSchema,
    },
    socialSupport: {
      title: 'Social Support',
      fields: preAssessmentSocialSupportFormConfig,
      schema: preAssessmentSocialSupportFormSchema,
    },
    financialSecurity: {
      title: 'Financial Security',
      fields: preAssessmentFinancialSecurityFormConfig,
      schema: preAssessmentFinancialSecurityFormSchema,
    },
    depression: {
      title: 'Depression',
      fields: preAssessmentDepressionFormConfig,
      schema: preAssessmentDepressionSchema,
    },
  },
};

export type { PreAssessmentStep, PreAssessmentSteps, PreAssessmentFormsConfig };
