'use client';

import GenericTable from '@/components/custom/generic-table';
import { Button } from '@/components/ui/button';
import { TableCell } from '@/components/ui/table';
import renderIconForType from '@/components/utils/renderIconForType';
import { useFindManyConnectionLog } from '@/hooks/generated';
import { format } from 'date-fns';
import { CloudDownload } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useCallback, useMemo, useState } from 'react';

import CoordinatorProfileWrapper from '../../../_components/coordinators/coordinator-profile-wrapper';
import MomConnectionLogDrawer from '../../../_components/mom-connection-log-drawer';
import { getContactMethodMappings } from '../../../_utils/contact-methods-utils';

type CoordinatorConnectionLogTableData = {
  contact_method_c: string;
  date_created_c: Date;
  summary_c: string;
  created_by_name: string | null;
  name: string | null;
  is_visible_to_advocates_c: boolean;
} | null;

const CoordinatorConnectionLogPage = () => {
  const params = useParams<{ id: string }>();
  const coordinatorId = params.id;
  const [selectedEntry, setSelectedEntry] = useState<CoordinatorConnectionLogTableData | null>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const { data: connectionLog, isLoading: connectionLogLoading } = useFindManyConnectionLog({
    where: {
      created_by_id: coordinatorId,
    },
  });

  const handleRowClick = (log: CoordinatorConnectionLogTableData) => {
    setSelectedEntry(log);
    setIsDrawerOpen(true);
  };

  const contactMethodMappings = getContactMethodMappings();

  const filteredConnectionLog = useMemo(
    () =>
      connectionLog
        ?.filter((log) => log?.is_visible_to_advocates_c)
        .map((log) => ({
          name: log.name ?? '',
          contact_method_c: log.contact_method_c ?? '',
          date_created_c: log.date_created_c,
          summary_c: log.summary_c ?? '',
          created_by_name: log.created_by_name ?? '',
          is_visible_to_advocates_c: log.is_visible_to_advocates_c ?? false,
        })) ?? [],
    [connectionLog],
  );

  const renderRow = useCallback((log: CoordinatorConnectionLogTableData) => {
    if (!log) return null;
    return (
      <>
        <TableCell>
          <div className='flex items-center gap-2'>
            {renderIconForType(log.contact_method_c ?? '')}
            {contactMethodMappings[log.contact_method_c ?? '']}
          </div>
        </TableCell>
        <TableCell>{format(new Date(log.date_created_c), 'MMM d, yyyy')}</TableCell>
        <TableCell>{log.name}</TableCell>
        <TableCell>{log.summary_c}</TableCell>
        <TableCell>
          <Button onClick={() => handleRowClick(log)} variant='outline'>
            View
          </Button>
        </TableCell>
      </>
    );
  }, []);

  const renderMobileRow = useCallback((log: CoordinatorConnectionLogTableData) => {
    if (!log) return null;
    return (
      <div className='flex flex-col justify-between gap-2 rounded-md p-3 sm:flex-row'>
        <div className='min-w-0 space-y-2'>
          <div className='flex flex-wrap items-center gap-2 text-sm'>
            <span className='flex items-center gap-2'>
              {renderIconForType(log.contact_method_c ?? '')}
              {contactMethodMappings[log.contact_method_c ?? '']}
              <span className='hidden sm:inline'>•</span>
              <span className='text-emaTextSecondary'>{format(new Date(log.date_created_c), 'MMM d, yyyy')}</span>
            </span>
          </div>
          <div className='break-words text-sm text-emaTextSecondary'>{log.name}</div>
          <div className='break-words text-sm text-emaTextSecondary'>{log.summary_c}</div>
        </div>

        <Button onClick={() => handleRowClick(log)} variant='outline'>
          View
        </Button>
      </div>
    );
  }, []);

  const handleExport = useCallback(() => {
    if (!connectionLog) return;

    const csvData = connectionLog.map((log) => ({
      'Contact Method': contactMethodMappings[log.contact_method_c],
      Date: format(new Date(log.date_created_c), 'MMM d, yyyy'),
      Summary: log.summary_c,
      'Created By': log.created_by_name || 'Unknown',
    }));

    const headers = Object.keys(csvData[0]);
    const csvRows = [
      headers.join(','),
      ...csvData.map((row) => headers.map((header) => JSON.stringify(row[header as keyof typeof row] || '')).join(',')),
    ];
    const csvString = csvRows.join('\n');

    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', `connection_logs_${format(new Date(), 'yyyy-MM-dd')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [connectionLog, contactMethodMappings]);

  return (
    <CoordinatorProfileWrapper viewId='connection-log'>
      <GenericTable
        data={filteredConnectionLog}
        headers={['Type', 'Date', 'Contacted', 'Summary', 'Action']}
        rowRenderer={renderRow}
        mobileRenderer={renderMobileRow}
        isLoading={connectionLogLoading}
        headerSection={
          <div className='flex justify-between'>
            <h2 className='text-lg font-semibold'>Connection Logs</h2>
            <Button variant='outline' className='flex gap-1' onClick={handleExport} disabled={!connectionLog?.length}>
              <CloudDownload className='h-5 w-5' />
              Export All
            </Button>
          </div>
        }
      />
      <MomConnectionLogDrawer
        isOpen={isDrawerOpen}
        onClose={() => setIsDrawerOpen(false)}
        entry={selectedEntry}
        contactMethodMappings={contactMethodMappings}
      />
    </CoordinatorProfileWrapper>
  );
};

export default CoordinatorConnectionLogPage;
