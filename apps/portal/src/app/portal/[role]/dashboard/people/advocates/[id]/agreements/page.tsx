'use client';

import { useParams } from 'next/navigation';

import AdvocateProfileWrapper from '../../../_components/advocates/advocate-profile-wrapper';

const AdvocateAgreementsPage = () => {
  const params = useParams();
  const advocateId = params.id as string;

  return (
    <AdvocateProfileWrapper viewId='agreements' advocateId={advocateId}>
      <div>AdvocateAgreementsPage</div>
    </AdvocateProfileWrapper>
  );
};

export default AdvocateAgreementsPage;
