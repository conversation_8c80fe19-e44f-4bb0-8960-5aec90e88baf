'use client';

import AssessmentsScore from '@/app/portal/_components/assessments/assessments-score';
import { useAssessmentsTabsContext } from '@/app/portal/context/assessments-tabs-context';
import { useEffect } from 'react';

const AssessmentsPage = () => {
  const { setCurrentTab } = useAssessmentsTabsContext();

  useEffect(() => {
    setCurrentTab('assessments');
  }, []);

  return (
    <div className='w-full'>
      <AssessmentsScore />
    </div>
  );
};

export default AssessmentsPage;
