'use client';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useFindManyCoordinatorNote } from '@/hooks/generated/coordinator-note';
import { useToast } from '@/hooks/use-toast';
import { useParams, useRouter } from 'next/navigation';

import AdvocateProfileWrapper from '../../../_components/advocates/advocate-profile-wrapper';
import AdvocateInterviewReviewModal from '../../_components/advocate-interview-review-modal';

const AdvocateInterviewPage = () => {
  const params = useParams<{ id: string; role: string }>();
  const router = useRouter();
  const { toast } = useToast();

  // Fetch existing coordinator notes to show when reviews have been completed
  const { data: coordinatorNotes } = useFindManyCoordinatorNote({
    where: {
      advocate_id: params.id,
      type_c: 'interview_advocate',
    },
    include: {
      coordinator: {
        select: {
          firstName: true,
          lastName: true,
        },
      },
    },
    orderBy: {
      created_at: 'desc',
    },
  });

  // Helper function to handle successful review submission
  const handleReviewSuccess = () => {
    toast({
      title: 'Review Submitted',
      description: 'Your review has been successfully saved.',
      variant: 'success',
    });

    // Refresh the page to show the new review
    router.refresh();
  };

  // Get count of interview reviews and application reviews
  const interviewReviews = coordinatorNotes?.filter((note) =>
    note.name?.toLowerCase().includes('interview review: interview'),
  );

  const applicationReviews = coordinatorNotes?.filter((note) =>
    note.name?.toLowerCase().includes('interview review: application'),
  );

  // Function to navigate to the application page
  const handleNavigateToApplication = () => {
    router.push(`/portal/${params.role}/dashboard/people/advocates/${params.id}/application`);
  };

  return (
    <AdvocateProfileWrapper viewId='interview' advocateId={params.id}>
      <div className='mt-6 flex flex-col gap-4'>
        <div className='flex flex-row justify-between gap-4 rounded-lg border border-emaBorderPrimary bg-white p-4'>
          <div>
            <h2 className='text-lg font-semibold'>Interview</h2>
            <p className='text-sm text-emaTextSecondary'>Interview with the prospective Advocate</p>
            {interviewReviews && interviewReviews.length > 0 && (
              <p className='mt-2 text-xs text-green-600'>
                {interviewReviews.length} {interviewReviews.length === 1 ? 'review' : 'reviews'} completed
              </p>
            )}
          </div>

          <AdvocateInterviewReviewModal
            title='Interview Review'
            description='Record your notes from the interview with this advocate'
            successCallback={handleReviewSuccess}
          >
            <Button variant='outline'>Review</Button>
          </AdvocateInterviewReviewModal>
        </div>
        <div className='flex flex-row justify-between gap-4 rounded-lg border border-emaBorderPrimary bg-white p-4'>
          <div>
            <h2 className='text-lg font-semibold'>Advocate Application & Onboarding</h2>
            <p className='text-sm text-emaTextSecondary'>
              Application and onboarding information for the prospective Advocate
            </p>
            {applicationReviews && applicationReviews.length > 0 && (
              <p className='mt-2 text-xs text-green-600'>
                {applicationReviews.length} {applicationReviews.length === 1 ? 'review' : 'reviews'} completed
              </p>
            )}
          </div>
          <Button variant='outline' onClick={handleNavigateToApplication}>
            Review
          </Button>
        </div>

        {/* Previous Interview Reviews section */}
        {interviewReviews && interviewReviews.length > 0 && (
          <Card className='mt-4'>
            <CardHeader>
              <CardTitle>Previous Interview Reviews ({interviewReviews.length})</CardTitle>
            </CardHeader>
            <CardContent>
              {interviewReviews.map((review, index) => (
                <div key={review.id} className={`pb-4 ${index < interviewReviews.length - 1 ? 'mb-4 border-b' : ''}`}>
                  <div className='flex items-center justify-between'>
                    <p className='text-sm font-medium'>
                      Reviewed on {new Date(review.created_at).toLocaleDateString()}
                    </p>
                    <p className='text-xs text-gray-500'>
                      by{' '}
                      {review.coordinator?.firstName && review.coordinator?.lastName
                        ? `${review.coordinator.firstName} ${review.coordinator.lastName}`
                        : 'Unknown Reviewer'}
                    </p>
                  </div>
                  <p className='mt-1 text-sm'>{review.description}</p>
                </div>
              ))}
            </CardContent>
          </Card>
        )}
      </div>
    </AdvocateProfileWrapper>
  );
};

export default AdvocateInterviewPage;
