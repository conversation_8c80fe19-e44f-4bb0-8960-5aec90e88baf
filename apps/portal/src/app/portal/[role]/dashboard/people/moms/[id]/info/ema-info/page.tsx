'use client';

import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import {
  momBasicInfoEMAInfoPairingFields,
  momBasicInfoEmaInfoAffiliateLocationField,
  momBasicInfoEmaInfoAffiliateLocationSchema,
  momBasicInfoEmaInfoFormFields,
  momBasicInfoEmaInfoFormSchema,
  momBasicInfoEmaInfoPairingSchema,
} from '@/app/portal/lib/mom-basic-info-ema-info-form.config';
import FormFactory from '@/components/custom/form-factory';
import GenericFormModal from '@/components/custom/generic-form-modal';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import {
  useFindManyPairing,
  useFindManyTrack,
  useFindManyUser,
  useFindUniqueMom,
  useUpdateMom,
  useUpdatePairing,
} from '@/hooks/generated';
import {
  CompleteReasonSubStatusType,
  DischargeIncompleteSubStatusType,
  InProgramTrackSubStatusType,
  IncompleteReasonSubStatusType,
  TrackStatusType,
} from '@/hooks/generated/__types';
import { useCreateMomPairing } from '@/hooks/use-create-pairing';
import { useToast } from '@/hooks/use-toast';
import { YupSchemas } from '@suiteapi/models';
import React from 'react';

import MomBasicInfoWrapper from '../../../../_components/mom-basic-info-form-wrapper';
import MomProfileWrapper from '../../../../_components/mom-profile-wrapper';
import WithMomProfile from '../../with-mom-profile';

export interface Advocate {
  id: string;
  name: string;
}

export interface Track {
  id: string;
  name: string;
}

const MomEmaInfoPage = ({ params }: { params: { id: string } }): React.ReactNode => {
  const { toast } = useToast();
  const { id } = params;
  const { profile } = useDashboardContext();
  const [, setMomIsPaired] = React.useState(false);
  const [isModalOpen, setIsModalOpen] = React.useState(false);

  const { data: momData, isLoading: isLoadingMomData } = useFindUniqueMom({
    where: { id },
    select: {
      status: true,
      affiliate: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });

  const { data: pairingData } = useFindManyPairing({
    where: {
      momId: id,
    },
    select: {
      id: true,
      status: true,
      track_status: true,
      in_program_track_sub_status: true,
      discharge_incomplete_sub_status: true,
      incomplete_reason_sub_status: true,
      complete_reason_sub_status: true,
      advocateUser: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
        },
      },
      track: {
        select: {
          id: true,
          title: true,
        },
      },
    },
  });

  React.useEffect(() => {
    setMomIsPaired(Boolean(pairingData?.length));
  }, [pairingData]);

  const isCoordinatorOrAdmin =
    profile?.portalRole === 'coordinator' ||
    profile?.portalRole === 'administrator' ||
    profile?.portalRole === 'supervisor';

  const { data: tracks = [] } = useFindManyTrack({
    where: {
      affiliates: {
        some: {
          moms: {
            some: {
              id: id,
            },
          },
        },
      },
    },
    select: {
      id: true,
      title: true,
      affiliates: {
        select: {
          id: true,
          name: true,
          moms: {
            select: {
              id: true,
            },
          },
        },
      },
    },
  });

  const { data: advocates = [] } = useFindManyUser({
    where: {
      userRoles: {
        some: {
          role: {
            key: 'advocate',
          },
        },
      },
    },
    select: {
      id: true,
      firstName: true,
      lastName: true,
    },
  });
  const advocateOptions: Advocate[] = advocates.map((advocate) => ({
    id: advocate.id,
    name: `${advocate.firstName} ${advocate.lastName}`,
  }));
  const trackOptions: Track[] = tracks.map((track) => ({
    id: track.id,
    name: track.title,
  }));

  const { createMomPairing } = useCreateMomPairing(id);

  const handleCreatePairing = async (data: YupSchemas.PairingSchema) => {
    const pairingData = {
      advocateUser: data.advocateUser as string,
      track: data.track as string,
      status: data.status as YupSchemas.PairingStatusType,
      track_status: data.track_status as TrackStatusType,
      in_program_track_sub_status: data.in_program_track_sub_status as InProgramTrackSubStatusType,
      discharge_incomplete_sub_status: data.discharge_incomplete_sub_status as DischargeIncompleteSubStatusType,
      incomplete_reason_sub_status: data.incomplete_reason_sub_status as IncompleteReasonSubStatusType,
      complete_reason_sub_status: data.complete_reason_sub_status as CompleteReasonSubStatusType,
    };
    const success = await createMomPairing(pairingData);
    if (success) {
      setMomIsPaired(true);
    }
  };

  const updatePairing = useUpdatePairing();

  const handleUpdatePairing = async (data: YupSchemas.PairingSchema) => {
    try {
      if (!data.id) {
        throw new Error('Pairing ID is required for updates');
      }

      await updatePairing.mutateAsync({
        where: {
          id: data.id,
        },
        data: {
          status: data.status as YupSchemas.PairingStatusType,
          track_status: data.track_status as TrackStatusType,
          in_program_track_sub_status: data.in_program_track_sub_status as InProgramTrackSubStatusType,
          discharge_incomplete_sub_status: data.discharge_incomplete_sub_status as DischargeIncompleteSubStatusType,
          incomplete_reason_sub_status: data.incomplete_reason_sub_status as IncompleteReasonSubStatusType,
          complete_reason_sub_status: data.complete_reason_sub_status as CompleteReasonSubStatusType,
          advocateUserId: data.advocateUser,
          trackId: data.track,
        },
      });
      toast({
        title: 'Success',
        description: 'The pairing has been successfully updated',
        variant: 'success',
      });
    } catch (error) {
      console.error('Error updating pairing:', error);
      toast({
        title: 'Error',
        description: 'There was an error updating the pairing. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const canCreateNewPairing = !pairingData?.some(
    (pairing) =>
      pairing.status === YupSchemas.PairingStatusType.PAIRED ||
      pairing.status === YupSchemas.PairingStatusType.WAITING_TO_BE_PAIRED,
  );

  const isEditingTrackEnabled = canCreateNewPairing && isCoordinatorOrAdmin;

  const updateMom = useUpdateMom();
  const handleUpdateMomStatusInProgram = async (data: Record<string, unknown>) => {
    try {
      await updateMom.mutateAsync({
        where: {
          id,
        },
        data: {
          ...(data.status && data.status === YupSchemas.MomStatus.ACTIVE
            ? {
                prospect_status: YupSchemas.ProspectStatus.ENGAGED_IN_PROGRAM,
              }
            : {}),
          ...data,
        },
      });
      toast({
        title: 'Success',
        description: 'The mom status in program has been successfully updated',
        variant: 'success',
      });
    } catch (error) {
      console.error('Error updating mom status in program:', error);
      toast({
        title: 'Error',
        description: 'There was an error updating the mom status in program. Please try again.',
        variant: 'destructive',
      });
    }
  };

  if (isLoadingMomData) {
    return <div>Loading...</div>;
  }

  if (!momData) {
    return <div>No mom data found</div>;
  }

  const locationFormValues = {
    affiliate: momData.affiliate?.name ?? '',
  };

  const statusFormValues = {
    status: momData.status ?? '',
  };

  return (
    <WithMomProfile momId={id}>
      <MomProfileWrapper viewId='info' momId={id}>
        <MomBasicInfoWrapper momId={id} currentTab='ema-info'>
          <div className='rounded-md border bg-white p-6'>
            <h2 className='mb-4 text-2xl font-bold'>ĒMA Info</h2>

            <FormFactory
              key='location-form'
              fields={momBasicInfoEmaInfoAffiliateLocationField}
              schema={momBasicInfoEmaInfoAffiliateLocationSchema}
              onSubmit={() => {}}
              actionButtonsComponent={<div></div>}
              defaultValues={locationFormValues}
            />

            <FormFactory
              key='status-form'
              fields={momBasicInfoEmaInfoFormFields}
              schema={momBasicInfoEmaInfoFormSchema}
              onSubmit={handleUpdateMomStatusInProgram}
              defaultValues={statusFormValues}
              actionButtonsComponent={
                <div className='my-auto flex w-full min-w-[240px] flex-1 shrink basis-0 items-center justify-end gap-3 self-stretch border-t-[1px] pt-4'>
                  <Button type='submit'>Save changes</Button>
                </div>
              }
            />

            {canCreateNewPairing && (
              <>
                <div className='my-8 flex w-full items-center justify-end justify-between'>
                  <h2 className='text-2xl font-semibold'>Create New Pairing</h2>
                  <GenericFormModal
                    modalTitle='Create Pairing'
                    fields={momBasicInfoEMAInfoPairingFields(
                      advocateOptions,
                      trackOptions,
                      isCoordinatorOrAdmin,
                      isEditingTrackEnabled,
                    )}
                    schema={momBasicInfoEmaInfoPairingSchema}
                    onSubmit={handleCreatePairing}
                    trigger={<Button>Create Pairing</Button>}
                    open={isModalOpen}
                    onOpenChange={setIsModalOpen}
                  />
                </div>
              </>
            )}

            {pairingData
              ?.filter((pairing) => pairing.status !== YupSchemas.PairingStatusType.PAIRING_COMPLETE)
              .map((pairing) => {
                const transformedData = {
                  id: pairing.id,
                  advocateUser: pairing.advocateUser?.id,
                  track: pairing.track?.id,
                  status: pairing.status || undefined,
                  track_status: pairing.track_status || undefined,
                  in_program_track_sub_status: pairing.in_program_track_sub_status || undefined,
                  discharge_incomplete_sub_status: pairing.discharge_incomplete_sub_status || undefined,
                  incomplete_reason_sub_status: pairing.incomplete_reason_sub_status || undefined,
                  complete_reason_sub_status: pairing.complete_reason_sub_status || undefined,
                };

                return (
                  <div key={pairing.id}>
                    <h2 className='mb-4 text-2xl font-semibold'>Current Pairings</h2>
                    <div key={pairing.id} className='mb-8'>
                      <h3 className='text-md mb-4 font-semibold text-emaTextSecondary'>
                        Pairing with {pairing.advocateUser?.firstName} {pairing.advocateUser?.lastName}
                      </h3>
                      <FormFactory
                        fields={momBasicInfoEMAInfoPairingFields(
                          advocateOptions,
                          trackOptions,
                          isCoordinatorOrAdmin,
                          isEditingTrackEnabled,
                        )}
                        schema={momBasicInfoEmaInfoPairingSchema}
                        onSubmit={handleUpdatePairing}
                        actionButtonsComponent={
                          <div className='my-auto flex w-full min-w-[240px] flex-1 shrink basis-0 items-center justify-end gap-3 self-stretch border-t-[1px] pt-4'>
                            <Button type='submit'>Save changes</Button>
                          </div>
                        }
                        defaultValues={transformedData}
                      />
                    </div>
                  </div>
                );
              })}
            <div className='mb-4 text-xl font-semibold text-emaTextSecondary'>Completed Pairings</div>
            <Accordion type='single' collapsible className='w-full'>
              {pairingData
                ?.filter((pairing) => pairing.status === YupSchemas.PairingStatusType.PAIRING_COMPLETE)
                .map((pairing) => {
                  const transformedData = {
                    id: pairing.id,
                    advocateUser: pairing.advocateUser?.id,
                    track: pairing.track?.id,
                    status: pairing.status || undefined,
                    track_status: pairing.track_status || undefined,
                    in_program_track_sub_status: pairing.in_program_track_sub_status || undefined,
                    discharge_incomplete_sub_status: pairing.discharge_incomplete_sub_status || undefined,
                    incomplete_reason_sub_status: pairing.incomplete_reason_sub_status || undefined,
                    complete_reason_sub_status: pairing.complete_reason_sub_status || undefined,
                  };
                  return (
                    <AccordionItem key={pairing.id} value={pairing.id}>
                      <AccordionTrigger className='text-md font-semibold text-emaTextSecondary'>
                        Pairing with {pairing.advocateUser?.firstName} {pairing.advocateUser?.lastName}
                      </AccordionTrigger>
                      <AccordionContent>
                        <FormFactory
                          fields={momBasicInfoEMAInfoPairingFields(
                            advocateOptions,
                            trackOptions,
                            isCoordinatorOrAdmin,
                            isEditingTrackEnabled,
                          )}
                          schema={momBasicInfoEmaInfoPairingSchema}
                          onSubmit={handleUpdatePairing}
                          actionButtonsComponent={<div></div>}
                          defaultValues={transformedData}
                          readOnly={true}
                        />
                      </AccordionContent>
                    </AccordionItem>
                  );
                })}
            </Accordion>
          </div>
        </MomBasicInfoWrapper>
      </MomProfileWrapper>
    </WithMomProfile>
  );
};

export default MomEmaInfoPage;
