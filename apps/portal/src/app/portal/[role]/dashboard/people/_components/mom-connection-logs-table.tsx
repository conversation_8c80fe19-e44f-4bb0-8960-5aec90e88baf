import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import TablePaginationFooter from '@/components/custom/table-pagination-footer';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import renderIconForType from '@/components/utils/renderIconForType';
import { useFindManyConnectionLog } from '@/hooks/generated';
import { decodeHtmlEntities } from '@/lib/utils';
import { formatDateFromString } from '@suiteapi/models';
import { YupSchemas } from '@suiteapi/models';
import { ChevronRight, CloudDownload } from 'lucide-react';
import { useState } from 'react';

import { openExportPage } from '../_utils/connection-logs-utils';
import { getContactMethodMappings } from '../_utils/contact-methods-utils';
import MomConnectionLogDrawer from './mom-connection-log-drawer';

const ConnectionLogTable: React.FC = () => {
  const { momProfile } = useMomProfileContext();
  const momId = momProfile?.id || '';
  const { isLoading: connectionLogsLoading, data: momConnectionLogs } = useFindManyConnectionLog({
    where: {
      mom_id: momId,
    },
  });

  const [selectedEntry, setSelectedEntry] = useState<YupSchemas.ConnectionLogSchema | null>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  const connectionsLogList = momConnectionLogs || [];

  const handleRowClick = (entry: YupSchemas.ConnectionLogSchema): void => {
    setSelectedEntry(entry);
    setIsDrawerOpen(true);
  };

  const handleCloseDrawer = (): void => {
    setIsDrawerOpen(false);
  };

  const itemsSortedByDate = [...connectionsLogList].sort((a, b) => {
    const dateA = new Date(a.date_created_c || '');
    const dateB = new Date(b.date_created_c || '');
    return dateB.getTime() - dateA.getTime();
  });

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = itemsSortedByDate.slice(indexOfFirstItem, indexOfLastItem);

  const totalPages = Math.ceil(connectionsLogList.length / itemsPerPage);

  const handlePageChange = (pageNumber: number): void => {
    setCurrentPage(pageNumber);
  };

  const contactMethodMappings = getContactMethodMappings();

  return (
    <section className='hi mt-6 w-full overflow-hidden border-gray-200 md:rounded-xl md:border md:bg-white md:shadow-sm'>
      <header className='flex w-full flex-row px-6 pb-5 pt-6 font-semibold max-md:px-5'>
        <div className='flex w-full flex-wrap gap-4'>
          <h2 className='flex-1 text-lg leading-7 text-gray-900'>Connection Logs</h2>
        </div>
        <div className='flex items-center gap-3 text-sm leading-5'>
          <Button variant='outline' className='flex gap-1' onClick={() => openExportPage(momId)}>
            <CloudDownload className='h-5 w-5' />
            Export All
          </Button>
        </div>
      </header>
      <div className='mb-4 flex flex-col gap-3 md:hidden'>
        {connectionLogsLoading ? (
          <div className='w-full px-4 pb-8 text-center'>Loading...</div>
        ) : currentItems.length <= 0 ? (
          <div className='w-full px-4 pb-8 text-center'>There are currently no connection logs.</div>
        ) : (
          currentItems.map((entry) => (
            <div
              key={entry.id}
              onClick={() => {
                handleRowClick(entry);
              }}
              className='cursor-pointer rounded-2xl bg-emaBgQuaternary p-4'
            >
              <div className='mb-2 flex w-full flex-row justify-between'>
                <div className='flex items-center gap-2 text-sm'>
                  {renderIconForType(entry.contact_method_c)} {contactMethodMappings[entry.contact_method_c]}
                </div>
                <div className='flex flex-row'>
                  <p className='text-emaTextQuaternary'>
                    {entry.date_created_c
                      ? formatDateFromString(new Date(entry.date_created_c).toISOString())
                      : 'date not available'}
                  </p>
                  <ChevronRight className='text-emaTextQuaternary' />
                </div>
              </div>
              <div className='mb-2 line-clamp-2'>{decodeHtmlEntities(entry.summary_c)}</div>
              <p className='text-sm font-light'>Contacted by: {entry.created_by_name}</p>
            </div>
          ))
        )}
      </div>
      <Table className='hidden md:block'>
        <TableHeader>
          <TableRow>
            <TableHead className='md:min-w-[159px]'>Type</TableHead>
            <TableHead className='md:min-w-[110px]'>Date</TableHead>
            <TableHead className='md:w-full'>Summary</TableHead>
          </TableRow>
        </TableHeader>
        {connectionLogsLoading || currentItems.length <= 0 ? (
          <TableBody>
            <TableRow>
              <TableCell colSpan={3} className='pt-4 text-center'>
                <div className='w-full pt-8'>
                  {connectionLogsLoading ? 'Loading...' : 'There are currently no connection logs.'}
                </div>
              </TableCell>
            </TableRow>
          </TableBody>
        ) : (
          <TableBody>
            {currentItems.map((entry) => (
              <TableRow
                key={entry.id}
                onClick={() => {
                  handleRowClick(entry);
                }}
                className='cursor-pointer hover:bg-gray-50'
              >
                <TableCell className='font-medium'>
                  <div className='flex items-center gap-2'>
                    {renderIconForType(entry.contact_method_c)} {contactMethodMappings[entry.contact_method_c]}
                  </div>
                </TableCell>
                <TableCell>
                  {entry.date_created_c
                    ? formatDateFromString(new Date(entry.date_created_c).toISOString())
                    : 'date not available'}
                </TableCell>
                <TableCell>{decodeHtmlEntities(entry.summary_c)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        )}
      </Table>
      <div className='rounded bg-background md:p-4'>
        <TablePaginationFooter currentPage={currentPage} totalPages={totalPages} onPageChange={handlePageChange} />
      </div>
      <MomConnectionLogDrawer
        isOpen={isDrawerOpen}
        onClose={handleCloseDrawer}
        entry={selectedEntry}
        contactMethodMappings={contactMethodMappings}
      />
    </section>
  );
};

export default ConnectionLogTable;
