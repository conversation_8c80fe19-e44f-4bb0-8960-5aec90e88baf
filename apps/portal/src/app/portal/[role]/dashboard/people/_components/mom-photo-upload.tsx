'use client';

import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { PhotoUpload } from '@/components/custom/photo-upload';
import { removeMomPhoto, uploadMomPhoto } from '@/lib/portal';
import type { MomType } from '@/types/schemas/mom';
import { useQueryClient } from '@tanstack/react-query';
import { UserRound } from 'lucide-react';
import { useEffect, useState } from 'react';

// Event name for mom photo updates
const MOM_PHOTO_UPDATED_EVENT = 'mom-photo-uploaded';
const MOM_PHOTO_DELETED_EVENT = 'mom-photo-deleted';

export default function MomPhotoUpload() {
  const { momProfile, fetchMomProfile, updateMomProfile } = useMomProfileContext();
  const momId = momProfile?.id;
  const queryClient = useQueryClient();
  const [refreshKey, setRefreshKey] = useState(Date.now());

  // Listen for photo updates from other components
  useEffect(() => {
    const handlePhotoUpdate = () => {
      // Force invalidation when photo is updated elsewhere
      if (momId) {
        queryClient.invalidateQueries({ queryKey: ['FindUniqueMom', { where: { id: momId } }] });
        // Re-fetch the mom profile to get the updated photo URLs
        fetchMomProfile(momId);
      }
    };

    // Listen for general photo updates
    window.addEventListener('photo-updated', handlePhotoUpdate);

    // Listen for mom-specific photo updates
    window.addEventListener(MOM_PHOTO_UPDATED_EVENT, handlePhotoUpdate);
    window.addEventListener(MOM_PHOTO_DELETED_EVENT, handlePhotoUpdate);

    return () => {
      window.removeEventListener('photo-updated', handlePhotoUpdate);
      window.removeEventListener(MOM_PHOTO_UPDATED_EVENT, handlePhotoUpdate);
      window.removeEventListener(MOM_PHOTO_DELETED_EVENT, handlePhotoUpdate);
    };
  }, [momId, queryClient, fetchMomProfile]);

  // Get the photo URL from the mom profile data
  const momPhoto = momProfile?.photoUrl || undefined;
  // Safely access thumbnailUrl if available
  const momThumbnail = momProfile?.thumbnailUrl || undefined;

  return (
    <div className='rounded-md border bg-white p-3 pb-6'>
      <PhotoUpload
        key={refreshKey}
        id={momId}
        photoUrl={momPhoto}
        thumbnailUrl={momThumbnail}
        uploadFunction={uploadMomPhoto as (id: string, file: File) => Promise<MomType>}
        removeFunction={removeMomPhoto as (id: string) => Promise<MomType>}
        queryKey='mom'
        additionalInvalidations={['mom-photo', 'FindUniqueMom']}
        entityName='Mom'
        placeholderComponent={<UserRound className='h-28 w-28 text-muted-foreground' />}
        onSuccess={(data?: MomType) => {
          // Invalidate queries to refresh data
          if (momId) {
            queryClient.invalidateQueries({ queryKey: ['FindUniqueMom'] });
            queryClient.invalidateQueries({ queryKey: ['FindUniqueMom', { where: { id: momId } }] });
            queryClient.invalidateQueries({ queryKey: ['mom-photo'] });

            // Update the momProfile in context with the new data
            if (data) {
              // Convert languages_c to string[] if needed
              if (data.languages_c) {
                data.languages_c = data.languages_c.map((lang: string) => String(lang));
              }
              updateMomProfile(data as MomType);
            }

            // Also re-fetch from API to ensure we have the latest data
            fetchMomProfile(momId);
          }

          // Force complete component remount by updating key
          setRefreshKey(Date.now());

          // Dispatch general event with entity info for backward compatibility
          const photoUpdatedEvent = new CustomEvent('photo-updated', {
            detail: {
              entityType: 'mom',
              entityId: momId,
            },
          });
          window.dispatchEvent(photoUpdatedEvent);

          // Dispatch mom-specific event
          window.dispatchEvent(
            new CustomEvent(MOM_PHOTO_UPDATED_EVENT, {
              detail: { momId, action: 'upload', timestamp: Date.now() },
            }),
          );
        }}
      />
    </div>
  );
}
