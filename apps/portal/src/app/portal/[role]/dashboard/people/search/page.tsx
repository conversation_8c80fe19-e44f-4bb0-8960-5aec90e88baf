'use client';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { sfetch } from '@/lib/sfetch';
import { formatPhoneNumber } from '@/lib/utils';
import { useSearchParams } from 'next/navigation';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

const SearchPage = () => {
  const searchParams = useSearchParams();
  const query = searchParams.get('q');
  const [results, setResults] = useState<{ id: string; name: string; role: string; email: string; phone: string }[]>(
    [],
  );
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const pathname = usePathname();

  useEffect(() => {
    if (query) {
      sfetch(`/v1/search?q=${encodeURIComponent(query)}`)
        .then((res) => res.json())
        .then((data) => setResults([...data.results]))
        .catch((err) => console.error('Error fetching search results:', err))
        .finally(() => setIsLoading(false));
    }
  }, [query]);

  // This is necessary because the role is returned from the API in lowercase or it is not returned at all
  const capitalizeFirstLetter = (str?: string) => {
    return str ? str.charAt(0).toUpperCase() + str.slice(1) : 'No role';
  };

  return (
    <div className='p-6'>
      <h1 className='mb-4 text-xl font-semibold'>Search Results for &quot;{query}&quot;</h1>
      {isLoading ? (
        <p>Loading...</p>
      ) : results.length > 0 ? (
        <div className='grid gap-4'>
          {results.map((result) => (
            <Card key={result.id} className='flex items-center justify-between p-4'>
              <div className='flex flex-col'>
                <span className='text-lg font-semibold'>{result.name}</span>
                <span className='flex items-center gap-2'>
                  <span className='text-sm font-semibold'>Role:</span>
                  <span className='text-sm'>{capitalizeFirstLetter(result.role)}</span>
                </span>
                {result.email ? (
                  <span className='flex items-center gap-2'>
                    <span className='text-sm font-semibold'>Email:</span>
                    <span className='text-sm'>{result.email}</span>
                  </span>
                ) : null}
                <span className='flex items-center gap-2'>
                  {result.phone ? (
                    <>
                      <span className='text-sm font-semibold'>Phone:</span>
                      <span className='text-sm'>{formatPhoneNumber(result.phone)}</span>
                    </>
                  ) : null}
                </span>
              </div>

              <Button
                variant='outline'
                onClick={() => {
                  const basePathname = pathname.replace('/search', '');
                  router.push(`${basePathname}/${result.role}s/${result.id}`);
                }}
              >
                View
              </Button>
            </Card>
          ))}
        </div>
      ) : (
        <p className='text-gray-500'>No results found.</p>
      )}
    </div>
  );
};

export default SearchPage;
