'use client';

import MomAssessments from '@/app/portal/_components/assessments/assessment';
import { useAssessmentsTabsContext } from '@/app/portal/context/assessments-tabs-context';
import { useEffect } from 'react';

const AssessmentPage = () => {
  const { setCurrentTab } = useAssessmentsTabsContext();

  useEffect(() => {
    setCurrentTab('assessment');
  }, []);

  return (
    <div className='w-full'>
      <MomAssessments />
    </div>
  );
};

export default AssessmentPage;
