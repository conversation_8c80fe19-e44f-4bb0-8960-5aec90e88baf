import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import {
  editConcreteIndicatorsFormFields,
  editConcreteIndicatorsFormSchema,
} from '@/app/portal/lib/edit-concrete-indicators-form-config';
import FormFactory from '@/components/custom/form-factory';
import ResponsiveDrawer from '@/components/custom/responsive-drawer';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useState } from 'react';

interface EditConcreteIndicatorsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

const tabs = [
  { label: 'Pre Assessment', value: 'pre' },
  { label: 'Post Assessment', value: 'post' },
];

const EditConcreteIndicatorsDrawer: React.FC<EditConcreteIndicatorsDrawerProps> = ({ isOpen, onClose }) => {
  const { momProfile } = useMomProfileContext();
  const [activeTab, setActiveTab] = useState('pre');

  const mom = momProfile?.name || '';

  const changeTab = () => {
    const tab = activeTab == 'post' ? 'pre' : 'post';
    setActiveTab(tab);
  };

  return (
    <ResponsiveDrawer isOpen={isOpen} onClose={onClose} title={<DrawerTitle momName={mom} />}>
      <div className='space-y-6'>
        <Tabs value={activeTab} onValueChange={changeTab}>
          <TabsList className='m-6 mx-6 grid grid-cols-2'>
            {tabs.map((item) => (
              <TabsTrigger
                key={item.value}
                value={item.value}
                className='text-sm font-semibold leading-5 text-gray-500 data-[state=active]:bg-white data-[state=active]:shadow'
              >
                {item.label}
              </TabsTrigger>
            ))}
          </TabsList>
          <div className='px-6'>
            <TabsContent value='pre' className='space-y-6 text-emaTextSecondary'>
              <div>
                <FormFactory
                  fields={editConcreteIndicatorsFormFields}
                  schema={editConcreteIndicatorsFormSchema}
                  onSubmit={() => {}}
                  formWrapperClassName='flex-col'
                  formFieldElClass='w-full'
                  actionButtonsComponent={
                    <div className='mt-2 flex justify-end border-t pb-5 pt-4'>
                      <Button type='submit' onClick={() => {}} className='bg-darkGreen'>
                        Save
                      </Button>
                    </div>
                  }
                />
              </div>
            </TabsContent>
            <TabsContent value='post'>
              <div>
                <FormFactory
                  fields={editConcreteIndicatorsFormFields}
                  schema={editConcreteIndicatorsFormSchema}
                  onSubmit={() => {}}
                  formWrapperClassName='flex-col'
                  formFieldElClass='w-full'
                  actionButtonsComponent={
                    <div className='mt-2 flex justify-end border-t pb-5 pt-4'>
                      <Button type='submit' onClick={() => {}} className='bg-darkGreen'>
                        Save
                      </Button>
                    </div>
                  }
                />
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </ResponsiveDrawer>
  );
};

export default EditConcreteIndicatorsDrawer;

const DrawerTitle = ({ momName }: { momName?: string }) => {
  return (
    <div className='ml-4 flex flex-col'>
      <p className='mb-2 text-xl font-semibold'>Edit Concrete Indicators</p>
      <p className='text-left text-sm font-light'>{momName}</p>
    </div>
  );
};
