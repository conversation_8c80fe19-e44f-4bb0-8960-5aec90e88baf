'use client';

import { useMomFlaggedNeedsContext } from '@/app/portal/context/mom-flagged-needs-context';
import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { useMomSessionNotes } from '@/app/portal/context/mom-session-notes-context';
import GenericTable from '@/components/custom/generic-table';
import { Button } from '@/components/ui/button';
import { TableCell } from '@/components/ui/table';
import activityDescriptionToRender from '@/components/utils/renderIconForActivityDescription';
import { useFindManyConnectionLog, useFindManyCoordinatorNote } from '@/hooks/generated';
import { useToast } from '@/hooks/use-toast';
import { SessionSchema } from '@/types/schemas/session';
import { formatDateFromString } from '@suiteapi/models';
import { ninetyDaysAgo } from '@suiteapi/models';
import { YupSchemas } from '@suiteapi/models';
import { usePathname, useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import React from 'react';

import BenevolenceNeedDrawer from '../../../_components/benevolence-need-drawer';
import CoordinatorNotesDrawer from '../../../_components/coordinator-notes-drawer';
import MomConnectionLogDrawer from '../../../_components/mom-connection-log-drawer';
import { getContactMethodMappings } from '../../../_utils/contact-methods-utils';

type activityTableData = {
  date: Date;
  person: string | null | undefined;
  activityDescription: string | React.ReactNode;
  action: boolean;
  url?: string;
} & (
  | { type: 'connectionLog'; originalData: YupSchemas.ConnectionLogSchema }
  | { type: 'benevolence'; originalData: YupSchemas.BenevolenceSchema }
  | { type: 'sessionLog'; originalData: SessionSchema }
  | { type: 'coordinatorNote'; originalData: YupSchemas.CoordinatorNotesSchema }
);

const OverviewActivityOverview = () => {
  const [activityData, setActivityData] = useState<activityTableData[]>([]);
  const { momFlaggedNeeds, fetchMomFlaggedNeeds } = useMomFlaggedNeedsContext();
  const { momProfile } = useMomProfileContext();
  const { fetchSessionsList, sessionsList } = useMomSessionNotes();
  const pathname = usePathname();
  const router = useRouter();
  const momId = momProfile?.id || '';
  const { toast } = useToast();
  const [isConnectionLogDrawerOpen, setIsConnectionLogDrawerOpen] = useState(false);
  const [isBenevolenceDrawerOpen, setIsBenevolenceDrawerOpen] = useState(false);
  const [selectedConnectionLogEntry, setSelectedConnectionLogEntry] = useState<YupSchemas.ConnectionLogSchema | null>(
    null,
  );
  const [selectedBenevolenceEntry, setSelectedBenevolenceEntry] = useState<YupSchemas.BenevolenceSchema | null>(null);
  const [isCoordinatorNotesDrawerOpen, setIsCoordinatorNotesDrawerOpen] = useState(false);
  const [selectedCoordinatorNotesEntry, setSelectedCoordinatorNotesEntry] =
    useState<YupSchemas.CoordinatorNotesSchema | null>(null);

  const handleRowClick = (item: activityTableData) => {
    if (item.type === 'connectionLog') {
      setSelectedConnectionLogEntry(item.originalData);
      setIsConnectionLogDrawerOpen(true);
    } else if (item.type === 'benevolence') {
      setSelectedBenevolenceEntry(item.originalData);
      setIsBenevolenceDrawerOpen(true);
    } else if (item.type === 'sessionLog') {
      router.push(item.url || '');
    } else if (item.type === 'coordinatorNote') {
      setSelectedCoordinatorNotesEntry(item.originalData);
      setIsCoordinatorNotesDrawerOpen(true);
    }
  };

  const rowRenderer = useCallback((item: activityTableData) => {
    return (
      <>
        <TableCell>{formatDateFromString(item.date.toISOString())}</TableCell>
        <TableCell>{item.person}</TableCell>
        <TableCell>
          <span className='flex items-center capitalize'>{item.activityDescription}</span>
        </TableCell>
        <TableCell>
          {item.action ? (
            <Button variant='outline' size='sm' onClick={() => handleRowClick(item)}>
              View
            </Button>
          ) : (
            ''
          )}
        </TableCell>
      </>
    );
  }, []);

  const mobileRenderer = useCallback((item: activityTableData) => {
    return (
      <div onClick={() => handleRowClick(item)} className='flex items-start justify-between gap-4 p-2'>
        <div className='flex-1 space-y-2'>
          <div className='text-sm text-muted-foreground'>{formatDateFromString(item.date.toISOString())}</div>
          <div className='font-medium'>{item.person}</div>
          <div className='line-clamp-2 text-sm text-gray-600'>
            <span className='flex items-center capitalize'>{item.activityDescription}</span>
          </div>
        </div>
        {item.action && (
          <Button variant='outline' onClick={() => router.push(item.url || '')}>
            View
          </Button>
        )}
      </div>
    );
  }, []);

  useEffect(() => {
    if (momProfile?.id) {
      fetchMomFlaggedNeeds(String(momProfile.id));
    }
  }, [momProfile, fetchMomFlaggedNeeds]);

  const {
    isLoading: connectionLogsLoading,
    data: momConnectionLogs,
    isError: connectionLogsIsError,
    error: connectionLogsError,
  } = useFindManyConnectionLog({
    where: {
      mom_id: momId,
      created_at: {
        gte: ninetyDaysAgo(), // only show logs from the last 30 days
      },
    },
  });

  const {
    data: coordinatorNotes,
    isLoading: coordinatorNotesLoading,
    isError: coordinatorNotesIsError,
    error: coordinatorNotesError,
  } = useFindManyCoordinatorNote({
    where: {
      mom_id: momId,
      created_at: {
        gte: ninetyDaysAgo(), // only show logs from the last 30 days
      },
    },
  });

  useEffect(() => {
    if (momId) {
      fetchSessionsList(momId);
    }
  }, [momId, fetchSessionsList]);

  useEffect(() => {
    const allActivity = [
      ...(momConnectionLogs?.map((log) => ({
        date: log.created_at,
        person: log.created_by_name,
        activityDescription: activityDescriptionToRender('Connection_Log'),
        action: true,
        type: 'connectionLog' as const,
        originalData: log,
      })) || []),
      ...(momFlaggedNeeds?.map((need) => ({
        date: new Date(need.created_at || ''),
        person: need.created_by_name,
        activityDescription: activityDescriptionToRender('Benevolence'),
        action: true,
        type: 'benevolence' as const,
        originalData: need,
      })) || []),
      ...(sessionsList
        ?.filter(
          (session): session is typeof session & { session_type: 'Support_Session' | 'Track_Session' } =>
            session.session_type !== 'Referral_Session',
        )
        .map((session) => ({
          date: new Date(session.date_start || ''),
          person: session.created_by_name,
          activityDescription: activityDescriptionToRender(session.session_type),
          action: true,
          type: 'sessionLog' as const,
          url: `${pathname}/session-notes/${session.id}`,
          originalData: session,
        })) || []),
      ...(coordinatorNotes?.map((note) => ({
        date: new Date(note.created_at || ''),
        person: note.created_by_name,
        activityDescription: activityDescriptionToRender('Coordinator_Note'),
        action: true,
        type: 'coordinatorNote' as const,
        originalData: note,
      })) || []),
    ];

    const sortedActivity = allActivity.sort((a, b) => b.date.getTime() - a.date.getTime());
    setActivityData(sortedActivity);
  }, [momConnectionLogs, momFlaggedNeeds, sessionsList, coordinatorNotes]);

  // Handle error states for what we have error handling on at the moment
  if (connectionLogsIsError) {
    toast({
      title: 'Error loading connection logs',
      description: connectionLogsError?.message || 'Failed to load connection history',
      variant: 'destructive',
    });
  }

  if (coordinatorNotesIsError) {
    toast({
      title: 'Error loading coordinator notes',
      description: coordinatorNotesError?.message || 'Failed to load coordinator notes',
      variant: 'destructive',
    });
  }

  if (connectionLogsLoading || coordinatorNotesLoading) {
    return <div>Loading history...</div>;
  }

  return (
    <div className='py-8 xl:min-w-[700px]'>
      <GenericTable
        headers={['Date', 'Person', 'Activity Added', 'Action']}
        headerSection={
          <div>
            <h2 className='text-lg font-semibold'>All Activity</h2>
            <p className='text-sm text-emaTextTertiary'>
              View support sessions, track sessions, connection logs, coordinator notes, and flagged needs from the last
              30 days.
            </p>
          </div>
        }
        columnWidths={[15, 25, 45, 20]}
        data={activityData}
        emptyMessage='No activity found'
        isLoading={false}
        rowRenderer={rowRenderer}
        mobileRenderer={mobileRenderer}
        itemsPerPage={10}
      />
      <MomConnectionLogDrawer
        isOpen={isConnectionLogDrawerOpen}
        onClose={() => setIsConnectionLogDrawerOpen(false)}
        entry={selectedConnectionLogEntry}
        contactMethodMappings={getContactMethodMappings()}
      />
      <BenevolenceNeedDrawer
        isOpen={isBenevolenceDrawerOpen}
        onClose={() => setIsBenevolenceDrawerOpen(false)}
        entry={selectedBenevolenceEntry}
      />
      <CoordinatorNotesDrawer
        isOpen={isCoordinatorNotesDrawerOpen}
        onClose={() => setIsCoordinatorNotesDrawerOpen(false)}
        entry={selectedCoordinatorNotesEntry}
      />
    </div>
  );
};

export default OverviewActivityOverview;
