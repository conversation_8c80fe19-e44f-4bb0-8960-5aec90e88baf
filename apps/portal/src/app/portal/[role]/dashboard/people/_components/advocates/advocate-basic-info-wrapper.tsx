'use client';

import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import { useRouter } from 'next/navigation';
import React from 'react';

const tabs = [
  {
    navLabel: 'Basic Information',
    title: 'basic-info',
    description: 'Update advocate’s personal details and important ĒMA info here.',
  },
  {
    navLabel: 'ĒMA Info',
    title: 'ema-info',
    description: 'Here’s where you can modify the ĒMA specific information about this user. ',
  },
  {
    navLabel: 'Background Check',
    title: 'background-check',
    description: 'Check this area once the background check is complete',
  },
];

const AdvocateBasicInfoWrapper = ({
  advocateId,
  currentTab,
  children,
}: {
  advocateId: string;
  currentTab: string;
  children: React.ReactNode;
}) => {
  const router = useRouter();
  const { profile } = useDashboardContext();
  const navigateToTab = (tab: string): void => {
    const tabRoute = tab === 'basic-info' ? '/' : `/${tab}`;
    router.push(`/portal/${profile.portalRole}/dashboard/people/advocates/${advocateId}/basic-info${tabRoute}`);
  };

  return (
    <section>
      <div className='mb-5 mt-5 flex flex-col border-b border-emaBorderSecondary pb-5'>
        <h2 className='text-lg font-semibold'>Advocate Info</h2>
        <p className='text-sm text-emaTextSecondary'>
          Update this advocate’s personal details and important ĒMA info here.
        </p>
      </div>
      <div className='flex max-md:flex-col'>
        <nav className='mb-4 hidden w-full flex-col text-sm font-semibold leading-5 text-emaTextPrimary md:mb-0 md:flex md:w-[280px]'>
          {tabs.map((tab, index) => (
            <div
              key={tab.title}
              onClick={() => navigateToTab(tab.title)}
              className={`cursor-pointer gap-2 self-stretch px-3 py-2 ${index === 0 ? '' : 'mt-1'} w-full ${
                currentTab === tab.title ? 'border-l-2 border-l-emaBrandSecondary' : 'text-emaTextQuaternary'
              } min-h-[36px]`}
            >
              <p>{tab.navLabel || tab.title}</p>
              <p className='text-xs font-light text-emaTextTertiary'>{tab.description}</p>
            </div>
          ))}
        </nav>

        <div className='w-full'>{children}</div>
      </div>
    </section>
  );
};

export default AdvocateBasicInfoWrapper;
