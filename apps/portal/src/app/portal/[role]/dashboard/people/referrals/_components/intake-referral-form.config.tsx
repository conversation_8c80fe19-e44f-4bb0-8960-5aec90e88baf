import { StandardFieldConfig } from '@/types/form-field';
import { YupSchemas } from '@suiteapi/models';
import * as yup from 'yup';

import { stateOptions } from '../../../../../lib/mom-basic-info-form-config';
import {
  FatherInvolved,
  LegalCustodyStatus,
  LivesWith,
  formatFatherInvolved,
  formatLegalCustodyStatus,
  formatLivesWith,
} from '../../_utils/people-utils';

export const maltreatmentTypeOptions = [
  { value: 'neglect', label: 'Neglect' },
  { value: 'physical_abuse', label: 'Physical Abuse' },
  { value: 'sexual_abuse', label: 'Sexual Abuse' },
  { value: 'sex_trafficking', label: 'Sex Trafficking' },
  { value: 'drug_abuse', label: 'Drug Abuse' },
  { value: 'parental_incarceration', label: 'Parental Incarceration' },
  { value: 'abandonment', label: 'Abandonment' },
  { value: 'inadequate_housing', label: 'Inadequate Housing' },
  { value: 'domestic_violence', label: 'Domestic Violence' },
  { value: 'inability_to_cope', label: 'Inability to Cope (Caregiver physical or mental illness)' },
  { value: 'unknown', label: 'Unknown' },
];

export const financialStrugglesOptions = [
  { value: '-1_rent', label: 'Rent or mortgage' },
  { value: '-1_utilities', label: 'Utilities or bills (electricity/gas/heat, cell phone, etc.)' },
  { value: '-1_groceries', label: 'Groceries' },
  { value: '-1_child_care', label: 'Child care/daycare' },
  { value: '-1_medicine', label: 'Medicine, medical expenses, or co-pays' },
  { value: '-1_household', label: 'Basic household or personal hygiene items' },
  { value: '-1_transportation', label: 'Transportation' },
  { value: '100_none', label: 'I was able to pay for all of these' },
];

export const challengesOptions = [
  { value: '-1_no_medical', label: 'Delayed or not gotten medical or dental care' },
  { value: '-1_evicted', label: 'Been evicted from your home or apartment' },
  { value: '-1_shelter', label: 'Lived in a shelter, in a hotel/motel, in an abandoned building, or in a vehicle' },
  {
    value: '-1_moved',
    label: 'Moved in with other people, even temporarily, because you could not afford to pay rent, mortgage or bills',
  },
  {
    value: '-1_no_transportation',
    label: 'Lost access to your regular transportation (e.g. vehicle totaled or repossessed ',
  },
  { value: '-1_unemployed', label: 'Been unemployed when you really needed and wanted a job' },
  { value: '100_none', label: 'None of these apply to me' },
];

export const trustToAskForOptions = [
  { value: '1_money', label: 'Money/Bills/Budgeting' },
  { value: '1_relationships', label: 'Relationships and/or My Love Life' },
  { value: '1_food', label: 'Food/Nutrition' },
  { value: '1_anxiety', label: 'Anxiety/Stress and/or Depression' },
  { value: '1_parenting', label: 'Parenting/My Kids' },
  { value: '0_none', label: 'None of the Above' },
];

export const zeroToTenDisagreeAgreeOptions = [
  { value: '0', label: '0 = Strongly Disagree' },
  { value: '1', label: '1' },
  { value: '2', label: '2' },
  { value: '3', label: '3' },
  { value: '4', label: '4' },
  { value: '5', label: '5' },
  { value: '6', label: '6' },
  { value: '7', label: '7' },
  { value: '8', label: '8' },
  { value: '9', label: '9' },
  { value: '10', label: '10 = Strongly Agree' },
];

export const raceOptions = [
  { value: 'american_indian', label: 'American Indian' },
  { value: 'alaskan_native', label: 'Alaskan Native' },
  { value: 'asian', label: 'Asian' },
  { value: 'black', label: 'Black or African American' },
  { value: 'hispanic', label: 'Hispanic or Latino' },
  { value: 'middle_eastern', label: 'Middle Eastern or North African' },
  { value: 'hawaiian', label: 'Native Hawaiian or Pacific Islander' },
  { value: 'white', label: 'White' },
];

export const martialStatusOptions = [
  { value: 'single', label: 'Single and Never Married' },
  { value: 'married', label: 'Married' },
  { value: 'separated', label: 'Separated' },
  { value: 'divorced', label: 'Divorced' },
  { value: 'windowed', label: 'Windowed' },
];

export const genderOptions = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'no_answer', label: 'Prefer Not to Answer' },
];

export const fatherInvolvedOptions = Object.values(FatherInvolved)
  .filter((value) => value !== FatherInvolved.NotSpecified)
  .map((value) => ({
    value,
    label: formatFatherInvolved([value]).replace(/,\s*$/, ''),
  }));

export const frequencyInLastWeekOptions = [
  { value: '0_not_at_all', label: 'Not at all' },
  { value: '1_several', label: 'Several days' },
  { value: '2_more_then_half', label: 'More than half the days' },
  { value: '3_nearly_every', label: 'Nearly every day' },
];

export const likeMyLifeOptions = [
  { value: '0_not_at_all', label: 'Not at all like my life' },
  { value: '1_not_much', label: 'Not much like my life' },
  { value: '2_somewhat', label: 'Somewhat like my life' },
  { value: '3_quite', label: 'Quite a lot like my life' },
  { value: '4_just', label: 'Just like my life' },
];

export const empSupportOptions = [
  { value: 'ssi', label: 'Supplemental Security Income (SSI)' },
  { value: 'disability', label: 'Disability' },
  { value: 'snap', label: 'The Supplemental Nutrition Assistance Program (SNAP)' },
  { value: 'wic', label: 'WIC' },
  { value: 'tanf', label: 'Temporary Assistance for Needy Families (TANF)' },
  { value: 'child_support', label: 'Child Support' },
];

const activeChildWelfareInvolvementOptions = [
  { value: '30_custody_maintained', label: 'No Active Case - Custody Maintained' },
  { value: '0_permanently_removed', label: 'No Active Case - Permanently Removed' },
  { value: '25_supportive_services', label: 'Supportive Services' },
  { value: '20_differential_Response', label: 'Differential Response' },
  { value: '15_open_investigation', label: 'Open Investigation' },
  { value: '10_protective_services', label: 'Protective Services' },
  { value: '0_foster_care', label: 'Foster Care' },
];

export const familyPreservationGoalOptions = [
  { value: 'prevent_cps_involvement', label: 'Prevent CPS Involvement' },
  { value: 'prevent_foster_care_placement', label: 'Prevent Foster Care Placement' },
  { value: 'prevent_permanent_removal', label: 'Prevent Permanent Removal' },
];

export const familyPreservationImpactOptions = [
  { value: 'prevented_from_cps_involvement', label: 'Prevented from CPS Involvement' },
  { value: 'prevented_from_foster_care_placement', label: 'Prevented from Foster Care Placement' },
  { value: 'prevented_from_permanent_removal', label: 'Prevented from Permanent Removal' },
  { value: 'temporary_removal', label: 'Temporary Removal' },
  { value: 'permanent_removal', label: 'Permanent Removal' },
];

export const livesWithOptions = Object.values(LivesWith)
  .filter((value) => value !== LivesWith.NotSpecified)
  .map((value) => ({
    value,
    label: formatLivesWith(value),
  }));

export const custodyStatusOptions = Object.values(LegalCustodyStatus)
  .filter((value) => value !== LegalCustodyStatus.NotSpecified)
  .map((value) => ({
    value,
    label: formatLegalCustodyStatus(value),
  }));

export const referralBasicInfoFields: StandardFieldConfig[] = [
  {
    name: 'first_name',
    label: 'First Name',
    type: 'text',
    placeholder: 'First Name',
    schema: yup.string().required('First Name is required'),
  },
  {
    name: 'last_name',
    label: 'Last Name',
    type: 'text',
    placeholder: 'Last Name',
    schema: yup.string().required('Last Name is required'),
  },
  {
    name: 'primary_address_street',
    label: 'Street Address',
    type: 'text',
    placeholder: 'Street Address',
    schema: yup.string().required('Street Address is required'),
  },
  {
    name: 'primary_address_street_two_c',
    label: 'Street Address 2',
    type: 'text',
    placeholder: 'Street Address 2',
    schema: yup.string().nullable(),
  },
  {
    name: 'primary_address_city',
    label: 'City',
    type: 'text',
    placeholder: 'City',
    schema: yup.string().required('City is required'),
  },
  {
    name: 'primary_address_state',
    label: 'State',
    type: 'select',
    placeholder: 'Select',
    options: stateOptions,
    schema: yup
      .string()
      .oneOf(
        stateOptions.map((option) => option.value),
        'Invalid State',
      )
      .required('State is required'),
  },
  {
    name: 'primary_address_postalcode',
    label: 'Zip Code',
    type: 'text',
    placeholder: 'Zip Code',
    schema: yup
      .string()
      .matches(/^[0-9]{5}$/, 'Zip Code must be a 5-digit number')
      .required('Zip Code is required'),
  },
  {
    name: 'address_access_c',
    label: 'Address Access Instructions',
    type: 'textarea',
    placeholder: 'Address Access Instructions',
    schema: yup.string().nullable(),
  },
  {
    name: 'birthdate',
    label: 'Date of Birth',
    type: 'date',
    placeholder: 'Date of Birth',
    schema: yup.date().max(new Date(), 'Date of Birth must be in the past').required('Date of Birth is required'),
  },
  {
    name: 'phone_other',
    label: 'Main Phone Number',
    type: 'text',
    placeholder: '(XXX) XXX-XXXX',
    schema: YupSchemas.phoneSchema.required('Main Phone Number is required'),
  },
  {
    name: 'phone_alternate_c',
    label: 'Alternate Phone Number',
    type: 'text',
    placeholder: '(XXX) XXX-XXXX',
    schema: YupSchemas.phoneSchema.nullable(),
  },
  {
    name: 'email1',
    label: 'Personal Email',
    type: 'email',
    placeholder: 'Personal Email',
    schema: YupSchemas.emailSchema.required('Personal Email is required'),
  },
  {
    name: 'emergency_contact_name_c',
    label: 'Emergency Contact Name',
    type: 'text',
    placeholder: 'Emergency Contact Name',
    schema: yup.string().required('Emergency Contact Name is required'),
  },
  {
    name: 'emergency_contact_relation_c',
    label: 'Emergency Contact Relationship',
    type: 'text',
    placeholder: 'Emergency Contact Relationship',
    schema: yup.string().required('Emergency Contact Relationship is required'),
  },
  {
    name: 'emergency_contact_number_c',
    label: 'Emergency Contact Number',
    type: 'text',
    placeholder: '(XXX) XXX-XXXX',
    schema: YupSchemas.phoneSchema.required('Emergency Contact Number is required'),
  },
];

export const referralClientInfoFields: StandardFieldConfig[] = [
  {
    name: 'caregiver_type_c',
    label: 'Caregiver Type',
    type: 'select',
    options: YupSchemas.caregiverOptions,
    placeholder: 'Select an option',
    schema: yup
      .string()
      .oneOf(
        YupSchemas.caregiverOptions.map((option) => option.value),
        'Invalid Caregiver Type',
      )
      .required('Caregiver Type is required'),
  },
  {
    name: 'race_c',
    label: 'Race',
    type: 'select',
    options: raceOptions,
    placeholder: 'Select an option',
    schema: yup
      .string()
      .oneOf(
        raceOptions.map((option) => option.value),
        'Invalid Race',
      )
      .required('Race is required'),
  },
  {
    name: 'languages',
    label: 'Languages',
    type: 'text',
    placeholder: 'Languages you speak',
    schema: yup.string().required('Languages are required'),
  },
  {
    name: 'cultural_heritage_c',
    label: 'Cultural Heritage',
    labelDescription:
      "Share a little about your cultural background or the culture you've grown up in or lived in most of your life.",
    type: 'textarea',
    placeholder: 'Share Your Comments',
    schema: yup.string().required('Cultural Heritage is required'),
  },
  {
    name: 'martial_status',
    label: 'Martial Status',
    type: 'select',
    placeholder: 'Select One',
    options: martialStatusOptions,
    schema: yup
      .string()
      .oneOf(
        martialStatusOptions.map((option) => option.value),
        'Invalid Martial Status',
      )
      .required('Martial Status is required'),
  },
  {
    name: 'currently_pregnant_c',
    label: 'Are You Currently Pregnant?',
    type: 'button-radio-group',
    options: [
      { value: 'yes', label: 'Yes' },
      { value: 'no', label: 'No' },
    ],
    schema: yup.string().oneOf(['yes', 'no'], 'Invalid selection').required('Selection is required'),
  },
  {
    name: 'pregnant_due_date',
    label: 'Pregnancy Due Date',
    type: 'date',
    placeholder: 'Approximate due date',
    schema: yup.date().required('Pregnancy Due Date is required'),
    hidden: (values) => values?.currently_pregnant_c !== 'yes',
  },
];

export const momChildFormFields: StandardFieldConfig[] = [
  {
    name: 'first_name',
    label: 'First Name',
    type: 'text',
    placeholder: 'First Name',
    labelDescription: "Enter the child's first name",
    schema: yup.string().required('First Name is required').max(255, 'First Name cannot exceed 255 characters'),
  },
  {
    name: 'gender',
    label: 'Gender',
    type: 'select',
    options: genderOptions,
    labelDescription: "Select the child's gender.",
    placeholder: 'Select',
    schema: yup
      .string()
      .oneOf(
        genderOptions.map((option) => option.value),
        'Invalid gender',
      )
      .required('Gender is required'),
  },
  {
    name: 'lives_with',
    label: 'Lives With',
    type: 'select',
    options: livesWithOptions,
    placeholder: 'Select',
    labelDescription: 'Indicate who the child lives with',
    schema: yup
      .string()
      .oneOf(
        livesWithOptions.map((option) => option.value),
        'Invalid Lives With',
      )
      .required('Lives With is required'),
  },
  {
    name: 'legal_custody_status',
    label: 'Legal Custody Status with Client',
    type: 'select',
    placeholder: 'Select Custody Status',
    options: custodyStatusOptions,
    labelDescription: "Specify the child's legal custody status.",
    schema: yup
      .string()
      .oneOf(
        custodyStatusOptions.map((option) => option.value),
        'Invalid custody status',
      )
      .required('Legal Custody Status is required'),
  },
  {
    name: 'birthdate',
    label: 'Date of Birth',
    type: 'date',
    placeholder: 'Due Date',
    schema: yup
      .date()
      .required('Date of Birth is required')
      .nullable()
      .max(new Date(), 'Date of Birth cannot be in the future'),
  },
  {
    name: 'father_involved',
    label: 'Father Involved',
    type: 'multi-select',
    options: fatherInvolvedOptions,
    placeholder: 'Select All Applicable',
    labelDescription: "Indicate the level of involvement from the child's father.",
    schema: yup
      .array()
      .of(
        yup.object().shape({
          value: yup.string().oneOf(
            fatherInvolvedOptions.map((option) => option.value),
            'Invalid option',
          ),
          label: yup.string(),
        }),
      )
      .required('Father Involved is required'),
  },
  {
    name: 'father_involvement',
    label: 'Father Involvement',
    type: 'textarea',
    placeholder: 'Share Your Comments',
    labelDescription: "Describe the child's relationship with their father, including any support or challenges.",
    schema: yup
      .string()
      .required('Father Involvement is required')
      .max(1000, 'Father Involvement description cannot exceed 1000 characters'),
  },
  {
    name: 'active_child_welfare_involvement',
    label: 'Active Child Welfare Involvement',
    type: 'select',
    options: activeChildWelfareInvolvementOptions,
    placeholder: 'Select',
    labelDescription: 'Lowest level of child welfare involvement for any of the children.',
    schema: yup
      .string()
      .oneOf(activeChildWelfareInvolvementOptions.map((option) => option.value))
      .required('Active Child Welfare Involvement is required'),
  },
  {
    name: 'date_of_child_welfare_involvement',
    label: 'Date of Child Welfare Involvement',
    type: 'date',
    labelDescription: 'If possible, could you share when the latest change in your active child welfare case occurred?',
    schema: yup
      .date()
      .required('Date of Child Welfare Involvement is required')
      .max(new Date(), 'Date of Child Welfare Involvement cannot be in the future'),
    hidden: (values) =>
      !values?.active_child_welfare_involvement ||
      values.active_child_welfare_involvement === '30_custody_maintained' ||
      values.active_child_welfare_involvement === '0_permanently_removed',
  },
  {
    name: 'family_preservation_goal',
    label: 'Family Preservation Goal',
    type: 'select',
    options: familyPreservationGoalOptions,
    placeholder: 'Select',
    labelDescription:
      "The mother's primary goal for maintaining stability and preventing further child welfare involvement.",
    schema: yup
      .string()
      .oneOf(familyPreservationGoalOptions.map((option) => option.value))
      .required('Family Preservation Goal is required'),
  },
  {
    name: 'family_preservation_impact',
    label: 'Family Preservation Impact',
    type: 'select',
    options: familyPreservationImpactOptions,
    placeholder: 'Select',
    labelDescription: "The outcome of the mother's efforts to achieve their preservation goal.",
    schema: yup
      .string()
      .oneOf(familyPreservationImpactOptions.map((option) => option.value))
      .required('Family Preservation Impact is required'),
  },
];

export const momChildrenFormFields: StandardFieldConfig[] = [
  {
    name: 'number_of_children_c',
    label: 'Total Number of Children in and out of home',
    type: 'number',
    placeholder: 'Enter a Number',
    labelDescription: 'Number of children documented',
    schema: yup
      .number()
      .required('Total Number of Children is required')
      .integer('Value must be an integer')
      .min(0, 'Value must be greater than or equal to 0'),
  },
  {
    name: 'number_of_children_in_home_c',
    label: 'Total Number of Children in Home',
    type: 'number',
    placeholder: 'Enter a Number',
    labelDescription:
      'Number of children that live with mother. Include children temporarily placed out of home by CPS.',
    schema: yup
      .number()
      .required('Total Number of Children in Home is required')
      .integer('Value must be an integer')
      .min(0, 'Value must be greater than or equal to 0'),
  },
];

// Intake Information
const getWellBeingAssessmentIntakeInformationFields = (person: 'mom' | 'coordinator'): StandardFieldConfig[] =>
  person === 'coordinator'
    ? [
        {
          heading: 'Intake Information',
          name: 'completed_ahead',
          label: 'Completed by Mother Ahead of Time?',
          type: 'button-radio-group',
          options: [
            { value: true, label: 'Yes' },
            { value: false, label: 'No' },
          ],
          schema: yup.boolean().required('Completed ahead status is required'),
        },
        {
          name: 'meeting_method',
          label: 'Method of Meeting',
          type: 'button-radio-group',
          options: [
            { value: 'in_person', label: 'In Person' },
            { value: 'virtual', label: 'Virtual (Online)' },
          ],
          labelDescription:
            'Was this initial Family Well-being Assessment completed with the mother in person or virtually?',
          schema: yup
            .string()
            .oneOf(['in_person', 'virtual'], 'Invalid meeting method')
            .required('Meeting method is required'),
        },
        {
          name: 'staff_name_c',
          label: 'Staff Name',
          type: 'text',
          labelDescription: 'Record of Client or Coordinator documenting the Family Wellbeing Assessment',
          schema: yup.string().required('Staff name is required').max(255, 'Staff name cannot exceed 255 characters'),
        },
        {
          name: 'start_date_c',
          label: 'Assessment Start Date',
          type: 'date',
          labelDescription: 'Record of date assessment was started',
          schema: yup
            .date()
            .required('Assessment start date is required')
            .max(new Date(), 'Start date cannot be in the future'),
        },
        {
          name: 'completed_date',
          label: 'Assessment End Date',
          type: 'date',
          labelDescription: 'Record of date assessment was completed',
          schema: yup
            .date()
            .required('Assessment end date is required')
            .max(new Date(), 'End date cannot be in the future')
            .min(yup.ref('start_date_c'), 'End date cannot be before start date'),
        },
      ]
    : [];

// Child Welfare
const getWellBeingAssessmentChildWelfareFields = (person: 'mom' | 'coordinator'): StandardFieldConfig[] => [
  {
    heading: 'Child Welfare',
    name: 'cw_home_status',
    label: 'Children Home Status',
    type: 'summary-select',
    options: [
      { value: '20_in_home', label: 'No children placed out of home' },
      { value: '10_in_out_home', label: 'At least one child placed out of home and at least one child in home' },
      { value: '0_out_home', label: 'All children placed out of home' },
    ],
    labelDescription: 'Could you summarize where your children are currently living?',
    placeholder: 'Select',
    schema: yup
      .string()
      .oneOf(['20_in_home', '10_in_out_home', '0_out_home'], 'Invalid home status')
      .required('Home status is required'),
  },
  {
    name: 'cw_involvement_as_child',
    label: 'Child Welfare Involvement as a Child',
    type: 'select',
    options: [
      { value: 'no_interactions', label: 'No interactions with child welfare' },
      { value: 'family_investigated', label: 'Family investigated' },
      { value: 'received_welfare', label: 'Received child welfare services while stayed at home' },
      { value: 'placed_in_foster_care', label: 'Placed in foster care' },
      { value: 'lived_with_relatives', label: 'Lived with relatives under supervision' },
    ],
    labelDescription: 'When you were a child, did your family have any interactions with the child welfare system?',
    placeholder: 'Select',
    schema: yup
      .string()
      .oneOf(
        ['no_interactions', 'family_investigated', 'received_welfare', 'placed_in_foster_care', 'lived_with_relatives'],
        'Invalid involvement status',
      )
      .required('Child welfare involvement status is required'),
  },
  {
    name: 'cw_involvement_as_mom',
    label: 'Child Welfare Involvement as a Mom',
    labelDescription: 'Have you ever had any interactions with the child welfare system as a mom?',
    type: 'button-radio-group',
    options: [
      { value: true, label: 'Yes' },
      { value: false, label: 'No' },
    ],
    schema: yup.boolean().required('Child welfare involvement as mom is required'),
  },
  {
    name: 'cw_allegations',
    label: 'Child Welfare Allegations',
    labelDescription: 'If yes, were the allegations substantiated?',
    type: 'button-radio-group',
    options: [
      { value: true, label: 'Yes' },
      { value: false, label: 'No' },
    ],
    hidden: (values) => values?.cw_involvement_as_mom !== true,
    schema: yup.boolean().required('Child Welfare Allegations is required'),
  },
  {
    name: 'cw_maltreatment_type',
    label: 'Maltreatment Type',
    type: 'multi-select',
    options: maltreatmentTypeOptions,
    labelDescription: 'If yes, what was the maltreatment type?',
    placeholder: 'Select All Applicable',
    hidden: (values) => values?.cw_involvement_as_mom !== true,
    schema: yup
      .array()
      .of(
        yup.object().shape({
          value: yup.string().oneOf(
            maltreatmentTypeOptions.map((option) => option.value),
            'Invalid maltreatment type',
          ),
          label: yup.string(),
        }),
      )
      .required('Maltreatment Type is required'),
  },
  {
    name: 'cw_active_involvement',
    label: 'Active Child Welfare Involvement',
    type: 'summary-select',
    options: activeChildWelfareInvolvementOptions,
    placeholder: 'Select',
    labelDescription: 'Lowest level of child welfare involvement for any of the children.',
    schema: yup
      .string()
      .oneOf(
        activeChildWelfareInvolvementOptions.map((option) => option.value),
        'Invalid involvement status',
      )
      .required('Active involvement status is required'),
  },
  {
    name: 'cw_date_of_involvement',
    label: 'Date of Child Welfare Involvement',
    type: 'summary-date',
    labelDescription: 'If possible, could you share when the latest change in your active child welfare case occurred?',
    schema: yup.date().required('Date of Child Welfare Involvement is required'),
    hidden: (values) =>
      !values?.cw_active_involvement ||
      values.cw_active_involvement === '30_custody_maintained' ||
      values.cw_active_involvement === '0_permanently_removed',
  },
  {
    name: 'cw_fp_goal',
    label: 'Family Preservation Goal',
    type: 'summary-select',
    options: familyPreservationGoalOptions,
    placeholder: 'Select',
    labelDescription:
      "The mother's primary goal for maintaining stability and preventing further child welfare involvement.",
    schema: yup
      .string()
      .oneOf(
        familyPreservationGoalOptions.map((option) => option.value),
        'Invalid Family Preservation Goal',
      )
      .required('Family Preservation Goal is required'),
  },
  {
    name: 'cw_fp_impact',
    label: 'Family Preservation Impact',
    type: 'summary-select',
    options: familyPreservationImpactOptions,
    placeholder: 'Select',
    labelDescription: "The outcome of the mother's efforts to achieve their preservation goal.",
    schema: yup
      .string()
      .oneOf(
        familyPreservationImpactOptions.map((option) => option.value),
        'Invalid Family Preservation Impact',
      )
      .required('Family Preservation Impact is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'cw_notes',
          label: 'Notes: Child Welfare',
          type: 'textarea',
          labelDescription: 'Please add any additional observations or context relevant to this section. (Optional)',
          schema: yup.string().nullable(),
        },
      ] satisfies StandardFieldConfig[])
    : []),
];

// Care of Children
const getWellBeingAssessmentCareOfChildrenFields = (person: 'mom' | 'coordinator'): StandardFieldConfig[] => [
  {
    heading: 'Care of Children',
    descriptionToHeading:
      "In this section, we'll discuss your children's needs, so we can support your connection with helpful resources.",
    name: 'cc_reliable_care',
    label: 'Reliable Care for Children',
    type: 'select',
    options: [
      { value: '1_none', label: 'None' },
      { value: '5_informal', label: 'Informal' },
      { value: '12_formal', label: 'Formal' },
      { value: '15_not_needed', label: 'Not Needed (not working or all children 13+)' },
    ],
    labelDescription: 'Do you have reliable childcare that allows you to work?',
    placeholder: 'Select',
    schema: yup
      .string()
      .oneOf(['1_none', '5_informal', '12_formal', '15_not_needed'], 'Invalid care status')
      .required('Reliable care status is required'),
  },
  {
    name: 'cc_affordability',
    label: 'Childcare Affordability',
    type: 'select',
    labelDescription: 'How do you pay for childcare?',
    placeholder: 'Select',
    options: [
      { value: 'no_payment_needed', label: 'No Payment Needed' },
      { value: 'subsidized', label: 'Subsidized' },
      { value: 'subsidized_waitlist', label: 'Subsidized Waitlist' },
      { value: 'unable_to_pay', label: 'Unable to Pay' },
    ],
    schema: yup
      .string()
      .oneOf(
        ['no_payment_needed', 'subsidized', 'subsidized_waitlist', 'unable_to_pay'],
        'Invalid affordability status',
      )
      .required('Childcare affordability status is required'),
  },
  {
    name: 'cc_childcare_safety',
    label: 'Safety of Childcare Arrangements',
    type: 'button-radio-group',
    options: [
      { value: true, label: 'Yes' },
      { value: false, label: 'No' },
    ],
    labelDescription:
      'Are you comfortable with the safety and quality of your childcare arrangements? Let us know if there are concerns.',
    schema: yup.boolean().required('Childcare safety status is required'),
  },
  {
    name: 'cc_backup_care',
    label: 'Backup and Emergency Care',
    type: 'select',
    labelDescription:
      'Do you have reliable childcare options for taking a break, attending appointments, or handling emergencies?',
    placeholder: 'Select',
    options: [
      { value: '0_none', label: 'None' },
      { value: '2_sometimes', label: 'Sometimes available (day or night)' },
      { value: '3_always', label: 'Always available (day or night)' },
      { value: '3_not_needed', label: 'Not Needed (all children 13+)' },
    ],
    schema: yup
      .string()
      .oneOf(['0_none', '2_sometimes', '3_always', '3_not_needed'], 'Invalid backup care status')
      .required('Backup care status is required'),
  },
  {
    name: 'cc_school_enrollment',
    label: 'School Enrollment and Attendance',
    type: 'select',
    labelDescription: 'Are all school-age children currently enrolled and attending regularly?',
    placeholder: 'Select',
    options: [
      { value: 'yes', label: 'Yes' },
      { value: 'no', label: 'No' },
      { value: 'partial_attend', label: 'Partial Attend' },
    ],
    schema: yup
      .string()
      .oneOf(['yes', 'no', 'partial_attend'], 'Invalid enrollment status')
      .required('School enrollment status is required'),
  },
  {
    name: 'cc_special_ed',
    label: 'Special Education Needs',
    type: 'button-radio-group',
    options: [
      { value: true, label: 'Yes' },
      { value: false, label: 'No' },
    ],
    labelDescription:
      'Do any of your children have an Individualized Education Program (IEP) or require special education support?',
    schema: yup.boolean().required('Special education status is required'),
  },
  {
    name: 'cc_ed_concerns',
    label: 'Concerns About Education',
    type: 'button-radio-group',
    options: [
      { value: true, label: 'Yes' },
      { value: false, label: 'No' },
    ],
    labelDescription:
      "Do you have any concerns about your children's education or access to needed services? Has the school shared any concerns?",
    schema: yup.boolean().required('Education concerns status is required'),
  },
  {
    name: 'cc_health_ins',
    label: 'Health Insurance for Family',
    type: 'select',
    labelDescription: 'Do you and your children have insurance for routine medical care?',
    placeholder: 'Select',
    options: [
      { value: 'yes', label: 'Yes' },
      { value: 'only_children', label: 'Only Children' },
      { value: 'only_self', label: 'Only Self' },
      { value: 'none', label: 'None' },
    ],
    schema: yup
      .string()
      .oneOf(['yes', 'only_children', 'only_self', 'none'], 'Invalid insurance status')
      .required('Health insurance status is required'),
  },
  {
    name: 'cc_special_med',
    label: 'Access to Specialized Medical Care',
    type: 'select',
    placeholder: 'Select',
    options: [
      { value: 'yes_and_access', label: 'Yes, and we currently have access to care' },
      { value: 'yes_no_access', label: 'Yes, but we do not currently have access to care' },
      { value: 'no', label: 'No, there are no medical conditions' },
      { value: 'unsure', label: 'Unsure or not yet assessed' },
    ],
    labelDescription:
      'Do you or your children have any medical conditions that require specialized care, like a physical or mental health condition?',
    schema: yup
      .string()
      .oneOf(['yes_and_access', 'yes_no_access', 'no', 'unsure'], 'Invalid medical care status')
      .required('Special medical care status is required'),
  },
  {
    name: 'cc_med_access',
    label: 'Access to Medications',
    type: 'select',
    placeholder: 'Select',
    options: [
      { value: 'yes_with_access', label: 'Yes, with necessary access' },
      { value: 'yes_limited_access', label: 'Yes, with limited access' },
      { value: 'yes_no_access', label: 'Yes, without access' },
      { value: 'no', label: 'No medications' },
    ],
    labelDescription: [
      'Do you or your children take any medications, and can you access them as needed?',
      ...(person === 'coordinator'
        ? ['For Discussion:', 'Let us know if there are any issues with affordability or availability.']
        : []),
    ].join('\n'),
    schema: yup
      .string()
      .oneOf(['yes_with_access', 'yes_limited_access', 'yes_no_access', 'no'], 'Invalid Access to Medications')
      .required('Access to Medications is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'cc_notes',
          label: 'Notes: Care of Children',
          type: 'textarea',
          labelDescription: 'Please add any additional observations or context relevant to this section. (Optional)',
          schema: yup.string().nullable(),
        },
      ] satisfies StandardFieldConfig[])
    : []),
];

// Housing
const getWellBeingAssessmentHousingFields = (person: 'mom' | 'coordinator'): StandardFieldConfig[] => [
  {
    heading: 'Housing',
    descriptionToHeading:
      'Your home should be a place of safety, stability, and comfort for you and your family. In this section, we’ll talk about your current housing situation and explore any areas where support might be helpful.',
    name: 'home_type',
    label: 'Type of Housing',
    labelDescription: ' Can you describe your current housing situation?',
    type: 'select',
    placeholder: 'Select',
    options: [
      { value: '0_incarcerated', label: 'Incarcerated' },
      { value: '0_hotel', label: 'Living in a hotel/car' },
      { value: '1_shelter', label: 'Living in a shelter' },
      { value: '5_temporarily_neighbor', label: 'Temporarily living with a neighbor/friend/relative' },
      { value: '10_permanently_neighbor', label: 'Permanently living with a neighbor/friend/relative' },
      { value: '12_rental_subsidy', label: 'Rental subsidy' },
      { value: '15_rental', label: 'Rental' },
      { value: '15_owns', label: 'Owns apartment/townhouse/house' },
    ],
    schema: yup
      .string()
      .oneOf(
        [
          '0_incarcerated',
          '0_hotel',
          '1_shelter',
          '5_temporarily_neighbor',
          '10_permanently_neighbor',
          '12_rental_subsidy',
          '15_rental',
          '15_owns',
        ],
        'Invalid housing type',
      )
      .required('Housing type is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'home_category',
          label: 'Housing Category Summary',
          labelDescription: 'This question is for data purposes only; please summarize your housing situation.',
          type: 'summary-select',
          placeholder: 'Select',
          options: [
            { value: 'transitional', label: 'Transitional/Shelter' },
            { value: 'temporary', label: 'Stable Temporary' },
            { value: 'permanent', label: 'Permanent - Permanently living, Rents, or Owns' },
          ],
          schema: yup
            .string()
            .oneOf(['transitional', 'temporary', 'permanent'], 'Invalid housing category')
            .required('Housing category is required'),
        },
      ] satisfies StandardFieldConfig[])
    : []),
  {
    name: 'home_name_on_lease',
    label: 'Name on Lease',
    type: 'select',
    labelDescription: 'Is the housing in your name (i.e., is your name on the lease or mortgage)?',
    placeholder: 'Select',
    options: [
      { value: '5_yes', label: 'Yes' },
      { value: '0_no', label: 'No' },
      { value: '0_not_applicable', label: 'Not Applicable' },
    ],
    schema: yup
      .string()
      .oneOf(['5_yes', '0_no', '0_not_applicable'], 'Invalid lease status')
      .required('Lease status is required'),
  },
  // this is different than the checkboxes in the design
  {
    name: 'home_security_concerns',
    label: 'Housing Security Concerns',
    type: 'select',
    placeholder: 'Select',
    options: [
      { value: '0_yes', label: 'Yes' },
      { value: '5_no', label: 'No' },
    ],
    labelDescription: 'Do you worry about possibly losing your housing?',
    schema: yup
      .string()
      .oneOf(['0_yes', '5_no'], 'Invalid security concerns status')
      .required('Security concerns status is required'),
  },
  // this is different than the checkboxes in the design
  {
    name: 'home_recent_homeless',
    label: 'Recent Homelessness',
    type: 'select',
    placeholder: 'Select',
    options: [
      { value: '0_yes', label: 'Yes' },
      { value: '5_no', label: 'No' },
    ],
    labelDescription:
      "In the past 2 years, have you slept outside, in a car, in a shelter, or at someone else's home temporarily or in an emergency?",
    schema: yup
      .string()
      .oneOf(['0_yes', '5_no'], 'Invalid homelessness status')
      .required('Recent homelessness status is required'),
  },
  {
    name: 'home_voucher',
    label: 'Housing Voucher',
    type: 'select',
    labelDescription: [
      'Do you currently have a housing voucher through Section 8?',
      ...(person === 'coordinator'
        ? ['For Discussion:', 'Are you in need of a housing voucher through Section 8?']
        : []),
    ].join('\n'),
    placeholder: 'Select',
    options: [
      { value: 'yes', label: 'Yes' },
      { value: 'no', label: 'No' },
      { value: 'waitlisted', label: 'Waitlisted' },
    ],
    schema: yup
      .string()
      .oneOf(['yes', 'no', 'waitlisted'], 'Invalid voucher status')
      .required('Housing voucher status is required'),
  },
  {
    name: 'home_perc_toward',
    label: 'Percentage of Income Toward Housing',
    type: 'select',
    labelDescription:
      "Do you know roughly what percent of your income goes toward housing? If you're unsure, just let us know if it feels like half or more of your income.",
    placeholder: 'Select',
    options: [
      { value: '0_>50%', label: 'More than 50%' },
      { value: '3_31-49%', label: '31-49%' },
      { value: '5_<31%', label: 'Less than 31%' },
      { value: '0_not_paying', label: 'Not Paying Anything' },
    ],
    schema: yup
      .string()
      .oneOf(['0_>50%', '3_31-49%', '5_<31%', '0_not_paying'], 'Invalid percentage')
      .required('Housing percentage is required'),
  },
  {
    name: 'home_safe',
    label: 'Feeling of Safety at Home',
    type: 'button-radio-group',
    options: [
      { value: true, label: 'Yes' },
      { value: false, label: 'No' },
    ],
    labelDescription: [
      'Our homes are supposed to be a place of safety and rest for our minds and bodies. Do you and your children feel safe in your current home?',
      ...(person === 'coordinator' ? ['For Discussion:', 'What would make it feel more safe and comfortable?'] : []),
    ].join('\n'),
    schema: yup.boolean().required('Housing safety status is required'),
  },
  {
    name: 'home_risk_in_home',
    label: 'Risk from Other People in Household',
    type: 'button-radio-group',
    options: [
      { value: true, label: 'Yes' },
      { value: false, label: 'No' },
    ],
    placeholder: 'Select',
    labelDescription: 'Is there anyone in your home who makes you or your children feel uncomfortable or unsafe?',
    schema: yup.boolean().required('Housing risk status is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'home_notes',
          label: 'Notes: Housing',
          type: 'textarea',
          labelDescription: 'Please add any additional observations or context relevant to this section. (Optional)',
          schema: yup.string().nullable(),
        },
      ] satisfies StandardFieldConfig[])
    : []),
];

// Transportation
const getWellBeingAssessmentTransportationFields = (person: 'mom' | 'coordinator'): StandardFieldConfig[] => [
  {
    heading: 'Transportation',
    descriptionToHeading:
      "Let's talk about your transportation needs and access to reliable options for you and your family.",
    name: 'trnprt_access',
    label: 'Transportation Access',
    placeholder: 'Select',
    labelDescription:
      'How affordable is transportation for your daily needs?\n' +
      '• Limited Access: Dependent on others or rely on Uber/Lyft/Taxi\n' +
      '• Challenging Public Access: Bus/Metro is far, difficult with children, or not available when needed\n' +
      '• Convenient Public Access: Bus/Metro is nearby, easy with children, and available when needed\n' +
      "• Reliable Private Access: I have my own car or access to a friend/relative's car as needed\n",
    type: 'select',
    options: [
      { value: '0_limited', label: 'Limited Access' },
      { value: '3_challenging', label: 'Challenging Public Access' },
      { value: '10_convenient', label: 'Convenient Public Access' },
      { value: '10_reliable', label: 'Reliable Private Access' },
    ],
    schema: yup
      .string()
      .oneOf(['0_limited', '3_challenging', '10_convenient', '10_reliable'], 'Invalid access status')
      .required('Transportation access status is required'),
  },
  {
    name: 'trnprt_affordable',
    label: 'Transportation Affordability',
    type: 'select',
    labelDescription:
      "How affordable is transportation for your daily needs? If you're unsure, we would describe affordable as less than 10% of your monthly income.",
    placeholder: 'Select',
    options: [
      { value: '0_not_affordable', label: 'Not Affordable' },
      { value: '5_affordable', label: 'Affordable' },
    ],
    schema: yup
      .string()
      .oneOf(['0_not_affordable', '5_affordable'], 'Invalid affordability status')
      .required('Transportation affordability status is required'),
  },
  {
    name: 'trnprt_seat_access',
    label: 'Car Seat and Booster Access',
    type: 'button-radio-group',
    options: [
      { value: true, label: 'Yes' },
      { value: false, label: 'No' },
    ],
    placeholder: 'Select',
    labelDescription: 'If a private vehicle, are all children secured in car seats or boosters?',
    schema: yup.boolean().optional(),
  },
  {
    name: 'trnprt_private_safety',
    label: 'Private Vehicle Safety',
    type: 'select',
    labelDescription: [
      'If you use a private vehicle, is it safe and reliable for your needs?',
      ...(person === 'coordinator'
        ? ['For Discussion:', 'Do you have car insurance or the use of a private vehicle?']
        : []),
    ].join('\n'),
    placeholder: 'Select',
    options: [
      { value: 'safe', label: 'Safe' },
      { value: 'unsafe', label: 'Unsafe' },
      { value: 'na', label: 'Not Applicable' },
    ],
    schema: yup.string().oneOf(['safe', 'unsafe', 'na'], 'Invalid safety status').optional(),
  },
  {
    name: 'trnprt_license',
    label: "Valid Driver's License",
    type: 'select',
    labelDescription: "Do you have a valid driver's license?",
    placeholder: 'Select',
    options: [
      { value: '5_yes', label: 'Yes' },
      { value: '0_no', label: 'No' },
      { value: '5_not_required', label: 'Not required for transportation' },
      { value: '0_unable', label: 'Unable to obtain' },
    ],
    schema: yup
      .string()
      .oneOf(['5_yes', '0_no', '5_not_required', '0_unable'], 'Invalid license status')
      .required('License status is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'trnprt_notes',
          label: 'Notes: Transportation',
          type: 'textarea',
          labelDescription: 'Please add any additional observations or context relevant to this section. (Optional)',
          schema: yup.string().nullable(),
        },
      ] satisfies StandardFieldConfig[])
    : []),
];

// Resilience
const getWellBeingAssessmentResilienceFields = (person: 'mom' | 'coordinator'): StandardFieldConfig[] => [
  {
    heading: 'Resilience',
    descriptionToHeading:
      'Life can bring a lot of ups and downs. In this section, we’ll focus on the strength, hope, and determination already in you. We want to understand how you cope during hard times and how we can walk alongside you when things feel overwhelming.',
    name: 'res_positive_outlook',
    label: "Positive Outlook for Family's Future",
    type: 'select',
    labelDescription: 'The future looks good for our family.',
    placeholder: 'Select',
    options: likeMyLifeOptions,
    schema: yup
      .string()
      .oneOf(
        likeMyLifeOptions.map((option) => option.value),
        'Invalid outlook rating',
      )
      .required('Positive outlook rating is required'),
  },
  {
    name: 'res_communication',
    label: 'Family Communication',
    type: 'select',
    labelDescription: 'In my family, we take time to listen to each other.',
    placeholder: 'Select',
    options: likeMyLifeOptions,
    schema: yup
      .string()
      .oneOf(
        likeMyLifeOptions.map((option) => option.value),
        'Invalid communication rating',
      )
      .required('Communication rating is required'),
  },
  {
    name: 'res_traditions',
    label: 'Family Traditions',
    type: 'select',
    labelDescription: 'There are things we do as a family that are special just to us.',
    placeholder: 'Select',
    options: likeMyLifeOptions,
    schema: yup
      .string()
      .oneOf(
        likeMyLifeOptions.map((option) => option.value),
        'Invalid traditions rating',
      )
      .required('Traditions rating is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'res_fr_pfs_summary',
          label: 'Functioning/Resilience PFS Summary',
          type: 'summary-select',
          labelDescription:
            "Protective Factors Survey 2.0 Mean FF/R Subscale Score (Total score, divided by 3). \nDefinition: Having adaptive skills and strategies to persevere in times of crisis. Family's ability to openly share positive and negative experiences and mobilize to accept, solve, and manage problems.",
          options: [
            { value: '0_no', label: 'No Access' },
            { value: '1_rarely', label: 'Rarely Has Access' },
            { value: '2_sometimes', label: 'Sometimes Has Access' },
            { value: '3_often', label: 'Often Has Access' },
            { value: '4_always', label: 'Almost Always Has Access' },
          ],
          schema: yup
            .string()
            .oneOf(
              ['0_no', '1_rarely', '2_sometimes', '3_often', '4_always'],
              'Invalid Functioning/Resilience PFS Summary',
            )
            .required('Functioning/Resilience PFS Summary is required'),
        },
      ] satisfies StandardFieldConfig[])
    : []),
  {
    name: 'res_overall_sat',
    label: 'Overall Life Satisfaction',
    type: 'select',
    labelDescription: 'Overall, how satisfied are you with life as a whole these days?',
    placeholder: 'Select',
    options: [
      { value: '0', label: '0 = Not Satisfied at All' },
      { value: '1', label: '1' },
      { value: '2', label: '2' },
      { value: '3', label: '3' },
      { value: '4', label: '4' },
      { value: '5', label: '5' },
      { value: '6', label: '6' },
      { value: '7', label: '7' },
      { value: '8', label: '8' },
      { value: '9', label: '9' },
      { value: '10', label: '10 = Completely Satisfied' },
    ],
    schema: yup
      .string()
      .oneOf(['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10'], 'Invalid satisfaction rating')
      .required('Overall satisfaction rating is required'),
  },
  {
    name: 'res_happiness',
    label: 'Happiness in Daily Life',
    type: 'select',
    labelDescription: 'In general, how happy or unhappy do you usually feel?',
    placeholder: 'Select',
    options: [
      { value: '0', label: '0 = Extremely Unhappy' },
      { value: '1', label: '1' },
      { value: '2', label: '2' },
      { value: '3', label: '3' },
      { value: '4', label: '4' },
      { value: '5', label: '5' },
      { value: '6', label: '6' },
      { value: '7', label: '7' },
      { value: '8', label: '8' },
      { value: '9', label: '9' },
      { value: '10', label: '10 = Extremely Happy' },
    ],
    schema: yup
      .string()
      .oneOf(['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10'], 'Invalid happiness rating')
      .required('Happiness rating is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'res_sat_summary',
          label: 'Flourishing Satisfaction Summary',
          type: 'summary-select',
          labelDescription: 'Flourishing Measure Domain 1: Happiness and Life Satisfaction',
          options: [
            { value: 'low', label: 'Low (6 or less)' },
            { value: 'medium', label: 'Medium (7 to 14)' },
            { value: 'high', label: 'High (15 or more)' },
          ],
          schema: yup
            .string()
            .oneOf(['low', 'medium', 'high'], 'Invalid satisfaction summary')
            .required('Flourishing Satisfaction Summary is required'),
        },
      ] satisfies StandardFieldConfig[])
    : []),
  {
    name: 'res_fulfillment',
    label: 'Sense of Fulfillment',
    type: 'select',
    labelDescription: 'Overall, to what extent do you feel the things you do in your life are worthwhile?',
    placeholder: 'Select',
    options: [
      { value: '0', label: '0 = Not at All Worthwhile' },
      { value: '1', label: '1' },
      { value: '2', label: '2' },
      { value: '3', label: '3' },
      { value: '4', label: '4' },
      { value: '5', label: '5' },
      { value: '6', label: '6' },
      { value: '7', label: '7' },
      { value: '8', label: '8' },
      { value: '9', label: '9' },
      { value: '10', label: '10 = Completely Worthwhile' },
    ],
    schema: yup
      .string()
      .oneOf(['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10'], 'Invalid fulfillment rating')
      .required('Fulfillment rating is required'),
  },
  {
    name: 'res_purpose',
    label: 'Understanding Life Purpose',
    type: 'select',
    labelDescription: 'I understand my purpose in life',
    placeholder: 'Select',
    options: [
      { value: '0', label: '0 - Not Fulfilled at All' },
      { value: '1', label: '1' },
      { value: '2', label: '2' },
      { value: '3', label: '3' },
      { value: '4', label: '4' },
      { value: '5', label: '5' },
      { value: '6', label: '6' },
      { value: '7', label: '7' },
      { value: '8', label: '8' },
      { value: '9', label: '9' },
      { value: '10', label: '10 - Completely Fulfilled' },
    ],
    schema: yup
      .string()
      .oneOf(['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10'], 'Invalid purpose rating')
      .required('Purpose rating is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'res_purpose_summary',
          label: 'Flourishing Purpose Summary',
          type: 'summary-select',
          labelDescription: 'Flourishing Measure Domain 3: Meaning and Purpose',
          options: [
            { value: 'low', label: 'Low (6 or less)' },
            { value: 'medium', label: 'Medium (7 to 14)' },
            { value: 'high', label: 'High (15 or more)' },
          ],
          schema: yup
            .string()
            .oneOf(['low', 'medium', 'high'], 'Invalid Flourishing Purpose Summary')
            .required('Flourishing Purpose Summary is required'),
        },
      ] satisfies StandardFieldConfig[])
    : []),
  {
    name: 'res_goodness',
    label: 'Acting with Goodness',
    type: 'select',
    labelDescription:
      'I always act to promote good in all circumstances, even in difficult and challenging situations.',
    placeholder: 'Select',
    options: [
      { value: '0', label: '0 = Not True of Me' },
      { value: '1', label: '1' },
      { value: '2', label: '2' },
      { value: '3', label: '3' },
      { value: '4', label: '4' },
      { value: '5', label: '5' },
      { value: '6', label: '6' },
      { value: '7', label: '7' },
      { value: '8', label: '8' },
      { value: '9', label: '9' },
      { value: '10', label: '10 = Completely True of Me' },
    ],
    schema: yup
      .string()
      .oneOf(['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10'], 'Invalid goodness rating')
      .required('Goodness rating is required'),
  },
  {
    name: 'res_sacrifice',
    label: 'Sacrificing for Future Happiness',
    type: 'select',
    labelDescription: 'I am always able to give up some happiness now for greater happiness later.',
    placeholder: 'Select',
    options: [
      { value: '0', label: '0 = Not True of Me' },
      { value: '1', label: '1' },
      { value: '2', label: '2' },
      { value: '3', label: '3' },
      { value: '4', label: '4' },
      { value: '5', label: '5' },
      { value: '6', label: '6' },
      { value: '7', label: '7' },
      { value: '8', label: '8' },
      { value: '9', label: '9' },
      { value: '10', label: '10 = Completely True of Me' },
    ],
    schema: yup
      .string()
      .oneOf(['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10'], 'Invalid sacrifice rating')
      .required('Sacrifice rating is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'res_virtue_summary',
          label: 'Flourishing Virtue Summary',
          type: 'summary-select',
          labelDescription: 'Flourishing Measure Domain 4: Character and Virtue',
          options: [
            { value: 'low', label: 'Low (6 or less)' },
            { value: 'medium', label: 'Medium (7 to 14)' },
            { value: 'high', label: 'High (15 or more)' },
          ],
          schema: yup
            .string()
            .oneOf(['low', 'medium', 'high'], 'Invalid virtue summary')
            .required('Flourishing Virtue Summary is required'),
        },
      ] satisfies StandardFieldConfig[])
    : []),
  {
    name: 'res_belief_self',
    label: 'Belief in Self',
    type: 'select',
    labelDescription: 'I believe I have the ability to make positive changes for myself and my family.',
    placeholder: 'Select',
    options: [
      { value: '0', label: '0 = Not True of Me' },
      { value: '1', label: '1' },
      { value: '2', label: '2' },
      { value: '3', label: '3' },
      { value: '4', label: '4' },
      { value: '5', label: '5' },
      { value: '6', label: '6' },
      { value: '7', label: '7' },
      { value: '8', label: '8' },
      { value: '9', label: '9' },
      { value: '10', label: '10 = Completely True of Me' },
    ],
    schema: yup
      .string()
      .oneOf(['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10'], 'Invalid belief rating')
      .required('Self-belief rating is required'),
  },
  {
    name: 'res_support',
    label: 'Supportive Faith Community',
    type: 'button-radio-group',
    labelDescription: 'Do you have a supportive faith community?',
    options: [
      { value: true, label: 'Yes' },
      { value: false, label: 'No' },
    ],
    schema: yup.boolean().required('Faith support status is required'),
  },
  {
    name: 'res_attendance',
    label: 'Faith Services Attendance',
    type: 'select',
    labelDescription: 'Do you regularly attend faith services?',
    placeholder: 'Select',
    options: [
      { value: 'yes', label: 'Yes, regularly' },
      { value: 'sometimes', label: 'Sometimes' },
      { value: 'rarely', label: 'Rarely' },
      { value: 'no', label: 'No' },
      { value: 'na', label: 'Prefer Not to Answer' },
    ],
    schema: yup
      .string()
      .oneOf(['yes', 'sometimes', 'rarely', 'no', 'na'], 'Invalid attendance status')
      .required('Faith attendance status is required'),
  },
  {
    name: 'res_reflections',
    label: 'Reflections on Resilience',
    type: 'textarea',
    labelDescription: 'Anything else you want to tell us about this area?',
    schema: yup.string().nullable(),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'res_notes',
          label: 'Staff Notes on Resilience',
          type: 'textarea',
          labelDescription: 'Please add any additional observations or context relevant to this section. (Optional)',
          schema: yup.string().nullable(),
        },
      ] satisfies StandardFieldConfig[])
    : []),
];

// Wellness
const getWellBeingAssessmentWellnessFields = (person: 'mom' | 'coordinator'): StandardFieldConfig[] => [
  {
    heading: 'Wellness',
    descriptionToHeading:
      "In this section, we'll discuss your personal wellness, so we can connect you with helpful resources.",
    name: 'well_gad_q1',
    label: 'Feeling nervous, anxious or on edge',
    type: 'select',
    placeholder: 'Select',
    options: frequencyInLastWeekOptions,
    schema: yup
      .string()
      .oneOf(
        frequencyInLastWeekOptions.map((option) => option.value),
        'Invalid GAD rating',
      )
      .required('GAD Q1 is required'),
  },
  {
    name: 'well_gad_q2',
    label: 'Not being able to stop or control worrying',
    type: 'select',
    placeholder: 'Select',
    options: frequencyInLastWeekOptions,
    schema: yup
      .string()
      .oneOf(
        frequencyInLastWeekOptions.map((option) => option.value),
        'Invalid GAD rating',
      )
      .required('GAD Q2 is required'),
  },
  {
    name: 'well_gad_q3',
    label: 'Worrying too much about different things',
    type: 'select',
    placeholder: 'Select',
    options: frequencyInLastWeekOptions,
    schema: yup
      .string()
      .oneOf(
        frequencyInLastWeekOptions.map((option) => option.value),
        'Invalid GAD rating',
      )
      .required('GAD Q3 is required'),
  },
  {
    name: 'well_gad_q4',
    label: 'Trouble relaxing',
    type: 'select',
    placeholder: 'Select',
    options: frequencyInLastWeekOptions,
    schema: yup
      .string()
      .oneOf(
        frequencyInLastWeekOptions.map((option) => option.value),
        'Invalid GAD rating',
      )
      .required('GAD Q4 is required'),
  },
  {
    name: 'well_gad_q5',
    label: 'Being so restless it is hard to sit still',
    type: 'select',
    placeholder: 'Select',
    options: frequencyInLastWeekOptions,
    schema: yup
      .string()
      .oneOf(
        frequencyInLastWeekOptions.map((option) => option.value),
        'Invalid GAD rating',
      )
      .required('GAD Q5 is required'),
  },
  {
    name: 'well_gad_q6',
    label: 'Becoming easily annoyed or irritable',
    type: 'select',
    placeholder: 'Select',
    options: frequencyInLastWeekOptions,
    schema: yup
      .string()
      .oneOf(
        frequencyInLastWeekOptions.map((option) => option.value),
        'Invalid GAD rating',
      )
      .required('GAD Q6 is required'),
  },
  {
    name: 'well_gad_q7',
    label: 'Feeling afraid as if something awful might happen',
    type: 'select',
    placeholder: 'Select',
    options: frequencyInLastWeekOptions,
    schema: yup
      .string()
      .oneOf(
        frequencyInLastWeekOptions.map((option) => option.value),
        'Invalid GAD rating',
      )
      .required('GAD Q7 is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'well_gad_total',
          label: 'GAD7 Anxiety Summary',
          type: 'summary-select',
          placeholder: 'Select',
          options: [
            { value: '5_minimal', label: 'Minimal (0 to 4)' },
            { value: '3_mild', label: 'Mild (5 to 9)' },
            { value: '1_moderate', label: 'Moderate (10 to 14)' },
            { value: '0_severe', label: 'Severe (15 to 21)' },
          ],
          schema: yup
            .string()
            .oneOf(['5_minimal', '3_mild', '1_moderate', '0_severe'], 'Invalid GAD7 Anxiety Summary')
            .required('GAD7 Anxiety Summary is required'),
        },
      ] satisfies StandardFieldConfig[])
    : []),
  {
    name: 'well_phq_q1',
    label: 'Little interest or pleasure in doing things',
    type: 'select',
    placeholder: 'Select',
    options: frequencyInLastWeekOptions,
    schema: yup
      .string()
      .oneOf(
        frequencyInLastWeekOptions.map((option) => option.value),
        'Invalid PHQ rating',
      )
      .required('PHQ Q1 is required'),
  },
  {
    name: 'well_phq_q2',
    label: 'Feeling down, depressed, or hopeless',
    type: 'select',
    placeholder: 'Select',
    options: frequencyInLastWeekOptions,
    schema: yup
      .string()
      .oneOf(
        frequencyInLastWeekOptions.map((option) => option.value),
        'Invalid PHQ rating',
      )
      .required('PHQ Q2 is required'),
  },
  {
    name: 'well_phq_q3',
    label: 'Trouble falling or staying asleep, or sleeping too much',
    type: 'select',
    placeholder: 'Select',
    options: frequencyInLastWeekOptions,
    schema: yup
      .string()
      .oneOf(
        frequencyInLastWeekOptions.map((option) => option.value),
        'Invalid PHQ rating',
      )
      .required('PHQ Q3 is required'),
  },
  {
    name: 'well_phq_q4',
    label: 'Feeling tired or having little energy',
    type: 'select',
    placeholder: 'Select',
    options: frequencyInLastWeekOptions,
    schema: yup
      .string()
      .oneOf(
        frequencyInLastWeekOptions.map((option) => option.value),
        'Invalid PHQ rating',
      )
      .required('PHQ Q4 is required'),
  },
  {
    name: 'well_phq_q5',
    label: 'Poor appetite or overeating',
    type: 'select',
    placeholder: 'Select',
    options: frequencyInLastWeekOptions,
    schema: yup
      .string()
      .oneOf(
        frequencyInLastWeekOptions.map((option) => option.value),
        'Invalid PHQ rating',
      )
      .required('PHQ Q5 is required'),
  },
  {
    name: 'well_phq_q6',
    label: 'Feeling bad about yourself — or that you are a failure or have let yourself or your family down',
    type: 'select',
    placeholder: 'Select',
    options: frequencyInLastWeekOptions,
    schema: yup
      .string()
      .oneOf(
        frequencyInLastWeekOptions.map((option) => option.value),
        'Invalid PHQ rating',
      )
      .required('PHQ Q6 is required'),
  },
  {
    name: 'well_phq_q7',
    label: 'Trouble concentrating on things, such as reading the newspaper or watching television',
    type: 'select',
    labelDescription: 'Trouble concentrating on things, such as reading the newspaper or watching television',
    placeholder: 'Select',
    options: frequencyInLastWeekOptions,
    schema: yup
      .string()
      .oneOf(
        frequencyInLastWeekOptions.map((option) => option.value),
        'Invalid PHQ rating',
      )
      .required('PHQ Q7 is required'),
  },
  {
    name: 'well_phq_q8',
    label:
      'Moving or speaking so slowly that other people could have noticed? Or the opposite — being so fidgety or restless that you have been moving around a lot more than usual',
    type: 'select',
    placeholder: 'Select',
    options: frequencyInLastWeekOptions,
    schema: yup
      .string()
      .oneOf(
        frequencyInLastWeekOptions.map((option) => option.value),
        'Invalid PHQ rating',
      )
      .required('PHQ Q8 is required'),
  },
  {
    name: 'well_phq_q9',
    label: 'Thoughts that you would be better off dead or of hurting yourself in some way',
    type: 'select',
    placeholder: 'Select',
    options: frequencyInLastWeekOptions,
    schema: yup
      .string()
      .oneOf(
        frequencyInLastWeekOptions.map((option) => option.value),
        'Invalid PHQ rating',
      )
      .required('PHQ Q9 is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'well_phq_total',
          label: 'Depression Summary',
          type: 'summary-select',
          placeholder: 'Select',
          options: [
            { value: '5_minimal', label: 'Minimal (0 to 9)' },
            { value: '3_mild', label: 'Mild (10 to 14)' },
            { value: '1_moderate', label: 'Moderate (15 to 19)' },
            { value: '0_severe', label: 'Severe (20 to 27)' },
          ],
          schema: yup
            .string()
            .oneOf(['5_minimal', '3_mild', '1_moderate', '0_severe'], 'Invalid Depression Summary')
            .required('Depression Summary is required'),
        },
      ] satisfies StandardFieldConfig[])
    : []),
  {
    name: 'well_health_insurance',
    label: 'Health Insurance for Mother',
    labelDescription: 'Do you have insurance for routine medical care?',
    placeholder: 'Select',
    type: 'button-radio-group',
    options: [
      { value: true, label: 'Yes' },
      { value: false, label: 'No' },
    ],
    schema: yup.boolean().required('Health Insurance for Mother is required'),
  },
  {
    name: 'well_medical_care',
    label: "Mother's Access to Specialized Medical Care",
    type: 'select',
    placeholder: 'Select',
    labelDescription:
      'Do you have any medical conditions that require specialized care, like a physical or mental health condition?',
    options: [
      { value: 'yes_currently', label: 'Yes, and currently have access to care' },
      { value: 'yes_not_currently', label: 'Yes, but do not currently have access to care' },
      { value: 'no', label: 'No, there are no medical conditions' },
      { value: 'unsure', label: 'Unsure or not yet assessed' },
    ],
    schema: yup
      .string()
      .oneOf(['yes_currently', 'yes_not_currently', 'no', 'unsure'], 'Invalid medical care status')
      .required('Medical care status is required'),
  },
  {
    name: 'well_discouragement',
    label: 'Discouragement',
    type: 'select',
    placeholder: 'Select',
    labelDescription: 'Have you regularly felt down or nervous?',
    options: [
      { value: 'yes', label: "It's a current challenge for me" },
      { value: 'not_last_6_months', label: 'I have, but not in the last 6 months' },
      { value: 'not_last_year', label: 'I have, but not in the last year' },
      { value: 'no', label: "I haven't regularly felt this way" },
    ],
    schema: yup
      .string()
      .oneOf(['yes', 'not_last_6_months', 'not_last_year', 'no'], 'Invalid discouragement status')
      .required('Discouragement status is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'well_discouragement_summary',
          label: 'Occurrence',
          type: 'summary-select',
          disabled: true,
          options: [
            { value: '1_yes', label: 'Current challenge' },
            { value: '5_not_last_6_months', label: 'No challenge in the last 6 months' },
            { value: '10_not_last_12_years', label: 'No challenge in the last 12 months' },
          ],
          schema: yup
            .string()
            .oneOf(['1_yes', '5_not_last_6_months', '10_not_last_12_years'], 'Invalid occurrence status')
            .required('Occurrence status is required'),
        },
      ] satisfies StandardFieldConfig[])
    : []),
  {
    name: 'well_counseling',
    label: 'Experience with Counseling',
    type: 'select',
    placeholder: 'Select',
    labelDescription: 'What has your experience been with counseling?',
    options: [
      { value: 'past_again', label: 'I have in the past, and could benefit from it again' },
      { value: 'past_not_now', label: 'I have in the past, but do not currently need it' },
      { value: 'now', label: 'I am currently in counseling' },
      { value: 'no_need', label: 'I have not needed counseling' },
    ],
    schema: yup
      .string()
      .oneOf(['past_again', 'past_not_now', 'now', 'no_need'], 'Invalid experience with counseling status')
      .required('Experience with counseling status is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'well_counseling_summary',
          label: 'Counseling Support',
          type: 'summary-select',
          disabled: true,
          options: [
            { value: '0_need_support', label: "Need support, but currently doesn't have any" },
            { value: '4_historically', label: 'Historically have had support' },
            { value: '9_currently', label: 'Currently have support' },
            { value: '10_not_required', label: 'No support required' },
          ],
          schema: yup
            .string()
            .oneOf(
              ['0_need_support', '4_historically', '9_currently', '10_not_required'],
              'Invalid counseling support status',
            )
            .required('Counseling support status is required'),
        },
      ] satisfies StandardFieldConfig[])
    : []),
  {
    name: 'well_strategies',
    label: 'Strategies',
    type: 'select',
    placeholder: 'Select',
    labelDescription: "Do you have strategies to use when you're feeling down or nervous?",
    options: [
      { value: 'no', label: 'No' },
      { value: 'yes_not_using', label: 'Yes, but I have a hard time using them' },
      { value: 'yes_regularly', label: 'I use strategies regularly' },
      { value: 'no_need', label: "I don't regularly feel stressed or worn down" },
    ],
    schema: yup
      .string()
      .oneOf(['no', 'yes_not_using', 'yes_regularly', 'no_need'], 'Invalid strategy status')
      .required('Strategy status is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'well_strategies_summary',
          label: 'Demonstrate Capacity/Treatment',
          type: 'summary-select',
          disabled: true,
          options: [
            { value: '0_none', label: "Doesn't have any" },
            { value: '5_not_current', label: 'Not current ' },
            { value: '10_current', label: 'Current' },
            { value: '10_none_required', label: 'None Required' },
          ],
          schema: yup
            .string()
            .oneOf(
              ['0_none', '5_not_current', '10_current', '10_none_required'],
              'Invalid Demonstrate capacity/treatment summary',
            )
            .required('Demonstrate capacity/treatment is required'),
        },
      ] satisfies StandardFieldConfig[])
    : []),
  {
    name: 'well_phys_health',
    label: 'Physical Health',
    type: 'select',
    labelDescription: 'In general, how would you rate your physical health?',
    placeholder: 'Select',
    options: [
      { value: '0', label: '0 = Poor' },
      { value: '1', label: '1' },
      { value: '2', label: '2' },
      { value: '3', label: '3' },
      { value: '4', label: '4' },
      { value: '5', label: '5' },
      { value: '6', label: '6' },
      { value: '7', label: '7' },
      { value: '8', label: '8' },
      { value: '9', label: '9' },
      { value: '10', label: '10 = Excellent' },
    ],
    schema: yup
      .string()
      .oneOf(['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10'], 'Invalid health rating')
      .required('Physical health rating is required'),
  },
  {
    name: 'well_mental_health',
    label: 'Mental Health',
    type: 'select',
    labelDescription: 'How would you rate your overall mental health?',
    placeholder: 'Select',
    options: [
      { value: '0', label: '0 = Poor' },
      { value: '1', label: '1' },
      { value: '2', label: '2' },
      { value: '3', label: '3' },
      { value: '4', label: '4' },
      { value: '5', label: '5' },
      { value: '6', label: '6' },
      { value: '7', label: '7' },
      { value: '8', label: '8' },
      { value: '9', label: '9' },
      { value: '10', label: '10 = Excellent' },
    ],
    schema: yup
      .string()
      .oneOf(['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10'], 'Invalid health rating')
      .required('Mental health rating is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'well_health_summary',
          label: 'Flourishing Health Summary',
          type: 'summary-select',
          disabled: true,
          labelDescription: 'Flourishing Measure Domain 2: Mental and Physical Health',
          options: [
            { value: 'low', label: 'Low (6 or less)' },
            { value: 'medium', label: 'Medium (7 to 14)' },
            { value: 'high', label: 'High (15 or more)' },
          ],
          schema: yup
            .string()
            .oneOf(['low', 'medium', 'high'], 'Invalid health summary')
            .required('Flourishing Health Summary is required'),
        },
      ] satisfies StandardFieldConfig[])
    : []),
  {
    name: 'well_counseling_past',
    label: 'Counseling in Past',
    type: 'select',
    placeholder: 'Select',
    labelDescription: 'Have you considered or accessed counseling services in the past?',
    options: [
      { value: '5_yes_currently', label: 'Yes, I am currently receiving counseling/therapy' },
      { value: '5_yes_past', label: 'Yes, I have received counseling/therapy in the past' },
      { value: '0_no', label: 'No, I do not feel I need counseling/therapy at this time' },
    ],
    schema: yup
      .string()
      .oneOf(['5_yes_currently', '5_yes_past', '0_no'], 'Invalid counseling status')
      .required('Past counseling status is required'),
  },
  {
    name: 'well_counseling_interest',
    label: 'Interest in Counseling',
    type: 'select',
    labelDescription: 'Are you interested in counseling or therapy at this time?',
    placeholder: 'Select',
    options: [
      { value: '5_yes_currently', label: 'Yes, I am currently receiving counseling/therapy' },
      { value: '5_yes_interested', label: 'Yes, I am interested in starting counseling/therapy' },
      { value: '0_no', label: 'No, I am not interested right now, but I might be interested in the future' },
    ],
    schema: yup
      .string()
      .oneOf(['5_yes_currently', '5_yes_interested', '0_no'], 'Invalid counseling interest')
      .required('Counseling interest is required'),
  },
  {
    name: 'well_reflections',
    label: 'Reflections on Wellness',
    type: 'textarea',
    labelDescription: 'Anything else you want to tell us about this area?',
    placeholder: 'Share Your Comments',
    schema: yup.string().nullable(),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'well_notes',
          label: 'Staff Notes on Wellness',
          type: 'textarea',
          labelDescription: 'Please add any additional observations or context relevant to this section.',
          placeholder: 'Share Your Comments',
          schema: yup.string().nullable(),
        },
      ] satisfies StandardFieldConfig[])
    : []),
];

// Stress and Substance Abuse
const getWellBeingAssessmentStressAndSubstanceAbuseFields = (person: 'mom' | 'coordinator'): StandardFieldConfig[] => [
  {
    heading: 'Managing Stress and Substance Abuse',
    descriptionToHeading:
      'As part of our commitment to understanding and supporting your well-being, we ask everyone a few brief questions about their use of substances. This includes any kind of substance, such as alcohol, medications, or recreational drugs. These questions help us identify if additional resources or support might be helpful to you.',
    name: 'subs_recency',
    labelDescription:
      'In the past month, have you used alcohol, medications, or drugs that made it harder to take care of yourself, your family, or your daily tasks?',
    label: 'Recent Substance Abuse',
    type: 'select',
    placeholder: 'Select',
    options: [
      { value: 'frequently', label: 'Frequently' },
      { value: 'occasionally', label: 'Occasionally' },
      { value: 'never', label: 'Never' },
      { value: 'na', label: 'Prefer Not to Say' },
    ],
    schema: yup
      .string()
      .oneOf(['frequently', 'occasionally', 'never', 'na'], 'Invalid substance use status')
      .required('Substance use status is required'),
  },
  {
    name: 'subs_support_needed',
    label: 'Support Needed',
    type: 'select',
    labelDescription:
      'Do you feel like you might benefit from additional support to help manage or reduce your substance use? (Optional)',
    placeholder: 'Select',
    options: [
      { value: 'yes', label: 'Yes, I would like support' },
      { value: 'no', label: 'No, I feel like I am managing' },
      { value: 'unsure', label: 'Unsure but open to discussing further' },
    ],
    schema: yup
      .string()
      .oneOf(['yes', 'no', 'unsure'], 'Invalid support selection')
      .required('Support needed status is required'),
  },
  {
    name: 'subs_treatment_history',
    label: 'Treatment History',
    labelDescription: 'Trouble falling or staying asleep, or sleeping too much',
    placeholder: 'Select',
    type: 'button-radio-group',
    options: [
      { value: true, label: 'Yes' },
      { value: false, label: 'No' },
    ],
    schema: yup.boolean().required('Treatment History is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'subs_notes',
          label: 'Notes: Managing Stress and Substance Abuse',
          type: 'textarea',
          labelDescription: [
            'Please add any additional observations or context relevant to this section.',
            'For Discussion:',
            "If you've participated in treatment, was it helpful? Are you interested in continuing with similar services? (Optional)",
          ].join('\n'),
          placeholder: 'Share Your Comments',
          schema: yup.string().nullable(),
        },
      ] satisfies StandardFieldConfig[])
    : []),
];

// Social support
const getWellBeingAssessmentSocialSupportFields = (person: 'mom' | 'coordinator'): StandardFieldConfig[] => [
  {
    heading: 'Social Support',
    descriptionToHeading:
      'We ask all the moms in our program about their current partner and social relationships because we know that relationships can impact both mental health and children’s wellbeing.',
    name: 'soc_status',
    label: 'Relationship Status',
    type: 'select',
    labelDescription: 'What is your current relationship status?',
    placeholder: 'Select',
    options: [
      { value: 'partner_in_home', label: 'Partner in Home' },
      { value: 'partner_not_in_home', label: 'Partner Not in Home' },
      { value: 'no_partner', label: 'No Partner' },
    ],
    schema: yup
      .string()
      .oneOf(['partner_in_home', 'partner_not_in_home', 'no_partner'], 'Invalid relationship status')
      .required('Relationship status is required'),
  },
  {
    name: 'soc_length',
    label: 'Relationship Length',
    type: 'select',
    labelDescription: 'How long have you been in your current relationship?',
    placeholder: 'Select',
    options: [
      { value: 'less_than_6_months', label: 'Less than 6 months' },
      { value: '6_months_to_year', label: '6 to 1 year' },
      { value: '1_to_3_years', label: '1 to 3 years' },
      { value: '3_to_5_years', label: '3 to 5 years' },
      { value: '5_to_10_years', label: '5 to 10 years' },
      { value: 'more_than_10_years', label: 'More than 10 years' },
    ],
    hidden: (values) => !values?.soc_status || values.soc_status === 'no_partner',
    schema: yup
      .string()
      .oneOf(
        [
          'less_than_6_months',
          '6_months_to_year',
          '1_to_3_years',
          '3_to_5_years',
          '5_to_10_years',
          'more_than_10_years',
        ],
        'Invalid relationship length',
      )
      .required('Relationship length is required'),
  },
  {
    name: 'soc_tension',
    label: 'Relationship Tension',
    type: 'select',
    labelDescription: 'Would you say your relationship is calm, tense, or somewhere in between?',
    placeholder: 'Select',
    options: [
      { value: '6_no', label: 'No Tension' },
      { value: '3_some', label: 'Some Tension' },
      { value: '0_a_lot', label: 'A Lot of Tension' },
    ],
    hidden: (values) => !values?.soc_status || values.soc_status === 'no_partner',
    schema: yup
      .string()
      .oneOf(['6_no', '3_some', '0_a_lot'], 'Invalid tension rating')
      .required('Relationship tension rating is required'),
  },
  {
    name: 'soc_resolve_arguments',
    label: 'Resolving Arguments',
    type: 'select',
    labelDescription: 'How hard is it for you and your partner to solve problems when you disagree?',
    placeholder: 'Select',
    options: [
      { value: '6_no', label: 'No Difficulty' },
      { value: '3_some', label: 'Some Difficulty' },
      { value: '0_great', label: 'Great dDifficulty' },
    ],
    hidden: (values) => !values?.soc_status || values.soc_status === 'no_partner',
    schema: yup
      .string()
      .oneOf(['6_no', '3_some', '0_great'], 'Invalid resolution rating')
      .required('Argument resolution rating is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'soc_wast_sf',
          label: 'WAST-SF Domestic Violence Summary',
          type: 'summary-select',
          options: [
            { value: 'low', label: 'Low Likelihood of Abuse' },
            { value: 'discussion_recommended', label: 'Further Discussion Recommended' },
          ],
          labelDescription:
            'When a mother describes her relationship with her partner as having "A lot of tension" and/or "Great difficulty" working out arguments, it suggests the need for additional discussion to determine whether a referral to domestic abuse support services is necessary.',
          hidden: (values) => !values?.soc_status || values.soc_status === 'no_partner',
          schema: yup.string().oneOf(['low', 'discussion_recommended'], 'Invalid WAST-SF summary').optional(),
        },
      ] satisfies StandardFieldConfig[])
    : []),
  {
    name: 'soc_dynamics',
    label: 'Relationship Dynamics',
    type: 'textarea',
    labelDescription: 'How do you think your relationship impacts your children, both positively and negatively?',
    placeholder: 'Share Your Comments',
    schema: yup.string().required('Relationship dynamics are required'),
  },
  {
    name: 'soc_rel_reflections',
    label: 'Reflections on Partner Dynamics',
    type: 'textarea',
    labelDescription: 'Anything else you want to tell us about this area?',
    placeholder: 'Share Your Comments',
    schema: yup.string().nullable(),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'soc_rel_notes',
          label: 'Staff Notes on Partner Dynamics',
          type: 'textarea',
          labelDescription:
            'Please add any additional observations or context relevant to this section.\n' +
            'For Discussion:\n' +
            '• How would you describe the relationship?\n' +
            '• What happens when you and your partner disagree?\n' +
            '• What feelings do you have when you disagree (discomfort, anxious, scared, calm)?\n' +
            '• When you have disagreements, how do the children react?\n' +
            "• I learned that at least 1 in 4 women experience abusive relationships in their lives but it's often really hard to talk about. Is that something you would like to talk with a counselor about?",
          placeholder: 'Share Your Comments',
          schema: yup.string().nullable(),
        },
      ] satisfies StandardFieldConfig[])
    : []),
  {
    name: 'soc_faith_id',
    label: 'Faith Identity',
    type: 'select',
    labelDescription: 'Which faith tradition, if any, do you most identify with?',
    placeholder: 'Select',
    options: [
      { value: 'christian', label: 'Christian' },
      { value: 'muslim', label: 'Muslim' },
      { value: 'jewish', label: 'Jewish' },
      { value: 'hindu', label: 'Hindu' },
      { value: 'buddhist', label: 'Buddhist' },
      { value: 'none', label: 'None' },
      { value: 'other', label: 'Other' },
      { value: 'na', label: 'Prefer Not to Answer' },
    ],
    schema: yup
      .string()
      .oneOf(['christian', 'muslim', 'jewish', 'hindu', 'buddhist', 'none', 'other', 'na'], 'Invalid faith ID')
      .required('Faith identification is required'),
  },
  {
    name: 'soc_pfs_supportive_rels',
    label: 'Supportive Relationships',
    type: 'select',
    labelDescription: 'I have people who believe in me',
    placeholder: 'Select',
    options: likeMyLifeOptions,
    schema: yup
      .string()
      .oneOf(
        likeMyLifeOptions.map((option) => option.value),
        'Invalid support rating',
      )
      .required('Supportive relationships rating is required'),
  },
  {
    name: 'soc_pfs_supportive_advice',
    label: 'Supportive Advice',
    type: 'select',
    labelDescription: "I have someone in my life that gives me advice, even when it's hard to hear",
    placeholder: 'Select',
    options: likeMyLifeOptions,
    schema: yup
      .string()
      .oneOf(
        likeMyLifeOptions.map((option) => option.value),
        'Invalid advice rating',
      )
      .required('Supportive advice rating is required'),
  },
  {
    name: 'soc_pfs_support_goals',
    label: 'Support for Achieving Goals',
    type: 'select',
    labelDescription: 'When I am trying to work on achieving a goal I have friends that will support me',
    placeholder: 'Select',
    options: likeMyLifeOptions,
    schema: yup
      .string()
      .oneOf(
        likeMyLifeOptions.map((option) => option.value),
        'Invalid goals support rating',
      )
      .required('Goals support rating is required'),
  },
  {
    name: 'soc_pfs_emergency_contact',
    label: 'Emergency Childcare Support',
    type: 'select',
    labelDescription: 'When I need someone to look after my kids on short notice, I can find someone I trust',
    placeholder: 'Select',
    options: likeMyLifeOptions,
    schema: yup
      .string()
      .oneOf(
        likeMyLifeOptions.map((option) => option.value),
        'Invalid emergency contact rating',
      )
      .required('Emergency contact rating is required'),
  },
  {
    name: 'soc_trusted_network',
    label: 'Trusted Support Network for Advice',
    type: 'multi-select',
    labelDescription: 'I have people I trust to ask for advice about:',
    placeholder: 'Select',
    options: trustToAskForOptions,
    schema: yup
      .array()
      .of(
        yup.object().shape({
          value: yup.string().oneOf(
            trustToAskForOptions.map((option) => option.value),
            'Invalid Trusted network selection',
          ),
          label: yup.string(),
        }),
      )
      .required('Trusted network selection is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'soc_pfs_summary_score',
          label: 'Social Support PFS Summary',
          type: 'summary-select',
          labelDescription:
            'Perceived informal support (from family, friends, and neighbors) that helps provide for emotional needs.',
          options: [
            { value: '0_no', label: 'No support' },
            { value: '1_not_much', label: 'Not much support' },
            { value: '2_some', label: 'Some support' },
            { value: '3_a_lot', label: 'A lot of support' },
            { value: '4_full', label: 'Full support' },
          ],
          schema: yup
            .string()
            .oneOf(['0_no', '1_not_much', '2_some', '3_a_lot', '4_full'], 'Invalid Social Support PFS Summary')
            .required('Social Support PFS Summary is required'),
        },
      ] satisfies StandardFieldConfig[])
    : []),
  {
    name: 'soc_frs_content_with_rels',
    label: 'Contentment with Relationships',
    type: 'select',
    labelDescription: 'I am content with my friendships and relationships',
    placeholder: 'Scale From 0 to 10',
    options: zeroToTenDisagreeAgreeOptions,
    schema: yup
      .string()
      .oneOf(
        zeroToTenDisagreeAgreeOptions.map((option) => option.value),
        'Invalid relationship content rating',
      )
      .required('Relationship content rating is required'),
  },
  {
    name: 'soc_frs_rel_sat',
    label: 'Satisfaction in Relationships',
    type: 'select',
    labelDescription: 'My relationships are as satisfying as I would want them to be',
    placeholder: 'Scale From 0 to 10',
    options: zeroToTenDisagreeAgreeOptions,
    schema: yup
      .string()
      .oneOf(
        zeroToTenDisagreeAgreeOptions.map((option) => option.value),
        'Invalid relationship satisfaction rating',
      )
      .required('Relationship satisfaction rating is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'soc_frs_summary',
          label: 'Flourishing Relationships Summary',
          type: 'summary-select',
          labelDescription: 'Flourishing Measure Domain 5: Close Social Relationships',
          options: [
            { value: 'low', label: 'Low (6 or less)' },
            { value: 'medium', label: 'Medium (7 to 14)' },
            { value: 'high', label: 'High (15 or more)' },
          ],
          schema: yup
            .string()
            .oneOf(['low', 'medium', 'high'], 'Invalid Flourishing Relationships Summary')
            .required('Flourishing Relationships Summary is required'),
        },
      ] satisfies StandardFieldConfig[])
    : []),
  {
    name: 'soc_reflections',
    label: 'Reflections on Social Support',
    type: 'textarea',
    labelDescription: 'Anything else you want to tell us about this area?',
    placeholder: 'Share Your Comments',
    schema: yup.string().nullable(),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'soc_notes',
          label: 'Staff Notes on Social Support',
          type: 'textarea',
          labelDescription: 'Please add any additional observations or context relevant to this section.',
          placeholder: 'Share Your Comments',
          schema: yup.string().nullable(),
        },
      ] satisfies StandardFieldConfig[])
    : []),
];

// Employment and Financial
const getWellBeingAssessmentsEmploymentFields = (person: 'mom' | 'coordinator'): StandardFieldConfig[] => [
  {
    heading: 'Employment and Financial',
    descriptionToHeading:
      "Let's discuss financial stability and resources to see how we can help with your monthly needs.",
    name: 'emp_fin_struggles',
    label: 'Recent Financial Struggles',
    type: 'multi-select',
    labelDescription: 'In the past month, were you unable to pay for:',
    placeholder: 'Select All Applicable',
    options: financialStrugglesOptions,
    schema: yup
      .array()
      .of(
        yup.object().shape({
          value: yup.string().oneOf(
            financialStrugglesOptions.map((option) => option.value),
            'Invalid financial struggle option',
          ),
          label: yup.string(),
        }),
      )
      .required('Financial struggles selection is required'),
  },
  {
    name: 'emp_challenges',
    label: 'Major Challenges Over the Past Year',
    type: 'multi-select',
    labelDescription: 'In the past year, have you:',
    placeholder: 'Select All Applicable',
    options: challengesOptions,
    schema: yup
      .array()
      .of(
        yup.object().shape({
          value: yup.string().oneOf(
            challengesOptions.map((option) => option.value),
            'Invalid challenge option',
          ),
          label: yup.string(),
        }),
      )
      .required('Challenges selection is required'),
  },
  {
    name: 'emp_difficulty',
    label: 'Difficulty Meeting Monthly Needs',
    type: 'select',
    labelDescription: 'I have trouble affording what I need each month.',
    placeholder: 'Select',
    options: [
      { value: '4_never', label: 'Never' },
      { value: '3_rarely', label: 'Rarely' },
      { value: '2_sometimes', label: 'Sometimes' },
      { value: '1_often', label: 'Often' },
      { value: '0_always', label: 'Almost Always' },
    ],
    schema: yup
      .string()
      .oneOf(['4_never', '3_rarely', '2_sometimes', '1_often', '0_always'], 'Invalid difficulty rating')
      .required('Difficulty rating is required'),
  },
  {
    name: 'emp_afford_food',
    label: 'Affording Food for Family',
    type: 'select',
    labelDescription: 'I am able to afford the food I want to feed my family.',
    placeholder: 'Select',
    options: [
      { value: '0_never', label: 'Never' },
      { value: '1_rarely', label: 'Rarely' },
      { value: '2_sometimes', label: 'Sometimes' },
      { value: '3_often', label: 'Often' },
      { value: '4_always', label: 'Almost Always' },
    ],
    schema: yup
      .string()
      .oneOf(['0_never', '1_rarely', '2_sometimes', '3_often', '4_always'], 'Invalid affordability rating')
      .required('Food affordability rating is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'emp_concrete_pfs_total',
          label: 'Concrete Supports PFS Summary',
          type: 'summary-select',
          disabled: true,
          labelDescription:
            'Protective Factors Survey 2.0 Mean CS Subscale Score (Total score, divided by 4) \nDefinition: Perceived access to tangible goods and services to help families cope with stress, particularly in times of crisis or intensified need.',
          placeholder: 'Calculated After Changes are Saved',
          options: [
            { value: 'no', label: 'No Access' },
            { value: 'rarely', label: 'Rarely Has Access' },
            { value: 'sometimes', label: 'Sometimes Has Access' },
            { value: 'often', label: 'Often Has Access' },
            { value: 'always', label: 'Almost Always Has Access' },
          ],
          schema: yup.string().oneOf(['no', 'rarely', 'sometimes', 'often', 'always'], 'Invalid PFS total').optional(),
        },
      ] satisfies StandardFieldConfig[])
    : []),
  {
    name: 'emp_support_received',
    label: 'Support Received',
    type: 'multi-select',
    labelDescription: 'Do you currently receive any of the following types of support?',
    placeholder: 'Select All Applicable',
    options: empSupportOptions,
    schema: yup
      .array()
      .of(
        yup.object().shape({
          value: yup.string().oneOf(
            empSupportOptions.map((option) => option.value),
            'Invalid Support Received selection',
          ),
          label: yup.string(),
        }),
      )
      .required('Support received selection is required'),
  },
  {
    name: 'emp_support_needed',
    label: 'Support Needed',
    type: 'multi-select',
    labelDescription: 'Would you benefit from any of the following types of support?',
    placeholder: 'Select All Applicable',
    options: empSupportOptions,
    schema: yup
      .array()
      .of(
        yup.object().shape({
          value: yup.string().oneOf(
            empSupportOptions.map((option) => option.value),
            'Invalid Support Needed selection',
          ),
          label: yup.string(),
        }),
      )
      .required('Support needed selection is required'),
  },
  {
    name: 'emp_diff_manage_bills',
    label: 'Difficulty Managing Bills',
    type: 'select',
    labelDescription:
      "Do you find it hard to keep track of how much you're spending and where your money goes each month?",
    placeholder: 'Select',
    options: [
      { value: '0_always', label: 'Always' },
      { value: '3_sometimes', label: 'Sometimes' },
      { value: '5_never', label: 'Never' },
    ],
    schema: yup
      .string()
      .oneOf(['0_always', '3_sometimes', '5_never'], 'Invalid bill management rating')
      .required('Bill management rating is required'),
  },
  {
    name: 'emp_emergency_funds',
    label: 'Availability of Emergency Funds',
    type: 'select',
    labelDescription: 'Do you have any savings or extra income available for emergencies or unexpected expenses?',
    placeholder: 'Select',
    options: [
      { value: '5_always', label: 'Always' },
      { value: '2_sometimes', label: 'Sometimes' },
      { value: '0_never', label: 'Never' },
    ],
    schema: yup
      .string()
      .oneOf(['5_always', '2_sometimes', '0_never'], 'Invalid emergency funds rating')
      .required('Emergency funds rating is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'emp_fin_notes',
          label: 'Notes: Financial',
          type: 'textarea',
          labelDescription: [
            'Please add any additional observations or context relevant to this section.',
            'For Discussion:',
            '• How do you typically obtain food?',
            '• Eligibility for Food Stamps',
          ].join('\n'),
          placeholder: 'Share Your Comments',
        },
      ] satisfies StandardFieldConfig[])
    : []),
  {
    name: 'emp_status',
    label: 'Current Employment Status',
    type: 'select',
    labelDescription: 'Could you describe your current employment situation?',
    placeholder: 'Select',
    options: [
      { value: '1_>_9_months', label: 'Unemployed more than 9 months' },
      { value: '5_<_9_months', label: 'Unemployed less than 9 months' },
      { value: '8_temp_part', label: 'Temp Part Time' },
      { value: '10_temp_full', label: 'Temp Full Time' },
      { value: '13_perm_part', label: 'Perm Part Time' },
      { value: '15_perm_full', label: 'Perm Full Time' },
      { value: '15_na', label: 'Not Applicable / Income Subsidized' },
    ],
    schema: yup
      .string()
      .oneOf(
        ['1_>_9_months', '5_<_9_months', '8_temp_part', '10_temp_full', '13_perm_part', '15_perm_full', '15_na'],
        'Invalid employment status',
      )
      .required('Employment status is required'),
  },
  {
    name: 'emp_duration_current',
    label: 'Duration of Current Employment',
    type: 'select',
    labelDescription: 'If employed, how long have you been working at your current job?',
    placeholder: 'Select',
    options: [
      { value: 'none', label: 'Not Employed' },
      { value: 'less_than_3_months', label: 'Less than 3 months' },
      { value: '3_to_5_months', label: '3 to 5 months' },
      { value: '6_to_12_months', label: '6 to 12 months' },
      { value: 'more_than_12_months', label: 'More than 12 months' },
    ],
    schema: yup
      .string()
      .oneOf(['none', 'less_than_3_months', '3_to_5_months', '6_to_12_months', 'more_than_12_months'])
      .required('Employment duration is required'),
  },
  {
    name: 'emp_work_eligibility',
    label: 'Work Eligibility and Legal History',
    type: 'select',
    labelDescription:
      "To help us understand the types of jobs you may be eligible for, could you share if you've had any involvement with the legal system, such as being arrested, charged, or convicted of a crime?",
    placeholder: 'Select',
    options: [
      { value: 'no_history', label: 'No Criminal History' },
      { value: 'awaiting_trial', label: 'Awaiting Trial or Other Legal Proceedings' },
      { value: 'probation', label: 'Currently on Probation or Parole for a conviction' },
      { value: 'past_charges', label: 'Past Charges or Convictions, with all requirements completed' },
      { value: 'na', label: 'Prefer not to answer' },
    ],
    schema: yup
      .string()
      .oneOf(['no_history', 'awaiting_trial', 'probation', 'past_charges', 'na'])
      .required('Work eligibility status is required'),
  },
  {
    name: 'emp_highest_ed',
    label: 'Highest Level of Education Completed',
    type: 'select',
    labelDescription: 'What level of education have you completed so far?',
    placeholder: 'Select',
    options: [
      { value: 'some_school', label: 'Some school' },
      { value: 'hs', label: 'High School Diploma' },
      { value: 'ged', label: 'GED' },
      { value: 'certificate', label: 'Vocational or Trade SchoolCertificate' },
      { value: 'college', label: 'Some College' },
      { value: 'associates', label: 'Associate Degree' },
      { value: 'bachelors', label: "Bachelor's Degree" },
      { value: 'masters', label: 'Master Degree' },
      { value: 'doctor', label: 'Doctoral Degree' },
    ],
    schema: yup
      .string()
      .oneOf(['some_school', 'hs', 'ged', 'certificate', 'college', 'associates', 'bachelors', 'masters', 'doctor'])
      .required('Education level is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'emp_notes',
          label: 'Notes: Employment',
          type: 'textarea',
          labelDescription:
            'Please add any additional observations or context relevant to this section. \n' +
            'For Discussion:\n' +
            '• What are your career goals? \n' +
            '• What kind of support would help you succeed in your career journey? \n' +
            '• Do you have any certifications, licenses, or specialized skills? \n' +
            '• What kind of work environment do you prefer? \n' +
            '• Have you worked with any employment or career services in the past? If so, what was your experience?',
          placeholder: 'Share Your Comments',
          schema: yup.string().nullable(),
        },
      ] satisfies StandardFieldConfig[])
    : []),
];

// Legal Services
const getWellBeingAssessmentLegalFields = (person: 'mom' | 'coordinator'): StandardFieldConfig[] => [
  {
    heading: 'Legal Services',
    descriptionToHeading:
      'This section will cover any legal matters you may be facing, so we can help connect you with the right resources.',
    name: 'legal_current',
    label: 'Involvement in Legal Matters',
    type: 'button-radio-group',
    labelDescription:
      'Are you currently engaged in any unresolved legal matters, such as custody, CPS, or other issues?',
    placeholder: 'Select',
    options: [
      { value: 'no', label: 'No' },
      { value: 'yes', label: 'Yes' },
    ],
    schema: yup.string().oneOf(['no', 'yes'], 'Invalid legal status').required('Current legal status is required'),
  },
  {
    name: 'legal_rep',
    label: 'Legal Representation',
    type: 'select',
    labelDescription: 'If you have an ongoing legal matter, do you currently have an attorney assisting you?',
    placeholder: 'Select',
    options: [
      { value: 'no', label: 'No' },
      { value: 'yes', label: 'Yes' },
    ],
    hidden: (values) => values?.legal_current !== 'yes',
    schema: yup
      .string()
      .oneOf(['no', 'yes'], 'Invalid representation status')
      .required('Legal representation status is required'),
  },
  {
    name: 'legal_rep_access',
    label: 'Access to Legal Representation',
    type: 'select',
    labelDescription: 'Have you experienced any challenges in reaching or working with your attorney?',
    placeholder: 'Select',
    options: [
      { value: 'no', label: 'No' },
      { value: 'yes', label: 'Yes' },
    ],
    hidden: (values) => values?.legal_current !== 'yes',
    schema: yup
      .string()
      .oneOf(['no', 'yes'], 'Invalid access status')
      .required('Legal representation access status is required'),
  },
  {
    name: 'legal_plan_childcare',
    label: "Plan for Children's Care in Absence",
    type: 'select',
    labelDescription:
      "Do you have a plan for your children's care in case you're unavailable, like a power of attorney?",
    placeholder: 'Select',
    options: [
      { value: 'no', label: 'No' },
      { value: 'yes', label: 'Yes' },
    ],
    hidden: (values) => values?.legal_current !== 'yes',
    schema: yup.string().oneOf(['no', 'yes'], 'Invalid plan status').required('Childcare plan status is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'legal_notes',
          label: 'Notes: Legal Services',
          type: 'textarea',
          labelDescription: 'Please add any additional observations or context relevant to this section.',
          placeholder: 'Share Your Comments',
          schema: yup.string().nullable(),
        },
      ] satisfies StandardFieldConfig[])
    : []),
];

// Nurturing and Attachment
const getWellBeingAssessmentNurturingAndAttachmentFields = (person: 'mom' | 'coordinator'): StandardFieldConfig[] => [
  {
    heading: 'Nurturing and Attachment',
    descriptionToHeading:
      'This section helps us understand how you and your child connect with each other. We’ll talk about how you interact day-to-day, so we can celebrate the bond you’re building and support you in creating a strong, secure relationship.',
    name: 'naa_child_behavior',
    label: 'Understanding Child Behavior',
    type: 'select',
    labelDescription: 'My child misbehaves just to upset me.',
    placeholder: 'Select',
    options: likeMyLifeOptions,
    schema: yup
      .string()
      .oneOf(
        likeMyLifeOptions.map((option) => option.value),
        'Invalid behavior rating',
      )
      .required('Child behavior rating is required'),
  },
  {
    name: 'naa_discipline',
    label: 'Challenges with Discipline',
    type: 'select',
    labelDescription: "I feel like I'm always telling my kids no or stop.",
    placeholder: 'Select',
    options: likeMyLifeOptions,
    schema: yup
      .string()
      .oneOf(
        likeMyLifeOptions.map((option) => option.value),
        'Invalid discipline rating',
      )
      .required('Discipline rating is required'),
  },
  {
    name: 'naa_power_struggles',
    label: 'Power Struggles with Children',
    type: 'select',
    labelDescription: 'I have frequent power struggles with my kids.',
    placeholder: 'Select',
    options: likeMyLifeOptions,
    schema: yup
      .string()
      .oneOf(
        likeMyLifeOptions.map((option) => option.value),
        'Invalid struggles rating',
      )
      .required('Power struggles rating is required'),
  },
  {
    name: 'naa_emotions',
    label: 'Impact of Emotions on Parenting',
    type: 'select',
    labelDescription: "How I respond to my child depends on how I'm feeling.",
    placeholder: 'Select',
    options: likeMyLifeOptions,
    schema: yup
      .string()
      .oneOf(
        likeMyLifeOptions.map((option) => option.value),
        'Invalid emotions rating',
      )
      .required('Emotions rating is required'),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'naa_nurture_pfs_summary',
          label: 'Nurturing and Attachment PFS Summary',
          type: 'summary-select',
          labelDescription:
            'Protective Factors Survey 2.0 Mean NA Subscale Score (Total score, divided by 4). \nDefinition: The emotional tie along with a pattern of positive interaction between the parent and child that develops over time.',
          options: [
            { value: '0_no', label: 'No Access' },
            { value: '1_rarely', label: 'Rarely Has Access' },
            { value: '2_sometimes', label: 'Sometimes Has Access' },
            { value: '3_often', label: 'Often Has Access' },
            { value: '4_always', label: 'Almost Always Has Access' },
          ],
          schema: yup
            .string()
            .oneOf(
              ['0_no', '1_rarely', '2_sometimes', '3_often', '4_always'],
              'Invalid Nurturing and Attachment PFS Summary',
            )
            .required('Nurturing and Attachment PFS Summary is required'),
        },
      ] satisfies StandardFieldConfig[])
    : []),
  {
    name: 'naa_reflections',
    label: 'Reflections on Nurturing and Attachment',
    type: 'textarea',
    labelDescription: 'Anything else you want to tell us about this area?',
    placeholder: 'Share Your Comments',
    schema: yup.string().nullable(),
  },
  ...(person === 'coordinator'
    ? ([
        {
          name: 'naa_notes',
          label: 'Staff Notes on Nurturing and Attachment',
          type: 'textarea',
          labelDescription: 'Please add any additional observations or context relevant to this section.',
          placeholder: 'Share Your Comments',
          schema: yup.string().nullable(),
        },
      ] satisfies StandardFieldConfig[])
    : []),
];

export const getWellBeingAssessmentFields = (person: 'mom' | 'coordinator') => [
  ...getWellBeingAssessmentIntakeInformationFields(person),
  ...getWellBeingAssessmentChildWelfareFields(person),
  ...getWellBeingAssessmentCareOfChildrenFields(person),
  ...getWellBeingAssessmentHousingFields(person),
  ...getWellBeingAssessmentTransportationFields(person),
  ...getWellBeingAssessmentResilienceFields(person),
  ...getWellBeingAssessmentWellnessFields(person),
  ...getWellBeingAssessmentStressAndSubstanceAbuseFields(person),
  ...getWellBeingAssessmentSocialSupportFields(person),
  ...getWellBeingAssessmentsEmploymentFields(person),
  ...getWellBeingAssessmentLegalFields(person),
  ...getWellBeingAssessmentNurturingAndAttachmentFields(person),
];
