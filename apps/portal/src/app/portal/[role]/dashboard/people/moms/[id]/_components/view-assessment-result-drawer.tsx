import ResponsiveDrawer from '@/components/custom/responsive-drawer';
import { useState } from 'react';

interface MomViewAssessmentResultDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

const MomViewAssessmentResultDrawer: React.FC<MomViewAssessmentResultDrawerProps> = ({ isOpen, onClose }) => {
  const [data] = useState<{
    prescore: string;
    pre_assessment_recommendation: string;
    post_assessment_recommendation: string;
    postscore: string;
    overall_recommendation: string;
  }>({
    prescore: '50',
    pre_assessment_recommendation: `Per the Parental Stress Scale, the mother is considered to have moderate stress. It is recommended that the mother actively address and manage her moderate stress levels through a combination of stress-reduction techniques, healthy lifestyle choices, seeking social support, and considering professional guidance if needed. Taking proactive steps now can significantly improve your overall well-being and long-term mental health.`,
    post_assessment_recommendation: `Per the Parental Stress Scale, the mother is considered to have moderate stress. It is recommended that the mother actively address and manage her moderate stress levels through a combination of stress-reduction techniques, healthy lifestyle choices, seeking social support, and considering professional guidance if needed. Taking proactive steps now can significantly improve your overall well-being and long-term mental health.`,
    postscore: '50',
    overall_recommendation: `<PERSON>ress is a natural reaction to difficult situations in life, such as work, family, relationships and money problems. A moderate amount of stress can help us perform better in challenging situations, but too much or prolonged stress can lead to physical problems. It&apos;s okay to ask for professional help if you feel that you are struggling to manage on your own. It&apos;s also important to get help as soon as possible so you can begin to get better.1 If you feel overwhelmed and self-help isn&apos;t helping, look for a psychologist or other mental health provider who can help you learn how to manage your stress effectively. He or she can help you identify situations or behaviors that contribute to your stress and then develop an action plan to change the stressors, change your environment, and change your responses.`,
  });

  return (
    <ResponsiveDrawer isOpen={isOpen} onClose={onClose} title={<DrawerTitle />}>
      <div className='space-y-6 p-6'>
        <div>
          <div className='text-md mb-2 flex justify-between border-b border-emaBgQuaternary pb-2 text-emaTextSecondary'>
            <h3>Stress Evaluation PRE Score</h3>
            <span>{data.prescore}</span>
          </div>
          <p className='text-sm text-emaTextSecondary'>{data.pre_assessment_recommendation}</p>
        </div>
        <div>
          <div className='text-md mb-2 flex justify-between border-b border-emaBgQuaternary pb-2 text-emaTextSecondary'>
            <h3>Stress Evaluation POST Score</h3>
            <span>{data.postscore}</span>
          </div>
          <p className='text-sm text-emaTextSecondary'>{data.post_assessment_recommendation}</p>
        </div>
        <div className='space-y-4'>
          <h3 className='text-emaTextSecondary'>What the Experts are Saying</h3>
          <p className='text-sm text-emaTextSecondary'>{data.overall_recommendation}</p>
        </div>
      </div>
    </ResponsiveDrawer>
  );
};

export default MomViewAssessmentResultDrawer;

const DrawerTitle = () => {
  return (
    <div className='ml-4 flex flex-col'>
      <p className='mb-2 text-xl font-semibold'>Assessment Results & Resources</p>
      <p className='text-left text-sm font-light'>Stress Assessment</p>
    </div>
  );
};
