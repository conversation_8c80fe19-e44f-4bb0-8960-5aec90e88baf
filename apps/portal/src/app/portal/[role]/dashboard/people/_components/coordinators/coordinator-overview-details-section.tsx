'use client';

import { useCoordinatorProfileContext } from '@/app/portal/context/coordinator-profile-context';
import { formatPhoneNumber } from '@/lib/utils';
import { format } from 'date-fns';
import { CircleUser, Mail, Phone } from 'lucide-react';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import React from 'react';

import DetailsItem from '../../_components/details-item';
import { formatCommunicationPreference, formatSnakeCaseToTitleCase, formatTimezone } from '../../_utils/language-utils';

const CoordinatorOverviewDetailsSection = () => {
  const params = useParams<{ id: string }>();
  const coordinatorId = params.id;
  const { coordinatorProfile, isLoading, fetchCoordinator } = useCoordinatorProfileContext();
  React.useEffect(() => {
    if (coordinatorId) {
      fetchCoordinator(coordinatorId);
    }
  }, [coordinatorId, fetchCoordinator]);

  if (isLoading) {
    return <div>Loading...</div>;
  }
  return (
    <div>
      <DetailsItem label={'First Name'} value={coordinatorProfile?.firstName} />
      <DetailsItem label={'Last Name'} value={coordinatorProfile?.lastName} />
      <DetailsItem
        label={'Profile Photo'}
        value={
          coordinatorProfile?.photoUrl ? (
            <Image
              src={coordinatorProfile?.photoUrl}
              alt='Profile Image'
              width={75}
              height={75}
              className='h-full w-full rounded-full object-cover'
              style={{ width: '75px', height: '75px' }}
            />
          ) : (
            <CircleUser />
          )
        }
      />
      <DetailsItem label={'Home Church'} value={coordinatorProfile?.home_church ?? 'None on file'} />
      <DetailsItem
        label={'Email'}
        icon={<Mail className='h-4 w-4' />}
        value={coordinatorProfile?.email ?? 'None on file'}
      />
      <DetailsItem
        label={'Phone'}
        icon={<Phone className='h-4 w-4' />}
        value={formatPhoneNumber(coordinatorProfile?.phone_mobile ?? 'None on file')}
      />
      <DetailsItem
        label={'Secondary Phone'}
        icon={<Phone className='h-4 w-4' />}
        value={formatPhoneNumber(coordinatorProfile?.secondary_phone ?? 'None on file')}
      />
      <DetailsItem
        label={'Communication Preference'}
        value={
          coordinatorProfile?.communication_preference
            ? formatCommunicationPreference(coordinatorProfile?.communication_preference)
            : 'None on file'
        }
      />
      <DetailsItem
        label={'Birthday'}
        value={
          coordinatorProfile?.date_of_birth ? format(coordinatorProfile.date_of_birth, 'MMMM d, yyyy') : 'None on file'
        }
      />
      <DetailsItem label={'Timezone'} value={formatTimezone(coordinatorProfile?.timezone)} />
      <DetailsItem label={'ĒMA Location'} value={coordinatorProfile?.affiliate?.name ?? 'Unassigned'} />
      <DetailsItem label={'Availability'} value={coordinatorProfile?.availability ?? 'None on file'} />
      <DetailsItem
        label={'Language Preference'}
        value={formatSnakeCaseToTitleCase(coordinatorProfile?.language_preference_c) ?? 'None on file'}
      />
      <DetailsItem
        label={'Languages'}
        value={
          coordinatorProfile?.languages_c?.length
            ? coordinatorProfile.languages_c.map((language) => formatSnakeCaseToTitleCase(language)).join(', ')
            : 'None on file'
        }
      />
    </div>
  );
};

export default CoordinatorOverviewDetailsSection;
