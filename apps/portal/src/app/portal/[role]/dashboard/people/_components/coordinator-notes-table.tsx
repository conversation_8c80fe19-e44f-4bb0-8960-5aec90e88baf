import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import GenericTable from '@/components/custom/generic-table';
import { Button } from '@/components/ui/button';
import { TableCell } from '@/components/ui/table';
import { useFindManyCoordinatorNote } from '@/hooks/generated';
import { YupSchemas } from '@suiteapi/models';
import { formatDateFromString } from '@suiteapi/models';
import { ChevronRight, DownloadCloud } from 'lucide-react';
import { useEffect, useState } from 'react';

import AddCoordinatorNoteModal from './add-coordinator-note-modal';
import CoordinatorNotesDrawer from './coordinator-notes-drawer';

const CoordinatorNotesTable = () => {
  const [selectedEntry, setSelectedEntry] = useState<YupSchemas.CoordinatorNotesSchema | null>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [coordinatorNotesList, setCoordinatorNotesList] = useState<YupSchemas.CoordinatorNotesSchema[]>([]);
  const { profile } = useDashboardContext();
  const { momProfile } = useMomProfileContext();

  const { data: momCoordinatorNotes } = useFindManyCoordinatorNote({
    where: {
      mom_id: momProfile?.id ?? '',
    },
  });

  useEffect(() => {
    if (momCoordinatorNotes) {
      setCoordinatorNotesList(momCoordinatorNotes);
    }
  }, [momCoordinatorNotes]);

  const handleRowClick = (entry: YupSchemas.CoordinatorNotesSchema): void => {
    setSelectedEntry(entry);
    setIsDrawerOpen(true);
  };

  const handleCloseDrawer = (): void => {
    setIsDrawerOpen(false);
  };

  const handleExport = (): void => {
    if (!coordinatorNotesList || coordinatorNotesList.length === 0) {
      alert('No data to export');
      return;
    }

    const csvHeader = 'Date,Note Type,Note Details\n';
    const csvRows = coordinatorNotesList.map((note) => {
      const createdAt = note.created_at ? formatDateFromString(note.created_at.toISOString()) : '';
      const type_c =
        note.type_c === YupSchemas.CoordinatorNoteTypeEnum.SafetyOrConcernUpdate
          ? 'Safety or Concern Update'
          : 'Court Update';

      // Handle note description, ensure it's properly escaped for CSV... Escape double quotes in description
      const description = note.description ? note.description.replace(/"/g, '""') : '';
      const escapedDescription =
        description.includes(',') || description.includes('\n') || description.includes('"')
          ? `"${description}"`
          : description; // quote the description if it contains commas or new lines

      return `${createdAt},${type_c},${escapedDescription}`;
    });

    const csvContent = csvHeader + csvRows.join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'coordinator_notes.csv';
    link.click();
  };

  return (
    <section className='mt-6 md:mt-0'>
      <GenericTable
        data={coordinatorNotesList}
        isLoading={false}
        headers={['Date', 'Note Type', 'Note Details', 'Actions']}
        columnWidths={[10, 30, 50, 10]}
        emptyMessage='There are currently no notes to display.'
        headerSection={
          <div className='flex items-center justify-between gap-8'>
            <div>
              <h1 className='mb-2 text-2xl font-medium'>Coordinator Notes</h1>
              <p>Use this section to add notes for a mom’s profile that are not connected to a session</p>
            </div>
            <div className='flex gap-4'>
              <Button variant='outline' onClick={handleExport}>
                <DownloadCloud /> <span className='ml-2'>Export All</span>
              </Button>
              {profile?.portalRole === 'advocate' ? '' : <AddCoordinatorNoteModal />}
            </div>
          </div>
        }
        rowRenderer={(item) => (
          <>
            <TableCell>{item.created_at ? formatDateFromString(item.created_at.toISOString()) : ''}</TableCell>
            <TableCell>
              {item.type_c === YupSchemas.CoordinatorNoteTypeEnum.SafetyOrConcernUpdate
                ? 'Safety or Concern Update'
                : 'Court Update'}
            </TableCell>
            <TableCell>{item.description}</TableCell>
            <TableCell>
              <Button onClick={() => handleRowClick(item)} variant='outline' size='sm' className='border-none'>
                Review
              </Button>
            </TableCell>
          </>
        )}
        mobileRenderer={(item) => (
          <div className='flex flex-col gap-2'>
            <div className='flex items-center justify-between'>
              <p>
                <span className='font-semibold'>Mom: </span>
                {item.name}
              </p>
              <div className='cursor-pointer' onClick={() => handleRowClick(item)}>
                <ChevronRight />
              </div>
            </div>

            <p>
              <span className='font-semibold'>Date: </span>
              {item.created_at ? formatDateFromString(item.created_at.toISOString()) : ''}
            </p>
            <p>
              <span className='font-semibold'>Note Type: </span>
              {item.type_c === YupSchemas.CoordinatorNoteTypeEnum.SafetyOrConcernUpdate
                ? 'Safety or Concern Update'
                : 'Court Update'}
            </p>
            <p>
              <span className='font-semibold'>Note Details: </span>
              {item.description}
            </p>
          </div>
        )}
      />
      <CoordinatorNotesDrawer isOpen={isDrawerOpen} onClose={handleCloseDrawer} entry={selectedEntry} />
    </section>
  );
};

export default CoordinatorNotesTable;
