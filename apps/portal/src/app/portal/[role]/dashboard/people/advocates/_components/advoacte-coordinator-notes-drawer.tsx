import { useAdvocateProfileContext } from '@/app/portal/context/advocate-profile-context';
import ResponsiveDrawer from '@/components/custom/responsive-drawer';
import { formatDateFromString } from '@suiteapi/models';
import { YupSchemas } from '@suiteapi/models';

interface AdvocateCoordinatorNotesDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  entry: YupSchemas.CoordinatorNotesSchema | null;
}

const AdvocateCoordinatorNotesDrawer = ({ isOpen, onClose, entry }: AdvocateCoordinatorNotesDrawerProps) => {
  const { advocateProfile } = useAdvocateProfileContext();
  if (!entry) return null;

  return (
    <ResponsiveDrawer
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className='ml-4 pr-4'>
          <h1 className='mb-1 text-xl font-semibold'>Coordinator Note</h1>
          {/* TODO update advocate name */}
          <p className='text-sm font-normal text-emaTextTertiary'>
            Advocate: {advocateProfile?.firstName} {advocateProfile?.lastName}
          </p>
        </div>
      }
    >
      <div className='mt-5 flex flex-col gap-5 border-t px-6 pt-5 text-sm text-emaTextSecondary'>
        <p className='text-sm text-emaTextQuaternary'>
          Added: {entry.created_at ? formatDateFromString(entry.created_at.toISOString()) : 'date not available'}
        </p>
        <div>
          <p className='font-semibold text-black'>Note Type: </p>
          <p>
            {entry.type_c === YupSchemas.CoordinatorNoteTypeEnum.SafetyOrConcernUpdate
              ? 'Safety or Concern Update'
              : 'Interview - Advocate   '}
          </p>
        </div>
        <div>
          <p className='font-semibold text-black'>Note Details: </p>
          <p>{entry.description}</p>
        </div>
      </div>
    </ResponsiveDrawer>
  );
};

export default AdvocateCoordinatorNotesDrawer;
