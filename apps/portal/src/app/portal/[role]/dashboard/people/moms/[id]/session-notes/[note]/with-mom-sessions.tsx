'use client';

import { useMomSessionNotes } from '@/app/portal/context/mom-session-notes-context';
import { useEffect } from 'react';

interface WithMomProfileProps {
  momId: string;
  children: React.ReactNode;
}

/*
 * This component is a wrapper around the mom sessions context
 * that fetches the mom's sessions if they're not already in the context
 * It needs to be wrapped around any view that is on its own route,
 * which could lose context if the user navigates directly to it or it's "hard refreshed"
 */

const WithMomSessions = ({ momId, children }: WithMomProfileProps): React.ReactNode => {
  const { sessionsList, fetchSessionsList } = useMomSessionNotes();
  const shouldFetchMomSessions = !sessionsList;

  useEffect(() => {
    if (shouldFetchMomSessions) {
      void fetchSessionsList(momId);
    }
  }, [momId, shouldFetchMomSessions, fetchSessionsList]);

  if (shouldFetchMomSessions) {
    // TODO: Replace with loading skeleton
    return <div>Loading...</div>;
  }

  return children;
};

export default WithMomSessions;
