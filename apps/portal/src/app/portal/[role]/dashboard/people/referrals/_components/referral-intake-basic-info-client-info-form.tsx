import { FormFactoryRef, WellBeingAssessmentFormData } from '@/app/portal/types';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { generateFieldsSchema } from '@/types/form-field';
import { PlusIcon, Trash2 } from 'lucide-react';
import { Dispatch, SetStateAction, useEffect, useMemo, useRef, useState } from 'react';

import IntakeFormDescriptiveHeading from './intake-form-descriptive-heading';
import {
  momChildFormFields,
  momChildrenFormFields,
  referralBasicInfoFields,
  referralClientInfoFields,
} from './intake-referral-form.config';
import MomChildForm from './mom-child-form';
import { validateBasicInformationForm } from './utils';

interface ReferralBasicInfoClientInfoFormProps {
  formData: WellBeingAssessmentFormData;
  setFormData: Dispatch<SetStateAction<WellBeingAssessmentFormData>>;
  onNextStep: () => void;
}

const ReferralBasicInfoClientInfoForm = ({
  formData,
  setFormData,
  onNextStep,
}: ReferralBasicInfoClientInfoFormProps) => {
  const basicFormRef = useRef<FormFactoryRef>(null);
  const clientFormRef = useRef<FormFactoryRef>(null);
  const childrenFormRef = useRef<FormFactoryRef>(null);
  const [formError, setFormError] = useState<string>();

  const basicFormFieldsSchema = useMemo(
    () => generateFieldsSchema(referralBasicInfoFields, formData.basicForm.values),
    [formData.basicForm.values],
  );
  const clientFormFieldsSchema = useMemo(
    () => generateFieldsSchema(referralClientInfoFields, formData.clientForm.values),
    [formData.clientForm.values],
  );
  const childrenFormFieldsSchema = useMemo(
    () => generateFieldsSchema(momChildrenFormFields, formData.childrenForm.values),
    [formData.childrenForm.values],
  );

  const handleFormChange = <K extends keyof typeof formData>(key: K, values: (typeof formData)[K]['values']) => {
    setFormData((formData) => ({
      ...formData,
      [key]: { ...formData[key], values },
    }));
  };

  const handleRemoveChild = (index: number) => {
    setFormData((formData) => ({
      ...formData,
      childForms: [...formData.childForms.slice(0, index), ...formData.childForms.slice(index + 1)],
    }));
  };

  const handleAddChild = () => {
    setFormData((formData) => ({
      ...formData,
      childForms: [
        ...formData.childForms,
        { values: {}, schema: generateFieldsSchema(momChildFormFields, {}), ref: null },
      ],
    }));
  };

  useEffect(() => {
    setFormData((forms) => ({
      ...forms,
      basicForm: { ...forms.basicForm, schema: basicFormFieldsSchema, ref: basicFormRef },
      clientForm: { ...forms.clientForm, schema: clientFormFieldsSchema, ref: clientFormRef },
      childrenForm: { ...forms.childrenForm, schema: childrenFormFieldsSchema, ref: childrenFormRef },
    }));
  }, [setFormData, basicFormFieldsSchema, clientFormFieldsSchema, childrenFormFieldsSchema]);

  return (
    <div className='mb-14 flex flex-col gap-4'>
      <IntakeFormDescriptiveHeading
        title="Introduction to Every Mother's Advocate"
        description={
          <>
            This initial discussion is a chance for us to get to know you and work together to create a plan that fits
            your goals and needs. We&apos;ll talk about where you are in life right now, explore any areas where you
            might want support, and build a plan to help you move toward stability and growth. This plan will change as
            your needs change, and we&apos;ll be here to support you every step of the way.
          </>
        }
      />
      <div className='border-emaBorder rounded-lg border bg-white p-4'>
        <h3 className='mb-4 text-2xl font-semibold'>Basic Information</h3>
        <FormFactory
          ref={basicFormRef}
          fields={referralBasicInfoFields}
          onSubmit={() => {}}
          schema={basicFormFieldsSchema}
          defaultValues={formData.basicForm.values}
          actionButtonsComponent={null}
          onChange={(values) => handleFormChange('basicForm', values)}
        />
      </div>
      <div className='border-emaBorder rounded-lg border bg-white p-4'>
        <h3 className='mb-4 text-2xl font-semibold'>Client Information</h3>
        <FormFactory
          ref={clientFormRef}
          fields={referralClientInfoFields}
          onSubmit={() => {}}
          schema={clientFormFieldsSchema}
          defaultValues={formData.clientForm.values}
          actionButtonsComponent={null}
          onChange={(values) => handleFormChange('clientForm', values)}
        />
      </div>
      <div className='border-emaBorder rounded-lg border bg-white p-4'>
        <h2 className='mb-2 text-2xl font-semibold'>Children&apos;s Information</h2>
        <p className='mb-4 text-sm'>
          We&apos;d love to know more about your children. Please share information about children under 18 who live
          with you, those you care for, or those you have parental rights to. If any of your children live outside your
          home, such as in foster care or with relatives, that&apos;s okay—feel free to share what you&apos;re
          comfortable with. This helps us understand your family&apos;s situation so we can support you better.
        </p>
        {formData.childForms.map((childForm, index) => (
          <div key={index} className='border-emaBorder mb-4 flex flex-col border-b pb-4'>
            <MomChildForm index={index} values={childForm.values} setFormData={setFormData} />
            <Button
              variant='outline'
              onClick={() => handleRemoveChild(index)}
              className='mt-4 self-end border-red-500 text-red-500'
            >
              <Trash2 className='mr-2 h-4 w-4' />
              Remove Child
            </Button>
          </div>
        ))}
        <div className='flex justify-end'>
          <Button variant='outline' onClick={handleAddChild}>
            <PlusIcon className='mr-2 h-4 w-4' />
            Add Child
          </Button>
        </div>
        <div className='mt-4 pt-4'>
          <FormFactory
            ref={childrenFormRef}
            fields={momChildrenFormFields}
            schema={childrenFormFieldsSchema}
            defaultValues={formData.childrenForm.values}
            onSubmit={() => {}}
            actionButtonsComponent={null}
            onChange={(values) => handleFormChange('childrenForm', values)}
          />
        </div>
      </div>
      {formError && <p className='self-end text-red-500'>{formError}</p>}
      <div className='flex justify-end gap-4'>
        <Button
          onClick={async () => {
            setFormError(undefined);
            const formError = await validateBasicInformationForm(formData);
            if (formError) {
              setFormError(formError);
            } else {
              onNextStep();
            }
          }}
        >
          Next: Well-Being Assessment
        </Button>
      </div>
    </div>
  );
};

export default ReferralBasicInfoClientInfoForm;
