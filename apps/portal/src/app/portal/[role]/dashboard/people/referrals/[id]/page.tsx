'use client';

import Mom<PERSON><PERSON>ImageHeader from '@/app/portal/[role]/dashboard/people/_components/mom-name-photo-header';
import Breadcrumbs from '@/app/portal/_components/navigation/breadcrumbs';
import { useFindFirstWellnessAssessment, useFindUniqueMom } from '@/hooks/generated';
import { useMutation } from '@tanstack/react-query';
import isEqual from 'lodash/isEqual';
import { Send } from 'lucide-react';
import { useMemo, useState } from 'react';

import { Button } from '../../../../../../../components/ui/button';
import { WellnessAssessment } from '../../../../../../../hooks/generated/__types';
import { sfetch } from '../../../../../../../lib/sfetch';
import { WellBeingAssessmentFormData } from '../../../../../types';
import WithMomProfile from '../../moms/[id]/with-mom-profile';
import IntakeStepper from '../_components/intake-stepper';
import {
  MomWithChildren,
  StepId,
  getInitialWellBeingAssessmentFormData,
  validateBasicInformationForm,
  validateWellbeingAssessmentForm,
} from '../_components/utils';

const Intake = ({ mom, wellnessAssessment }: { mom: MomWithChildren; wellnessAssessment?: WellnessAssessment }) => {
  const defaultFormData = useMemo(
    () =>
      getInitialWellBeingAssessmentFormData({
        person: 'coordinator',
        mom,
        wellnessAssessment,
      }),
    [mom, wellnessAssessment],
  );
  const [formData, setFormData] = useState(defaultFormData);
  const [formError, setFormError] = useState<string>();
  const [currentStepId, setCurrentStepId] = useState<StepId>('basic-information');

  const createWellnessAssessment = useMutation<void, Error, { momId: string; formData: WellBeingAssessmentFormData }>({
    mutationFn: async ({ momId, formData }) => {
      const response = await sfetch(`/v1/wellness-assessment/mom/${momId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          basicForm: { values: formData.basicForm.values },
          clientForm: { values: formData.clientForm.values },
          childForms: formData.childForms.map((childForm) => ({ values: childForm.values })),
          childrenForm: { values: formData.childrenForm.values },
          wellBeingForm: { values: formData.wellBeingForm.values },
          updateMom: true,
          createWellnessAssessment: true,
        }),
      });
      if (!response.ok) {
        throw new Error('Failed to send the wellness assessment');
      }
    },
  });

  const steps: { id: StepId; name: string }[] = [
    { id: 'basic-information', name: 'Basic Information' },
    { id: 'wellbeing-assessment', name: 'Family Well-Being' },
    { id: 'approval-ema', name: 'Approval for ĒMA Program' },
    { id: 'action-plan', name: 'Create an Action Plan' },
  ];

  const handleBackStep = () => {
    const currentStepIndex = steps.findIndex((step) => step.id === currentStepId);
    if (currentStepIndex > 0) {
      setCurrentStepId(steps[currentStepIndex - 1].id);
    }
  };

  const handleNextStep = async () => {
    const currentStepIndex = steps.findIndex((step) => step.id === currentStepId);
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepId(steps[currentStepIndex + 1].id);
    }
  };

  const handleCreateWellnessAssessment = async () => {
    const noChanges =
      isEqual(defaultFormData.basicForm.values, formData.basicForm.values) &&
      isEqual(defaultFormData.clientForm.values, formData.clientForm.values) &&
      isEqual(
        defaultFormData.childForms.map(({ values }) => values),
        formData.childForms.map(({ values }) => values),
      ) &&
      isEqual(defaultFormData.childrenForm.values, formData.childrenForm.values) &&
      isEqual(defaultFormData.wellBeingForm.values, formData.wellBeingForm.values);

    if (noChanges) {
      return;
    }

    await createWellnessAssessment.mutateAsync({
      momId: mom.id,
      formData,
    });
  };

  const handleSaveChanges = async () => {
    setFormError(undefined);
    const basicInformationFormError = await validateBasicInformationForm(formData);
    const wellbeingAssessmentFormError = await validateWellbeingAssessmentForm(formData);
    const formError = basicInformationFormError || wellbeingAssessmentFormError;

    if (!formError) {
      await handleCreateWellnessAssessment();
    } else {
      setFormError(formError);
    }
  };

  const sendAssessment = useMutation<void, Error, string>({
    mutationFn: async (momId) => {
      const response = await sfetch(`/v1/wellness-assessment/self-intake/notification?mom_id=${momId}`, {
        method: 'POST',
      });
      if (!response.ok) {
        throw new Error('Failed to send the wellness assessment');
      }
    },
  });

  return (
    <WithMomProfile momId={mom.id}>
      <div>
        <div className='my-10 border-b border-gray-200 pb-5'>
          <div className='mt-5 flex w-full flex-wrap items-start justify-end gap-5'>
            <MomNameImageHeader momName={`${mom.first_name} ${mom.last_name}`} momId={mom.id} referral={true} />
            {(currentStepId === 'basic-information' || currentStepId === 'wellbeing-assessment') && (
              <div className='flex flex-col items-end gap-1'>
                <div className='flex flex-wrap items-center gap-3'>
                  <Button
                    variant='outline'
                    onClick={() => sendAssessment.mutateAsync(mom.id)}
                    disabled={sendAssessment.isPending}
                  >
                    <Send className='mr-2 h-4 w-4' />
                    Send Assessment
                  </Button>
                  <Button onClick={handleSaveChanges} disabled={createWellnessAssessment.isPending}>
                    Save changes
                  </Button>
                </div>
                {formError && <p className='self-end text-red-500'>{formError}</p>}
              </div>
            )}
          </div>
          <h1 className='mt-5 text-2xl font-semibold'>Family Well-Being Assessment</h1>
        </div>
        <div>
          <IntakeStepper
            mom={mom}
            person='coordinator'
            steps={steps}
            currentStepId={currentStepId}
            setCurrentStepId={setCurrentStepId}
            onBackStep={handleBackStep}
            onNextStep={handleNextStep}
            wellBeingAssessmentStepLabel='Next: Approval For EMA Program'
            formData={formData}
            setFormData={setFormData}
          />
        </div>
      </div>
    </WithMomProfile>
  );
};

const ReferralIDPage = ({ params }: { params: { id: string } }) => {
  const { id } = params;
  const mom = useFindUniqueMom({
    include: {
      children: true,
    },
    where: { id },
  });
  const wellnessAssessment = useFindFirstWellnessAssessment({
    where: { mom_id: id },
    orderBy: {
      created_at: 'desc',
    },
  });

  return (
    <>
      <Breadcrumbs
        items={[
          { label: 'People', href: '/people' },
          { label: 'Referrals', href: '/people/referrals' },
        ]}
      />
      {((mom.isLoading || wellnessAssessment.isLoading) && <div>Loading...</div>) ||
        (mom.data && <Intake mom={mom.data} wellnessAssessment={wellnessAssessment.data} />)}
    </>
  );
};

export default ReferralIDPage;
