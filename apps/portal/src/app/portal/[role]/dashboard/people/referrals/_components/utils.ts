import { Mom, WellnessAssessment } from '../../../../../../../hooks/generated/__types';
import { FieldOptions, generateFieldsSchema } from '../../../../../../../types/form-field';
import { WellBeingAssessmentFormData } from '../../../../../types';
import { formatFatherInvolved } from '../../_utils/people-utils';
import {
  challengesOptions,
  empSupportOptions,
  financialStrugglesOptions,
  getWellBeingAssessmentFields,
  maltreatmentTypeOptions,
  momChildFormFields,
  momChildrenFormFields,
  referralBasicInfoFields,
  referralClientInfoFields,
  trustToAskForOptions,
} from './intake-referral-form.config';

export type StepId = 'basic-information' | 'wellbeing-assessment' | 'approval-ema' | 'action-plan';

export type MomWithChildren = Mom & {
  children: {
    [key: string]: unknown;
  }[];
};

const decodeArray = (data: unknown, options: FieldOptions) => {
  return String(data || '')
    .split(',')
    .filter(Boolean)
    .map((value) => ({
      value,
      label: options.find((option) => option.value === value)?.label ?? '',
    }));
};

export const getInitialWellBeingAssessmentFormData = ({
  person,
  mom,
  wellnessAssessment,
}: {
  person: 'mom' | 'coordinator';
  mom?: MomWithChildren;
  wellnessAssessment?: WellnessAssessment;
}): WellBeingAssessmentFormData => {
  const momValues = {
    ...mom,
    children: mom?.children.map((child) => ({
      ...child,
      father_involved: Array.isArray(child.father_involved)
        ? child.father_involved.map((value) => ({
            value,
            label: formatFatherInvolved([value]),
          }))
        : [],
    })),
  };

  const wellnessAssessmentValues = {
    ...(wellnessAssessment ?? {}),
    cw_maltreatment_type: decodeArray(wellnessAssessment?.cw_maltreatment_type, maltreatmentTypeOptions),
    emp_fin_struggles: decodeArray(wellnessAssessment?.emp_fin_struggles, financialStrugglesOptions),
    emp_challenges: decodeArray(wellnessAssessment?.emp_challenges, challengesOptions),
    emp_support_received: decodeArray(wellnessAssessment?.emp_support_received, empSupportOptions),
    emp_support_needed: decodeArray(wellnessAssessment?.emp_support_needed, empSupportOptions),
    soc_trusted_network: decodeArray(wellnessAssessment?.soc_trusted_network, trustToAskForOptions),
  };

  const wellBeingAssessmentFields = getWellBeingAssessmentFields(person);

  return {
    basicForm: {
      values: momValues,
      schema: generateFieldsSchema(referralBasicInfoFields, momValues),
      ref: null,
    },
    clientForm: {
      values: momValues,
      schema: generateFieldsSchema(referralClientInfoFields, momValues),
      ref: null,
    },
    childForms: momValues?.children?.map((childValues) => {
      return {
        values: childValues,
        schema: generateFieldsSchema(momChildFormFields, childValues),
        ref: null,
      };
    }) ?? [
      {
        values: {},
        schema: generateFieldsSchema(momChildFormFields, {}),
        ref: null,
      },
    ],
    childrenForm: {
      values: momValues,
      schema: generateFieldsSchema(momChildrenFormFields, momValues),
      ref: null,
    },
    wellBeingForm: {
      values: wellnessAssessmentValues,
      schema: generateFieldsSchema(wellBeingAssessmentFields, wellnessAssessmentValues),
      ref: null,
    },
  };
};

export const validateBasicInformationForm = async (formData: WellBeingAssessmentFormData) => {
  formData.basicForm.ref?.current?.form.trigger();
  formData.clientForm.ref?.current?.form.trigger();
  formData.childForms.forEach((childForm) => childForm.ref?.current?.form.trigger());
  formData.childrenForm.ref?.current?.form.trigger();

  const isValid = await Promise.all([
    formData.basicForm.schema.validate(formData.basicForm.values).then(() => true),
    formData.clientForm.schema.validate(formData.clientForm.values).then(() => true),
    ...formData.childForms.flatMap((childForm) => [childForm.schema.validate(childForm.values).then(() => true)]),
    formData.childrenForm.schema.validate(formData.childrenForm.values).then(() => true),
  ])
    .then(() => true)
    .catch(() => false);

  if (!isValid) {
    return 'Basic Information form contains errors';
  }

  if (!formData.childForms.length) {
    return 'At least one child is required';
  }

  return null;
};

export const validateWellbeingAssessmentForm = async (formData: WellBeingAssessmentFormData) => {
  formData.wellBeingForm.ref?.current?.form.trigger();

  const isValid = await formData.wellBeingForm.schema
    .validate(formData.wellBeingForm.values)
    .then(() => true)
    .catch(() => false);

  if (!isValid) {
    return 'Well-Being form contains errors';
  }

  return null;
};
