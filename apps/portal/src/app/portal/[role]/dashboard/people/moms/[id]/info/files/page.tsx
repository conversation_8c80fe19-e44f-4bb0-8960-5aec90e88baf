import DocumentManagement from '../../../../_components/document-management';
import MomBasicInfoWrapper from '../../../../_components/mom-basic-info-form-wrapper';
import MomProfileWrapper from '../../../../_components/mom-profile-wrapper';
import WithMomProfile from '../../with-mom-profile';

const MomFilesPage = ({ params }: { params: { id: string } }): React.ReactNode => {
  const { id } = params;

  return (
    <WithMomProfile momId={id}>
      <MomProfileWrapper viewId='info' momId={id}>
        <MomBasicInfoWrapper momId={id} currentTab='files'>
          <DocumentManagement
            documentFetchWhereClause={{ mom_id: id }}
            documentDataEnhancements={{
              mom_id: id,
            }}
          />
        </MomBasicInfoWrapper>
      </MomProfileWrapper>
    </WithMomProfile>
  );
};

export default MomFilesPage;
