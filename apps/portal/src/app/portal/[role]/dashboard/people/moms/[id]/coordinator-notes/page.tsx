'use client';

import CoordinatorNotesTable from '../../../_components/coordinator-notes-table';
import MomProfileWrapper from '../../../_components/mom-profile-wrapper';
import WithMomProfile from '../with-mom-profile';

const CoordinatorNotesPage = ({ params }: { params: { id: string } }): React.ReactNode => {
  const { id } = params;

  return (
    <WithMomProfile momId={id}>
      <MomProfileWrapper viewId='coordinator-notes' momId={id}>
        <CoordinatorNotesTable />
      </MomProfileWrapper>
    </WithMomProfile>
  );
};

export default CoordinatorNotesPage;
