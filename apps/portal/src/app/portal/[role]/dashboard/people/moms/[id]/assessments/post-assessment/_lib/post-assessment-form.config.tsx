import { FieldConfig } from '@/types/form-field';
import { ObjectSchema } from 'yup';

import { postAssessmentDepressionSchema } from './post-assessment-depression-form.config';
import { postAssessmentDepressionFormConfig } from './post-assessment-depression-form.config';
import {
  postAssessmentFinancialSecurityFormConfig,
  postAssessmentFinancialSecurityFormSchema,
} from './post-assessment-financial-security-form.config';
import {
  postAssessmentSocialSupportFormConfig,
  postAssessmentSocialSupportFormSchema,
} from './post-assessment-social-support-form.config';
import { postAssessmentStressFormConfig, postAssessmentStressFormSchema } from './post-assessment-stress-form.config';
import { postAssessmentTraumaFormConfig, postAssessmentTraumaFormSchema } from './post-assessment-trauma-form.config';

interface PostAssessmentStep {
  title: string;
  fields: FieldConfig[];
  schema: ObjectSchema<Record<string, unknown>>;
}

interface PostAssessmentSteps {
  [key: string]: PostAssessmentStep;
}

// Define the structure of the entire config
interface PostAssessmentFormsConfig {
  steps: PostAssessmentSteps;
}

export const postAssessmentFormsConfig: PostAssessmentFormsConfig = {
  steps: {
    stress: {
      title: 'Stress',
      fields: postAssessmentStressFormConfig,
      schema: postAssessmentStressFormSchema,
    },
    trauma: {
      title: 'Trauma',
      fields: postAssessmentTraumaFormConfig,
      schema: postAssessmentTraumaFormSchema,
    },
    socialSupport: {
      title: 'Social Support',
      fields: postAssessmentSocialSupportFormConfig,
      schema: postAssessmentSocialSupportFormSchema,
    },
    financialSecurity: {
      title: 'Financial Security',
      fields: postAssessmentFinancialSecurityFormConfig,
      schema: postAssessmentFinancialSecurityFormSchema,
    },
    depression: {
      title: 'Depression',
      fields: postAssessmentDepressionFormConfig,
      schema: postAssessmentDepressionSchema,
    },
  },
};

export type { PostAssessmentStep, PostAssessmentSteps, PostAssessmentFormsConfig };
