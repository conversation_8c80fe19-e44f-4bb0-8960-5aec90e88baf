'use client';

import MomBasicInfoWrapper from '@/app/portal/[role]/dashboard/people/_components/mom-basic-info-form-wrapper';
import MomProfileWrapper from '@/app/portal/[role]/dashboard/people/_components/mom-profile-wrapper';
import {
  formatFatherInvolved,
  formatGender,
  formatLegalCustodyStatus,
  formatLivesWith,
} from '@/app/portal/[role]/dashboard/people/_utils/people-utils';
import { FatherInvolved } from '@/app/portal/[role]/dashboard/people/_utils/people-utils';
import WithMomProfile from '@/app/portal/[role]/dashboard/people/moms/[id]/with-mom-profile';
import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import {
  type ChildFormSchema,
  childFormFields,
  childFormSchema,
  childrenTotalsFormFields,
  childrenTotalsFormSchema,
  defaultChildFormValues,
} from '@/app/portal/lib/mom-basic-info-children-form-config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DialogFooter } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Child, Mom } from '@/hooks/generated/__types';
import { useCreateChild, useFindManyChild, useUpdateChild } from '@/hooks/generated/child';
import { useUpdateMom } from '@/hooks/generated/mom';
import { useToast } from '@/hooks/use-toast';
import { type FormValues } from '@/types/form-field';
import { Fragment, useState } from 'react';
import { type FieldValues } from 'react-hook-form';

// Add totals form component
const ChildrenTotalsForm = ({ momId, momProfile }: { momId: string; momProfile: Partial<Mom> | null }) => {
  const { toast } = useToast();
  const updateMom = useUpdateMom();
  const [isSaving, setIsSaving] = useState(false);

  // Convert values to strings to ensure they display properly in the form
  const childrenInHome = String(momProfile?.children_in_home ?? 0);
  const totalChildren = String(momProfile?.number_of_children_c ?? 0);

  const handleSubmit = async (data: FormValues) => {
    setIsSaving(true);

    try {
      await updateMom.mutateAsync({
        where: { id: momId },
        data: {
          number_of_children_c: Number(data.total_children) || 0,
          children_in_home: Number(data.children_in_home) || 0,
        },
      });

      toast({
        title: 'Success',
        description: 'Children totals updated successfully',
        variant: 'success',
      });
    } catch {
      toast({
        title: 'Error',
        description: 'Failed to update children totals',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className='mt-6 rounded-md border p-6'>
      <FormFactory
        fields={[
          {
            ...childrenTotalsFormFields[0],
            defaultValue: totalChildren,
          },
          {
            ...childrenTotalsFormFields[1],
            defaultValue: childrenInHome,
          },
        ]}
        schema={childrenTotalsFormSchema}
        onSubmit={handleSubmit}
        defaultValues={{
          total_children: totalChildren,
          children_in_home: childrenInHome,
        }}
        actionButtonsConfig={{
          continueLabel: isSaving ? 'Saving...' : 'Save',
        }}
        submitDisabled={isSaving}
        formFieldElClass='w-full'
        formWrapperClassName='flex-col !gap-1 !items-start'
        labelWrapperClassName='!max-w-full !w-full'
      />
    </div>
  );
};

// Helper function to merge existing data with defaults
const mergeWithDefaults = (child: Child): ChildFormSchema => {
  // Format father_involved for the multi-select component
  let formattedFatherInvolved: { value: string; label: string }[] = [];

  if (child.father_involved && Array.isArray(child.father_involved)) {
    formattedFatherInvolved = child.father_involved
      .filter((value: unknown): value is string => typeof value === 'string')
      .map((value: string) => {
        const enumValue = Object.values(FatherInvolved).find((v) => v === value);
        return { value, label: enumValue ? formatFatherInvolved([value]) : value };
      });
  }

  // If no father involvement data is found, set a default value
  if (formattedFatherInvolved.length === 0) {
    formattedFatherInvolved = [{ value: FatherInvolved.Unknown, label: 'Unknown' }];
  }

  return {
    ...defaultChildFormValues,
    first_name: child.first_name || '',
    birthdate: child.birthdate,
    gender: child.gender || '',
    lives_with: child.lives_with || '',
    legal_custody_status: child.legal_custody_status || '',
    father_involved: formattedFatherInvolved,
    father_involvement: child.father_involvement || '',
    additional_info: child.additional_info || '',
  };
};

const MomChildrenInfoPage = ({ params }: { params: { id: string } }): React.ReactNode => {
  const { id } = params;
  const { momProfile, fetchMomProfile } = useMomProfileContext();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [currentChild, setCurrentChild] = useState<Child | null>(null);
  const [childToDelete, setChildToDelete] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});

  // Toggle expanded state for a child row
  const toggleRowExpand = (childId: string) => {
    setExpandedRows((prev) => ({
      ...prev,
      [childId]: !prev[childId],
    }));
  };

  // Fetch children for this mom
  const { data: children = [], isLoading } = useFindManyChild({
    where: {
      mom_id: id,
      deleted_at: 0,
    },
  });

  const activeChildren = children || [];
  const hasChildren = activeChildren.length > 0;

  // Create child mutation
  const createChild = useCreateChild();

  // Update child mutation for soft delete and edit
  const updateChild = useUpdateChild();

  const handleAddSubmit = async (data: FieldValues) => {
    try {
      setIsSubmitting(true);
      const formData = data as ChildFormSchema;
      // Extract simple string values from father_involved if it's an array of objects
      const fatherInvolved = Array.isArray(formData.father_involved)
        ? formData.father_involved
            .map((item) => {
              if (typeof item === 'string') return item;
              if (typeof item === 'object' && item !== null) return item.value;
              return '';
            })
            .filter(Boolean)
        : [];

      await createChild.mutateAsync({
        data: {
          first_name: formData.first_name,
          birthdate: formData.birthdate,
          gender: formData.gender,
          mom_id: id,
          lives_with: formData.lives_with,
          legal_custody_status: formData.legal_custody_status,
          father_involved: fatherInvolved,
          father_involvement: formData.father_involvement,
          additional_info: formData.additional_info,
        },
      });

      toast({
        title: 'Success',
        description: 'Child added successfully',
        variant: 'success',
      });

      fetchMomProfile(id);
      setShowAddModal(false);
    } catch {
      toast({
        title: 'Error',
        description: 'Failed to add child',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditSubmit = async (data: FieldValues) => {
    if (!currentChild) return;

    try {
      setIsSubmitting(true);
      const formData = data as ChildFormSchema;
      // Extract simple string values from father_involved if it's an array of objects
      const fatherInvolved = Array.isArray(formData.father_involved)
        ? formData.father_involved
            .map((item) => {
              if (typeof item === 'string') return item;
              if (typeof item === 'object' && item !== null) return item.value;
              return '';
            })
            .filter(Boolean)
        : [];

      await updateChild.mutateAsync({
        where: { id: currentChild.id },
        data: {
          first_name: formData.first_name,
          birthdate: formData.birthdate,
          gender: formData.gender,
          lives_with: formData.lives_with,
          legal_custody_status: formData.legal_custody_status,
          father_involved: fatherInvolved,
          father_involvement: formData.father_involvement,
          additional_info: formData.additional_info,
        },
      });

      toast({
        title: 'Success',
        description: 'Child updated successfully',
        variant: 'success',
      });

      fetchMomProfile(id);
      setShowEditModal(false);
      setCurrentChild(null);
    } catch {
      toast({
        title: 'Error',
        description: 'Failed to update child',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const confirmDeleteChild = (childId: string) => {
    setChildToDelete(childId);
    setShowDeleteConfirm(true);
  };

  const executeDeleteChild = async () => {
    if (!childToDelete) return;

    try {
      // Soft delete by setting deleted_at
      await updateChild.mutateAsync({
        where: { id: childToDelete },
        data: { deleted_at: Date.now() },
      });

      toast({
        title: 'Success',
        description: 'Child deleted successfully',
        variant: 'success',
      });

      fetchMomProfile(id);
    } catch {
      toast({
        title: 'Error',
        description: 'Failed to delete child',
        variant: 'destructive',
      });
    } finally {
      setShowDeleteConfirm(false);
      setChildToDelete(null);
    }
  };

  const openEditModal = (child: Child) => {
    setCurrentChild(child);
    setShowEditModal(true);
  };

  return (
    <WithMomProfile momId={id}>
      <MomProfileWrapper viewId='info' momId={id}>
        <MomBasicInfoWrapper momId={id} currentTab='children-info'>
          <div className='ml-10 max-w-[600px] rounded-md border bg-white p-6 max-md:ml-0 max-md:max-w-full'>
            <div className='mb-4 flex justify-end'>
              <Button
                type='button'
                onClick={() => setShowAddModal(true)}
                className='flex items-center gap-2 border border-gray-200 bg-white text-gray-800 hover:bg-gray-50'
              >
                <svg
                  xmlns='http://www.w3.org/2000/svg'
                  width='16'
                  height='16'
                  viewBox='0 0 24 24'
                  fill='none'
                  stroke='currentColor'
                  strokeWidth='2'
                  strokeLinecap='round'
                  strokeLinejoin='round'
                >
                  <line x1='12' y1='5' x2='12' y2='19'></line>
                  <line x1='5' y1='12' x2='19' y2='12'></line>
                </svg>
                Add Child
              </Button>
            </div>

            {/* Add Child Modal */}
            <Dialog open={showAddModal} onOpenChange={setShowAddModal}>
              <DialogContent className='max-h-[80vh] overflow-y-auto sm:max-w-[500px]'>
                <DialogHeader>
                  <DialogTitle>Add Child</DialogTitle>
                  <p className='text-sm text-gray-500'>Fields marked with * are required</p>
                </DialogHeader>
                <FormFactory
                  fields={childFormFields}
                  schema={childFormSchema}
                  onSubmit={handleAddSubmit}
                  defaultValues={defaultChildFormValues}
                  actionButtonsComponent={
                    <DialogFooter className='mt-4 flex w-full flex-row justify-end gap-2'>
                      <Button type='submit' disabled={isSubmitting}>
                        {isSubmitting ? 'Adding...' : 'Add Child'}
                      </Button>
                    </DialogFooter>
                  }
                  formFieldElClass='w-full'
                />
              </DialogContent>
            </Dialog>

            {/* Edit Child Modal */}
            <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
              <DialogContent className='max-h-[80vh] overflow-y-auto sm:max-w-[500px]'>
                <DialogHeader>
                  <DialogTitle>Edit Child</DialogTitle>
                  <p className='text-sm text-gray-500'>Fields marked with * are required</p>
                </DialogHeader>
                {currentChild && (
                  <FormFactory
                    fields={childFormFields}
                    schema={childFormSchema}
                    onSubmit={handleEditSubmit}
                    defaultValues={currentChild ? mergeWithDefaults(currentChild) : defaultChildFormValues}
                    actionButtonsComponent={
                      <DialogFooter className='mt-4 flex w-full flex-row justify-end gap-2'>
                        <Button type='submit' disabled={isSubmitting}>
                          {isSubmitting ? 'Saving...' : 'Save Changes'}
                        </Button>
                      </DialogFooter>
                    }
                    formFieldElClass='w-full'
                  />
                )}
              </DialogContent>
            </Dialog>

            {/* Delete Confirmation Dialog */}
            <Dialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
              <DialogContent className='sm:max-w-[425px]'>
                <DialogHeader>
                  <DialogTitle>Confirm Deletion</DialogTitle>
                </DialogHeader>
                <div className='py-4'>
                  <p>Are you sure you want to delete this child? This action cannot be undone.</p>
                </div>
                <DialogFooter className='flex justify-end gap-2'>
                  <Button type='button' onClick={() => setShowDeleteConfirm(false)}>
                    Cancel
                  </Button>
                  <Button type='button' className='bg-red-600 text-white hover:bg-red-700' onClick={executeDeleteChild}>
                    Delete
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {isLoading ? (
              <p className='py-4 text-center text-gray-500'>Loading children...</p>
            ) : hasChildren ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead></TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Birthdate</TableHead>
                    <TableHead>Gender</TableHead>
                    <TableHead className='w-[80px]'>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {activeChildren.map(
                    (child: {
                      id: string;
                      first_name: string;
                      birthdate: Date | null;
                      gender: string | null;
                      lives_with: string | null;
                      legal_custody_status: string | null;
                      father_involved: string[];
                      father_involvement: string | null;
                      additional_info: string | null;
                    }) => (
                      <Fragment key={child.id}>
                        <TableRow>
                          <TableCell>
                            <Button
                              className='h-8 w-8 p-0'
                              onClick={() => toggleRowExpand(child.id)}
                              aria-label={expandedRows[child.id] ? 'Collapse details' : 'Expand details'}
                            >
                              {expandedRows[child.id] ? (
                                <svg
                                  xmlns='http://www.w3.org/2000/svg'
                                  width='16'
                                  height='16'
                                  viewBox='0 0 24 24'
                                  fill='none'
                                  stroke='currentColor'
                                  strokeWidth='2'
                                  strokeLinecap='round'
                                  strokeLinejoin='round'
                                >
                                  <path d='M18 15l-6-6-6 6' />
                                </svg>
                              ) : (
                                <svg
                                  xmlns='http://www.w3.org/2000/svg'
                                  width='16'
                                  height='16'
                                  viewBox='0 0 24 24'
                                  fill='none'
                                  stroke='currentColor'
                                  strokeWidth='2'
                                  strokeLinecap='round'
                                  strokeLinejoin='round'
                                >
                                  <path d='M6 9l6 6 6-6' />
                                </svg>
                              )}
                            </Button>
                          </TableCell>
                          <TableCell className='font-medium'>{child.first_name}</TableCell>
                          <TableCell>
                            {child.birthdate ? new Date(child.birthdate).toLocaleDateString() : 'Not specified'}
                          </TableCell>
                          <TableCell>{formatGender(child.gender)}</TableCell>
                          <TableCell className='text-right'>
                            <div className='flex justify-end space-x-2'>
                              <Button
                                type='button'
                                onClick={() => openEditModal(child as Child)}
                                className='h-8 w-8 p-0'
                              >
                                <span className='sr-only'>Edit</span>
                                <svg
                                  xmlns='http://www.w3.org/2000/svg'
                                  width='16'
                                  height='16'
                                  viewBox='0 0 24 24'
                                  fill='none'
                                  stroke='currentColor'
                                  strokeWidth='2'
                                  strokeLinecap='round'
                                  strokeLinejoin='round'
                                >
                                  <path d='M17 3a2.85 2.85 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3Z'></path>
                                </svg>
                              </Button>
                              <Button
                                type='button'
                                onClick={() => confirmDeleteChild(child.id)}
                                className='h-8 w-8 bg-destructive p-0 text-destructive-foreground hover:bg-destructive/90'
                              >
                                <span className='sr-only'>Delete</span>
                                <svg
                                  xmlns='http://www.w3.org/2000/svg'
                                  width='16'
                                  height='16'
                                  viewBox='0 0 24 24'
                                  fill='none'
                                  stroke='currentColor'
                                  strokeWidth='2'
                                  strokeLinecap='round'
                                  strokeLinejoin='round'
                                >
                                  <path d='M3 6h18'></path>
                                  <path d='M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6'></path>
                                  <path d='M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2'></path>
                                  <line x1='10' y1='11' x2='10' y2='17'></line>
                                  <line x1='14' y1='11' x2='14' y2='17'></line>
                                </svg>
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                        {expandedRows[child.id] && (
                          <TableRow>
                            <TableCell colSpan={5} className='bg-muted/50 p-4'>
                              <div className='grid grid-cols-2 gap-4'>
                                <div>
                                  <h4 className='text-sm font-medium'>Living Situation</h4>
                                  <p className='text-sm text-muted-foreground'>{formatLivesWith(child.lives_with)}</p>
                                </div>
                                <div>
                                  <h4 className='text-sm font-medium'>Legal Custody Status</h4>
                                  <p className='text-sm text-muted-foreground'>
                                    {formatLegalCustodyStatus(child.legal_custody_status)}
                                  </p>
                                </div>
                                <div className='col-span-2'>
                                  <h4 className='text-sm font-medium'>Father Involved</h4>
                                  <p className='text-sm text-muted-foreground'>
                                    {formatFatherInvolved(child.father_involved)}
                                  </p>
                                </div>
                                {child.father_involvement && (
                                  <div className='col-span-2'>
                                    <h4 className='text-sm font-medium'>Father Involvement Details</h4>
                                    <p className='text-sm text-muted-foreground'>{child.father_involvement}</p>
                                  </div>
                                )}
                                {child.additional_info && (
                                  <div className='col-span-2'>
                                    <h4 className='text-sm font-medium'>Additional Information</h4>
                                    <p className='text-sm text-muted-foreground'>{child.additional_info}</p>
                                  </div>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        )}
                      </Fragment>
                    ),
                  )}
                </TableBody>
              </Table>
            ) : (
              <p className='py-4 text-center text-gray-500'>No children added yet</p>
            )}

            <ChildrenTotalsForm momId={id} momProfile={momProfile as Partial<Mom>} />
          </div>
        </MomBasicInfoWrapper>
      </MomProfileWrapper>
    </WithMomProfile>
  );
};

export default MomChildrenInfoPage;
