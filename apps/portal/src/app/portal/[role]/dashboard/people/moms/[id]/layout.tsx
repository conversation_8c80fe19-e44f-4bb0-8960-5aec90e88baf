import { MomConnectionLogsProvider } from '@/app/portal/context/mom-connection-logs-context';
import { MomFlaggedNeedsProvider } from '@/app/portal/context/mom-flagged-needs-context';
import { MomProfileProvider } from '@/app/portal/context/mom-profile-context';
import { MomSessionNotesProvider } from '@/app/portal/context/mom-session-notes-context';
import { PairedMomsListProvider } from '@/app/portal/context/paired-moms-context';
import WithRestrictedScrolling from '@/components/custom/with-restricted-scrolling';

const MomProfileLayout = ({ children }: { children: React.ReactNode }): React.ReactNode => {
  return (
    <WithRestrictedScrolling>
      <PairedMomsListProvider>
        <MomProfileProvider>
          <MomFlaggedNeedsProvider>
            <MomConnectionLogsProvider>
              <MomSessionNotesProvider>{children}</MomSessionNotesProvider>
            </MomConnectionLogsProvider>
          </MomFlaggedNeedsProvider>
        </MomProfileProvider>
      </PairedMomsListProvider>
    </WithRestrictedScrolling>
  );
};

export default MomProfileLayout;
