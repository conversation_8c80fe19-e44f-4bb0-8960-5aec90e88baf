import { BadgeProps } from '@/components/ui/badge';
import { convertUnderscoreCase } from '@/lib/utils';
import type { AdvocateStatus } from '@/types/schemas/user';
import type { YupSchemas } from '@suiteapi/models';

export const getMomStatusLabel = (status: YupSchemas.MomStatusType | undefined): string => {
  switch (status) {
    case 'inactive':
      return 'Inactive';
    case 'active':
      return 'Active';
    default:
      return status ? convertUnderscoreCase(status) : 'Unknown';
  }
};

export const mapMomStatusToBadgeVariant = (status: YupSchemas.MomStatusType | undefined): BadgeProps['variant'] => {
  switch (status) {
    case 'inactive':
      return 'neutral';
    case 'active':
      return 'success';
    default:
      return 'neutral';
  }
};

export const mapAdvocateStatusToBadgeVariant = (status: AdvocateStatus | 'Unknown'): BadgeProps['variant'] => {
  switch (status) {
    case 'Applied':
      return 'info1';
    case 'Interested':
      return 'info2';
    case 'Rejected':
      return 'danger';
    case 'Inactive':
      return 'neutral';
    case 'Active':
      return 'success';
    case 'In_Training':
      return 'info2';
    case 'Awaiting_Pairing':
      return 'warn';
    default:
      return 'neutral';
  }
};

// Gender enum
export enum Gender {
  Male = 'male',
  Female = 'female',
  Not_Specified = 'Not specified',
}

// Format gender display with the enum
export const formatGender = (genderCode: string | null): string => {
  // Handle null/undefined case
  if (!genderCode) {
    return Gender.Not_Specified;
  }

  // Find matching enum value
  const gender = Object.values(Gender).find((value) => value === genderCode);

  // If no match found, return original code
  if (!gender) {
    return genderCode;
  }

  // If it's not specified, return as is
  if (gender === Gender.Not_Specified) {
    return gender;
  }

  // For valid gender values, capitalize first letter
  return gender.charAt(0).toUpperCase() + gender.slice(1);
};

// Lives with enum
export enum LivesWith {
  Mother = 'mother',
  Father = 'father',
  BothParents = 'both_parents',
  Grandparents = 'grandparents',
  OtherFamily = 'other_family',
  FosterCare = 'foster_care',
  Other = 'other',
  NotSpecified = 'Not specified',
}

// Format lives with display with the enum
export const formatLivesWith = (livesWithCode: string | null): string => {
  // Handle null/undefined case
  if (!livesWithCode) {
    return LivesWith.NotSpecified;
  }

  // Find matching enum value
  const livesWith = Object.values(LivesWith).find((value) => value === livesWithCode);

  // If no match found, return original code
  if (!livesWith) {
    return livesWithCode;
  }

  // If it's NotSpecified, return as is
  if (livesWith === LivesWith.NotSpecified) {
    return livesWith;
  }

  // Format the display value
  switch (livesWith) {
    case LivesWith.Mother:
      return 'Mother (client)';
    case LivesWith.Father:
      return 'Father';
    case LivesWith.BothParents:
      return 'Both parents';
    case LivesWith.Grandparents:
      return 'Grandparents';
    case LivesWith.OtherFamily:
      return 'Other family member';
    case LivesWith.FosterCare:
      return 'Foster care';
    case LivesWith.Other:
      return 'Other';
    default:
      return livesWithCode;
  }
};

// Legal custody status enum
export enum LegalCustodyStatus {
  FullCustody = 'full_custody',
  SharedCustody = 'shared_custody',
  NoCustody = 'no_custody',
  TemporaryCustody = 'temporary_custody',
  Other = 'other',
  NotSpecified = 'Not specified',
}

// Format legal custody status display with the enum
export const formatLegalCustodyStatus = (custodyStatusCode: string | null): string => {
  // Handle null/undefined case
  if (!custodyStatusCode) {
    return LegalCustodyStatus.NotSpecified;
  }

  // Find matching enum value
  const status = Object.values(LegalCustodyStatus).find((value) => value === custodyStatusCode);

  // If no match found, return original code
  if (!status) {
    return custodyStatusCode;
  }

  // If it's NotSpecified, return as is
  if (status === LegalCustodyStatus.NotSpecified) {
    return status;
  }

  // Format the display value
  switch (status) {
    case LegalCustodyStatus.FullCustody:
      return 'Full custody';
    case LegalCustodyStatus.SharedCustody:
      return 'Shared custody';
    case LegalCustodyStatus.NoCustody:
      return 'No custody';
    case LegalCustodyStatus.TemporaryCustody:
      return 'Temporary custody';
    case LegalCustodyStatus.Other:
      return 'Other';
    default:
      return custodyStatusCode;
  }
};

// Father involved enum
export enum FatherInvolved {
  PhysicalPresence = 'physical_presence',
  FinancialSupport = 'financial_support',
  Childcare = 'childcare',
  EmotionalSupport = 'emotional_support',
  DecisionMaking = 'decision_making',
  NotInvolved = 'not_involved',
  Unknown = 'unknown',
  NotSpecified = 'Not specified',
}

// Format father involved display with the enum (handles array of codes)
export const formatFatherInvolved = (fatherInvolvedCodes: string[] | null): string => {
  if (!fatherInvolvedCodes || !Array.isArray(fatherInvolvedCodes) || fatherInvolvedCodes.length === 0) {
    return FatherInvolved.NotSpecified;
  }

  const formattedValues = fatherInvolvedCodes
    .map((code) => {
      // Handle object format if present
      if (typeof code === 'object' && code !== null && 'label' in code && 'value' in code) {
        return (
          (code as { label: string; value: string }).label ||
          (code as { label: string; value: string }).value ||
          'Unknown'
        );
      }

      // Find matching enum value
      const involvement = Object.values(FatherInvolved).find((value) => value === code);

      if (!involvement) {
        return code;
      }

      if (involvement === FatherInvolved.NotSpecified) {
        return involvement;
      }

      // Format the display value
      switch (involvement) {
        case FatherInvolved.PhysicalPresence:
          return 'Physical presence';
        case FatherInvolved.FinancialSupport:
          return 'Financial support';
        case FatherInvolved.Childcare:
          return 'Childcare';
        case FatherInvolved.EmotionalSupport:
          return 'Emotional support';
        case FatherInvolved.DecisionMaking:
          return 'Decision making';
        case FatherInvolved.NotInvolved:
          return 'Not involved';
        case FatherInvolved.Unknown:
          return 'Unknown';
        default:
          return code;
      }
    })
    .filter(Boolean);

  return formattedValues.length > 0 ? formattedValues.join(', ') : FatherInvolved.NotSpecified;
};

export const isUserCoordinatorOrAdvocate = (roles?: string[]): boolean => {
  if (!roles?.length) return false;
  return (new Set(roles).size === 1 && roles[0] === 'coordinator') || roles[0] === 'advocate';
};

export const isUserAdvocateOnly = (roles?: string[]): boolean => {
  if (!roles?.length) return false;
  return new Set(roles).size === 1 && roles[0] === 'advocate';
};
