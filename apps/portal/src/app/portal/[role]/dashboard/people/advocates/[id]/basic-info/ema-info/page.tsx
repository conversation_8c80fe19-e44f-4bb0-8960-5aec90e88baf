'use client';

import {
  editAdvocateEMAInfoFormSchema,
  editAdvocateEMAInformation,
} from '@/app/portal/lib/advocates/edit-advocate-ema-information.config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { useFindManyUser, useUpdateUser } from '@/hooks/generated/user';
import { useToast } from '@/hooks/use-toast';
import { useFindAdvocate } from '@/hooks/useFindAdvocate';
import { useParams } from 'next/navigation';
import React, { useState } from 'react';
import { FieldValues } from 'react-hook-form';

import AdvocateBasicInfoWrapper from '../../../../_components/advocates/advocate-basic-info-wrapper';
import AdvocateProfileWrapper from '../../../../_components/advocates/advocate-profile-wrapper';

const EMAInfoPage = () => {
  const params = useParams();
  const id = params.id as string;
  const { toast } = useToast();
  const { mutateAsync: updateUser } = useUpdateUser();

  const { advocate, isLoading } = useFindAdvocate(id);

  const { data: listOfCoordinators } = useFindManyUser({
    where: {
      userRoles: {
        some: {
          role: {
            key: 'coordinator',
          },
        },
      },
    },
    include: {
      userRoles: {
        select: {
          role: true,
        },
      },
    },
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const formatOptionsListForCoordinators = (coordinator: any) => ({
    label: `${coordinator.firstName} ${coordinator.lastName}`,
    value: coordinator.id,
  });

  const listOfCoordinatorsOptions = listOfCoordinators?.map(formatOptionsListForCoordinators);

  const handleSubmit = async (data: FieldValues) => {
    setIsSubmitting(true);
    const { advocate_status, assignedCoordinators } = data;
    await updateUser({
      where: { id },
      data: {
        advocate_status,
        assignedCoordinators: {
          set: assignedCoordinators.map((coordinatorId: string) => ({
            id: coordinatorId,
          })),
        },
      },
    });

    try {
      toast({
        title: 'Success',
        description: 'ema info updated successfully.',
      });
      setIsSubmitting(false);
    } catch (error) {
      console.error('Error updating ema info:', error);
      toast({
        title: 'Error',
        description: 'Failed to update ema info. Please try again.',
        variant: 'destructive',
      });
      setIsSubmitting(false);
    }
  };
  if (isLoading) {
    return <div>Loading...</div>;
  }

  const defaultValues = {
    advocate_status: advocate?.advocate_status || '',
    assignedCoordinators: advocate?.assignedCoordinators?.map(formatOptionsListForCoordinators) || [],
  };

  return (
    <AdvocateProfileWrapper viewId='ema-info' advocateId={id}>
      <AdvocateBasicInfoWrapper advocateId={id} currentTab='ema-info'>
        <div className='max-w-[600px] gap-4 rounded-lg border border-emaBorderSecondary bg-white p-4'>
          <FormFactory
            fields={editAdvocateEMAInformation(listOfCoordinatorsOptions ?? [])}
            schema={editAdvocateEMAInfoFormSchema}
            onSubmit={handleSubmit}
            defaultValues={defaultValues}
            formWrapperClassName='flex flex-col w-full'
            formFieldElClass='w-full'
            actionButtonsComponent={
              <div className='flex justify-end gap-2'>
                <Button variant='outline'>Cancel</Button>
                <Button variant='default' type='submit' disabled={isSubmitting}>
                  {isSubmitting ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            }
          />
        </div>
      </AdvocateBasicInfoWrapper>
    </AdvocateProfileWrapper>
  );
};

export default EMAInfoPage;
