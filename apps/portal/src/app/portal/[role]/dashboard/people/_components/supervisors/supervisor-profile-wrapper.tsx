'use client';

import { useSupervisorProfileContext } from '@/app/portal/context/supervisor-profile-context';
import GenericProfileHeader, { DEFAULT_AVATAR } from '@/components/custom/generic-profile-header';
import { useParams, usePathname, useRouter } from 'next/navigation';
import React from 'react';

import PersonActionTabs from '../person-action-tabs';

type TabValue = 'overview' | 'connection-log';

const SupervisorProfileWrapper = ({
  viewId = 'overview',
  children,
}: {
  viewId: TabValue;
  children: React.ReactNode;
}) => {
  const params = useParams();
  const router = useRouter();
  const pathname = usePathname();
  const supervisorId = params.id as string;
  const { supervisorProfile, isLoading, fetchSupervisor } = useSupervisorProfileContext();

  React.useEffect(() => {
    if (supervisorId) {
      fetchSupervisor(supervisorId);
    }
  }, [supervisorId]);

  const navigateToTab = (tab: string): void => {
    const basePath = pathname.split('/overview')[0].split('/connection-log')[0];
    const tabRoute = tab === 'overview' ? '' : `/${tab}`;
    router.push(`${basePath}${tabRoute}`);
  };
  const COORDINATOR_PROFILE_TABS = [
    { label: 'Overview', value: 'overview' },
    { label: 'Connection Log', value: 'connection-log' },
  ];

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!supervisorProfile) {
    return <div>No supervisor profile found</div>;
  }

  return (
    <div className='flex flex-col overflow-hidden pb-12'>
      <GenericProfileHeader
        photoUrl={supervisorProfile.photoUrl || DEFAULT_AVATAR}
        firstName={supervisorProfile.firstName ?? ''}
        lastName={supervisorProfile.lastName ?? ''}
        role='Supervisor'
        breadcrumbItems={[
          { label: 'People', href: '/people' },
          { label: 'Supervisors', href: '/people/supervisors' },
        ]}
      />

      <main className='mt-8 flex w-full flex-col max-md:max-w-full'>
        <div className='flex w-full flex-col px-8 max-md:max-w-full max-md:px-5 max-sm:px-1'>
          <PersonActionTabs items={COORDINATOR_PROFILE_TABS} activeTab={viewId} onTabChange={navigateToTab} />
          {children}
        </div>
      </main>
    </div>
  );
};

export default SupervisorProfileWrapper;
