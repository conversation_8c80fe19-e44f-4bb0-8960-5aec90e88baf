'use client';

import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { PhotoUpload } from '@/components/custom/photo-upload';
import { removeMomPhoto, uploadMomPhoto } from '@/lib/portal';
import type { MomType } from '@/types/schemas/mom';
import { useQueryClient } from '@tanstack/react-query';
import { CircleUserRound } from 'lucide-react';
import { useEffect } from 'react';

// Define the upload/remove function types
type UploadPhotoFn = (id: string, file: File) => Promise<MomType>;
type RemovePhotoFn = (id: string) => Promise<MomType>;

export function MomPhoto() {
  const { momProfile, fetchMomProfile, updateMomProfile } = useMomProfileContext();
  const momId = momProfile?.id;
  const momPhoto = momProfile?.photoUrl;
  const momThumbnail = momProfile?.thumbnailUrl;
  const queryClient = useQueryClient();

  // Handle photo-updated events from other components
  useEffect(() => {
    const handlePhotoUpdated = () => {
      if (momId) {
        fetchMomProfile(momId);
      }
    };

    window.addEventListener('photo-updated', handlePhotoUpdated);
    return () => window.removeEventListener('photo-updated', handlePhotoUpdated);
  }, [momId, fetchMomProfile]);

  const MomPlaceholder = <CircleUserRound className='h-28 w-28 text-muted-foreground' />;

  return (
    <PhotoUpload
      id={momId}
      photoUrl={momPhoto}
      thumbnailUrl={momThumbnail}
      uploadFunction={uploadMomPhoto as UploadPhotoFn}
      removeFunction={removeMomPhoto as RemovePhotoFn}
      queryKey='mom'
      additionalInvalidations={['mom-photo', 'mom-data']}
      entityName='mom'
      placeholderComponent={MomPlaceholder}
      onSuccess={(result) => {
        // Make sure existing photo cache entries are invalidated
        if (momPhoto) {
          const filename = momPhoto.split('/').pop();
          if (filename) {
            queryClient.invalidateQueries({ queryKey: ['photo', momPhoto] });
          }
        }

        // Also invalidate thumbnail cache if it exists
        if (momThumbnail) {
          const thumbFilename = momThumbnail.split('/').pop();
          if (thumbFilename) {
            queryClient.invalidateQueries({ queryKey: ['photo', momThumbnail] });
          }
        }

        // Invalidate all relevant queries
        if (momId) {
          queryClient.invalidateQueries({ queryKey: ['mom-photo', momId] });
          queryClient.invalidateQueries({ queryKey: ['mom', momId] });
          queryClient.invalidateQueries({ queryKey: ['mom-data', momId] });

          // Update the entire mom profile in context if result is valid
          if (result) {
            updateMomProfile(result);
          }

          // Add a delay before refreshing the profile to ensure server consistency
          setTimeout(() => {
            fetchMomProfile(momId).catch(() => {
              // Silently fail if profile fetch fails
            });
          }, 1000);
        }

        // Trigger photo-updated event to notify all components
        // Use a CustomEvent to include data about which entity was updated
        const photoUpdatedEvent = new CustomEvent('photo-updated', {
          detail: {
            entityType: 'mom',
            entityId: momId,
          },
        });
        window.dispatchEvent(photoUpdatedEvent);
      }}
    />
  );
}
