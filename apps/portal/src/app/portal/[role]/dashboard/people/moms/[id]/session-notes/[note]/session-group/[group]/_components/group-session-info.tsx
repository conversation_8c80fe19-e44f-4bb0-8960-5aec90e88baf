'use client';

import { getNextLessonId } from '@/app/portal/[role]/dashboard/people/_utils/session-utils';
import { usePairedMomsListContext } from '@/app/portal/context/paired-moms-context';
import { getGroupSessionNoteInfoConfig } from '@/app/portal/lib/group-session/group-session-note-info-config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { FieldConfig } from '@/types/form-field';
import { LessonSchema } from '@/types/schemas/lesson';
import { scheduleSessionFormSchema } from '@/types/schemas/session';
import React, { useState } from 'react';

const GroupSessionInfo = () => {
  const { pairedMomsList } = usePairedMomsListContext();
  const [, setSelectedMomId] = useState<string | null>(null);
  const [, setIsTrackSessionSelected] = useState(false);

  const momOptions =
    pairedMomsList?.map((pairing) => ({
      value: String(pairing.mom?.id),
      label: `${String(pairing.mom?.first_name)} ${String(pairing.mom?.last_name)}`,
    })) || [];

  const lessonOptions: LessonSchema[] = [
    {
      id: '1',
      title: 'Lesson 1',
      order: 1,
    },
  ];

  const lessonOptionsFieldConfig: FieldConfig['options'] = lessonOptions.map((lesson) => ({
    value: String(lesson.id),
    label: String(lesson.title),
  }));

  const currentLesson = getNextLessonId(lessonOptions);
  const formFields = getGroupSessionNoteInfoConfig(
    momOptions,
    lessonOptionsFieldConfig,
    currentLesson,
    setSelectedMomId,
    setIsTrackSessionSelected,
  );

  return (
    <FormFactory
      formWrapperClassName='flex-col'
      fields={formFields}
      schema={scheduleSessionFormSchema}
      formFieldElClass='w-full'
      formFieldSubFieldsWrapperClass='max-w-[420px]'
      onSubmit={() => {}}
      actionButtonsComponent={
        <div className='flex justify-end'>
          <Button type='submit'>Save Changes</Button>
        </div>
      }
    />
  );
};

export default GroupSessionInfo;
