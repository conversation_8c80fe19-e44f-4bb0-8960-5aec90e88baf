'use client';

import { FileUpload } from '@/components/custom/file-upload';
import GenericTable from '@/components/custom/generic-table';
import { Button } from '@/components/ui/button';
import { TableCell } from '@/components/ui/table';
import { useFindManyDocument } from '@/hooks/generated';
import type { Prisma } from '@/hooks/generated/__types';
import { useToast } from '@/hooks/use-toast';
import { downloadDocument } from '@/lib/portal';
import { sfetch } from '@/lib/sfetch';
import { YupSchemas } from '@suiteapi/models';
import { DownloadCloud, FileText, PlusIcon } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

interface DocumentManagementProps {
  documentFetchWhereClause: Prisma.DocumentWhereInput;
  documentDataEnhancements: object;
}

const DocumentManagement = ({ documentFetchWhereClause, documentDataEnhancements }: DocumentManagementProps) => {
  const { toast } = useToast();
  const [documents, setDocuments] = useState<YupSchemas.DocumentSchema[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { data: rawDocuments, isLoading } = useFindManyDocument({
    where: documentFetchWhereClause,
    orderBy: {
      created_at: 'desc',
    },
  });

  useEffect(() => {
    if (rawDocuments) {
      const documentsArray = Array.isArray(rawDocuments) ? rawDocuments : [rawDocuments];
      const formattedDocuments = documentsArray.map((doc) => ({
        id: doc.id,
        filename: doc.document_name || doc.filename,
        created_at: doc.created_at ? new Date(doc.created_at) : new Date(),
        mimeType: doc.mimeType,
      }));

      setDocuments(formattedDocuments);
    } else {
      setDocuments([]);
    }
  }, [rawDocuments]);

  const handleFileUpload = async (files: File | File[]) => {
    try {
      const processFile = async (file: File) => {
        const fileReader = new FileReader();
        return new Promise<void>((resolve, reject) => {
          fileReader.onload = async () => {
            try {
              const base64String = fileReader.result as string;
              const filecontents = base64String.split(',')[1] || base64String;

              const documentData = {
                filecontents,
                filename: file.name,
                mimeType: file.type,
                document_name: file.name,
                ...documentDataEnhancements,
              };

              const response = await sfetch('/v1/document', {
                method: 'POST',
                body: JSON.stringify(documentData),
                headers: {
                  'Content-Type': 'application/json',
                },
              });

              if (!response.ok) {
                throw new Error('Failed to upload file');
              }

              // Get the newly created document from the response
              const newDocument = await response.json();

              // Add the new document to the existing documents list
              setDocuments((prevDocuments) => [
                ...prevDocuments,
                {
                  id: newDocument.id,
                  filename: newDocument.document_name || newDocument.filename,
                  created_at: newDocument.created_at ? new Date(newDocument.created_at) : new Date(),
                  mimeType: newDocument.mimeType,
                },
              ]);
              resolve();
            } catch (error) {
              reject(error);
            }
          };
          fileReader.onerror = reject;
          fileReader.readAsDataURL(file);
        });
      };

      if (Array.isArray(files)) {
        for (const file of files) {
          await processFile(file);
        }
      } else {
        await processFile(files);
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      toast({
        title: 'Failed to Upload Document',
        description: 'There was an error uploading the document. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) handleFileUpload(file);
  };

  const onFileSelect = (files: File | File[]) => {
    if (files) handleFileUpload(files);
  };

  const handleDownload = async (documentId: string) => {
    try {
      const filename = documents.find((doc) => doc.id === documentId)?.filename || 'document';
      await downloadDocument(documentId, filename);
    } catch (error) {
      console.error('Error downloading file:', error);
      toast({
        title: 'Failed to Download Document',
        description: 'There was an error downloading the document. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const headers = ['File', 'Upload Date', 'Actions'];
  const columnWidths = [60, 30, 10];

  const renderRow = (item: YupSchemas.DocumentSchema) => (
    <>
      <TableCell className='py-4'>
        <div className='flex items-center gap-2 font-semibold'>
          <FileText className='h-8 w-8' />
          {item.filename}
        </div>
      </TableCell>
      <TableCell>{item.created_at instanceof Date ? item.created_at.toLocaleDateString() : ''}</TableCell>
      <TableCell>
        <button className='text-gray-600 hover:text-gray-800' onClick={() => handleDownload(item.id || '')}>
          <DownloadCloud />
        </button>
      </TableCell>
    </>
  );

  const renderMobileItem = (item: YupSchemas.DocumentSchema) => (
    <div className='space-y-2'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <FileText className='h-4 w-4' />
          {item.filename}
        </div>
        <button className='text-gray-600 hover:text-gray-800' onClick={() => handleDownload(item.id || '')}>
          <DownloadCloud />
        </button>
      </div>
      <div className='text-sm text-gray-500'>
        Uploaded: {item.created_at instanceof Date ? item.created_at.toLocaleDateString() : ''}
      </div>
    </div>
  );

  const headerSection = (
    <div className='flex items-center justify-between'>
      <h2 className='text-xl font-semibold'>Documents</h2>
      <div>
        <input
          type='file'
          ref={fileInputRef}
          onChange={handleInputChange}
          className='hidden'
          accept='.jpeg,.png,.pdf,.jpg'
        />
        <Button onClick={() => fileInputRef.current?.click()}>
          <PlusIcon className='mr-2 h-4 w-4' />
          Add Document
        </Button>
      </div>
    </div>
  );

  return (
    <div>
      <GenericTable
        data={documents}
        headers={headers}
        columnWidths={columnWidths}
        rowRenderer={renderRow}
        mobileRenderer={renderMobileItem}
        isLoading={isLoading}
        headerSection={headerSection}
        tableWrapperClassName='shadow-none mt-0'
      />
      {isLoading ? null : <FileUpload onFileSelect={onFileSelect} />}
    </div>
  );
};

export default DocumentManagement;
