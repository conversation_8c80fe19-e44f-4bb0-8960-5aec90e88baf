'use client';

import GenericTable from '@/components/custom/generic-table';
import { Button } from '@/components/ui/button';
import { TableCell, TableRow } from '@/components/ui/table';
import { useFindManyCoordinatorNote } from '@/hooks/generated';
import { YupSchemas } from '@suiteapi/models';
import { formatDateFromString } from '@suiteapi/models';
import { DownloadCloud } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useState } from 'react';

import AdvocateProfileWrapper from '../../../_components/advocates/advocate-profile-wrapper';
import AdvocateCoordinatorNotesDrawer from '../../_components/advoacte-coordinator-notes-drawer';
import AdvocateAddCoordinatorNoteModal from '../../_components/advocate-add-coordinator-note-modal';

const AdvocateCoordinatorNotesPage = () => {
  const params = useParams();
  const advocateId = params.id as string;
  const [selectedEntry, setSelectedEntry] = useState<YupSchemas.CoordinatorNotesSchema | null>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const { data: advocateCoordinatorNotes, isLoading } = useFindManyCoordinatorNote({
    where: {
      advocate_id: advocateId,
    },
  });

  const handleRowClick = (note: YupSchemas.CoordinatorNotesSchema) => {
    setSelectedEntry(note);
    setIsDrawerOpen(true);
  };

  const handleCloseDrawer = () => {
    setIsDrawerOpen(false);
  };

  const renderRow = (note: YupSchemas.CoordinatorNotesSchema) => (
    <TableRow>
      <TableCell>{note.created_at ? formatDateFromString(note.created_at.toISOString()) : ''}</TableCell>
      <TableCell>
        {note.type_c === YupSchemas.CoordinatorNoteTypeEnum.InterviewAdvocate
          ? 'Interview Advocate'
          : 'Safety or Concern Update'}
      </TableCell>
      <TableCell>{note.description}</TableCell>
      <TableCell>
        <div className='flex items-center gap-3'>
          <Button onClick={() => handleRowClick(note)} variant='outline' size='sm' className='border-none'>
            Review
          </Button>
        </div>
      </TableCell>
    </TableRow>
  );

  const renderMobileItem = (note: YupSchemas.CoordinatorNotesSchema) => (
    <div className='space-y-2'>
      <div className='flex justify-between'>
        <span className='font-medium'>{note.type_c}</span>
        <span className='text-gray-600'>
          {note.created_at ? formatDateFromString(note.created_at.toISOString()) : ''}
        </span>
      </div>
      <div className='text-sm text-gray-600'>{note.description}</div>
      <div className='flex justify-end gap-2'>
        <Button onClick={() => handleRowClick(note)} variant='outline' size='sm' className='border-none'>
          Review
        </Button>
      </div>
    </div>
  );

  const handleExport = (): void => {
    if (!advocateCoordinatorNotes || advocateCoordinatorNotes.length === 0) {
      alert('No data to export');
      return;
    }

    const csvHeader = 'Date,Note Type,Note Details\n';
    const csvRows = advocateCoordinatorNotes.map((note) => {
      const createdAt = note.created_at ? formatDateFromString(note.created_at.toISOString()) : '';
      const type_c =
        note.type_c === YupSchemas.CoordinatorNoteTypeEnum.SafetyOrConcernUpdate
          ? 'Safety or Concern Update'
          : 'Interview - Advocate';

      // Handle note description, ensure it's properly escaped for CSV... Escape double quotes in description
      const description = note.description ? note.description.replace(/"/g, '""') : '';
      const escapedDescription =
        description.includes(',') || description.includes('\n') || description.includes('"')
          ? `"${description}"`
          : description; // quote the description if it contains commas or new lines

      return `${createdAt},${type_c},${escapedDescription}`;
    });

    const csvContent = csvHeader + csvRows.join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'coordinator_notes.csv';
    link.click();
  };
  const headerSection = (
    <div className='flex flex-row items-center justify-between'>
      <div className='flex flex-col gap-2'>
        <h2 className='text-lg font-semibold'>Coordinator Notes</h2>
        <p className='text-sm text-emaTextSecondary'>
          Use this section to add notes for an advocate. These notes are not visible to the advocate.
        </p>
      </div>
      <div className='flex gap-4'>
        <Button variant='outline' onClick={handleExport}>
          <DownloadCloud /> <span className='ml-2'>Export All</span>
        </Button>
        <AdvocateAddCoordinatorNoteModal />
      </div>
    </div>
  );
  return (
    <AdvocateProfileWrapper viewId='coordinator-notes' advocateId={advocateId}>
      <GenericTable
        headers={['Date', 'Note Type', 'Note Details', 'Actions']}
        headerSection={headerSection}
        data={advocateCoordinatorNotes ?? []}
        isLoading={isLoading}
        rowRenderer={renderRow}
        mobileRenderer={renderMobileItem}
        shouldUseCustomRowComponent={true}
        emptyMessage={isLoading ? 'Loading...' : 'No coordinator notes available.'}
      />
      <AdvocateCoordinatorNotesDrawer isOpen={isDrawerOpen} onClose={handleCloseDrawer} entry={selectedEntry} />
    </AdvocateProfileWrapper>
  );
};

export default AdvocateCoordinatorNotesPage;
