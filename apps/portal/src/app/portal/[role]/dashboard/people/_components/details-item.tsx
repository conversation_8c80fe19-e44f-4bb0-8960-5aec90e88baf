import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Info } from 'lucide-react';
import { ReactNode } from 'react';

interface DetailsItemProps {
  label: string;
  value: string | ReactNode;
  icon?: React.ReactNode;
  tooltip?: string;
  contentClassName?: string;
}

const DetailsItem: React.FC<DetailsItemProps> = ({ label, value, icon, tooltip, contentClassName }) => (
  <div className='mt-6 flex w-full flex-col'>
    <div className='text-sm font-medium leading-5 text-slate-600'>{label}</div>
    <div className='mt-2 flex items-center justify-center gap-2 self-start overflow-hidden text-base font-semibold leading-6 text-slate-800'>
      {icon ? <div className='h-5 w-5'>{icon}</div> : null}
      <div className={`my-auto self-stretch ${contentClassName ?? ''}`}>{value}</div>
      {tooltip ? (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Info className='h-5 w-5 cursor-pointer' />
            </TooltipTrigger>
            <TooltipContent
              side='right'
              align='center'
              sideOffset={8}
              className='relative z-50 max-w-xs rounded-md bg-emaTextPrimary px-3 py-2 text-white shadow-md'
              style={{ whiteSpace: 'normal' }}
            >
              {tooltip}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ) : null}
    </div>
  </div>
);

export default DetailsItem;
