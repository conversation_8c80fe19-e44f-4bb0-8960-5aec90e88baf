'use client';

import type { FormValues } from '@/types/form-field';
import React from 'react';

import AssessmentWrapper from '../_components/assessment-wrapper';
import { preAssessmentFormsConfig } from './_lib/pre-assessment-forms.config';

interface PreAssessmentProps {
  params: {
    id: string;
  };
}

const PreAssessment = ({ params }: PreAssessmentProps) => {
  const handleSubmit = async (data: FormValues) => {
    await fetch(`/api/moms/${params.id}/pre-assessment`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  };

  const fetchInitialData = async () => {
    // TODO:Update this API to whatever it is supposed to be
    const response = await fetch(`/api/moms/${params.id}/post-assessment`);
    if (!response.ok) {
      // If no existing data, return empty object
      if (response.status === 404) return {};
      throw new Error('Failed to fetch post-assessment data');
    }
    return response.json();
  };

  return (
    <AssessmentWrapper
      title='Pre-Assessment'
      config={preAssessmentFormsConfig}
      onSubmit={handleSubmit}
      fetchInitialData={fetchInitialData}
    />
  );
};

export default PreAssessment;
