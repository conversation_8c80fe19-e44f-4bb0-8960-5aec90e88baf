'use client';

import GenericTable from '@/components/custom/generic-table';
import { Badge } from '@/components/ui/badge';
import { TableCell, TableRow } from '@/components/ui/table';
import { useCoordinators } from '@/hooks/useCoordinators';
import type { User } from '@/types/schemas/user';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { mapLanguageCodesToOptions } from '../_utils/language-utils';

interface CoordinatorsTableProps {
  isLoading?: boolean;
}

const CoordinatorsTable = ({ isLoading: initialLoading = false }: CoordinatorsTableProps) => {
  const [data, setData] = useState<User[]>([]);
  const { coordinators, isLoading } = useCoordinators();
  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    if (coordinators) {
      const formattedCoordinators = coordinators.map((coordinator) => ({
        ...coordinator,
        languages_c: mapLanguageCodesToOptions(coordinator.languages_c).filter(
          (option): option is { label: string; value: string } => option !== undefined,
        ),
      }));
      setData(formattedCoordinators);
    }
  }, [coordinators]);

  const headers = ['First Name', 'Last Name', 'User Type'];

  const renderRow = (coordinator: User) => (
    <TableRow
      className='cursor-pointer'
      onClick={() => {
        router.push(`${pathname}/${coordinator.id}`);
      }}
    >
      <TableCell>{coordinator.firstName}</TableCell>
      <TableCell>{coordinator.lastName}</TableCell>
      <TableCell>Coordinator</TableCell>
    </TableRow>
  );

  const renderMobileItem = (coordinator: User) => (
    <div className='cursor-pointer' onClick={() => router.push(`${pathname}/${coordinator.id}`)}>
      <div className='space-y-2'>
        <div className='flex justify-between'>
          <span className='font-medium'>
            {coordinator.firstName} {coordinator.lastName}
          </span>
          <Badge variant='info1'>Coordinator</Badge>
        </div>
      </div>
    </div>
  );

  return (
    <GenericTable
      data={data}
      isLoading={isLoading || initialLoading}
      headers={headers}
      rowRenderer={renderRow}
      mobileRenderer={renderMobileItem}
      emptyMessage='No coordinators found.'
      shouldUseCustomRowComponent={true}
    />
  );
};

export default CoordinatorsTable;
