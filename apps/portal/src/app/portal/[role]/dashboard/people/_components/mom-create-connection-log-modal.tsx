'use client';

import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import {
  createConnectionLogFormFields,
  createConnectionLogFormSchema,
} from '@/app/portal/lib/create-connection-log-form.config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useUserInfo } from '@/context/user-info';
import { ContactMethodType } from '@/hooks/generated/__types';
import { useCreateConnectionLog } from '@/hooks/generated/connection-log';
import type { FormValues } from '@/types/form-field';
import { YupSchemas } from '@suiteapi/models';
import { format } from 'date-fns';
import { Plus } from 'lucide-react';
import { useState } from 'react';

interface MomCreateConnectionLogModalProps {
  momId: string;
  successCallback?: () => void;
  buttonComponent?: React.ReactNode;
  open?: boolean;
  onClose?: () => void;
}

const MomCreateConnectionLogModal: React.FC<MomCreateConnectionLogModalProps> = ({
  momId,
  successCallback,
  buttonComponent,
  open,
  onClose,
}) => {
  const { userData } = useUserInfo();
  const { profile } = useDashboardContext();

  // open state managed by parent is needed to support opening modal from dropdown menu item
  // (normally/ideally, component can manage its own open state if open trigger is a static button on parent)
  const externallyControlledOpenState = typeof open === 'boolean' && typeof onClose === 'function';
  const externalOpenProps = externallyControlledOpenState ? { open } : {};

  const { isAtLeastCoordinator } = useUserInfo();
  const defaultValues = {
    // `is_visible_to_advocates_c` is user-selectable only if user's role is at least 'Coordinator'
    // otherwise, the field is hidden and the value is set to Yes
    is_visible_to_advocates_c: isAtLeastCoordinator ? undefined : '1',
  };

  const { momProfile } = useMomProfileContext();

  const { isError, error, mutateAsync: createConnectionLogAsync, isPending } = useCreateConnectionLog();

  const [isSuccess, setIsSuccess] = useState(false);

  const handleCloseAndClearForm = (): void => {
    if (externallyControlledOpenState) {
      onClose?.();
    }

    // prevent brief impression of form view when closing modal after success screen
    setTimeout(() => {
      setIsSuccess(false);
    }, 200);
  };

  const handleSubmit = async (data: FormValues): Promise<void> => {
    const requestData = {
      ...data,
      date_created_c: format(String(data.date_created_c), 'yyyy-MM-dd'), // convert datepicker format to API format
    } as unknown as YupSchemas.CreateConnectionLogSchema;

    await createConnectionLogAsync({
      data: {
        ...requestData,
        is_visible_to_advocates_c: requestData.is_visible_to_advocates_c,
        date_created_c: new Date(requestData.date_created_c),
        mom_id: momId,
        contact_method_c: requestData.contact_method_c as ContactMethodType,
        user_id: userData?.sub,
        name: `${momProfile?.first_name} ${momProfile?.last_name}`.trim(),
        created_by_id: profile?.id,
        created_by_name: `${userData?.firstName} ${userData?.lastName}`.trim(),
      },
    });
    successCallback?.();
    setIsSuccess(true);
  };

  return (
    <Dialog onOpenChange={handleCloseAndClearForm} {...externalOpenProps}>
      <DialogTrigger asChild>
        {externallyControlledOpenState
          ? null
          : (buttonComponent ?? (
              <button
                type='button'
                className='my-auto flex flex-col items-center justify-center gap-2 self-stretch overflow-hidden rounded-lg border border-emaBgQuaternary bg-darkGreen px-3.5 py-2.5 text-white shadow-sm md:flex-row md:gap-1 md:border-solid md:border-slate-800'
              >
                <Plus className='h-4 w-4 text-white' />
                <span className='my-auto self-stretch px-0.5'>Log Connection</span>
              </button>
            ))}
      </DialogTrigger>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle>Log a Connection</DialogTitle>
          <DialogDescription>
            {isSuccess ? 'Connection logged successfully!' : 'Enter details for the new connection.'}
          </DialogDescription>
        </DialogHeader>
        {!isSuccess && (
          <div className='space-y-4'>
            <FormFactory
              fields={createConnectionLogFormFields(isAtLeastCoordinator)}
              schema={createConnectionLogFormSchema}
              defaultValues={defaultValues}
              onSubmit={handleSubmit}
              actionButtonsComponent={
                <DialogFooter className='mt-4 sm:justify-end'>
                  <DialogClose asChild>
                    <Button type='button' variant='secondary'>
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button type='submit' variant='default' disabled={isPending}>
                    {isPending ? 'Submitting...' : 'Submit'}
                  </Button>
                </DialogFooter>
              }
            />
            {isError ? <div className='w-full text-center text-destructive'>{error.message}</div> : null}
          </div>
        )}
        {isSuccess ? (
          <>
            <div>Connection was successfully logged.</div>
            <DialogFooter className='mt-4 sm:justify-start'>
              <DialogClose asChild>
                <Button type='button' variant='secondary'>
                  Close
                </Button>
              </DialogClose>
            </DialogFooter>
          </>
        ) : null}
      </DialogContent>
    </Dialog>
  );
};

export default MomCreateConnectionLogModal;
