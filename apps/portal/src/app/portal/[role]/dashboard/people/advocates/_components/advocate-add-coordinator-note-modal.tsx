import { useAdvocateProfileContext } from '@/app/portal/context/advocate-profile-context';
import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import {
  createCoordinatorNoteForAdvocateFormFields,
  createCoordinatorNoteForMomFormSchema,
} from '@/app/portal/lib/create-coordinator-note.config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useCreateCoordinatorNote } from '@/hooks/generated';
import { CoordinatorNoteType } from '@/hooks/generated/__types';
import { useToast } from '@/hooks/use-toast';
import { FormValues } from '@/types/form-field';
import { FilePlus2 } from 'lucide-react';
import { useState } from 'react';

const AddCoordinatorNoteModal = ({ successCallback }: { successCallback?: () => void }) => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState<boolean>(false); // State to manage modal open/close
  const { advocateProfile } = useAdvocateProfileContext();
  const {
    profile: { id: userId },
  } = useDashboardContext();
  const { mutateAsync: createCoordinatorNoteAsync } = useCreateCoordinatorNote();

  const handleSubmit = async (values: FormValues) => {
    setIsSubmitting(true);

    try {
      await createCoordinatorNoteAsync({
        data: {
          type_c: values.type_c as CoordinatorNoteType,
          description: values.description as string,
          ...values,
          name:
            values.type_c +
            ' for ' +
            advocateProfile?.firstName +
            ' ' +
            advocateProfile?.lastName +
            ' on ' +
            new Date().toLocaleDateString(),
          coordinator_id: userId,
          advocate_id: advocateProfile?.id ?? '',
        },
      });

      toast({
        title: 'Success',
        description: 'The coordinator note has been successfully added',
        variant: 'success',
      });
      setIsOpen(false);
      successCallback?.();
    } catch (error) {
      console.error('Error adding coordinator note:', error);
      toast({
        title: 'Error',
        description: 'There was an error adding the coordinator note. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      {/* Trigger */}
      <DialogTrigger asChild>
        <Button className='bg-darkGreen text-white'>
          <FilePlus2 />
          <span className='ml-2'>Add Note</span>
        </Button>
      </DialogTrigger>

      {/* Modal */}
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Coordinator Note</DialogTitle>
          <DialogDescription>Please fill out the form below to add a note on the advocate.</DialogDescription>
        </DialogHeader>

        <FormFactory
          fields={createCoordinatorNoteForAdvocateFormFields}
          schema={createCoordinatorNoteForMomFormSchema}
          onSubmit={handleSubmit}
          actionButtonsComponent={
            <DialogFooter className='mt-4 sm:justify-end'>
              <DialogClose asChild>
                <Button type='button' variant='secondary'>
                  Cancel
                </Button>
              </DialogClose>
              <Button type='submit' variant='default' disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : 'Save'}
              </Button>
            </DialogFooter>
          }
        />
      </DialogContent>
    </Dialog>
  );
};

export default AddCoordinatorNoteModal;
