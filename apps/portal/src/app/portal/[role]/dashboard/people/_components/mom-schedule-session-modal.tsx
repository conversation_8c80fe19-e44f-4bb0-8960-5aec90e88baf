'use client';

import { usePairedMomsListContext } from '@/app/portal/context/paired-moms-context';
import { getMomScheduleSessionFormFields } from '@/app/portal/lib/mom-schedule-session-form.config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useCreatePairingSession } from '@/hooks/use-create-session';
import type { FieldConfig, FormValues } from '@/types/form-field';
import type { PairingOrMomSchema } from '@/types/schemas/pairing';
import { type SessionSchema, scheduleMultipleSessionFormSchema } from '@/types/schemas/session';
import { CalendarPlus } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';

import { useFindManyLesson, useFindManyLessonTemplate, useFindManyMom } from '../../../../../../hooks/generated';
import { useDashboardContext } from '../../../../context/dashboard-context';
import { handleDownloadCalendarFile } from '../_utils/session-utils';
import MomScheduleSessionSuccess from './mom-schedule-session-success';

interface MomScheduleSessionModalProps {
  pairingId?: string;
  buttonComponent?: React.ReactNode;
  open?: boolean;
  onClose?: () => void;
  successCallback?: () => void;
}

const MomScheduleSessionModal: React.FC<MomScheduleSessionModalProps> = ({
  pairingId,
  buttonComponent,
  open,
  onClose,
  successCallback,
}) => {
  // open state managed by parent is needed to support opening modal from dropdown menu item
  // (normally/ideally, component can manage its own open state if open trigger is a static button on parent)
  const externallyControlledOpenState = typeof open === 'boolean' && typeof onClose === 'function';
  const externalOpenProps = externallyControlledOpenState ? { open } : {};

  const { profile } = useDashboardContext();
  const [isTrackSessionSelected, setIsTrackSessionSelected] = useState(false);
  const [selectedPairingIds, setSelectedPairingIds] = useState<string[]>([]);
  const [selectedTrackId, setSelectedTrackId] = useState<string | undefined>(undefined);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [scheduledSessionData, setScheduledSessionData] = useState<{ sessions: SessionSchema[] } | null>(null);
  const [scheduledPairingData, setScheduledPairingData] = useState<PairingOrMomSchema[] | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const isCoordinator = profile.roles.includes('coordinator');
  const coordinatorId = isCoordinator ? profile.id : undefined;

  const { data: connectedMoms } = useFindManyMom(
    { where: { assigned_user_id: profile.id } },
    { enabled: isCoordinator },
  );
  const { pairedMomsList } = usePairedMomsListContext();
  const { createSingleSession, createMultipleSessions } = useCreatePairingSession();

  const handleCloseAndClearForm = (): void => {
    if (isSuccess && successCallback) {
      // can be used to trigger refresh of content that relates to session data
      successCallback();
    }

    if (externallyControlledOpenState) {
      onClose?.();
    }

    // prevent brief impression of form view when closing modal after success screen
    setTimeout(() => {
      setIsTrackSessionSelected(false);
      setSelectedPairingIds([]);
      setSelectedTrackId(undefined);
      setIsSubmitting(false);
      setIsSuccess(false);
      setScheduledSessionData(null);
      setScheduledPairingData(null);
      setErrorMessage(null);
    }, 200);
  };

  const onScheduleSessionSuccess = (
    sessionsResponse: { sessions: SessionSchema[] },
    pairingsOrMoms: PairingOrMomSchema[],
  ): void => {
    setScheduledSessionData(sessionsResponse);
    setScheduledPairingData(pairingsOrMoms);
    setIsSuccess(true);
  };

  const pairingOrMomList = useMemo(
    (): PairingOrMomSchema[] =>
      isCoordinator
        ? connectedMoms?.map((mom) => ({
            id: mom.id,
            mom: {
              id: mom.id ?? undefined,
              first_name: mom.first_name ?? undefined,
              last_name: mom.last_name ?? '',
              assigned_user_id: mom.assigned_user_id ?? undefined,
            },
          })) || []
        : pairedMomsList?.map((pairing) => ({
            id: pairing.id ?? '',
            mom: {
              id: pairing.mom?.id ?? undefined,
              first_name: pairing.mom?.first_name ?? undefined,
              last_name: pairing.mom?.last_name ?? '',
              assigned_user_id: pairing.mom?.assigned_user_id ?? undefined,
            },
          })) || [],
    [connectedMoms, pairedMomsList],
  );

  const handleSubmit = async (data: FormValues): Promise<void> => {
    const selectedPairingOrMomIds = (data.pairing_or_mom_ids as { value: string }[]).map(
      (pairingOrMom) => pairingOrMom.value,
    );
    const pairingsOrMoms = pairingOrMomList?.filter((pairingOrMom) =>
      selectedPairingOrMomIds.includes(pairingOrMom.id),
    );

    if (pairingsOrMoms.length === 1) {
      createSingleSession({
        coordinatorId,
        data,
        pairingOrMom: pairingsOrMoms[0],
        setIsSubmitting,
        successCallback: (sessionResponse) => onScheduleSessionSuccess({ sessions: [sessionResponse] }, pairingsOrMoms),
        setErrorMessage,
      });
    } else {
      createMultipleSessions({
        coordinatorId,
        data,
        pairingsOrMoms,
        setIsSubmitting,
        successCallback: (sessionResponse) => onScheduleSessionSuccess(sessionResponse, pairingsOrMoms),
        setErrorMessage,
      });
    }
  };

  const pairingOrMomOptions = useMemo(
    () =>
      pairingOrMomList?.map(({ id, mom }) => ({
        value: id,
        label: `${mom?.first_name} ${mom?.last_name}`,
      })),
    [pairingOrMomList],
  );

  const trackOptions = useMemo(() => {
    if (!isTrackSessionSelected) {
      return [];
    }

    // Get all tracks from selected pairings
    const tracks = pairedMomsList
      ?.filter((pairing) => selectedPairingIds.includes(pairing.id ?? ''))
      .map((pairing) => pairing.track)
      .filter((track) => track !== undefined);

    // Deduplicate tracks using a Map with track ID as key
    const uniqueTracks = new Map(tracks?.map((track) => [track.id, track]));

    // Convert Map values back to array and map to options format
    return (
      Array.from(uniqueTracks.values()).map((track) => ({
        value: String(track.id),
        label: String(track.title),
      })) ?? []
    );
  }, [selectedPairingIds.join(','), isTrackSessionSelected]);

  const { data: lessons } = useFindManyLesson(
    { where: { pairing_id: selectedPairingIds[0] } },
    { enabled: selectedPairingIds.length === 1 },
  );

  const { data: lessonTemplates } = useFindManyLessonTemplate(
    { where: { track_id: selectedTrackId } },
    { enabled: !!selectedTrackId },
  );

  const lessonOptions = useMemo(
    () =>
      (lessons ?? lessonTemplates)?.map((lesson) => ({
        value: String(lesson.id),
        label: String(lesson.title),
      })) ?? [],
    [lessons, lessonTemplates],
  );

  const formFields: FieldConfig[] = useMemo(
    () =>
      getMomScheduleSessionFormFields(
        isCoordinator,
        pairingOrMomOptions,
        lessonOptions,
        trackOptions,
        selectedTrackId,
        setSelectedPairingIds,
        setIsTrackSessionSelected,
        setSelectedTrackId,
      ),
    [
      isCoordinator,
      pairingOrMomOptions,
      lessonOptions,
      trackOptions,
      selectedTrackId,
      setSelectedPairingIds,
      setIsTrackSessionSelected,
      setSelectedTrackId,
    ],
  );

  const defaultValues = {
    pairing_or_mom_ids: isCoordinator
      ? []
      : [pairingOrMomOptions.find((option) => option.value === pairingId)].filter(Boolean),
  };

  useEffect(() => {
    if (trackOptions.length === 1) {
      setSelectedTrackId(trackOptions[0].value);
    }
  }, [trackOptions]);

  return (
    <Dialog onOpenChange={handleCloseAndClearForm} {...externalOpenProps}>
      <DialogTrigger asChild>
        {externallyControlledOpenState
          ? null
          : (buttonComponent ?? (
              <Button>
                <CalendarPlus className='mr-2' size={16} />
                Schedule Session
              </Button>
            ))}
      </DialogTrigger>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle>Schedule session</DialogTitle>
          <DialogDescription>
            {isSuccess ? 'Session scheduled!' : 'Schedule a session for a group or individual.'}
          </DialogDescription>
        </DialogHeader>
        {!isSuccess && (
          <div className='space-y-4'>
            <FormFactory
              fields={formFields}
              schema={scheduleMultipleSessionFormSchema}
              onSubmit={handleSubmit}
              defaultValues={defaultValues}
              actionButtonsComponent={
                <DialogFooter className='mt-4 sm:justify-end'>
                  <DialogClose asChild>
                    <Button type='button' variant='secondary'>
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button type='submit' variant='default' disabled={isSubmitting}>
                    {isSubmitting ? 'Saving...' : 'Save Changes'}
                  </Button>
                </DialogFooter>
              }
            />
            {errorMessage ? <div className='w-full text-center text-destructive'>{errorMessage}</div> : null}
          </div>
        )}
        {isSuccess && scheduledSessionData && scheduledPairingData ? (
          <>
            <MomScheduleSessionSuccess
              scheduledSessions={scheduledSessionData.sessions}
              scheduledPairings={scheduledPairingData}
            />
            <DialogFooter className='mt-4 sm:justify-end'>
              <DialogClose asChild>
                <Button type='button' variant='secondary'>
                  Done
                </Button>
              </DialogClose>
              <Button
                type='button'
                onClick={() =>
                  handleDownloadCalendarFile(
                    scheduledSessionData.sessions[0],
                    scheduledPairingData
                      .map((pairing) => `${pairing.mom?.first_name} ${pairing.mom?.last_name}`)
                      .join(', '),
                  )
                }
                variant='default'
              >
                Add to Calendar
              </Button>
            </DialogFooter>
          </>
        ) : null}
      </DialogContent>
    </Dialog>
  );
};

export default MomScheduleSessionModal;
