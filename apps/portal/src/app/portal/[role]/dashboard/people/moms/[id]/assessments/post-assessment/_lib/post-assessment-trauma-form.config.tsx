import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

import { yesNoPNAOptions } from '../../pre-assessment/_lib/pre-assessment-trauma-form-config';

export const postAssessmentTraumaFormSchema = yup.object().shape({
  post_as_trau_physical_neglect: yup.string().required('Please select an option'),
  post_as_trau_pregnancy_loss: yup.string().required('Please select an option'),
  post_as_trau_child_separation: yup.string().required('Please select an option'),
  post_as_trau_child_disability: yup.string().required('Please select an option'),
  post_as_trau_caregiver_disability: yup.string().required('Please select an option'),
  post_as_trau_sudden_death: yup.string().required('Please select an option'),
});

export const postAssessmentTraumaFormConfig: FieldConfig[] = [
  {
    name: 'post_as_trau_physical_neglect',
    type: 'select',
    placeholder: 'Select',
    label:
      '13) Have you ever been physically neglected (for example, not fed, not properly clothed, or left to take care of yourself when you were too young or ill)?',
    options: yesNoPNAOptions,
  },
  {
    name: 'post_as_trau_pregnancy_loss',
    type: 'select',
    placeholder: 'Select',
    label: '14) Have you ever had an abortion or miscarriage (lost your baby)?',
    options: yesNoPNAOptions,
  },
  {
    name: 'post_as_trau_child_separation',
    type: 'select',
    placeholder: 'Select',
    label:
      '15) Have you ever been separated from your child against your will (for example, the loss of custody or visitation or kidnapping)?',
    options: yesNoPNAOptions,
  },
  {
    name: 'post_as_trau_child_disability',
    type: 'select',
    placeholder: 'Select',
    label:
      "16) Has a baby or child of yours ever had a severe physical or mental handicap (for example, mentally retarded, birth defects, can't hear, see, walk)?",
    options: yesNoPNAOptions,
  },
  {
    name: 'post_as_trau_caregiver_disability',
    type: 'select',
    placeholder: 'Select',
    label:
      "17) Have you ever been responsible for taking care of someone close to you (not your child) who had a severe physical or mental handicap (for example, cancer, stroke, AIDS, nerve problems, can't hear, see, walk)?",
    options: yesNoPNAOptions,
  },
  {
    name: 'post_as_trau_sudden_death',
    type: 'select',
    placeholder: 'Select',
    label:
      '18) Has someone close to you died suddenly or unexpectedly (for example, sudden heart attack, murder or suicide)?',
    options: yesNoPNAOptions,
  },
];
