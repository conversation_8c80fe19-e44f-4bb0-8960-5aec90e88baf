import { sendAssessment<PERSON><PERSON><PERSON>ields, sendAssessmentFormSchema } from '@/app/portal/lib/send-assessment-form-config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { Dialog, DialogClose, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useState } from 'react';

interface SendAssessmentFormModalProps {
  open?: boolean;
  onClose?: () => void;
}

const SendAssessmentFormModal: React.FC<SendAssessmentFormModalProps> = ({ open, onClose }) => {
  const [isSubmitting] = useState<boolean>(false);
  const [errorMessage] = useState<string | null>(null);

  const defaultValues = {};

  const handleSubmit = async (): Promise<void> => {
    onClose?.();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle>Send Pre or Post Assessment Link</DialogTitle>
        </DialogHeader>

        <div className='space-y-4'>
          <FormFactory
            fields={sendAssessmentFormFields}
            schema={sendAssessmentFormSchema}
            onSubmit={handleSubmit}
            defaultValues={defaultValues}
            actionButtonsComponent={
              <DialogFooter className='mt-4 gap-4 sm:justify-end'>
                <DialogClose asChild>
                  <Button type='button' variant='secondary'>
                    Cancel
                  </Button>
                </DialogClose>
                <Button type='submit' variant='default' disabled={isSubmitting}>
                  {isSubmitting ? 'Sending...' : 'Send'}
                </Button>
              </DialogFooter>
            }
          />
        </div>
        {errorMessage ? <div className='text-center text-destructive'>{errorMessage}</div> : null}
      </DialogContent>
    </Dialog>
  );
};

export default SendAssessmentFormModal;
