import { useFindManyAssessmentQuestion } from '../../../../../../../../../hooks/generated';

export const useAssessmentQuestions = ({
  assessmentId,
  assessmentResultId,
}: {
  assessmentId: string;
  assessmentResultId: string;
}) => {
  const { data: assessmentQuestions, isLoading: areAssessmentQuestionsLoading } = useFindManyAssessmentQuestion({
    where: {
      assessmentId: {
        equals: assessmentId,
      },
    },
    orderBy: {
      order: 'asc',
    },
    select: {
      id: true,
      question: true,
      order: true,
      responseType: true,
      assessmentConstruct: {
        select: {
          name: true,
          order: true,
        },
      },
      assessmentResultQuestionResponses: {
        where: {
          assessmentResultId: {
            equals: assessmentResultId,
          },
        },
        select: {
          id: true,
          intResponse: true,
          stringResponse: true,
        },
      },
    },
  });

  return { assessmentQuestions, areAssessmentQuestionsLoading };
};
