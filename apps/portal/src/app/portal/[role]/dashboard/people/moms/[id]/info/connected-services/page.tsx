'use client';

import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { momServicesFormFields, momServicesFormSchema } from '@/app/portal/lib/mom-basic-info-form-config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { useUpdateMom } from '@/hooks/generated/mom';
import { useToast } from '@/hooks/use-toast';
import { type FormValues } from '@/types/form-field';
import { useState } from 'react';

import MomBasicInfoWrapper from '../../../../_components/mom-basic-info-form-wrapper';
import MomProfileWrapper from '../../../../_components/mom-profile-wrapper';
import WithMomProfile from '../../with-mom-profile';

const MomConnectedServicesPage = ({ params }: { params: { id: string } }): React.ReactNode => {
  const { id } = params;
  const { momProfile, fetchMomProfile } = useMomProfileContext();
  const { toast } = useToast();
  const { mutateAsync: updateMomAsync } = useUpdateMom();

  const [isSubmitting, setIsSubmitting] = useState(false);

  const defaultValues = {
    connected_benevolance_c: momProfile?.connected_benevolance_c || false,
    connected_childcare_c: momProfile?.connected_childcare_c || false,
    connected_closet_c: momProfile?.connected_closet_c || false,
    connected_education_c: momProfile?.connected_education_c || false,
    connected_health_c: momProfile?.connected_health_c || false,
    connected_housing_c: momProfile?.connected_housing_c || false,
    connected_legal_c: momProfile?.connected_legal_c || false,
    connected_mental_health_c: momProfile?.connected_mental_health_c || false,
    connected_substance_c: momProfile?.connected_substance_c || false,
  };

  const handleSubmit = async (data: FormValues) => {
    setIsSubmitting(true);

    try {
      await updateMomAsync({
        where: { id },
        data: data,
      });

      toast({
        title: 'Success',
        description: 'The connected services information has been successfully saved',
        variant: 'success',
      });

      fetchMomProfile(id);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'There was an error saving the connected services information. Please try again.',
        variant: 'destructive',
      });

      console.error('Failed to submit:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <WithMomProfile momId={id}>
      <MomProfileWrapper viewId='info' momId={id}>
        <MomBasicInfoWrapper momId={id} currentTab='connected-services'>
          <div className='ml-10 w-full rounded-md border bg-white p-6 max-md:ml-0 md:w-[560px]'>
            <FormFactory
              fields={momServicesFormFields}
              schema={momServicesFormSchema}
              onSubmit={handleSubmit}
              defaultValues={defaultValues}
              actionButtonsComponent={
                <div className='my-auto flex w-full min-w-[240px] flex-1 shrink basis-0 items-center justify-end gap-3 self-stretch border-t-[1px] pt-4'>
                  <Button
                    type='submit'
                    className='my-auto flex items-center justify-center gap-1 self-stretch overflow-hidden rounded-lg border-2 border-solid border-white border-opacity-10 bg-slate-800 px-3.5 py-2.5 text-white shadow-sm'
                  >
                    {isSubmitting ? 'Saving...' : 'Save changes'}
                  </Button>
                </div>
              }
            />
          </div>
        </MomBasicInfoWrapper>
      </MomProfileWrapper>
    </WithMomProfile>
  );
};

export default MomConnectedServicesPage;
