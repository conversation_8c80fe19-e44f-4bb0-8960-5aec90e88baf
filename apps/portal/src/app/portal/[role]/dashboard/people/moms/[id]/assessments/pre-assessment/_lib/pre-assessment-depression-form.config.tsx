import { frequencyInLastWeekOptions } from '@/app/portal/[role]/dashboard/people/referrals/_components/intake-referral-form.config';
import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const difficultyOptions = [
  { label: 'Not difficult at all', value: 'not_difficult' },
  { label: 'Somewhat difficult', value: 'somewhat_difficult' },
  { label: 'Very difficult', value: 'very_difficult' },
  { label: 'Extremely difficult', value: 'extremely_difficult' },
];

export const preAssessmentDepressionFormConfig: FieldConfig[] = [
  {
    name: 'pre_as_dep_little_interest',
    type: 'select',
    placeholder: 'Select',
    label: '1) Little interest or pleasure in doing things',
    options: frequencyInLastWeekOptions,
  },
  {
    name: 'pre_as_dep_feeling_down',
    type: 'select',
    placeholder: 'Select',
    label: '2) Feeling down, depressed, or hopeless',
    options: frequencyInLastWeekOptions,
  },
  {
    name: 'pre_as_dep_sleep_trouble',
    type: 'select',
    placeholder: 'Select',
    label: '3) Trouble falling or staying asleep, or sleeping too much',
    options: frequencyInLastWeekOptions,
  },
  {
    name: 'pre_as_dep_tired',
    type: 'select',
    placeholder: 'Select',
    label: '4) Feeling tired or having little energy',
    options: frequencyInLastWeekOptions,
  },
  {
    name: 'pre_as_dep_appetite',
    type: 'select',
    placeholder: 'Select',
    label: '5) Poor appetite or overeating',
    options: frequencyInLastWeekOptions,
  },
  {
    name: 'pre_as_dep_feeling_bad',
    type: 'select',
    placeholder: 'Select',
    label: '6) Feeling bad about yourself or that you are a failure or have let yourself or your family down',
    options: frequencyInLastWeekOptions,
  },
  {
    name: 'pre_as_dep_concentration',
    type: 'select',
    placeholder: 'Select',
    label: '7) Trouble concentrating on things, such as reading the newspaper or watching television',
    options: frequencyInLastWeekOptions,
  },
  {
    name: 'pre_as_dep_movement',
    type: 'select',
    placeholder: 'Select',
    label:
      '8) Moving or speaking so slowly that other people could have noticed. Or the opposite being so fidgety or restless that you have been moving around a lot more than usual',
    options: frequencyInLastWeekOptions,
  },
  {
    name: 'pre_as_dep_self_harm',
    type: 'select',
    placeholder: 'Select',
    label: '9) Thoughts that you would be better off dead, or of hurting yourself',
    options: frequencyInLastWeekOptions,
  },
  {
    name: 'pre_as_dep_difficulty_level',
    type: 'select',
    placeholder: 'Select',
    label:
      '10) If you checked off any problems, how difficult have these problems made it for you to do your work, take care of things at home, or get along with other people?',
    options: difficultyOptions,
  },
];

export const preAssessmentDepressionSchema = yup.object().shape({
  pre_as_dep_little_interest: yup.string().required('Required'),
  pre_as_dep_feeling_down: yup.string().required('Required'),
  pre_as_dep_sleep_trouble: yup.string().required('Required'),
  pre_as_dep_tired: yup.string().required('Required'),
  pre_as_dep_appetite: yup.string().required('Required'),
  pre_as_dep_feeling_bad: yup.string().required('Required'),
  pre_as_dep_concentration: yup.string().required('Required'),
  pre_as_dep_movement: yup.string().required('Required'),
  pre_as_dep_self_harm: yup.string().required('Required'),
  pre_as_dep_difficulty_level: yup.string().required('Required'),
});
