'use client';

import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import { useRouter } from 'next/navigation';
import React from 'react';

const tabs = [
  { navLabel: 'Mom Information', title: 'contact-info' },
  { navLabel: 'Address', title: 'mom-address-info' },
  { navLabel: 'Children Information', title: 'children-info' },
  { navLabel: 'Documents', title: 'files' },
  { navLabel: 'Connected Services', title: 'connected-services' },
  { navLabel: 'EMA Info', title: 'ema-info' },
  { navLabel: 'Mom Photo', title: 'mom-photo' },
];

const MomBasicInfoWrapper = ({
  children,
  momId,
  currentTab,
}: {
  children?: React.ReactNode;
  momId: string;
  currentTab: string;
}) => {
  const router = useRouter();
  const { profile } = useDashboardContext();

  const navigateToTab = (tab: string): void => {
    const tabRoute = tab === 'contact-info' ? '/' : `/${tab}`;
    router.push(`/portal/${profile.portalRole}/dashboard/people/moms/${momId}/info${tabRoute}`);
  };

  return (
    <div className='mt-10 flex max-md:flex-col'>
      <nav className='mb-4 hidden w-full flex-col text-sm font-semibold leading-5 text-emaTextPrimary md:mb-0 md:flex md:w-[280px]'>
        {tabs.map((tab, index) => (
          <div
            key={tab.title}
            onClick={() => navigateToTab(tab.title)}
            className={`cursor-pointer gap-2 self-stretch px-3 py-2 ${index === 0 ? '' : 'mt-1'} w-full ${
              currentTab === tab.title ? 'border-l-2 border-l-emaBrandSecondary' : 'text-emaTextQuaternary'
            } min-h-[36px]`}
          >
            {tab.navLabel || tab.title}
          </div>
        ))}
      </nav>
      <div className='w-full'>{children}</div>
    </div>
  );
};

export default MomBasicInfoWrapper;
