'use client';

import React from 'react';

import MomBasicInfoWrapper from '../../../../_components/mom-basic-info-form-wrapper';
import MomPhotoUpload from '../../../../_components/mom-photo-upload';
import MomProfileWrapper from '../../../../_components/mom-profile-wrapper';
import WithMomProfile from '../../with-mom-profile';

const MomPhotoPage = ({ params }: { params: { id: string } }) => {
  const { id } = params;
  return (
    <WithMomProfile momId={id}>
      <MomProfileWrapper viewId='info' momId={id}>
        <MomBasicInfoWrapper momId={id} currentTab='mom-photo'>
          <MomPhotoUpload />
        </MomBasicInfoWrapper>
      </MomProfileWrapper>
    </WithMomProfile>
  );
};

export default MomPhotoPage;
