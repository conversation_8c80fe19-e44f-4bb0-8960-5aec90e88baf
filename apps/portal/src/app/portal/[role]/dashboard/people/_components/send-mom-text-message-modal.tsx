'use client';

import { getSendMomTextMessageFormFields } from '@/app/portal/lib/send-mom-text-message-form.config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  Dialog<PERSON>ontent,
  <PERSON><PERSON>Footer,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { sendTextMessageToContacts } from '@/lib/portal';
import type { FormValues } from '@/types/form-field';
import { type SendTextMessageSchema, sendTextMessageToOneContactUiFieldSchema } from '@/types/schemas/text-message';
import { Send } from 'lucide-react';
import { useState } from 'react';

interface SendMomTextMessageModalProps {
  momId: string;
  momFullName: string;
  momFirstName: string;
  momPhoneNumber: string;
  successCallback?: () => void;
  buttonComponent?: React.ReactNode;
  open?: boolean;
  onClose?: () => void;
}

const SendMomTextMessageModal: React.FC<SendMomTextMessageModalProps> = ({
  momId,
  momFullName,
  momFirstName,
  momPhoneNumber,
  successCallback,
  buttonComponent,
  open,
  onClose,
}) => {
  // open state managed by parent is needed to support opening modal from dropdown menu item
  // (normally/ideally, component can manage its own open state if open trigger is a static button on parent)
  const externallyControlledOpenState = typeof open === 'boolean' && typeof onClose === 'function';
  const externalOpenProps = externallyControlledOpenState ? { open } : {};

  const fieldsConfig = getSendMomTextMessageFormFields(momFirstName);

  const defaultValues = {
    contact_name: momFullName,
    contact_phone: momPhoneNumber,
  };

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const handleCloseAndClearForm = (): void => {
    if (externallyControlledOpenState) {
      onClose?.();
    }

    // prevent brief impression of form view when closing modal after success screen
    setTimeout(() => {
      setIsSuccess(false);
      setErrorMessage(null);
    }, 200);
  };

  const handleSubmit = async (data: FormValues): Promise<void> => {
    setIsSubmitting(true);
    setErrorMessage(null);
    setIsSuccess(false);

    const requestData: SendTextMessageSchema = {
      contact_ids: [momId],
      message_text: String(data.message_text),
    };

    try {
      await sendTextMessageToContacts(requestData);
      successCallback?.();
      setIsSuccess(true);
    } catch (error) {
      setErrorMessage('There was an error.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog onOpenChange={handleCloseAndClearForm} {...externalOpenProps}>
      <DialogTrigger asChild>
        {externallyControlledOpenState
          ? null
          : (buttonComponent ?? (
              <button
                type='button'
                className='my-auto flex flex-col items-center justify-center gap-2 self-stretch overflow-hidden rounded-lg border border-emaBgQuaternary bg-darkGreen px-3.5 py-2.5 text-white shadow-sm md:flex-row md:gap-1 md:border-solid md:border-slate-800'
              >
                <Send className='h-4 w-4 text-white' />
                <span className='my-auto self-stretch px-0.5'>Send Text Message</span>
              </button>
            ))}
      </DialogTrigger>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle>Send a Text Message</DialogTitle>
          {/* {isSuccess ? <DialogDescription>Text Message was sent successfully!</DialogDescription> : null} */}
        </DialogHeader>
        {!isSuccess && (
          <div className='space-y-4'>
            <FormFactory
              fields={fieldsConfig}
              schema={sendTextMessageToOneContactUiFieldSchema}
              defaultValues={defaultValues}
              onSubmit={handleSubmit}
              actionButtonsComponent={
                <DialogFooter className='mt-4 sm:justify-end'>
                  <DialogClose asChild>
                    <Button type='button' variant='secondary'>
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button type='submit' variant='default' disabled={isSubmitting}>
                    {isSubmitting ? 'Submitting...' : 'Submit'}
                  </Button>
                </DialogFooter>
              }
            />
            {errorMessage ? <div className='w-full text-center text-destructive'>{errorMessage}</div> : null}
          </div>
        )}
        {isSuccess ? (
          <>
            <div>Text Message was sent successfully!</div>
            <DialogFooter className='mt-4 sm:justify-start'>
              <DialogClose asChild>
                <Button type='button' variant='secondary'>
                  Close
                </Button>
              </DialogClose>
            </DialogFooter>
          </>
        ) : null}
      </DialogContent>
    </Dialog>
  );
};

export default SendMomTextMessageModal;
