import DetailsItem from '@/app/portal/[role]/dashboard/people/_components/details-item';
import MomFlaggedNeedsSection from '@/app/portal/[role]/dashboard/people/moms/[id]/_components/flagged-needs-section';
import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { useMomSessionNotes } from '@/app/portal/context/mom-session-notes-context';
import { type SessionNotesRecord } from '@/app/portal/types';
import { Button } from '@/components/ui/button';
import { SquareUserRound } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface SessionDetailsProps {
  sessionNote?: SessionNotesRecord;
}

const SessionNotesDetails: React.FC<SessionDetailsProps> = () => {
  const { momProfile, pairingInfo } = useMomProfileContext();
  const router = useRouter();
  const { profile } = useDashboardContext();
  const { sessionMeetingData } = useMomSessionNotes();

  return (
    <section className='flex min-w-[240px] max-w-[280px] flex-col'>
      {/* To be completed on: https://servant-io.atlassian.net/browse/EMA-546 */}

      {/* <DetailsItem label='Created' value={format(sessionNote.created_at, 'MM/dd/yyyy')} /> */}
      <DetailsItem
        label='Mom'
        value={`${momProfile?.first_name} ${momProfile?.last_name}`}
        icon={<SquareUserRound className='h-5 w-5' />}
      />
      {/* TODO: implement more pairing data from API response */}
      <DetailsItem
        label='Advocate'
        value={`${pairingInfo?.advocateUser?.firstName} ${pairingInfo?.advocateUser?.lastName}`}
        icon={<SquareUserRound className='h-5 w-5' />}
      />
      {/* To be completed on: https://servant-io.atlassian.net/browse/EMA-546 */}

      {/* <DetailsItem label='Track' value={sessionNote.learning_track ?? 'N/A'} />
      <DetailsItem label='Lesson' value={sessionNote.current_lesson.title} /> */}
      {(sessionMeetingData?.session_group_id as boolean) && (
        <Button
          variant='outline'
          className='mt-2 w-full text-sm'
          onClick={() =>
            router.push(
              `/portal/${profile?.portalRole}/dashboard/people/moms/${momProfile?.id}/session-notes/${sessionMeetingData?.id}/session-group/${sessionMeetingData?.session_group_id}`,
            )
          }
        >
          View Group Session
        </Button>
      )}
      <MomFlaggedNeedsSection />
    </section>
  );
};

export default SessionNotesDetails;
