import WellBeingAssessment from '@/app/portal/_components/assessments/well-being-assessment';
import { Mom } from '@/hooks/generated/__types';
import { cn } from '@/lib/utils';
import React, { Dispatch, SetStateAction } from 'react';

import { WellBeingAssessmentFormData } from '../../../../../types';
import ActionPlan from '../../moms/[id]/_components/action-plan';
import ApprovalForEmaStep from './approval-for-ema-step';
import ReferralBasicInfoClientInfoForm from './referral-intake-basic-info-client-info-form';
import { StepId } from './utils';

interface IntakeStepperProps {
  mom?: Mom;
  person: 'mom' | 'coordinator';
  steps: { id: StepId; name: string }[];
  currentStepId: StepId;
  setCurrentStepId: Dispatch<SetStateAction<StepId>>;
  onBackStep: () => void;
  onNextStep: () => void;
  wellBeingAssessmentStepLabel: string;
  isWellBeingAssessmentStepLoading?: boolean;
  formData: WellBeingAssessmentFormData;
  setFormData: Dispatch<SetStateAction<WellBeingAssessmentFormData>>;
}

const IntakeStepper = ({
  mom,
  person,
  steps,
  currentStepId,
  setCurrentStepId,
  onBackStep,
  onNextStep,
  wellBeingAssessmentStepLabel,
  isWellBeingAssessmentStepLoading,
  formData,
  setFormData,
}: IntakeStepperProps) => {
  return (
    <div className='flex'>
      {/* Sidebar Stepper */}
      <div className='hidden w-1/4 border-gray-200 p-4 md:block'>
        <ul>
          {steps.map((step) => (
            <li
              key={step.id}
              className={cn(
                'cursor-pointer py-1 pl-4',
                currentStepId === step.id ? 'border-l-2 border-black font-semibold' : 'text-gray-600',
              )}
              onClick={() => setCurrentStepId(step.id)}
            >
              {step.name}
            </li>
          ))}
        </ul>
      </div>
      {/* Step Content */}
      <div className='w-full p-4 md:w-3/4'>
        {currentStepId === 'basic-information' && (
          <ReferralBasicInfoClientInfoForm formData={formData} setFormData={setFormData} onNextStep={onNextStep} />
        )}
        {currentStepId === 'wellbeing-assessment' && (
          <WellBeingAssessment
            person={person}
            formData={formData}
            setFormData={setFormData}
            onBackStep={onBackStep}
            onNextStep={onNextStep}
            nextStepLabel={wellBeingAssessmentStepLabel}
            isNextStepLoading={isWellBeingAssessmentStepLoading}
          />
        )}
        {person === 'coordinator' ? (
          <>
            {currentStepId === 'approval-ema' && mom && (
              <ApprovalForEmaStep mom={mom} onBackStep={onBackStep} onNextStep={onNextStep} />
            )}
            {currentStepId === 'action-plan' && mom && <ActionPlan momId={mom.id} onBackStep={onBackStep} />}
          </>
        ) : null}
      </div>
    </div>
  );
};

export default IntakeStepper;
