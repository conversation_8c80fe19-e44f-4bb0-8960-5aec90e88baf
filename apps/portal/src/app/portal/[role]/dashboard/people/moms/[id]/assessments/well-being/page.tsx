'use client';

import WellBeingAssessment from '@/app/portal/_components/assessments/well-being-assessment';
import { useAssessmentsTabsContext } from '@/app/portal/context/assessments-tabs-context';
import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import { WellBeingAssessmentFormData } from '@/app/portal/types';
import { useFindFirstWellnessAssessment, useFindUniqueMom } from '@/hooks/generated';
import { sfetch } from '@/lib/sfetch';
import { useMutation } from '@tanstack/react-query';
import isEqual from 'lodash/isEqual';
import { useEffect, useMemo, useState } from 'react';

import { WellnessAssessment } from '../../../../../../../../../hooks/generated/__types';
import { MomWithChildren, getInitialWellBeingAssessmentFormData } from '../../../../referrals/_components/utils';

const WellBeingPage = ({
  mom,
  wellnessAssessment,
}: {
  mom: MomWithChildren;
  wellnessAssessment?: WellnessAssessment;
}) => {
  const { profile } = useDashboardContext();
  const { currentTab, setCurrentTab } = useAssessmentsTabsContext();

  useEffect(() => {
    setCurrentTab('well-being');
  }, []);

  const defaultFormData = useMemo(
    () => getInitialWellBeingAssessmentFormData({ person: 'coordinator', mom, wellnessAssessment }),
    [mom, wellnessAssessment],
  );
  const [formData, setFormData] = useState(defaultFormData);

  const createWellnessAssessment = useMutation<void, Error, { momId: string; formData: WellBeingAssessmentFormData }>({
    mutationFn: async ({ momId, formData }) => {
      const response = await sfetch(`/v1/wellness-assessment/mom/${momId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          basicForm: { values: formData.basicForm.values },
          clientForm: { values: formData.clientForm.values },
          childForms: formData.childForms.map((childForm) => ({ values: childForm.values })),
          childrenForm: { values: formData.childrenForm.values },
          wellBeingForm: { values: formData.wellBeingForm.values },
          updateMom: false,
          createWellnessAssessment: true,
        }),
      });
      if (!response.ok) {
        throw new Error('Failed to send the wellness assessment');
      }
    },
  });

  const handleCreateWellnessAssessment = async () => {
    const noChanges = isEqual(defaultFormData.wellBeingForm.values, formData.wellBeingForm.values);

    if (noChanges) {
      return;
    }

    if (!mom) {
      return;
    }

    await createWellnessAssessment.mutateAsync({
      momId: mom.id,
      formData,
    });
  };

  return (
    <div className='w-full'>
      {currentTab === 'well-being' && (
        <WellBeingAssessment
          person='coordinator'
          formData={formData}
          setFormData={setFormData}
          onNextStep={handleCreateWellnessAssessment}
          nextStepLabel='Save'
          isNextStepLoading={createWellnessAssessment.isPending}
          readOnly={profile.portalRole === 'advocate'}
        />
      )}
    </div>
  );
};

const WellBeingPageDataLoader = ({ params }: { params: { id: string } }) => {
  const { id } = params;
  const mom = useFindUniqueMom({
    include: {
      children: true,
    },
    where: { id },
  });
  const wellnessAssessment = useFindFirstWellnessAssessment({
    where: { mom_id: id },
    orderBy: {
      created_at: 'desc',
    },
  });

  return (
    ((mom.isLoading || wellnessAssessment.isLoading) && <div>Loading...</div>) ||
    (mom.data && <WellBeingPage mom={mom.data} wellnessAssessment={wellnessAssessment.data} />)
  );
};

export default WellBeingPageDataLoader;
