import { useMomFlaggedNeedsContext } from '@/app/portal/context/mom-flagged-needs-context';
import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { Button } from '@/components/ui/button';
import { YupSchemas } from '@suiteapi/models';
import { ChevronRight, CircleAlert } from 'lucide-react';
import { useEffect, useState } from 'react';

import BenevolenceNeedDrawer from '../../../_components/benevolence-need-drawer';
import CreateFlaggedNeedModal from './flag-a-need-modal';

interface FlaggedNeedProps {
  description: string;
  handleClick: (entry: YupSchemas.BenevolenceSchema) => void;
  entry: YupSchemas.BenevolenceSchema;
}

const FlaggedNeed: React.FC<FlaggedNeedProps> = ({ description, handleClick, entry }) => {
  return (
    <Button
      variant='ghost'
      className='mt-6 flex h-auto w-full flex-col justify-start rounded-xl border border-solid border-red-300 bg-white'
      onClick={() => handleClick(entry)}
    >
      <div className='flex w-full flex-col'>
        <div className='flex w-full items-start gap-2'>
          <CircleAlert className='h-5 w-5 text-destructive' />
          <h4 className='flex-1 shrink basis-0 text-sm font-medium leading-5 text-slate-600'>Flagged Need</h4>
          <div className='flex items-center justify-center gap-1.5 overflow-hidden whitespace-nowrap text-sm font-semibold leading-none text-slate-600'>
            <span className='my-auto self-stretch'>Manage</span>
            <ChevronRight className='h-5 w-5' />
          </div>
        </div>
        <p className='mt-2 text-wrap text-left text-base font-medium leading-6 text-slate-800'>
          {description || '(No description)'}
        </p>
      </div>
    </Button>
  );
};

const MomFlaggedNeedsSection: React.FC = () => {
  const { momProfile } = useMomProfileContext();
  const { momFlaggedNeeds, fetchMomFlaggedNeeds } = useMomFlaggedNeedsContext();
  const [selectedEntry, setSelectedEntry] = useState<YupSchemas.BenevolenceSchema | null>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const onCreateFlaggedNeedSuccess = async () => {
    await fetchMomFlaggedNeeds(String(momProfile?.id));
  };

  useEffect(() => {
    fetchMomFlaggedNeeds(String(momProfile?.id));
  }, [fetchMomFlaggedNeeds, momProfile]);

  const handleManage = (entry: YupSchemas.BenevolenceSchema) => {
    setSelectedEntry(entry);
    setIsDrawerOpen(true);
  };

  const handleCloseDrawer = async (): Promise<void> => {
    setIsDrawerOpen(false);
    await fetchMomFlaggedNeeds(String(momProfile?.id));
  };

  const filteredMomFlaggedNeeds = momFlaggedNeeds?.filter((need) => !need.resolved_date_c);

  return (
    <section className='flex w-full flex-col'>
      {filteredMomFlaggedNeeds?.map((need) => (
        <FlaggedNeed handleClick={handleManage} key={need.id} entry={need} description={String(need.description)} />
      ))}
      <div>
        <CreateFlaggedNeedModal momId={String(momProfile?.id)} successCallback={onCreateFlaggedNeedSuccess} />
      </div>
      <BenevolenceNeedDrawer isOpen={isDrawerOpen} onClose={handleCloseDrawer} entry={selectedEntry} />
    </section>
  );
};

export default MomFlaggedNeedsSection;
