import { Checkbox } from '@/components/ui/checkbox';
import { useUpdateActionItem } from '@/hooks/generated';
import { ActionItem } from '@/hooks/generated/__types';
import { cn, decodeHtmlEntities, formatRelativeDate } from '@/lib/utils';
import { isPast } from 'date-fns';
import React from 'react';

interface ActionPlanTaskListProps {
  actionItems: ActionItem[];
}

const ActionPlanTaskList: React.FC<ActionPlanTaskListProps> = ({ actionItems }) => {
  const { mutateAsync: updateActionItem } = useUpdateActionItem();

  const handleTaskChange = (index: number): void => {
    updateActionItem({
      where: {
        id: actionItems[index]?.id,
      },
      data: {
        doneDate: actionItems[index]?.doneDate ? null : new Date().toISOString(),
      },
    });
  };

  return (
    <div className='mt-6 flex w-full flex-col'>
      <h4 className='w-full gap-3 self-stretch whitespace-nowrap text-sm font-medium leading-5 text-slate-700'>
        Tasks
      </h4>
      <ul className='mt-2 flex w-full flex-col'>
        {actionItems.map((item, index) => {
          // Mock item ID
          const itemId = `item-${index.toString()}`;
          return (
            <li
              key={itemId}
              className={cn(
                'mt-2 flex w-full items-start gap-4 pb-2',
                index < actionItems.length - 1 && 'border-b border-gray-200',
              )}
            >
              <div className='flex min-w-[240px] flex-1 shrink basis-0 items-start gap-2'>
                <Checkbox
                  id={itemId}
                  className='mt-0.5'
                  checked={item.doneDate ? true : false}
                  onCheckedChange={() => {
                    handleTaskChange(index);
                  }}
                />
                <label htmlFor={itemId} className='flex-1 shrink text-sm font-medium leading-5 text-slate-700'>
                  {decodeHtmlEntities(item.name)}
                </label>
              </div>
              {item.dueDate ? (
                <div
                  className={cn(
                    'gap-2 whitespace-nowrap text-base leading-6',
                    isPast(item.dueDate) && !item.doneDate ? 'text-destructive' : 'text-slate-600',
                  )}
                >
                  {formatRelativeDate(item.dueDate.toISOString(), 'Due')}
                </div>
              ) : null}
            </li>
          );
        })}
      </ul>
    </div>
  );
};

export default ActionPlanTaskList;
