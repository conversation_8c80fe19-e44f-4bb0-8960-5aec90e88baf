import { groupSessionNoteSchema } from '@/app/portal/lib/group-session/group-session-note.config';
import { groupSessionNoteConfig } from '@/app/portal/lib/group-session/group-session-note.config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import React from 'react';

const GroupSessionNote = () => {
  return (
    <FormFactory
      formWrapperClassName='flex-col w-full'
      fields={groupSessionNoteConfig}
      schema={groupSessionNoteSchema}
      renderFormFieldItemClassName='w-full min-h-[128px]'
      formFieldElClass='w-full min-h-[128px]'
      onSubmit={() => {}}
      actionButtonsComponent={
        <div className='flex justify-end'>
          <Button type='submit'>Save Changes</Button>
        </div>
      }
    />
  );
};

export default GroupSessionNote;
