'use client';

import { getMoms } from '@/lib/portal';
import { YupSchemas } from '@suiteapi/models';
import Link from 'next/link';
import { useEffect, useState } from 'react';

interface MomsListProps {
  baseHref: string;
}

const MomsList = ({ baseHref }: MomsListProps): React.ReactNode => {
  const [moms, setMoms] = useState<YupSchemas.MomType[] | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  const fetchAndSetMoms = async (): Promise<void> => {
    try {
      const momsList = await getMoms();
      setMoms(momsList);
    } catch (error) {
      console.error(error);
    }

    setLoading(false);
  };

  useEffect(() => void fetchAndSetMoms(), []);

  // TODO: implement styled moms list
  return (
    <div>
      <h2 className='mb-4 font-bold'>Moms List</h2>

      {loading ? (
        <span>Loading...</span>
      ) : (
        <ul>
          {moms?.map((mom) => (
            <li key={mom.id as string}>
              <Link href={`${baseHref}/${mom.id as string}`}>
                <span className='underline'>{mom.name as string}</span>
              </Link>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default MomsList;
