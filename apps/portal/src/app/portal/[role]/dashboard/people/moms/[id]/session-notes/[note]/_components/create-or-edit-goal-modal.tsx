import { createAGoalFormConfig } from '@/app/portal/lib/create-a-goal-form.config';
import CircularProgress from '@/components/custom/circular-progress';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogClose,
  <PERSON>alogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useDeleteGoal } from '@/hooks/generated';
import { ActionItem, Goal } from '@/hooks/generated/__types';
import { generateFieldsSchema } from '@/types/form-field';
import { Pencil, Trash } from 'lucide-react';
import { useMemo, useState } from 'react';
import { FieldValues } from 'react-hook-form';

interface CreateOrEditGoalModalProps {
  goal?: Goal & { actionItems: ActionItem[] };
  onSubmit?: (data: FieldValues) => Promise<void>;
}

const CreateOrEditAGoalModal = ({ goal, onSubmit }: CreateOrEditGoalModalProps) => {
  const initialData = useMemo(
    () =>
      goal && {
        id: goal.id,
        name: goal.name,
        description: goal.description,
        dueDate: goal.dueDate,
        actionItems: goal.actionItems.map((item) => ({
          id: item.id,
          name: item.name,
          dueDate: item.dueDate,
        })),
      },
    [goal],
  );

  const [open, setOpen] = useState(false);
  const isEditing = !!goal;
  const deleteGoal = useDeleteGoal();

  const goalPercentComplete = goal?.actionItems.length
    ? Math.round((goal.actionItems.filter((actionItem) => actionItem.doneDate).length / goal.actionItems.length) * 100)
    : 0;

  const handleDeleteGoal = async () => {
    if (goal && confirm('Are you sure you want to delete this goal?')) {
      await deleteGoal.mutateAsync({ where: { id: goal.id } });
      setOpen(false);
    }
  };

  const handleSubmit = async (data: FieldValues) => {
    onSubmit?.(data);
    setOpen(false);
  };

  const createAGoalFormSchema = useMemo(() => generateFieldsSchema(createAGoalFormConfig, undefined), []);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {isEditing ? (
          <Button type='button' variant='outline'>
            <Pencil className='h-5 w-5' />
          </Button>
        ) : (
          <Button type='button'>Add goal</Button>
        )}
      </DialogTrigger>
      <DialogContent>
        <DialogHeader className='mb-2'>
          <DialogTitle>{isEditing ? 'Edit goal' : 'Create a goal'}</DialogTitle>
        </DialogHeader>
        <CircularProgress progress={goalPercentComplete} />
        <FormFactory
          fields={createAGoalFormConfig}
          schema={createAGoalFormSchema}
          defaultValues={initialData}
          onSubmit={handleSubmit}
          actionButtonsComponent={
            <DialogFooter className='mt-4 sm:justify-between'>
              <DialogClose asChild>
                <Button type='button' variant='destructive' onClick={handleDeleteGoal} disabled={deleteGoal.isPending}>
                  <Trash className='mr-2 h-5 w-5' />
                  Delete Goal
                </Button>
              </DialogClose>
              <Button type='submit' variant='default'>
                {goal ? 'Save' : 'Create'} Goal
              </Button>
            </DialogFooter>
          }
        />
      </DialogContent>
    </Dialog>
  );
};

export default CreateOrEditAGoalModal;
