import ImageSkeleton from '@/app/portal/_components/ui/image-skeleton';
import { useFindUniqueMom } from '@/hooks/generated/mom';
import { sfetch } from '@/lib/sfetch';
import { useQueryClient } from '@tanstack/react-query';
import Image from 'next/image';
// Import default avatar statically for preloading
// This is the most effective way to preload in Next.js
import DefaultAvatarImage from 'public/assets/mom_generic_avatar.png';
import { useEffect, useRef, useState } from 'react';

// Default avatar path
const DEFAULT_AVATAR = '/assets/mom_generic_avatar.png';

// Preload the default avatar image on client side
if (typeof window !== 'undefined') {
  const preloadImg = new window.Image();
  preloadImg.src = DEFAULT_AVATAR;
}

interface MomNameImageHeaderProps {
  momName: string | undefined;
  momId: string;
  referral?: boolean;
}

/**
 * Displays a mom's name and profile image in the header.
 * Uses direct API calls to fetch the mom's data and photo.
 */
const MomNameImageHeader = ({ momName, momId, referral = false }: MomNameImageHeaderProps) => {
  // We'll only have two states for display: LOADING and READY
  const [displayState, setDisplayState] = useState<'LOADING' | 'READY'>('LOADING');
  const [imageToShow, setImageToShow] = useState<string | null>(null);
  const isFetchingRef = useRef(false);
  const mountedRef = useRef(true);
  // Get the React Query client for cache invalidation
  const queryClient = useQueryClient();

  // Fetch mom data
  const { data: mom, refetch } = useFindUniqueMom(
    {
      where: { id: momId },
    },
    {
      enabled: !!momId,
      refetchOnWindowFocus: false,
      staleTime: 10000,
    },
  );

  // Effect for image loading - runs once when component mounts or mom changes
  useEffect(() => {
    // Set mounted ref for cleanup
    mountedRef.current = true;

    // Start in loading state
    setDisplayState('LOADING');

    // Function to load the image
    const loadImage = async () => {
      // Only proceed if we have mom data
      if (!mom) return;

      // Check if mom has photo URL
      if (!mom.thumbnailUrl) {
        // No photo, show default image
        if (mountedRef.current) {
          setImageToShow(null);
          setDisplayState('READY');
        }
        return;
      }

      // Mom has photo URL, try to load it
      try {
        if (isFetchingRef.current) return;
        isFetchingRef.current = true;

        // Extract filename - prefer thumbnailUrl for header displays
        const photoUrl = mom.thumbnailUrl as string;
        const filename = photoUrl.split('/').pop();

        if (!filename) {
          throw new Error('Invalid filename');
        }

        // Add timestamp to force cache bust
        const timestamp = Date.now();
        const imageUrl = `/v1/s3/proxy/${filename}?t=${timestamp}`;

        // Load image with cache-busting
        const response = await sfetch(imageUrl);
        if (!response.ok) throw new Error('Failed to load image');

        // Create blob URL
        const blob = await response.blob();
        const blobUrl = URL.createObjectURL(blob);

        // Set the image URL to display
        if (mountedRef.current) {
          setImageToShow(blobUrl);
          setDisplayState('READY');
        }
      } catch (error) {
        console.error('Error loading image:', error);
        // On error, show default image
        if (mountedRef.current) {
          setImageToShow(null);
          setDisplayState('READY');
        }
      } finally {
        isFetchingRef.current = false;
      }
    };

    // Load the image
    loadImage();

    // Cleanup function
    return () => {
      mountedRef.current = false;
      if (imageToShow) {
        URL.revokeObjectURL(imageToShow);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mom]);

  // Listen for photo upload events to refresh the image
  useEffect(() => {
    const handlePhotoEvent = async (event: Event) => {
      const customEvent = event as CustomEvent;
      const { momId: eventMomId } = customEvent.detail || {};

      // Only react to events for this mom
      if (eventMomId === momId) {
        // Clear current image and move to loading state
        if (imageToShow) {
          URL.revokeObjectURL(imageToShow);
          setImageToShow(null);
        }

        setDisplayState('LOADING');

        // Invalidate the mom query to fetch fresh data
        queryClient.invalidateQueries({
          queryKey: ['FindUniqueMom', { where: { id: momId } }],
        });

        // Force an immediate refetch to get the new data
        try {
          await refetch();
        } catch {
          // Error handling is implicit - will show default image
        }
      }
    };

    // Add event listeners for both upload and delete events
    window.addEventListener('mom-photo-uploaded', handlePhotoEvent);
    window.addEventListener('mom-photo-deleted', handlePhotoEvent);

    // Cleanup
    return () => {
      window.removeEventListener('mom-photo-uploaded', handlePhotoEvent);
      window.removeEventListener('mom-photo-deleted', handlePhotoEvent);
    };
  }, [momId, imageToShow, queryClient, refetch]);

  // Handle image loading error
  const handleImageError = () => {
    setImageToShow(null);
    setDisplayState('READY');
  };

  // Render component
  return (
    <div className='flex min-w-[320px] flex-1 shrink basis-0 flex-wrap items-center gap-5 max-md:max-w-full'>
      <div className='flex h-24 w-24 items-center justify-center'>
        {displayState === 'LOADING' ? (
          <ImageSkeleton size={96} />
        ) : (
          // Ready state - show either loaded image or default
          <Image
            src={imageToShow || DefaultAvatarImage}
            alt={momName || "Mom's profile"}
            width={96}
            height={96}
            className='h-full w-full rounded-full object-cover'
            onError={handleImageError}
            priority
            unoptimized={!!imageToShow}
          />
        )}
      </div>
      <div className='my-auto flex min-w-[240px] flex-1 shrink basis-0 flex-col items-start self-stretch font-medium text-slate-700'>
        {displayState === 'LOADING' && !momName ? (
          // Text skeleton loaders
          <>
            <div className='mb-2 h-8 w-48 animate-pulse rounded-md bg-gray-200' />
            <div className='h-5 w-24 animate-pulse rounded-md bg-gray-200' />
          </>
        ) : (
          <>
            <h1 className='mt-1 self-stretch text-3xl font-semibold leading-10 text-gray-900'>{momName}</h1>
            <span className='block font-semibold text-black'>{referral ? 'Mom Referral' : 'Mom'}</span>
          </>
        )}
      </div>
    </div>
  );
};

export default MomNameImageHeader;
