'use client';

import { useAssessmentsTabsContext } from '@/app/portal/context/assessments-tabs-context';
import { useEffect } from 'react';

const AssessmentResultsLayout = ({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { assessmentId: string };
}): React.ReactNode => {
  const { assessmentId } = params;
  const { setCurrentTab } = useAssessmentsTabsContext();

  useEffect(() => {
    setCurrentTab(assessmentId);
  }, []);

  return <>{children}</>;
};

export default AssessmentResultsLayout;
