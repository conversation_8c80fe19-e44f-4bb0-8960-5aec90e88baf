'use client';

import GenericTable from '@/components/custom/generic-table';
import { Badge } from '@/components/ui/badge';
import { TableCell, TableRow } from '@/components/ui/table';
import { useMyAdvocates } from '@/hooks/useMyAdvocates';
import { convertUnderscoreCase } from '@/lib/utils';
import type { AdvocateStatus, User } from '@/types/schemas/user';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { mapAdvocateStatusToBadgeVariant } from '../_utils/people-utils';

interface AdvocatesTableProps {
  isLoading?: boolean;
}

const AdvocatesTable = ({ isLoading: initialLoading = false }: AdvocatesTableProps) => {
  const [data, setData] = useState<User[]>([]);
  const { advocates, isLoading } = useMyAdvocates();
  const params = useParams();
  const router = useRouter();
  const baseHref = `/portal/${params.role}/dashboard/people/advocates`;

  useEffect(() => {
    if (advocates) {
      setData(
        advocates.map((advocate) => ({
          id: advocate.id,
          firstName: advocate.firstName,
          lastName: advocate.lastName,
          advocate_status: advocate.advocate_status,
          userRoles: advocate.userRoles?.map((role) => ({
            label: role.role.name,
            value: role.role.id,
          })),
        })),
      );
    }
  }, [advocates]);

  const headers = ['First Name', 'Last Name', 'User Type', 'Status'];

  const renderRow = (advocate: User) => (
    <TableRow
      className='cursor-pointer'
      onClick={() => {
        router.push(`${baseHref}/${advocate.id}`);
      }}
    >
      <TableCell>{advocate.firstName}</TableCell>
      <TableCell>{advocate.lastName}</TableCell>
      <TableCell>Advocate</TableCell>
      <TableCell>
        <AdvocateStatusBadge status={advocate.advocate_status as AdvocateStatus} />
      </TableCell>
    </TableRow>
  );

  const renderMobileItem = (advocate: User) => (
    <Link href={'#'}>
      <div className='space-y-2'>
        <div className='flex justify-between'>
          <span className='font-medium'>
            {advocate.firstName} {advocate.lastName}
          </span>
          <AdvocateStatusBadge status={advocate.advocate_status as AdvocateStatus} />
        </div>
        <div className='flex justify-between text-sm text-gray-500'>
          <span>Advocate</span>
        </div>
      </div>
    </Link>
  );

  return (
    <GenericTable
      data={data}
      isLoading={isLoading || initialLoading}
      headers={headers}
      rowRenderer={renderRow}
      mobileRenderer={renderMobileItem}
      emptyMessage='No advocates found.'
      shouldUseCustomRowComponent={true}
    />
  );
};

export default AdvocatesTable;

export const AdvocateStatusBadge: React.FC<{ status: AdvocateStatus | 'Unknown' }> = ({ status }) => (
  <Badge variant={mapAdvocateStatusToBadgeVariant(status)}>{status ? convertUnderscoreCase(status) : 'Unknown'}</Badge>
);
