import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { useMomSessionNotes } from '@/app/portal/context/mom-session-notes-context';
import TablePaginationFooter from '@/components/custom/table-pagination-footer';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { fetchSessionReportData } from '@/lib/portal';
import { decodeHtmlEntities } from '@/lib/utils';
import { SessionType } from '@/types/schemas/session';
import { format } from 'date-fns';
import { ChevronRight, CloudDownload, FileCheck2, FileHeart, FilePlus2, Pen } from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { openExportPage } from '../_utils/session-utils';
import MomCreateSessionNotesModal from './mom-create-session-notes-modal';

const getSessionTypeConfig = (
  sessionType: SessionType,
): {
  component: JSX.Element | null;
  label: string;
} => {
  switch (sessionType) {
    case SessionType.Support_Session:
      return {
        component: <FileHeart className='h-6 w-6' />,
        label: 'Support Session',
      };
    case SessionType.Track_Session:
      return {
        component: <FileCheck2 className='h-6 w-6' />,
        label: 'Track Session',
      };
    default:
      return {
        component: null,
        label: '',
      };
  }
};

const SessionNotesTable: React.FC = () => {
  const { momProfile } = useMomProfileContext();
  const momId = momProfile?.id || '';
  const { fetchSessionsList, sessionsList, sessionsListLoading: loading } = useMomSessionNotes();

  const router = useRouter();
  const pathname = usePathname();
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 8;
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = sessionsList ? sessionsList.slice(indexOfFirstItem, indexOfLastItem) : [];
  const totalPages = sessionsList ? Math.ceil(sessionsList.length / itemsPerPage) : 0;
  const [sessionReportDescriptions, setSessionReportDescriptions] = useState<{ [sessionId: string]: string | null }>(
    {},
  );

  const fetchSessionReport = async (sessionId: string) => {
    try {
      const data = await fetchSessionReportData(sessionId);

      // Update the sessionReports map to store the note for each session
      setSessionReportDescriptions((prevReports) => ({
        ...prevReports,
        [sessionId]: data.report.note ? String(data.report.note) : '', // Ensure we return a string
      }));
    } catch (error) {
      console.error('Error fetching session notes:', error);
      setSessionReportDescriptions((prevReports) => ({
        ...prevReports,
        [sessionId]: null,
      }));
    }
  };

  // Fetch full session report list
  useEffect(() => {
    fetchSessionsList(momId);
  }, [momId, fetchSessionsList]);

  // Fetch session report for each session
  useEffect(() => {
    setSessionReportDescriptions(
      (sessionsList || []).reduce((acc, session) => ({ ...acc, [session.id as string]: 'Loading...' }), {}),
    );
    sessionsList?.forEach((session) => {
      if (!session.id) return;
      fetchSessionReport(session.id);
    });
  }, [sessionsList]);

  const handleEditButtonClick = (id: string | undefined) => {
    router.push(`${pathname}/${id}`);
  };

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  return (
    <>
      {/* Mobile View: List of Session Cards */}
      <div className='mb-4 flex flex-col gap-3 md:hidden'>
        <div className='mt-4'>
          <SessionTableButtons momId={momId} />
        </div>

        {currentItems.map((session) => (
          <SessionCard
            key={session.id}
            type={session.session_type}
            description={session.description}
            date={session.date_start ? format(new Date(session.date_start), 'MM/dd/yyyy') : 'No start date'}
            handleEditButtonClick={() => handleEditButtonClick(session.id)}
          />
        ))}
      </div>

      {/* Desktop View: Full Table */}
      <section className='mt-6 w-full overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm'>
        <div className='mx-auto max-md:hidden'>
          <header className='flex w-full flex-row px-6 pb-5 pt-6 font-semibold max-md:px-5'>
            <div className='flex w-full flex-wrap gap-4'>
              <h2 className='flex-1 text-lg leading-7 text-gray-900'>Session Notes</h2>
            </div>
            <SessionTableButtons momId={momId} />
          </header>

          <div className='overflow-x-auto'>
            <Table className='w-full'>
              <TableHeader>
                <TableRow>
                  <TableHead className='md:w-[111px]'>Date</TableHead>
                  <TableHead className='md:w-[191px]'>Type</TableHead>
                  <TableHead className='md:w-[111px]'>Status</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead className='md:w-[108px]'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentItems.map((session) => {
                  const sessionTypeConfig = getSessionTypeConfig(session.session_type as SessionType);
                  const startDate = session.date_start
                    ? format(new Date(session.date_start), 'MM/dd/yyyy')
                    : 'No start date';
                  const descriptionValue = sessionReportDescriptions[session.id as string];
                  const descriptionText =
                    descriptionValue === null ? ( // null is only set if there was an API error fetching the session details
                      <span className='text-destructive'>Error fetching session details</span>
                    ) : (
                      decodeHtmlEntities(descriptionValue)
                    );

                  return (
                    <TableRow key={session.id}>
                      <TableCell className='text-emaTextTertiary'>{startDate}</TableCell>
                      <TableCell className='font-medium'>
                        <div className='flex items-center gap-2'>
                          {sessionTypeConfig.component}
                          <span>{sessionTypeConfig.label}</span>
                        </div>
                      </TableCell>
                      <TableCell className='max-w-[210px] truncate'>{session.status}</TableCell>
                      <TableCell className='max-w-[210px] truncate'>{descriptionText}</TableCell>

                      <TableCell>
                        <div className='flex gap-1'>
                          <Button variant='ghost' size='icon' onClick={() => openExportPage(momId, session.id)}>
                            <CloudDownload className='h-5 w-5' />
                          </Button>
                          <Button
                            variant='ghost'
                            size='icon'
                            onClick={() => handleEditButtonClick(session.id)}
                            disabled={descriptionValue === null}
                          >
                            <Pen className='h-5 w-5' />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </div>
        <div className='rounded bg-background md:p-4'>
          {!loading && currentItems.length > 0 ? (
            <TablePaginationFooter currentPage={currentPage} totalPages={totalPages} onPageChange={handlePageChange} />
          ) : (
            <p className='py-4 text-center'>{loading ? 'Loading...' : 'There are currently no sessions to display.'}</p>
          )}
        </div>
      </section>
    </>
  );
};

// SessionCard component for mobile display
interface SessionCardProps {
  handleEditButtonClick: () => void;
  type: string;
  date: string;
  description: string | undefined | null;
}

const SessionCard: React.FC<SessionCardProps> = ({ handleEditButtonClick, type, date, description }) => {
  const sessionTypeConfig = getSessionTypeConfig(type as SessionType);

  return (
    <div onClick={handleEditButtonClick} className='cursor-pointer rounded-2xl bg-emaBgQuaternary p-4'>
      <div className='mb-2 flex justify-between'>
        <div className='flex items-center gap-2'>
          {sessionTypeConfig.component}
          <span>{sessionTypeConfig.label}</span>
        </div>
        <div className='flex items-center'>
          <p>{date}</p>
          <ChevronRight className='text-emaTextQuaternary' />
        </div>
      </div>
      <p>{description}</p>
    </div>
  );
};

interface SessionTableButtonsProps {
  momId: string;
}

const SessionTableButtons: React.FC<SessionTableButtonsProps> = ({ momId }) => {
  return (
    <div className='flex items-center gap-3 text-sm leading-5'>
      <Button variant='outline' className='flex gap-1' onClick={() => openExportPage(momId)}>
        <CloudDownload className='h-5 w-5' />
        Export All
      </Button>
      <MomCreateSessionNotesModal
        buttonComponent={
          <Button variant='default' className='flex gap-1 bg-darkGreen'>
            <FilePlus2 className='h-5 w-5' />
            New Session Note
          </Button>
        }
      />
    </div>
  );
};

export default SessionNotesTable;
