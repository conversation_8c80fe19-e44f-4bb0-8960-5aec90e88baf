'use client';

import GenericTable from '@/components/custom/generic-table';
import { Badge } from '@/components/ui/badge';
import { TableCell, TableRow } from '@/components/ui/table';
import { useUserInfo } from '@/context/user-info';
import { useFindManyMom } from '@/hooks/generated/mom';
import { YupSchemas } from '@suiteapi/models';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';

import { getMomStatusLabel, isUserCoordinatorOrAdvocate, mapMomStatusToBadgeVariant } from '../_utils/people-utils';

const MomsTable = () => {
  const params = useParams();
  const baseHref = `/portal/${params.role}/dashboard/people/moms`;
  const router = useRouter();
  const { roles } = useUserInfo();
  const { data, isLoading, isError } = useFindManyMom({
    where: {
      // TODO: https://servant-io.atlassian.net/browse/EMA-1339
      // since admins are not assigned to a mom, they don't have access to the moms statuses.... so we are not filtering out any moms so they can see them all
      prospect_status: isUserCoordinatorOrAdvocate(roles)
        ? {
            in: [YupSchemas.ProspectStatus.ENGAGED_IN_PROGRAM],
          }
        : undefined, // removes the filter entirely for other roles, showing all moms
    },
  });

  if (isError) {
    return <div>Error: There was an error loading the moms.</div>;
  }

  const headers = ['First Name', 'Last Name', 'User Type', 'Status'];

  const renderRow = (mom: YupSchemas.MomType) => (
    <TableRow className='cursor-pointer' onClick={() => router.push(`${baseHref}/${mom.id}`)}>
      <TableCell>{mom.first_name}</TableCell>
      <TableCell>{mom.last_name}</TableCell>
      <TableCell>Mom</TableCell>
      <TableCell>{mom.status != undefined ? <MomStatusBadge status={mom.status} /> : ''}</TableCell>
    </TableRow>
  );

  const renderMobileItem = (mom: YupSchemas.MomType) => (
    <Link href={`${baseHref}/${mom.id}`}>
      <div className='space-y-2'>
        <div className='flex justify-between'>
          <span className='font-medium'>{`${mom.first_name || ''} ${mom.last_name || ''}`}</span>
          {/* the status is going to show up as null for admins who do not have the mom assigned to them */}
          {mom.status ? <MomStatusBadge status={mom.status} /> : ''}
        </div>
        <div className='flex justify-between text-sm text-gray-500'>
          <span>Mom</span>
        </div>
      </div>
    </Link>
  );

  return (
    <GenericTable
      data={
        data
          ? data.map((mom) => ({
              ...mom,
              photoUrl: mom.iconUrl || undefined,
              photoS3FileName: mom.iconS3FileName || undefined,
            }))
          : []
      }
      isLoading={isLoading}
      headers={headers}
      rowRenderer={renderRow}
      mobileRenderer={renderMobileItem}
      emptyMessage='No moms found.'
      shouldUseCustomRowComponent={true}
    />
  );
};

export default MomsTable;

const MomStatusBadge: React.FC<{ status: YupSchemas.MomStatusType | undefined }> = ({ status }) => (
  <Badge variant={mapMomStatusToBadgeVariant(status)}>{getMomStatusLabel(status)}</Badge>
);
