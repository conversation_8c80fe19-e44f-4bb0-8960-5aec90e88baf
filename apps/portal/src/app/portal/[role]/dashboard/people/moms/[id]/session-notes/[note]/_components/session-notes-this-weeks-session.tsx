import { CurrentSessionProvider } from '@/app/portal/context/current-session-context';
import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { useMomSessionNotes } from '@/app/portal/context/mom-session-notes-context';
import { getThisWeeksSessionFields, thisWeeksSessionNotesSchema } from '@/app/portal/lib/mom-session-notes-form.config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { LessonStatusType } from '@/hooks/generated/__types';
import { useUpdateLesson } from '@/hooks/generated/lesson';
import { useUpdateSession } from '@/hooks/generated/session';
import { useUpdateSessionNote } from '@/hooks/generated/session-note';
import { useToast } from '@/hooks/use-toast';
import { decodeHtmlEntities } from '@/lib/utils';
import { type FormValues } from '@/types/form-field';
import { LessonSchema } from '@/types/schemas/lesson';
import { SessionType } from '@/types/schemas/session';
import { updateSessionReportSchema } from '@/types/schemas/session-report';
import { Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';

interface SessionNotesThisWeekProps {
  sessionId: string;
}

const SessionNotesThisWeek: React.FC<SessionNotesThisWeekProps> = ({ sessionId }) => {
  const { fetchSessionReport, sessionReport, sessionMeetingData } = useMomSessionNotes();

  const [publishedCurrentLesson, setPublishedCurrentLesson] = useState<LessonSchema | undefined>();
  const { momProfile, fetchMomProfile } = useMomProfileContext();
  const { toast } = useToast();
  const { mutateAsync: updateSessionNote } = useUpdateSessionNote();
  const { mutateAsync: updateSession } = useUpdateSession();
  const { mutateAsync: updateLesson } = useUpdateLesson();

  useEffect(() => {
    fetchSessionReport(sessionId);
  }, [fetchSessionReport, sessionId]);

  const handleSubmit = async (data: FormValues): Promise<void> => {
    const validSessionFields = Object.keys(updateSessionReportSchema.fields);

    const filteredSessionReportData = Object.keys(data)
      .filter((key) => validSessionFields.includes(key))
      .reduce((obj, key) => {
        // @ts-expect-error
        obj[key] = data[key];
        return obj;
      }, {});

    try {
      if (publishedCurrentLesson?.id && data.lesson_status) {
        await updateLesson({
          where: {
            id: publishedCurrentLesson.id,
          },
          data: {
            status: data.lesson_status as LessonStatusType,
          },
        });
      }

      await updateSession({
        where: {
          id: sessionId,
        },
        data: {
          session_type: data.session_type as SessionType,
        },
      });
      await updateSessionNote({
        where: {
          id: sessionReport?.id ? String(sessionReport.id) : undefined,
        },
        data: {
          ...filteredSessionReportData,
          covered_lesson_id: publishedCurrentLesson?.id,
        },
      });

      toast({
        title: 'Success',
        description: 'The session notes have been successfully saved',
        variant: 'success',
      });

      // Refreshing mom's profile data here (in order to update lessons, specifically lesson statuses)
      // Otherwise you get a weird bug when setting a lesson's status, then changing the lesson and changing the lesson again (back to the original lesson)
      // The changed lesson status doesn't stick unless you refresh the lessons through the mom profile context.
      if (momProfile?.id) {
        fetchMomProfile(momProfile.id);
      }
    } catch (err) {
      toast({
        title: 'Error',
        description: 'There was an error saving the session notes. Please try again.',
        variant: 'destructive',
      });

      console.error('Failed to submit session report:', err);
    } finally {
      // Refetch session report and session data after submission
      await fetchSessionReport(sessionId);
    }
  };

  // Default values must be selected from sessionMeetingData andSessionReport
  const defaultValues: FormValues = {
    session_type: sessionMeetingData?.session_type ? String(sessionMeetingData.session_type) : undefined,
    attendance_and_promptness: sessionReport?.attendance_and_promptness
      ? String(sessionReport.attendance_and_promptness)
      : undefined,
    moms_engagement_c: sessionReport?.moms_engagement_c ? String(sessionReport.moms_engagement_c) : undefined,
    new_attempt: sessionReport?.new_attempt ? String(sessionReport.new_attempt) : undefined,
    new_attempt_example: sessionReport?.new_attempt_example
      ? decodeHtmlEntities(String(sessionReport.new_attempt_example))
      : undefined,
    note: sessionReport?.note ? decodeHtmlEntities(String(sessionReport.note)) : undefined,
  };

  // Add a key prop to FormFactory that changes when defaultValues change
  const formKey = JSON.stringify(defaultValues);

  return (
    <CurrentSessionProvider currentSessionId={sessionId}>
      <section className='mt-3 flex w-full flex-col rounded-xl border border-solid border-gray-100 bg-white shadow-sm'>
        <div className='flex w-full flex-col px-6 pb-4'>
          {sessionMeetingData || sessionReport ? (
            <FormFactory
              key={formKey}
              fields={getThisWeeksSessionFields(setPublishedCurrentLesson, publishedCurrentLesson?.status)}
              schema={thisWeeksSessionNotesSchema}
              onSubmit={handleSubmit}
              defaultValues={defaultValues}
              actionButtonsComponent={
                <div className='my-auto flex w-full min-w-[240px] flex-1 shrink basis-0 items-center justify-end gap-3 self-stretch'>
                  <Button
                    type='submit'
                    className='my-auto flex items-center justify-center gap-1 self-stretch overflow-hidden rounded-lg border-2 border-solid border-white border-opacity-10 bg-slate-800 px-3.5 py-2.5 text-white shadow-sm'
                  >
                    Save changes
                  </Button>
                </div>
              }
            />
          ) : (
            <div className='flex h-full items-center justify-center py-10'>
              Loading <Loader2 className='ml-1 h-6 w-6 animate-spin text-gray-500' />
            </div>
          )}
        </div>
      </section>
    </CurrentSessionProvider>
  );
};

export default SessionNotesThisWeek;
