'use client';

import MomProfileWrapper from '../../../_components/mom-profile-wrapper';
import SessionNotesTable from '../../../_components/mom-session-notes-table';
import WithMomProfile from '../with-mom-profile';

const MomSessionNotesPage = ({ params }: { params: { id: string } }): React.ReactNode => {
  const { id } = params;

  return (
    <WithMomProfile momId={id}>
      <MomProfileWrapper viewId='session-notes' momId={id}>
        <SessionNotesTable />
      </MomProfileWrapper>
    </WithMomProfile>
  );
};

export default MomSessionNotesPage;
