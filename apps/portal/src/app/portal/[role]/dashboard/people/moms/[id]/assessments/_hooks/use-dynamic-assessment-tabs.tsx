import { useFeatureFlags } from '@/hooks/use-feature-flags';
import { useMemo } from 'react';

const staticTabs = [
  { navLabel: 'Well-Being Assessment', title: 'well-being' },
  // https://servant-io.atlassian.net/browse/EMA-832
  // This is the assessment tab that stores the build out for the assessment form
  // { navLabel: 'Assessment', title: 'assessment' },
  // The above assessment tab could be implemented in the future if we want to adopt it.
  // We could also delete it but I don't want to do that right now since it's beyond the scope of my current ticket :)
  { navLabel: 'AAPI Assessment', title: 'assessments' },
];

export const useDynamicAssessmentTabs = (assessments: { name: string; id: string }[] | undefined) => {
  const { isFeatureEnabled } = useFeatureFlags();

  const tabs = useMemo<{ navLabel: string; title: string }[]>(() => {
    if (!assessments || !isFeatureEnabled('dynamic-assessments')) {
      return staticTabs;
    }

    return [
      ...staticTabs,
      ...assessments.map((assessment) => ({
        navLabel: assessment.name,
        title: assessment.id,
      })),
    ];
  }, [assessments]);

  return { tabs };
};
