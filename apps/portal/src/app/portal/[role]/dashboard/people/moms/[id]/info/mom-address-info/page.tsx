'use client';

import { momAddressInfoFormFields, momAddressInfoFormSchema } from '@/app/portal/lib/mom-basic-info-form-config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { useFindUniqueMom, useUpdateMom } from '@/hooks/generated/mom';
import { useToast } from '@/hooks/use-toast';
import { type FormValues } from '@/types/form-field';
import { useState } from 'react';

import MomBasicInfoWrapper from '../../../../_components/mom-basic-info-form-wrapper';
import MomProfileWrapper from '../../../../_components/mom-profile-wrapper';
import WithMomProfile from '../../with-mom-profile';

const MomClientInfoPage = ({ params }: { params: { id: string } }): React.ReactNode => {
  const { id } = params;
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { data: mom, isLoading } = useFindUniqueMom({
    where: { id },
  });
  const { mutateAsync: updateMomAsync } = useUpdateMom();

  const handleUpdateMomAddress = async (data: FormValues) => {
    if (isSubmitting) return;

    setIsSubmitting(true);
    try {
      await updateMomAsync({
        where: { id },
        data,
      });

      toast({
        title: 'Success',
        description: 'The client address information has been successfully saved',
        variant: 'success',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'There was an error saving the client address information. Please try again.',
        variant: 'destructive',
      });
      console.error('Failed to submit:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!mom) {
    return <div>No mom data found</div>;
  }

  const defaultValues = {
    primary_address_street: mom.primary_address_street ?? '',
    primary_address_state: mom.primary_address_state ?? '',
    primary_address_city: mom.primary_address_city ?? '',
    primary_address_county_c: mom.primary_address_county_c ?? '',
    primary_address_postalcode: mom.primary_address_postalcode ?? '',
    address_access_c: mom.address_access_c ?? '',
  };

  return (
    <WithMomProfile momId={id}>
      <MomProfileWrapper viewId='info' momId={id}>
        <MomBasicInfoWrapper momId={id} currentTab='mom-address-info'>
          <div className='ml-10 w-full rounded-md border bg-white p-6 max-md:ml-0 md:w-[560px]'>
            <FormFactory
              key={`mom-address-form-${mom.id}`}
              formWrapperClassName='flex flex-col w-full'
              formFieldElClass='w-full'
              fields={momAddressInfoFormFields}
              schema={momAddressInfoFormSchema}
              onSubmit={handleUpdateMomAddress}
              defaultValues={defaultValues}
              actionButtonsComponent={
                <div className='my-auto flex w-full min-w-[240px] flex-1 shrink basis-0 items-center justify-end gap-3 self-stretch border-t-[1px] pt-4'>
                  <Button
                    type='submit'
                    disabled={isSubmitting}
                    className='my-auto flex items-center justify-center gap-1 self-stretch overflow-hidden rounded-lg border-2 border-solid border-white border-opacity-10 bg-slate-800 px-3.5 py-2.5 text-white shadow-sm'
                  >
                    {isSubmitting ? 'Saving...' : 'Save changes'}
                  </Button>
                </div>
              }
            />
          </div>
        </MomBasicInfoWrapper>
      </MomProfileWrapper>
    </WithMomProfile>
  );
};

export default MomClientInfoPage;
