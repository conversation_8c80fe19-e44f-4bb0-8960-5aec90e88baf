import { useAdvocateProfileContext } from '@/app/portal/context/advocate-profile-context';
import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useCreateCoordinatorNote } from '@/hooks/generated';
import { useToast } from '@/hooks/use-toast';
import { getFieldKeysFromFieldConfig } from '@/lib/utils';
import { FieldConfig, FormValues } from '@/types/form-field';
import { YupSchemas } from '@suiteapi/models';
import pick from 'lodash/pick';
import React, { ReactNode, useState } from 'react';
import * as yup from 'yup';

interface AdvocateInterviewReviewModalProps {
  title: string;
  description: string;
  children: ReactNode;
  successCallback?: () => void;
}

const AdvocateInterviewReviewModal = ({
  title,
  description,
  children,
  successCallback,
}: AdvocateInterviewReviewModalProps) => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const { advocateProfile } = useAdvocateProfileContext();
  const {
    profile: { id: userId },
  } = useDashboardContext();
  const { mutateAsync: createCoordinatorNoteAsync } = useCreateCoordinatorNote();

  // Create a custom form schema that enforces interview_advocate as the type
  const interviewReviewFields: FieldConfig[] = [
    // Just using the description field, we'll hardcode the type_c value in the submit handler
    {
      name: 'description',
      label: 'Review Notes',
      type: 'textarea',
    },
  ];

  const fieldKeys = getFieldKeysFromFieldConfig(interviewReviewFields);
  const interviewReviewSchema = yup.object().shape(pick(YupSchemas.coordinatorNotesApiFieldSchemas, fieldKeys));

  const handleSubmit = async (values: FormValues) => {
    setIsSubmitting(true);

    try {
      await createCoordinatorNoteAsync({
        data: {
          type_c: YupSchemas.CoordinatorNoteTypeEnum.InterviewAdvocate,
          description: values.description as string,
          name: `Interview Review: ${title} - ${advocateProfile?.firstName} ${advocateProfile?.lastName} (${new Date().toLocaleDateString()})`,
          coordinator_id: userId,
          advocate_id: advocateProfile?.id ?? '',
        },
      });

      toast({
        title: 'Success',
        description: 'The interview review has been successfully saved',
        variant: 'success',
      });
      setIsOpen(false);
      successCallback?.();
    } catch (error) {
      console.error('Error saving interview review:', error);
      toast({
        title: 'Error',
        description: 'There was an error saving the interview review. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      {/* Trigger */}
      <DialogTrigger asChild>{children}</DialogTrigger>

      {/* Modal */}
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <FormFactory
          fields={interviewReviewFields}
          schema={interviewReviewSchema}
          onSubmit={handleSubmit}
          actionButtonsComponent={
            <DialogFooter className='mt-4 sm:justify-end'>
              <DialogClose asChild>
                <Button type='button' variant='secondary'>
                  Cancel
                </Button>
              </DialogClose>
              <Button type='submit' variant='default' disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : 'Save Review'}
              </Button>
            </DialogFooter>
          }
        />
      </DialogContent>
    </Dialog>
  );
};

export default AdvocateInterviewReviewModal;
