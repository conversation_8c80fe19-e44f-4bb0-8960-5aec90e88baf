'use client';

import { CoordinatorProfileProvider } from '@/app/portal/context/coordinator-profile-context';
import { useParams } from 'next/navigation';
import React from 'react';

const Layout = ({ children }: { children: React.ReactNode }) => {
  const { id } = useParams<{ id: string }>();
  return <CoordinatorProfileProvider coordinatorId={id}>{children}</CoordinatorProfileProvider>;
};

export default Layout;
