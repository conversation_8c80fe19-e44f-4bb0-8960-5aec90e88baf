import { useCreateGoal, useFindManyGoal, useUpdateGoal } from '@/hooks/generated';
import { FormValues } from '@/types/form-field';
import { Goal as GoalIcon } from 'lucide-react';

import { Button } from '../../../../../../../../components/ui/button';
import ActionPlanGoal from '../session-notes/[note]/_components/action-plan-goal';
import CreateOrEditAGoalModal from '../session-notes/[note]/_components/create-or-edit-goal-modal';

type Task = { id: string; name: string; dueDate?: string };
type Goal = { id: string; name: string; description: string; dueDate?: string; actionItems: Task[] };

const ActionPlan = ({ momId, onBackStep }: { momId: string; onBackStep?: () => void }) => {
  const findManyGoal = useFindManyGoal({ include: { actionItems: true }, where: { momId } });
  const createGoal = useCreateGoal();
  const { mutateAsync: updateGoal } = useUpdateGoal();

  async function handleAddGoal(data: FormValues): Promise<void> {
    const { name, description, dueDate, actionItems } = data as Goal;

    await createGoal.mutateAsync({
      data: {
        momId,
        name,
        description,
        dueDate: dueDate ? new Date(dueDate).toISOString() : null,
        actionItems: {
          createMany: {
            data: actionItems.map((item) => ({
              name: item.name,
              dueDate: item.dueDate ?? null,
            })),
          },
        },
      },
    });
  }

  async function handleUpdateGoal(data: FormValues): Promise<void> {
    const { id, name, description, dueDate, actionItems } = data as Goal;
    const goal = findManyGoal.data?.find((goal) => goal.id === id);

    const actionItemsIdsToDelete = goal?.actionItems
      .map((item) => item.id)
      .filter((itemId) => !actionItems.some((item) => item.id === itemId));

    await updateGoal({
      where: {
        id,
      },
      data: {
        momId,
        name,
        description,
        dueDate: dueDate ?? null,
        actionItems: {
          deleteMany: {
            id: {
              in: actionItemsIdsToDelete,
            },
          },
          upsert: actionItems.map((task) => ({
            where: {
              id: task.id,
            },
            create: {
              name: task.name,
              dueDate: task.dueDate ?? null,
            },
            update: {
              name: task.name,
              dueDate: task.dueDate ?? null,
            },
          })),
        },
      },
    });
  }

  return (
    <div className='mb-14 flex flex-col gap-4'>
      <section className='mt-3 flex w-full flex-col rounded-xl border border-solid border-gray-100 bg-white shadow-sm'>
        <div className='flex w-full flex-col'>
          {findManyGoal.data?.length ? (
            findManyGoal.data.map((goal) => <ActionPlanGoal key={goal.id} goal={goal} onSubmit={handleUpdateGoal} />)
          ) : (
            <div className='flex flex-col items-center justify-center p-8'>
              <div className='mb-4 rounded-full bg-offGrey p-3'>
                <GoalIcon className='h-6 w-6' />
              </div>
              <div className='mb-2 flex items-center justify-center text-lg font-semibold'>
                Let&apos;s Create an action plan
              </div>
              <p className='text-center text-emaTextTertiary'>Click Add Goal to get started</p>
            </div>
          )}
        </div>
        <div className='mt-3 flex w-full flex-col pb-3 text-sm font-semibold leading-5'>
          <div className='mt-3 flex w-full items-center gap-4 px-4'>
            <div className='my-auto flex w-full min-w-[240px] flex-1 shrink basis-0 items-center justify-end gap-3 self-stretch'>
              <CreateOrEditAGoalModal onSubmit={handleAddGoal} />
            </div>
          </div>
        </div>
      </section>
      {onBackStep && (
        <div className='flex justify-between'>
          <Button onClick={onBackStep} variant='outline' className='bg-gray-200 text-gray-700'>
            Back
          </Button>
        </div>
      )}
    </div>
  );
};

export default ActionPlan;
