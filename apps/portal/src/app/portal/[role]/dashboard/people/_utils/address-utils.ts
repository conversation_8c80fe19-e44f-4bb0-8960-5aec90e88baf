interface AddressFields {
  address_street?: string | null;
  address_city?: string | null;
  address_state?: string | null;
  address_postalcode?: string | null;
}

export const formatAddress = (address: AddressFields): string => {
  const street = address?.address_street;
  const city = address?.address_city;
  const state = address?.address_state;
  const zip = address?.address_postalcode;

  if (!street && !city && !state && !zip) {
    return 'No address on file';
  }

  const streetLine = street || 'No street address';
  const cityStateZipLine = [city ? (state ? `${city},` : city) : '', state || '', zip || ''].filter(Boolean).join(' ');

  return `${streetLine}\n${cityStateZipLine}`;
};
