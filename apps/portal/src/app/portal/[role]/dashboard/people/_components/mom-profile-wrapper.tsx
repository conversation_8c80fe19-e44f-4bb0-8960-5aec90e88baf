'use client';

import Breadcrumbs from '@/app/portal/_components/navigation/breadcrumbs';
import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import { MOM_PROFILE_TABS } from '@/app/portal/lib/mom-profile.config';
import { useRouter } from 'next/navigation';

import MomProfileHeader from './mom-profile-header';
import PersonActionTabs from './person-action-tabs';

interface MomProfileWrapperProps {
  momId: string;
  viewId: string;
}

const MomProfileWrapper = ({
  momId,
  viewId,
  children,
}: React.PropsWithChildren<MomProfileWrapperProps>): React.ReactNode => {
  const router = useRouter();
  const { profile } = useDashboardContext();

  const navigateToTab = (tab: string): void => {
    const tabRoute = tab === 'overview' ? '/' : `/${tab}`;
    router.push(`/portal/${profile.portalRole}/dashboard/people/moms/${momId}${tabRoute}`);
  };

  return (
    <div className='flex flex-col overflow-hidden pb-12'>
      <header className='flex w-full flex-col px-8 pt-6 max-md:max-w-full max-md:px-5 max-sm:px-1'>
        <Breadcrumbs
          items={[
            { label: 'People', href: '/people' },
            { label: 'Moms', href: '/people/moms' },
          ]}
        />
        <MomProfileHeader viewId={viewId} />
      </header>
      <main className='mt-8 flex w-full flex-col max-md:max-w-full'>
        <div className='flex w-full flex-col px-8 max-md:max-w-full max-md:px-5 max-sm:px-1'>
          <PersonActionTabs items={MOM_PROFILE_TABS} activeTab={viewId} onTabChange={navigateToTab} />
          {children}
        </div>
      </main>
    </div>
  );
};

export default MomProfileWrapper;
