'use client';

import { useAssessmentResults } from '@/app/portal/[role]/dashboard/people/moms/[id]/assessments/_hooks/use-assessment-results';
import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import GenericTable from '@/components/custom/generic-table';
import { Button } from '@/components/ui/button';
import { TableCell } from '@/components/ui/table';
import { useCreateAssessmentResult, useDeleteAssessmentResult } from '@/hooks/generated';
import { useToast } from '@/hooks/use-toast';
import { formatDateFromString } from '@suiteapi/models';
import { ChevronRight, FilePlus2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { useAssessments } from '../../_hooks/use-assessments';

const AssessmentResultsPage = ({ params }: { params: { id: string; assessmentId: string } }): React.ReactNode => {
  const { id, assessmentId } = params;
  const { profile } = useDashboardContext();
  const { toast } = useToast();
  const router = useRouter();

  const [isDeletingAssessmentResult, setIsDeletingAssessmentResult] = useState(false);

  // Note: useAssessments is also referenced in other locations up the layout/component tree.
  //        We're using Tanstack Query so it should automatically de-dupe the requests.
  const { assessments, areAssessmentsLoading } = useAssessments();
  const { assessmentResults, areAssessmentResultsLoading } = useAssessmentResults({ momId: id, assessmentId });

  const { mutateAsync: deleteAssessmentResultAsync } = useDeleteAssessmentResult({
    onSuccess: () => {
      setIsDeletingAssessmentResult(false);
    },
    onError: () => {
      setIsDeletingAssessmentResult(false);
    },
  });

  const { mutateAsync: createAssessmentResultAsync } = useCreateAssessmentResult({
    onSuccess: (data) => {
      router.push(
        `/portal/${profile?.portalRole}/dashboard/people/moms/${id}/assessments/${assessmentId}/results/${data?.id}`,
      );
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to create assessment result',
      });
    },
  });

  const onAddAssessmentResultClick = () => {
    createAssessmentResultAsync({
      data: {
        assessmentId,
        momId: id,
      },
    });
  };

  const onAssessmentResultRowClick = (item: NonNullable<typeof assessmentResults>[number]) => {
    router.push(
      `/portal/${profile?.portalRole}/dashboard/people/moms/${id}/assessments/${assessmentId}/results/${item.id}`,
    );
  };

  const onDeleteAssessmentResultClick = (item: NonNullable<typeof assessmentResults>[number]) => {
    setIsDeletingAssessmentResult(true);
    deleteAssessmentResultAsync({
      where: {
        id: item.id,
      },
    });
  };

  if (areAssessmentResultsLoading || areAssessmentsLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className='w-full'>
      <section className='mt-6 md:mt-0'>
        <GenericTable
          data={assessmentResults || []}
          isLoading={false}
          headers={['Start Date', 'Completed Date', 'Actions']}
          columnWidths={[25, 25, 20]}
          emptyMessage='There are currently no results to display.'
          headerSection={
            <div className='flex items-center justify-between gap-8'>
              <div>
                <h1 className='mb-2 text-2xl font-medium'>{assessments?.find((a) => a.id === assessmentId)?.name}</h1>
                <p>Below you'll find mom's results for this assessment.</p>
              </div>
              <div className='flex gap-4'>
                <Button variant='default' className='bg-darkGreen' onClick={onAddAssessmentResultClick}>
                  <FilePlus2 className='h-5 w-5' /> <span className='ml-2'>Add Result</span>
                </Button>
              </div>
            </div>
          }
          rowRenderer={(item) => (
            <>
              <TableCell>{item.created_at ? formatDateFromString(item.created_at.toISOString()) : ''}</TableCell>
              <TableCell>
                {item.completedAt ? formatDateFromString(item.completedAt.toISOString()) : 'Not Completed'}
              </TableCell>
              <TableCell>
                <Button
                  variant='destructive'
                  size='sm'
                  className='mr-2 border-none'
                  disabled={isDeletingAssessmentResult}
                  onClick={() => {
                    onDeleteAssessmentResultClick(item);
                  }}
                >
                  Delete
                </Button>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => {
                    onAssessmentResultRowClick(item);
                  }}
                >
                  Review
                </Button>
              </TableCell>
            </>
          )}
          mobileRenderer={(item) => (
            <div className='flex flex-col gap-2'>
              <div className='flex items-center justify-between'>
                <p>
                  <span className='font-semibold'>Start Date: </span>
                  {item.created_at ? formatDateFromString(item.created_at.toISOString()) : ''}
                </p>
                <div className='cursor-pointer' onClick={() => onAssessmentResultRowClick(item)}>
                  <ChevronRight />
                </div>
              </div>

              <p>
                <span className='font-semibold'>Completed Date: </span>
                {item.completedAt ? formatDateFromString(item.completedAt.toISOString()) : 'Not Completed'}
              </p>
              <Button
                variant='destructive'
                size='sm'
                className='border-none'
                disabled={isDeletingAssessmentResult}
                onClick={() => {
                  onDeleteAssessmentResultClick(item);
                }}
              >
                Delete
              </Button>
            </div>
          )}
        />
      </section>
    </div>
  );
};

export default AssessmentResultsPage;
