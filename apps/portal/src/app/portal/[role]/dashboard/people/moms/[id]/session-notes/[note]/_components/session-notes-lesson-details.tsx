import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { useMomSessionNotes } from '@/app/portal/context/mom-session-notes-context';
import { shrikhand } from '@/app/styles/fonts';
import { Button } from '@/components/ui/button';
import { downloadDocument } from '@/lib/portal';
import { getFindMany } from '@/lib/sfetch';
import { decodeHtmlEntities } from '@/lib/utils';
import { LessonSchema } from '@/types/schemas/lesson';
import { YupSchemas } from '@suiteapi/models';
import { CirclePlay, FileText, Presentation } from 'lucide-react';
import { useEffect, useState } from 'react';

import ChangeLessonModal from './change-lesson-modal';

const isResourceExternalLink = (resource: YupSchemas.DocumentSchema) => {
  return !!resource.external_url_c;
};

const LessonResourceItemLink: React.FC<{ resource: YupSchemas.DocumentSchema | null; nameOverride?: string }> = ({
  resource,
  nameOverride,
}) => (
  <div className='my-auto flex min-w-[240px] items-end gap-2 self-stretch'>
    {resource ? (
      <a
        href={isResourceExternalLink(resource) ? resource.external_url_c : `#`}
        target={isResourceExternalLink(resource) ? '_blank' : '_self'}
        rel='noreferrer'
        onClick={() => {
          if (!isResourceExternalLink(resource)) {
            downloadDocument(resource.id ?? '', nameOverride ?? resource.document_name ?? 'document');
          }
        }}
      >
        <div className='text-sm leading-5 text-slate-700'>{nameOverride ?? resource.document_name}</div>
      </a>
    ) : (
      <div className='text-sm leading-5 text-slate-700'>{nameOverride}</div>
    )}
  </div>
);

const LessonResourceItem: React.FC<{ resource: YupSchemas.DocumentSchema }> = ({ resource }) => (
  <div className='mb-1 flex min-h-[36px] w-full flex-wrap items-center gap-2 overflow-hidden px-3 py-2 max-md:max-w-full'>
    {isResourceExternalLink(resource) ? <CirclePlay className='h-5 w-5' /> : <FileText className='h-5 w-5' />}
    <LessonResourceItemLink resource={resource} />
  </div>
);

const SessionNotesLessonDetails: React.FC<{
  setPublishedCurrentLesson: (lesson: LessonSchema | undefined) => void;
}> = ({ setPublishedCurrentLesson }) => {
  const { pairingInfo, lessons } = useMomProfileContext();
  const { sessionReport } = useMomSessionNotes();
  const currentLesson = lessons?.find((lesson) => lesson.id === sessionReport?.covered_lesson_id);

  const [showResources, setShowResources] = useState(true);
  const [selectedLesson, setSelectedLesson] = useState<LessonSchema | null>(currentLesson ?? null);
  const [lessonResources, setLessonResources] = useState<YupSchemas.DocumentSchema[] | null>(null);
  const [primaryLessonResource, setPrimaryLessonResource] = useState<YupSchemas.DocumentSchema | null>(null);

  const handleLessonChange = (lesson: LessonSchema) => {
    setSelectedLesson(lesson);
  };

  useEffect(() => {
    // Fetch lesson resources
    if (selectedLesson) {
      setPublishedCurrentLesson(selectedLesson);
      getFindMany<YupSchemas.DocumentSchema>('document', {
        where: {
          lesson_id: selectedLesson.id,
        },
      }).then((resources) => {
        const primaryResource = resources?.find((resource) => resource.is_primary_lesson_resource);
        setPrimaryLessonResource(primaryResource ?? null);
        setLessonResources(resources?.filter((resource) => !resource.is_primary_lesson_resource) ?? null);
      });
    }
  }, [selectedLesson]);

  return (
    <div>
      <div className='mt-4 flex w-full flex-wrap items-center gap-3 border-t border-gray-200 pb-3 max-md:max-w-full'>
        <div className='flex flex-col'>
          <div className='mt-4 flex w-full flex-wrap items-center gap-3 pb-2 max-md:max-w-full'>
            <div className='my-auto self-stretch text-base leading-6 text-black'>Mom&lsquo;s Track:</div>
            <div className='my-auto self-stretch text-base text-slate-800'>{String(pairingInfo?.track?.title)}</div>
          </div>
          <div className='flex w-full flex-wrap items-center gap-3 pb-0 max-md:max-w-full'>
            <div className='my-auto self-stretch text-base leading-6 text-black'>Current Lesson:</div>
            <div className='my-auto self-stretch text-base text-slate-800'>
              <span className={shrikhand.className}>
                {selectedLesson ? String(selectedLesson.title) : 'None selected'}
              </span>
            </div>
          </div>
        </div>
      </div>
      {selectedLesson && <p className='mb-4 text-emaTextSecondary'>{decodeHtmlEntities(selectedLesson.description)}</p>}
      <div className='flex w-full flex-wrap items-center gap-2'>
        <div className='text-sm font-semibold leading-5 text-emaTextSecondary'>View Content</div>
        <ChangeLessonModal lessons={lessons ?? []} onLessonChange={handleLessonChange} />{' '}
        {/* Pass lessons and handler */}
      </div>
      {selectedLesson ? (
        <div className='mb-2 flex min-h-[36px] w-full flex-wrap items-center gap-2 overflow-hidden px-3 py-2 max-md:max-w-full'>
          <Presentation className='h-5 w-5' />
          <LessonResourceItemLink resource={primaryLessonResource} nameOverride={selectedLesson.title} />
        </div>
      ) : (
        <p className='italic text-emaTextSecondary'>No lesson selected</p>
      )}
      <div className='flex w-full flex-wrap items-center gap-2'>
        <h2 className='text-sm font-semibold leading-5 text-emaTextSecondary'>View Resources</h2>
        <Button
          type='button'
          variant='ghost'
          className='text-sm text-emaTextSecondary underline'
          onClick={() => setShowResources((prev) => !prev)}
        >
          {showResources ? 'Hide' : 'Show'}
        </Button>
      </div>
      {showResources ? (
        lessonResources ? (
          lessonResources?.map((resource) => <LessonResourceItem key={resource.id} resource={resource} />)
        ) : (
          <p className='italic text-emaTextSecondary'>No resources found</p>
        )
      ) : null}
    </div>
  );
};

export default SessionNotesLessonDetails;
