import { But<PERSON> } from '@/components/ui/button';
import { Mom } from '@/hooks/generated/__types';
import React from 'react';

import ApprovalForEmaForm from './approval-for-ema-form';
import SelectTrackForm from './select-track-form';

const ApprovalForEmaStep = ({
  mom,
  onBackStep,
  onNextStep,
}: {
  mom: Mom;
  onBackStep: () => void;
  onNextStep: () => void;
}) => {
  return (
    <div className='mb-14 flex flex-col gap-4'>
      <div className='border-emaBorder rounded-lg border bg-white p-4'>
        <h3 className='mb-4 text-2xl font-semibold'>Approval for ĒMA program</h3>
        <p className='mb-4 text-base'>
          Please mark change mom&apos;s status based on how she is engaging with the program.
        </p>
        <ApprovalForEmaForm mom={mom} />
      </div>
      <div className='border-emaBorder rounded-lg border bg-white p-4'>
        <h2 className='mb-2 text-2xl font-semibold'>Select Track</h2>
        <p className='mb-4 text-base'>
          Choose the best track for mom to engage in for the EMA program based on how she answered the questions.
        </p>
        <SelectTrackForm mom={mom} />
      </div>
      <div className='flex justify-between'>
        <Button onClick={onBackStep} variant='outline' className='bg-gray-200 text-gray-700'>
          Back
        </Button>
        <Button onClick={onNextStep}>Next: Create an Action Plan</Button>
      </div>
    </div>
  );
};

export default ApprovalForEmaStep;
