'use client';

import { openExportPage } from '@/app/portal/[role]/dashboard/people/_utils/session-utils';
import SessionNoteStatuses from '@/app/portal/[role]/dashboard/people/moms/[id]/session-notes/[note]/_components/sesion-note-statuses';
import SessionNotesDetails from '@/app/portal/[role]/dashboard/people/moms/[id]/session-notes/[note]/_components/session-notes-details';
import SessionNotesScheduleNext from '@/app/portal/[role]/dashboard/people/moms/[id]/session-notes/[note]/_components/session-notes-schedule-next-connection';
import SessionNotesThisWeek from '@/app/portal/[role]/dashboard/people/moms/[id]/session-notes/[note]/_components/session-notes-this-weeks-session';
import WithMomSessions from '@/app/portal/[role]/dashboard/people/moms/[id]/session-notes/[note]/with-mom-sessions';
import WithMomProfile from '@/app/portal/[role]/dashboard/people/moms/[id]/with-mom-profile';
import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { useMomSessionNotes } from '@/app/portal/context/mom-session-notes-context';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { ArrowLeft, CloudDownload } from 'lucide-react';
import Link from 'next/link';
import { useEffect } from 'react';

import ActionPlan from '../../_components/action-plan';

const SessionNotesPage = ({ params }: { params: { role: string; id: string; note: string } }): React.ReactNode => {
  const { role, id, note } = params;
  const { momProfile } = useMomProfileContext();
  const { fetchSessionReport, sessionMeetingData } = useMomSessionNotes();

  const backToSessionNotesPath = `/portal/${role}/dashboard`;

  useEffect(() => {
    fetchSessionReport(note);
  }, [fetchSessionReport, note]);

  const sessionCategories = [
    {
      id: 'action-plan-updates',
      title: 'Action Plan Updates',
      component: <ActionPlan momId={id} />,
    },
    {
      id: 'this-weeks-session',
      title: "This Week's Session",
      component: <SessionNotesThisWeek sessionId={note} />,
    },
    {
      id: 'schedule-next-connection',
      title: 'Schedule Next Connection',
      component: <SessionNotesScheduleNext />,
    },
  ];

  return (
    <div>
      <WithMomProfile momId={id}>
        <WithMomSessions momId={id}>
          <div className='flex w-full flex-col max-md:max-w-full'>
            <div className='flex w-full flex-col'>
              <div className='flex w-full items-center pb-4 pt-6 max-md:max-w-full max-md:px-5'>
                <Link href={backToSessionNotesPath}>
                  <div className='flex items-center justify-center gap-1 self-start overflow-hidden px-3 py-2 text-sm font-semibold leading-5 text-slate-700'>
                    <ArrowLeft className='h-5 w-5' />
                    <span className='my-auto self-stretch px-0.5 hover:underline'>Back to dashboard</span>
                  </div>
                </Link>
              </div>
              <div className='flex gap-2'>
                <SessionNoteStatuses sessionNoteId={sessionMeetingData?.session_note?.id ?? ''} />
              </div>
              <div className='mt-3 flex w-full items-center justify-between gap-10 text-lg font-semibold leading-7 text-slate-700 max-md:flex-col'>
                <div className='my-auto gap-2 self-stretch text-2xl'>
                  1:1 Meeting with {momProfile?.first_name} {momProfile?.last_name}
                </div>
                <div className='my-auto flex min-h-[24px] gap-2 self-stretch'>
                  <Button variant='outline' className='flex gap-1' onClick={() => openExportPage(id, note)}>
                    <CloudDownload className='h-5 w-5' />
                    <span>Export</span>
                  </Button>
                </div>
              </div>
              <div className='flex flex-col-reverse md:flex-row md:gap-5'>
                {/* To be completed on: https://servant-io.atlassian.net/browse/EMA-546 */}
                <div className='flex w-80 flex-col gap-5'>
                  <div className='flex w-full justify-end self-end'>
                    <SessionNotesDetails />
                  </div>
                </div>
                <Accordion
                  type='multiple'
                  defaultValue={sessionCategories.map((category) => category.id)}
                  className='mt-2 w-full max-md:max-w-full'
                >
                  {sessionCategories.map((category, index) => (
                    <AccordionItem key={category.id} value={category.id}>
                      <AccordionTrigger>
                        <div className='flex items-center gap-2'>
                          <div className='h-5 w-5 rounded-3xl bg-slate-800 text-center text-sm font-semibold leading-5 text-white'>
                            {index + 1}
                          </div>
                          <h2 className='text-lg font-medium leading-7 text-gray-900'>{category.title}</h2>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent>{category.component}</AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            </div>
          </div>
        </WithMomSessions>
      </WithMomProfile>
    </div>
  );
};

export default SessionNotesPage;
