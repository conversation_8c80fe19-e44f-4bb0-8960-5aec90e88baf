import {
  createBenevolenceReferralFormConfig,
  createBenevolenceReferralSchema,
} from '@/app/portal/lib/create-benevolence-referral-form.config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { toast } from '@/hooks/use-toast';
import { useState } from 'react';
import { FieldValues } from 'react-hook-form';
import { useCreateServiceReferral } from '@/hooks/generated';
import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';

const BenevolenceNeedReferralModal = () => {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { mutateAsync: createServiceReferral } = useCreateServiceReferral();
  const { momProfile } = useMomProfileContext();

  const handleSave = async (data: FieldValues) => {
    setIsLoading(true);

    try {
      // Send data to the API
     await createServiceReferral({
        data: {
          service: data.service_c,
          outcome: data.outcome_c,
          start_date: data.start_date,
          provider: data.provider,
          mom: {
            connect: {
              id: momProfile?.id,
            },
          },
        },
      });
    
      toast({
        title: 'Success',
        description: 'The referral information has been successfully saved',
        variant: 'success',
      });
    } catch (error) {
      console.error(error);
      toast({
        title: 'Error',
        description: 'There was an error saving the referral information. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
      setOpen(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button asChild className='bg-darkGreen'>
          <div>Open Referral</div>
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create a Referral</DialogTitle>
          <DialogDescription>Take note of any important details from this connection.</DialogDescription>
        </DialogHeader>
        <FormFactory
          fields={createBenevolenceReferralFormConfig}
          schema={createBenevolenceReferralSchema}
          onSubmit={handleSave}
          stopPropagation={true}
          actionButtonsComponent={
            <div className='flex justify-end gap-2'>
              <Button type='button' variant='outline' onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button type='submit' disabled={isLoading}>
                {isLoading ? 'Saving...' : 'Save'}
              </Button>
            </div>
          }
        />
      </DialogContent>
    </Dialog>
  );
};

export default BenevolenceNeedReferralModal;
