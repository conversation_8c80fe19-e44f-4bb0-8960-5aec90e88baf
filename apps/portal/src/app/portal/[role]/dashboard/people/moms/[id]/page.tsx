'use client';

import MomProfileWrapper from '../../_components/mom-profile-wrapper';
import MomOverviewPage from './overview/page';
import WithMomProfile from './with-mom-profile';

const MomConnectionLogsPage = ({ params }: { params: { id: string } }): React.ReactNode => {
  const { id } = params;

  return (
    <WithMomProfile momId={id}>
      <MomProfileWrapper viewId='overview' momId={id}>
        <MomOverviewPage />
      </MomProfileWrapper>
    </WithMomProfile>
  );
};

export default MomConnectionLogsPage;
