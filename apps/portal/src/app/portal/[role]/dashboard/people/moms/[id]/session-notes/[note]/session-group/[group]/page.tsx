'use client';

import GroupSessionInfo from '@/app/portal/[role]/dashboard/people/moms/[id]/session-notes/[note]/session-group/[group]/_components/group-session-info';
import GroupSessionNote from '@/app/portal/[role]/dashboard/people/moms/[id]/session-notes/[note]/session-group/[group]/_components/group-session-note';
import GroupSessionScheduleSessionSection from '@/app/portal/[role]/dashboard/people/moms/[id]/session-notes/[note]/session-group/[group]/_components/group-session-schedule-session-section';
import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { useMomSessionNotes } from '@/app/portal/context/mom-session-notes-context';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { ArrowLeft, DownloadCloud } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useEffect } from 'react';

const GroupSessionNotePage = () => {
  const params = useParams();
  const { note } = params;
  const { profile } = useDashboardContext();
  const { momProfile } = useMomProfileContext();
  const { fetchSessionReport, sessionMeetingData } = useMomSessionNotes();

  useEffect(() => {
    fetchSessionReport(note as string);
  }, [fetchSessionReport, note]);

  const sessionCategories = [
    {
      id: 'session-note-info',
      title: 'Session Note Info',
      component: <GroupSessionInfo />,
    },
    {
      id: 'this-weeks-session',
      title: "This Week's Session",
      component: <GroupSessionNote />,
    },
    {
      id: 'schedule-session',
      title: 'Schedule Session',
      component: <GroupSessionScheduleSessionSection />,
    },
  ];

  return (
    <div>
      <div className='flex w-full flex-row justify-between'>
        <Link
          href={`/portal/${profile?.portalRole}/dashboard/people/moms/${momProfile?.id}/session-notes/${sessionMeetingData?.id}`}
        >
          <Button variant='ghost'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back to Individual Session
          </Button>
        </Link>
      </div>
      <div className='mb-8 flex w-full flex-row items-center justify-between'>
        <h1 className='text-2xl font-semibold'>Group Session Note</h1>
        <div className='flex flex-row gap-2'>
          <Button variant='outline'>
            <DownloadCloud className='mr-2 h-4 w-4' /> Export
          </Button>
          <Button>Submit Report</Button>
        </div>
      </div>
      <Accordion
        type='multiple'
        defaultValue={sessionCategories.map((category) => category.id)}
        className='mt-2 w-full max-md:max-w-full'
      >
        {sessionCategories.map((category, index) => (
          <AccordionItem
            key={category.id}
            value={category.id}
            className='mb-5 max-w-[800px] rounded-lg border border-emaBorderSecondary bg-white px-4 shadow-sm'
          >
            <AccordionTrigger>
              <div className='flex items-center gap-2'>
                <div className='h-5 w-5 rounded-3xl bg-slate-800 text-center text-sm font-semibold leading-5 text-white'>
                  {index + 1}
                </div>
                <h2 className='text-lg font-medium leading-7 text-gray-900'>{category.title}</h2>
              </div>
            </AccordionTrigger>
            <AccordionContent>{category.component}</AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
};

export default GroupSessionNotePage;
