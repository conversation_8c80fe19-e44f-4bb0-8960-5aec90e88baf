'use client';

import { useRouter } from 'next/navigation';

import { useAssessmentQuestions } from '../../../_hooks/use-assessment-questions';

const AssessmentResultDetailsPage = ({
  params,
}: {
  params: { assessmentId: string; assessmentResultId: string };
}): React.ReactNode => {
  const router = useRouter();
  const { assessmentId, assessmentResultId } = params;
  const { assessmentQuestions, areAssessmentQuestionsLoading } = useAssessmentQuestions({
    assessmentId,
    assessmentResultId,
  });

  // TODO: BDL - Implement AssessmentQuestion and AssessmentResultQuestionResponse display here.

  if (areAssessmentQuestionsLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className='w-full'>
      <button onClick={() => router.back()}>Back</button>
      <div>
        {assessmentQuestions?.map((question) => {
          return (
            <div key={question.id}>
              <div>{question.order}</div>
              <div>{question.question}</div>
              <div>{question.assessmentConstruct?.name}</div>
              <div>{question.assessmentResultQuestionResponses[0]?.intResponse}</div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default AssessmentResultDetailsPage;
