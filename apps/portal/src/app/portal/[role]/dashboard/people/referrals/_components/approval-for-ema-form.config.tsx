import { denyReferralOptions } from '@/app/portal/lib/deny-referral.config';
import { ProspectStatus } from '@/hooks/generated/__types';
import { StandardFieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const referralMomStatusOptions: { label: string; value: ProspectStatus }[] = [
  { label: 'Engaged in Program', value: 'engaged_in_program' },
  { label: 'Did Not Engage in Program', value: 'did_not_engage_in_program' },
  { label: 'Prospect Intake Scheduled', value: 'prospect_intake_scheduled' },
  { label: 'Prospect', value: 'prospect' },
];

export const approvalForEmaFormFields: StandardFieldConfig[] = [
  {
    name: 'prospect_status',
    label: 'Mom Status',
    labelDescription: "Select a status based on mom's current progress in the program.",
    type: 'select',
    placeholder: 'Select',
    options: referralMomStatusOptions,
    schema: yup
      .string()
      .required('Status is required')
      .oneOf(referralMomStatusOptions.map((option) => option.value)),
  },
  {
    name: 'referral_sub_status',
    label: 'Did Not Engage Reason',
    labelDescription: 'If you answered Did Not Engage in Program, select a reason for not engaging with the program.',
    type: 'select',
    placeholder: 'Select',
    options: denyReferralOptions,
    schema: yup
      .string()
      .required('Reason is required')
      .oneOf(denyReferralOptions.map((option) => option.value)),
    hidden: (values) => values?.prospect_status !== 'did_not_engage_in_program',
  },
  {
    name: 'status_description',
    label: 'Notes if Not Approved',
    labelDescription: 'If mom is not approved for the program, please add details about why.',
    type: 'textarea',
    placeholder: 'Share Your Comments',
    schema: yup.string().optional().nullable(),
  },
];
