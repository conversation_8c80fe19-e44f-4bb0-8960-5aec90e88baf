'use client';

import { useAdvocateProfileContext } from '@/app/portal/context/advocate-profile-context';
import { usePairedMomsListContext } from '@/app/portal/context/paired-moms-context';
import GenericTable from '@/components/custom/generic-table';
import { TableCell } from '@/components/ui/table';
import { formatPhoneNumber } from '@/lib/utils';
import { RobustPairingSchema } from '@/types/schemas/pairing';
import { formatDateFromString } from '@suiteapi/models';
import { format } from 'date-fns';
import { Mail, Phone, User } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useEffect } from 'react';

import AdvocateProfileWrapper from '../../../_components/advocates/advocate-profile-wrapper';
import DetailsItem from '../../../_components/details-item';
import { formatAddress } from '../../../_utils/address-utils';
import { formatAdvocateStatus, formatSnakeCaseToTitleCase } from '../../../_utils/language-utils';

const AdvocateOverviewPage = () => {
  const params = useParams();
  const advocateId = params.id as string;
  const { pairedMomsList, fetchPairedMomsList, pairedMomsLoading } = usePairedMomsListContext();
  const { advocateProfile, isLoading } = useAdvocateProfileContext();
  const currentMoms = pairedMomsList?.length ?? 0;
  const momCapacity = advocateProfile?.advocate_capacity_for_moms ?? 0;
  const openSlots = momCapacity - currentMoms;
  let currentMomOpenSlotsValue = '';

  if (currentMoms > 0 && openSlots > 0) {
    currentMomOpenSlotsValue = `${currentMoms} Current / ${openSlots} Open`;
  } else if (currentMoms > 0 && openSlots <= 0) {
    currentMomOpenSlotsValue = `${currentMoms} Current / No Open Slots`;
  } else {
    currentMomOpenSlotsValue = `No Current Moms / ${openSlots} Open Slots`;
  }
  useEffect(() => {
    void fetchPairedMomsList(); // This is used to fetch the paired moms list, otherwise the pairedMomsList will be empty on initial load
  }, [fetchPairedMomsList]);

  const rowRenderer = (mom: RobustPairingSchema) => (
    <>
      <TableCell className='py-4'>
        {mom.mom?.first_name} {mom.mom?.last_name ?? 'No mom data'}
      </TableCell>
      <TableCell className='py-4'>{formatDateFromString(mom.created_at ?? '')}</TableCell>
      <TableCell className='py-4'>{mom.track?.title}</TableCell>
    </>
  );

  const mobileRenderer = (mom: RobustPairingSchema) => (
    <div className='space-y-2'>
      <div className='font-medium'> </div>
      <div className='text-sm text-gray-500'>Status: {formatDateFromString(mom.created_at ?? '')}</div>
      <div className='text-sm text-gray-500'>Track: {mom.track?.title}</div>
    </div>
  );

  const headerSection = <h2 className='text-lg font-bold'>Current Moms</h2>;

  const formattedAddress = formatAddress({
    address_street: advocateProfile?.address_street,
    address_city: advocateProfile?.address_city,
    address_state: advocateProfile?.address_state,
    address_postalcode: advocateProfile?.address_postalcode,
  });

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <AdvocateProfileWrapper viewId='overview' advocateId={advocateId}>
      <section className='flex flex-col gap-8 md:flex-row'>
        <div className='md:w-[20%]'>
          <DetailsItem label={'Current Status'} value={formatAdvocateStatus(advocateProfile?.advocate_status)} />
          <DetailsItem
            label={'ĒMA Location: '}
            value={advocateProfile?.affiliate?.name ?? 'Unassigned'}
            tooltip='Need to transfer to another location? Discuss with your affiliate support manager for transfer options.'
          />
          <DetailsItem label={'Current Moms / Open Slots'} value={currentMomOpenSlotsValue} />
          <DetailsItem label={'Availability'} value={advocateProfile?.availability ?? 'None on file'} />
          <DetailsItem
            label={'Coordinator'}
            value={
              <div className='flex items-center gap-2'>
                <User className='min-h-4 min-w-4' />
                {advocateProfile?.assignedCoordinators
                  ?.map((coordinator) => `${coordinator.firstName} ${coordinator.lastName}`)
                  .join(', ') ?? 'Unassigned'}
              </div>
            }
          />
          <DetailsItem
            label={'Phone Number'}
            value={
              <div className='flex items-center gap-2'>
                <Phone className='h-4 w-4' />
                {advocateProfile?.phone_mobile ? formatPhoneNumber(advocateProfile?.phone_mobile) : 'None on file'}
              </div>
            }
          />
          <DetailsItem
            label={'Email'}
            value={
              <div className='flex items-center gap-2'>
                <Mail className='h-4 w-4' />
                {advocateProfile?.email ?? 'None on file'}
              </div>
            }
          />
          <DetailsItem
            label={'Date of Birth'}
            value={
              advocateProfile?.date_of_birth ? format(advocateProfile.date_of_birth, 'MMMM d, yyyy') : 'None on file'
            }
          />
          <DetailsItem label={'Address'} value={<div style={{ whiteSpace: 'pre-line' }}>{formattedAddress}</div>} />
          <DetailsItem
            contentClassName='capitalize'
            label={'Language Preference'}
            value={formatSnakeCaseToTitleCase(advocateProfile?.language_preference_c) ?? 'None on file'}
          />
          <DetailsItem
            contentClassName='flex flex-wrap gap-2 capitalize'
            label={'Languages'}
            value={
              advocateProfile?.languages_c?.length
                ? advocateProfile.languages_c.map((language) => formatSnakeCaseToTitleCase(language)).join(', ')
                : 'None on file'
            }
          />
        </div>
        <div className='md:w-[80%]'>
          <GenericTable
            isLoading={pairedMomsLoading}
            data={pairedMomsList ?? []}
            rowRenderer={rowRenderer}
            mobileRenderer={mobileRenderer}
            columnWidths={[40, 20, 40]}
            headers={['Name', 'Start Date', 'Track']}
            headerSection={headerSection}
          />
        </div>
      </section>
    </AdvocateProfileWrapper>
  );
};

export default AdvocateOverviewPage;
