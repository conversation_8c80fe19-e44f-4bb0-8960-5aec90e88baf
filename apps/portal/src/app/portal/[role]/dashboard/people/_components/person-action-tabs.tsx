import { type MomProfileTabItem } from '@/app/portal/types';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ChevronDown } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

interface PersonActionTabsProps {
  items: MomProfileTabItem[];
  activeTab: string;
  onTabChange: (value: string) => void;
}

const PersonActionTabs: React.FC<PersonActionTabsProps> = ({ items, activeTab, onTabChange }) => {
  const [visibleTabs, setVisibleTabs] = useState<MomProfileTabItem[]>([]);
  const [overflowTabs, setOverflowTabs] = useState<MomProfileTabItem[]>([]);
  const tabsContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const calculateVisibleTabs = () => {
      const container = tabsContainerRef.current;
      if (!container) return;

      const containerWidth = container.offsetWidth;
      let totalWidth = 0;
      const visible: MomProfileTabItem[] = [];
      const overflow: MomProfileTabItem[] = [];

      // Reserve space for More dropdown (80px)
      const moreButtonWidth = 80;
      const availableWidth = containerWidth - moreButtonWidth;

      items.forEach((item) => {
        // Estimate tab width based on text length plus padding
        const tabWidth = item.label.length * 8 + 32; // rough estimation: 8px per character + 32px padding

        if (totalWidth + tabWidth <= availableWidth) {
          visible.push(item);
          totalWidth += tabWidth;
        } else {
          overflow.push(item);
        }
      });

      setVisibleTabs(visible);
      setOverflowTabs(overflow);
    };

    calculateVisibleTabs();
    window.addEventListener('resize', calculateVisibleTabs);

    return () => window.removeEventListener('resize', calculateVisibleTabs);
  }, [items]);

  return (
    <>
      <div className='hidden xl:block'>
        <Tabs value={activeTab} onValueChange={onTabChange}>
          <TabsList className='flex justify-start gap-2' ref={tabsContainerRef}>
            {visibleTabs.map((item) => (
              <TabsTrigger
                key={item.value}
                value={item.value}
                className='text-sm font-semibold leading-5 text-gray-500 data-[state=active]:bg-white data-[state=active]:shadow'
              >
                {item.label}
              </TabsTrigger>
            ))}
            {overflowTabs.length > 0 && (
              <Select onValueChange={onTabChange} value={activeTab}>
                <SelectTrigger className='w-20 border-none bg-transparent hover:bg-gray-100'>
                  <div className='flex items-center gap-1'>
                    <p className='text-sm font-semibold'>More</p>
                    <ChevronDown className='h-4 w-4' />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  {overflowTabs.map((item) => (
                    <SelectItem key={item.value} value={item.value}>
                      {item.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </TabsList>
        </Tabs>
      </div>
      <div className='xl:hidden'>
        {/* for mobile screens, use select menu  */}
        <Select onValueChange={onTabChange} value={activeTab}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {items.map((item) => (
              <SelectGroup key={item.value}>
                <SelectItem value={item.value}>
                  <SelectLabel>{item.label}</SelectLabel>
                </SelectItem>
              </SelectGroup>
            ))}
          </SelectContent>
        </Select>
      </div>
    </>
  );
};

export default PersonActionTabs;
