import CircularProgress from '@/components/custom/circular-progress';
import { ActionItem, Goal } from '@/hooks/generated/__types';
import { decodeHtmlEntities } from '@/lib/utils';
import { Calendar } from 'lucide-react';
import { FieldValues } from 'react-hook-form';

import ActionPlanTaskList from './action-plan-task-list';
import CreateOrEditAGoalModal from './create-or-edit-goal-modal';

interface ActionPlanGoalProps {
  goal: Goal & { actionItems: ActionItem[] };
  onSubmit?: (data: FieldValues) => Promise<void>;
}

const ActionPlanGoal = ({ goal, onSubmit }: ActionPlanGoalProps) => {
  const goalPercentComplete = goal.actionItems.length
    ? Math.round((goal.actionItems.filter((actionItem) => actionItem.doneDate).length / goal.actionItems.length) * 100)
    : 0;

  return (
    <div className='mt-4 flex w-full flex-col justify-center border-b border-gray-200 bg-white px-4 pb-8'>
      <div className='flex w-full items-start gap-6'>
        <div className='flex min-w-[240px] flex-1 shrink basis-5 items-center gap-4'>
          <CircularProgress progress={goalPercentComplete} />
          <div className='my-auto flex flex-col self-stretch text-base leading-6'>
            <h3 className='font-medium text-slate-700'>{decodeHtmlEntities(goal.name)}</h3>
            <div className='flex items-start gap-2 self-start text-slate-600'>
              <Calendar className='h-5 w-5' />
              <time>{goal.dueDate?.toLocaleDateString() ?? ''}</time>
            </div>
          </div>
        </div>
        <CreateOrEditAGoalModal goal={goal} onSubmit={onSubmit} />
      </div>
      <p className='mt-6 text-base leading-6 text-slate-600'>{decodeHtmlEntities(goal.description)}</p>
      <ActionPlanTaskList actionItems={goal.actionItems} />
    </div>
  );
};

export default ActionPlanGoal;
