'use client';

import { useCoordinatorProfileContext } from '@/app/portal/context/coordinator-profile-context';
import GenericProfileHeader, { DEFAULT_AVATAR } from '@/components/custom/generic-profile-header';
import { useParams, usePathname, useRouter } from 'next/navigation';
import React from 'react';

import PersonActionTabs from '../person-action-tabs';

type TabValue = 'overview' | 'connection-log';

const CoordinatorProfileWrapper = ({
  viewId = 'overview',
  children,
}: {
  viewId: TabValue;
  children: React.ReactNode;
}) => {
  const params = useParams();
  const router = useRouter();
  const pathname = usePathname();
  const coordinatorId = params.id as string;
  const { coordinatorProfile, isLoading, fetchCoordinator } = useCoordinatorProfileContext();

  React.useEffect(() => {
    if (coordinatorId) {
      fetchCoordinator(coordinatorId);
    }
  }, [coordinatorId]);

  const navigateToTab = (tab: string): void => {
    const basePath = pathname.split('/overview')[0].split('/connection-log')[0];
    const tabRoute = tab === 'overview' ? '' : `/${tab}`;
    router.push(`${basePath}${tabRoute}`);
  };
  const COORDINATOR_PROFILE_TABS = [
    { label: 'Overview', value: 'overview' },
    { label: 'Connection Log', value: 'connection-log' },
  ];

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!coordinatorProfile) {
    return <div>No coordinator profile found</div>;
  }

  return (
    <div className='flex flex-col overflow-hidden pb-12'>
      <GenericProfileHeader
        photoUrl={coordinatorProfile.photoUrl || DEFAULT_AVATAR}
        firstName={coordinatorProfile.firstName ?? ''}
        lastName={coordinatorProfile.lastName ?? ''}
        role='Coordinator'
        breadcrumbItems={[
          { label: 'People', href: '/people' },
          { label: 'Coordinators', href: '/people/coordinators' },
        ]}
      />

      <main className='mt-8 flex w-full flex-col max-md:max-w-full'>
        <div className='flex w-full flex-col px-8 max-md:max-w-full max-md:px-5 max-sm:px-1'>
          <PersonActionTabs items={COORDINATOR_PROFILE_TABS} activeTab={viewId} onTabChange={navigateToTab} />
          {children}
        </div>
      </main>
    </div>
  );
};

export default CoordinatorProfileWrapper;
