'use client';

import {
  editAdvocateBackgroundCheckConfig,
  editAdvocateBackgroundCheckFormSchema,
} from '@/app/portal/lib/advocates/edit-advocate-background-check.config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { useFindFirstAdvocateOnboarding, useUpdateAdvocateOnboarding } from '@/hooks/generated';
import { useToast } from '@/hooks/use-toast';
import { useFindAdvocate } from '@/hooks/useFindAdvocate';
import { useParams } from 'next/navigation';
import React, { useState } from 'react';
import { FieldValues } from 'react-hook-form';

import AdvocateBasicInfoWrapper from '../../../../_components/advocates/advocate-basic-info-wrapper';
import AdvocateProfileWrapper from '../../../../_components/advocates/advocate-profile-wrapper';

const BackgroundCheckPage = () => {
  const params = useParams();
  const advocateId = params.id as string;
  const { toast } = useToast();
  const { advocate, isLoading } = useFindAdvocate(advocateId);
  const { data: advocateOnboarding } = useFindFirstAdvocateOnboarding({
    where: {
      userId: advocateId,
    },
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { mutateAsync: updateAdvocateOnboarding } = useUpdateAdvocateOnboarding();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!advocate) {
    return <div>No data found</div>;
  }

  const handleSubmit = async (data: FieldValues) => {
    setIsSubmitting(true);
    try {
      await updateAdvocateOnboarding({
        where: { userId: advocateId },
        data: {
          backgroundCheckCompleted: data.backgroundCheck === 'yes',
        },
      });
      toast({
        title: 'Success',
        description: 'background check complete updated successfully.',
      });
      setIsSubmitting(false);
    } catch (error) {
      console.error('Error updating background check complete:', error);
      toast({
        title: 'Error',
        description: 'Failed to update background check complete. Please try again.',
        variant: 'destructive',
      });
      setIsSubmitting(false);
    }
  };

  const defaultValues = {
    backgroundCheck: advocateOnboarding?.backgroundCheckCompleted ? 'yes' : 'no',
  };

  return (
    <AdvocateProfileWrapper viewId='background-check' advocateId={advocateId}>
      <AdvocateBasicInfoWrapper advocateId={advocateId} currentTab='background-check'>
        <div className='max-w-[600px] gap-4 rounded-lg border border-emaBorderSecondary bg-white p-4'>
          <FormFactory
            fields={editAdvocateBackgroundCheckConfig}
            schema={editAdvocateBackgroundCheckFormSchema}
            onSubmit={handleSubmit}
            defaultValues={defaultValues}
            formWrapperClassName='flex flex-col w-full'
            formFieldElClass='w-full'
            actionButtonsComponent={
              <div className='flex justify-end gap-2'>
                <Button variant='outline'>Cancel</Button>
                <Button variant='default' type='submit' disabled={isSubmitting}>
                  {isSubmitting ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            }
          />
        </div>
      </AdvocateBasicInfoWrapper>
    </AdvocateProfileWrapper>
  );
};

export default BackgroundCheckPage;
