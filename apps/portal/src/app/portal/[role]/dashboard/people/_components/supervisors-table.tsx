'use client';

import GenericTable from '@/components/custom/generic-table';
import { Badge } from '@/components/ui/badge';
import { TableCell, TableRow } from '@/components/ui/table';
import { useSupervisors } from '@/hooks/useSupervisors';
import type { User } from '@/types/schemas/user';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { mapLanguageCodesToOptions } from '../_utils/language-utils';

interface SupervisorsTableProps {
  isLoading?: boolean;
}

const SupervisorsTable = ({ isLoading: initialLoading = false }: SupervisorsTableProps) => {
  const [data, setData] = useState<User[]>([]);
  const { supervisors, isLoading } = useSupervisors();
  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    if (supervisors) {
      const formattedSupervisors = supervisors.map((coordinator) => ({
        ...coordinator,
        languages_c: mapLanguageCodesToOptions(coordinator.languages_c).filter(
          (option): option is { label: string; value: string } => option !== undefined,
        ),
      }));
      setData(formattedSupervisors);
    }
  }, [supervisors]);

  const headers = ['First Name', 'Last Name', 'User Type'];

  const renderRow = (supervisor: User) => (
    <TableRow
      className='cursor-pointer'
      onClick={() => {
        router.push(`${pathname}/${supervisor.id}`);
      }}
    >
      <TableCell>{supervisor.firstName}</TableCell>
      <TableCell>{supervisor.lastName}</TableCell>
      <TableCell>Supervisor</TableCell>
    </TableRow>
  );

  const renderMobileItem = (supervisor: User) => (
    <div className='cursor-pointer' onClick={() => router.push(`${pathname}/${supervisor.id}`)}>
      <div className='space-y-2'>
        <div className='flex justify-between'>
          <span className='font-medium'>
            {supervisor.firstName} {supervisor.lastName}
          </span>
          <Badge variant='info1'>Supervisor</Badge>
        </div>
      </div>
    </div>
  );

  return (
    <GenericTable
      data={data}
      isLoading={isLoading || initialLoading}
      headers={headers}
      rowRenderer={renderRow}
      mobileRenderer={renderMobileItem}
      emptyMessage='No supervisors found.'
      shouldUseCustomRowComponent={true}
    />
  );
};

export default SupervisorsTable;
