'use client';

import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { Di<PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import type { EventRespondent } from '@/hooks/generated/__types';
import { useUpdateEventRespondent } from '@/hooks/generated/event-respondent';
import { toast } from '@/hooks/use-toast';
import React from 'react';
import { FieldValues, SubmitHandler } from 'react-hook-form';

import { editAttendeeFormConfig, editAttendeeFormSchema } from '../_lib/edit-attendee-form.config';

interface EditAttendeeModalProps {
  attendee:
    | (EventRespondent & {
        user: {
          firstName: string;
          lastName: string;
        } | null;
        mom: {
          first_name: string;
          last_name: string;
        } | null;
      })
    | null;
  onClose: () => void;
}

const EditAttendeeModal = ({ attendee, onClose }: EditAttendeeModalProps) => {
  const updateEventRespondent = useUpdateEventRespondent();

  const handleEditAttendee: SubmitHandler<FieldValues> = async (formData) => {
    if (!attendee) return;

    try {
      const isLinkedAttendee = !!(attendee.user || attendee.mom);

      await updateEventRespondent.mutateAsync({
        where: { id: attendee.id },
        data: {
          needsTransport: formData.needsTransport === 'yes',
          childrenCount: Number(formData.childrenCount),
          address: formData.address,
          phone_number: formData.phone,
          email: formData.email,
          name: !isLinkedAttendee ? formData.name : undefined,
        },
      });

      toast({
        title: 'Success',
        description: 'The attendee information has been successfully updated',
        variant: 'success',
      });
      onClose();
    } catch (error) {
      console.error(error);
      toast({
        title: 'Error',
        description: 'There was an error updating the attendee information. Please try again.',
        variant: 'destructive',
      });
    }
  };

  if (!attendee) return null;

  const attendeeName = attendee.user
    ? `${attendee.user.firstName} ${attendee.user.lastName}`
    : attendee.mom
      ? `${attendee.mom.first_name} ${attendee.mom.last_name}`
      : attendee.name || 'Unknown Attendee';

  return (
    <Dialog open={!!attendee} onOpenChange={(open) => !open && onClose()}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Attendee</DialogTitle>
        </DialogHeader>

        <FormFactory
          fields={editAttendeeFormConfig(attendee)}
          schema={editAttendeeFormSchema}
          onSubmit={handleEditAttendee}
          defaultValues={{
            name: attendeeName,
            email: attendee.email || '',
            phone: attendee.phone_number || '',
            address: attendee.address || '',
            needsTransport: attendee.needsTransport ? 'yes' : 'no',
            childrenCount: attendee.childrenCount,
          }}
          actionButtonsComponent={
            <DialogFooter>
              <Button variant='outline' onClick={onClose}>
                Cancel
              </Button>
              <Button type='submit' disabled={updateEventRespondent.isPending}>
                {updateEventRespondent.isPending ? 'Saving...' : 'Save Changes'}
              </Button>
            </DialogFooter>
          }
        />
      </DialogContent>
    </Dialog>
  );
};

export default EditAttendeeModal;
