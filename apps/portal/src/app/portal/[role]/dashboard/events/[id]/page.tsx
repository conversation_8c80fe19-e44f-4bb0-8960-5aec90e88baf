'use client';

import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import { Button } from '@/components/ui/button';
import { Toaster } from '@/components/ui/toaster';
import { useFindUniqueEvent } from '@/hooks/generated/event';
import { useUpdateEvent } from '@/hooks/generated/event';
import { toast } from '@/hooks/use-toast';
import { dateFromDateAndTimeFields } from '@suiteapi/models';
import { AlertCircle, ArrowLeft, Loader2 } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

import EventAttendees from '../_components/event-attendees';
import EventDetailWrapper from '../_components/event-detail-wrapper';
import EventForm from '../_components/event-form';
import { WorkshopEvent } from '../_components/events-table';

interface EventFormData {
  name: string;
  description?: string;
  format: 'virtual' | 'in-person';
  timeGroup: {
    dateStart: string;
    startTime: string;
    endTime: string;
  };
  location?: string;
  joinUrl?: string;
  maxAttendees?: number;
}

const EventPage = ({ params }: { params: { id: string; role: string } }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const activeTab = searchParams.get('tab') || 'basic-info';
  const { profile } = useDashboardContext();
  const updateEvent = useUpdateEvent();

  const {
    data: event,
    isLoading: isLoadingEvent,
    error,
  } = useFindUniqueEvent({
    where: { id: params.id },
  });

  useEffect(() => {
    // Remove empty effect since we're not using isLoading
  }, [event]);

  if (isLoadingEvent) {
    return (
      <div className='min-h-screen w-full bg-emaBgPage'>
        <div className='mx-auto max-w-[1200px] px-6 py-12'>
          <div className='mb-4 flex items-center'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => router.push(`/portal/${params.role}/dashboard/events`)}
              className='-ml-4 flex items-center gap-2 text-xs'
            >
              <ArrowLeft className='h-4 w-4' />
              Back to Events
            </Button>
          </div>

          <div className='flex flex-col gap-6'>
            <div className='flex flex-col gap-2'>
              <h1 className='text-2xl font-semibold text-emaTextPrimary'>Event Details</h1>
              <p className='text-sm text-emaTextQuaternary'>View and manage event information</p>
            </div>

            <div className='border-emaBorder rounded-lg border bg-white p-6'>
              <div className='flex flex-col items-center justify-center py-12 text-center'>
                <Loader2 className='mb-4 h-6 w-6 animate-spin' />
                <h2 className='mb-2 text-xl font-semibold text-emaTextPrimary'>Loading Event Details</h2>
                <p className='max-w-md text-emaTextQuaternary'>Please wait while we load the event information...</p>
              </div>
            </div>
          </div>
        </div>
        <Toaster />
      </div>
    );
  }

  if (error || !event) {
    return (
      <div className='min-h-screen w-full bg-emaBgPage'>
        <div className='mx-auto max-w-[1200px] px-6 py-12'>
          <div className='mb-4 flex items-center'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => router.push(`/portal/${params.role}/dashboard/events`)}
              className='-ml-4 flex items-center gap-2 text-xs'
            >
              <ArrowLeft className='h-4 w-4' />
              Back to Events
            </Button>
          </div>

          <div className='flex flex-col gap-6'>
            <div className='flex flex-col gap-2'>
              <h1 className='text-2xl font-semibold text-emaTextPrimary'>Event Details</h1>
              <p className='text-sm text-emaTextQuaternary'>View and manage event information</p>
            </div>

            <div className='border-emaBorder rounded-lg border bg-white p-6'>
              <div className='flex flex-col items-center justify-center py-12 text-center'>
                <div className='bg-emaError/10 mb-4 flex h-12 w-12 items-center justify-center rounded-full'>
                  <AlertCircle className='text-emaError h-6 w-6' />
                </div>
                <h2 className='mb-2 text-xl font-semibold text-emaTextPrimary'>Unable to Load Event</h2>
                <p className='max-w-md text-emaTextQuaternary'>
                  We couldn&apos;t load the event details. This might be because the event doesn&apos;t exist or you
                  don&apos;t have permission to view it.
                </p>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => router.push(`/portal/${params.role}/dashboard/events`)}
                  className='mt-6'
                >
                  Return to Events
                </Button>
              </div>
            </div>
          </div>
        </div>
        <Toaster />
      </div>
    );
  }

  const formattedEvent: WorkshopEvent = {
    id: event.id,
    name: event.event_title,
    event_title: event.event_title,
    description: event.description || '',
    format: event.join_url ? 'virtual' : 'in-person',
    date: event.start_date
      ? new Date(event.start_date).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        })
      : '',
    startTime: event.start_date
      ? (() => {
          const date = new Date(event.start_date);
          const dateStr = date.toISOString().split('T')[0];
          const timeStr = date.toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit' });
          return dateFromDateAndTimeFields(dateStr, timeStr).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true,
          });
        })()
      : '',
    endTime: event.end_date
      ? (() => {
          const date = new Date(event.end_date);
          const dateStr = date.toISOString().split('T')[0];
          const timeStr = date.toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit' });
          return dateFromDateAndTimeFields(dateStr, timeStr).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true,
          });
        })()
      : '',
    location: event.location || '',
    join_url: event.join_url || '',
    max_attendees: event.max_attendees || 100,
    start_date: event.start_date || new Date(),
    end_date: event.end_date || new Date(),
  };

  const handleSaveEventDetails = async (formData: EventFormData) => {
    try {
      // Validate required fields
      if (
        !formData.name ||
        !formData.timeGroup.dateStart ||
        !formData.timeGroup.startTime ||
        !formData.timeGroup.endTime
      ) {
        throw new Error('Missing required fields');
      }

      // Use dateFromDateAndTimeFields to properly combine date and time
      const startDate = dateFromDateAndTimeFields(formData.timeGroup.dateStart, formData.timeGroup.startTime);
      const endDate = dateFromDateAndTimeFields(formData.timeGroup.dateStart, formData.timeGroup.endTime);

      // Validate that end date is after start date
      if (endDate <= startDate) {
        throw new Error('End time must be after start time');
      }

      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        throw new Error('Invalid date format');
      }

      // Create event data
      const eventData = {
        event_title: formData.name,
        description: formData.description || '',
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
        location: formData.location || '',
        join_url: formData.format === 'virtual' ? formData.joinUrl : null,
        max_attendees: formData.maxAttendees || 100,
      };

      const result = await updateEvent.mutateAsync({
        where: { id: params.id },
        data: eventData,
      });

      if (result) {
        toast({
          title: 'Success',
          description: 'The event details have been successfully updated',
          variant: 'success',
        });
      } else {
        throw new Error('Failed to update event - no result returned');
      }
    } catch (error) {
      console.error('Error in handleSaveEventDetails:', error);
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'There was an error updating the event details. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className='min-h-screen w-full bg-emaBgPage'>
      <div className='mx-auto max-w-[1200px] px-6 py-12'>
        <div className='mb-4 flex items-center'>
          <Button
            variant='ghost'
            size='sm'
            onClick={() => router.push(`/portal/${params.role}/dashboard/events`)}
            className='-ml-4 flex items-center gap-2 text-xs'
          >
            <ArrowLeft className='h-4 w-4' />
            Back to Events
          </Button>
        </div>

        <div className='flex flex-col gap-6'>
          <div className='flex flex-col gap-2'>
            <h1 className='text-2xl font-semibold text-emaTextPrimary'>Event Details</h1>
            <p className='text-sm text-emaTextQuaternary'>View and manage event information</p>
          </div>

          <EventDetailWrapper eventId={params.id} currentTab={activeTab}>
            <div className='flex flex-col gap-6'>
              {activeTab === 'basic-info' && (
                <div className='flex flex-col gap-6'>
                  <EventForm
                    event={formattedEvent}
                    onSubmit={handleSaveEventDetails}
                    isSubmitting={updateEvent.isPending}
                    portalRole={profile.portalRole}
                  />
                </div>
              )}

              {activeTab === 'attendees' && (
                <div className='flex flex-col gap-6'>
                  <EventAttendees event={formattedEvent} />
                </div>
              )}
            </div>
          </EventDetailWrapper>
        </div>
      </div>
      <Toaster />
    </div>
  );
};

export default EventPage;
