import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export type Attendee = {
  id: string;
  email: string | null;
  phone: string | null;
  firstName: string;
  lastName: string;
};

export const addAttendeeFormSchema = yup.object().shape({
  attendeeNameOrId: yup
    .string()
    .required('Attendee is required')
    .test('custom-format', "Enter attendee's first and last name or select from the list", (value) => {
      if (!value) return false;

      const idPattern = /^(mom|user)-[a-zA-Z0-9-]+$/; // mom or user id
      const namePattern = /^[\p{L}]+(?: [\p{L}]+)*$/u; // One or more words, Unicode letters

      const idTest = idPattern.test(value);
      const nameTest = namePattern.test(value);

      return idTest || nameTest;
    }),
  email: yup.string().required('Email is required'),
  phone: yup
    .string()
    .required('Phone number is required')
    .test('phone', 'Please enter a valid phone number (e.g., (*************)', (value) => {
      if (!value) return false; // Required field, so empty is invalid
      // Strip any non-numeric characters
      const numbers = value.replace(/\D/g, '');
      // Accept either a raw 10-digit number or properly formatted number
      return numbers.length === 10 || /^\(\d{3}\) \d{3}-\d{4}$/.test(value);
    })
    .transform((value) => {
      if (!value) return value;
      // Strip any non-numeric characters
      const numbers = value.replace(/\D/g, '');
      // If it's a 10-digit number, format it
      if (numbers.length === 10) {
        return `(${numbers.slice(0, 3)}) ${numbers.slice(3, 6)}-${numbers.slice(6)}`;
      }
      return value;
    }),
  needsTransportation: yup.string().oneOf(['yes', 'no']).required('Transportation preference is required'),
  childrenCount: yup
    .number()
    .min(0, 'Number of children cannot be negative')
    .required('Number of children is required'),
});

export type AddAttendeeFormData = yup.InferType<typeof addAttendeeFormSchema>;

export const addAttendeeFormConfig = (attendees: Attendee[]): FieldConfig[] => [
  {
    name: 'attendeeNameOrId',
    label: 'Attendee',
    type: 'combo-box',
    placeholder: 'Select or enter attendee name',
    options: attendees.map((attendee) => ({
      label: `${attendee.firstName} ${attendee.lastName} (${attendee.email || 'No email'})`,
      value: attendee.id,
    })),
    defaultValue: '',
    customOptionLabel: 'Add ',
  },
  {
    name: 'email',
    label: 'Email',
    type: 'email',
    placeholder: 'Enter email address',
  },
  {
    name: 'phone',
    label: 'Phone',
    type: 'tel',
    placeholder: 'Enter phone number (e.g., (*************)',
  },
  {
    name: 'needsTransportation',
    label: 'Needs Transportation',
    type: 'button-radio-group',
    options: [
      { value: 'yes', label: 'Yes' },
      { value: 'no', label: 'No' },
    ],
    defaultValue: 'no',
  },
  {
    name: 'childrenCount',
    label: 'Number of Children',
    type: 'number',
    placeholder: '1',
    defaultValue: 1,
  },
];
