import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import { useRouter } from 'next/navigation';
import React from 'react';

const tabs = [
  { navLabel: 'Basic Info', title: 'basic-info' },
  { navLabel: 'Attendees', title: 'attendees' },
];

interface EventDetailWrapperProps {
  eventId: string;
  currentTab: string;
  children?: React.ReactNode;
}

const EventDetailWrapper = ({ eventId, currentTab, children }: EventDetailWrapperProps) => {
  const router = useRouter();
  const { profile } = useDashboardContext();

  const navigateToTab = (tab: string): void => {
    router.push(`/portal/${profile.portalRole}/dashboard/events/${eventId}?tab=${tab}`);
  };

  return (
    <div className='mt-10 flex max-md:flex-col'>
      <nav className='mb-4 hidden w-full flex-col text-sm font-semibold leading-5 text-emaTextPrimary md:mb-0 md:flex md:w-[240px]'>
        {tabs.map((tab, index) => (
          <div
            key={tab.title}
            onClick={() => navigateToTab(tab.title)}
            className={`cursor-pointer gap-2 self-stretch px-3 py-2 ${index === 0 ? '' : 'mt-1'} w-full ${
              currentTab === tab.title ? 'border-l-2 border-l-emaBrandSecondary' : 'text-emaTextQuaternary'
            } min-h-[36px]`}
          >
            {tab.navLabel}
          </div>
        ))}
      </nav>
      <div className='w-full'>{children}</div>
    </div>
  );
};

export default EventDetailWrapper;
