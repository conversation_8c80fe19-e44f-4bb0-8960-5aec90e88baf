import { FormFactoryRef } from '@/app/portal/types';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useCreateEventRespondent } from '@/hooks/generated/event-respondent';
import { useCreateNotification } from '@/hooks/generated/notification';
import { toast } from '@/hooks/use-toast';
import { SendIcon } from 'lucide-react';
import { useRef, useState } from 'react';
import { FieldValues } from 'react-hook-form';
import { ValidationError } from 'yup';

import { sendInviteFormConfig, sendInviteFormSchema } from '../_lib/send-event-invite-form.config';
import { getInviteType, useAvailableAttendees } from './useAvailableAttendees';

interface Props {
  eventId: string;
  attendees?: { user_id: string | null; mom_id: string | null; email?: string | null }[];
  eventTitle: string;
  eventDate: string;
  eventStartTime: string;
  eventEndTime: string;
  eventLocation?: string;
  eventJoinUrl?: string;
}

const SendEventInviteModal = ({
  eventId,
  attendees,
  eventTitle,
  eventDate,
  eventStartTime,
  eventEndTime,
  eventLocation,
  eventJoinUrl,
}: Props) => {
  const lastAttendeeValue = useRef('');
  const [open, setOpen] = useState(false);
  const formRef = useRef<FormFactoryRef>(null);
  const createEventRespondent = useCreateEventRespondent();
  const createNotification = useCreateNotification();
  const availableAttendees = useAvailableAttendees({ attendees });

  const handleValidationError = (error: unknown) => {
    if (error instanceof ValidationError) {
      // Display the first validation error
      toast({
        title: 'Validation Error',
        description: error.errors[0],
        variant: 'destructive',
      });
      return;
    }

    toast({
      title: 'Error',
      description: error instanceof Error ? error.message : 'An unexpected error occurred',
      variant: 'destructive',
    });
  };

  const sendEventInvite = async (formData: FieldValues) => {
    try {
      // Make sure all strings are trimmed
      const trimmedData = Object.keys(formData).reduce<Record<string, unknown>>(
        (acc, key) => ({
          ...acc,
          [key]: typeof formData[key] === 'string' ? formData[key].trim() : formData[key],
        }),
        {},
      );

      // Phone number standardization is now handled in the form validation itself

      // Validate the form data
      const validatedData = await sendInviteFormSchema.validate(trimmedData, {
        abortEarly: false,
        strict: true,
        stripUnknown: true,
      });

      // Email duplication check
      const emailExists = attendees?.some(
        (attendee) => attendee.email?.toLowerCase() === validatedData.email.toLowerCase(),
      );

      if (emailExists) {
        throw new Error('Someone with this email address has already been invited to the event.');
      }

      const { userId, momId, name } = getInviteType(
        validatedData.inviteType === 'existing' ? validatedData.existingAttendeeId! : validatedData.customAttendeeName!,
        availableAttendees,
      );

      // Create event respondent
      const eventRespondent = await createEventRespondent.mutateAsync({
        data: {
          event: {
            connect: { id: eventId },
          },
          user: userId ? { connect: { id: userId } } : undefined,
          mom: momId ? { connect: { id: momId } } : undefined,
          name: validatedData.inviteType === 'custom' ? validatedData.customAttendeeName : name,
          email: validatedData.email,
          phone_number: validatedData.phone || '',
          needsTransport: validatedData.needsTransportation === 'yes',
          childrenCount: validatedData.childrenCount,
          hasBeenInvited: true,
          didCheckin: false,
          didRsvp: false,
        },
      });

      if (eventRespondent) {
        // Create notification
        await createNotification.mutateAsync({
          data: {
            template: 'event_registration',
            status: 'pending',
            event_respondent: {
              connect: {
                id: eventRespondent.id,
              },
            },
            template_params: {
              eventTitle,
              eventDate,
              eventTime: `${eventStartTime}-${eventEndTime}`,
              eventLocation: eventLocation || 'Virtual Event',
              eventJoinUrl,
              recipientEmail: validatedData.email,
              recipientPhone: validatedData.phone || '',
              recipientFirstName: name.split(' ')[0],
              recipientLastName: name.split(' ').slice(1).join(' '),
            },
          },
        });

        toast({
          title: 'Success',
          description: `The event invitation has been successfully sent to ${validatedData.email}`,
          variant: 'success',
        });

        setOpen(false);
      }
    } catch (error) {
      console.error('Error sending invite:', error);
      handleValidationError(error);
    }
  };

  return (
    <>
      <Button variant='outline' onClick={() => setOpen(true)} size='sm'>
        <SendIcon className='mr-2 h-4 w-4' /> Send Invite
      </Button>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent aria-describedby={undefined}>
          <DialogHeader>
            <DialogTitle>Send Invite</DialogTitle>
          </DialogHeader>

          <div className='-mt-4'>
            <FormFactory
              ref={formRef}
              fields={sendInviteFormConfig(availableAttendees)}
              schema={sendInviteFormSchema}
              onSubmit={sendEventInvite}
              onChange={(values: FieldValues) => {
                if (values.existingAttendeeId !== lastAttendeeValue.current) {
                  lastAttendeeValue.current = values.existingAttendeeId;

                  const attendee = availableAttendees.find((a) => a.id === values.existingAttendeeId);
                  if (attendee) {
                    formRef.current?.form.setValue('phone', attendee.phone || '');
                    formRef.current?.form.setValue('email', attendee.email || '');
                  }
                }
              }}
              actionButtonsComponent={
                <DialogFooter>
                  <Button variant='outline' onClick={() => setOpen(false)}>
                    Cancel
                  </Button>
                  <Button type='submit' disabled={createEventRespondent.isPending || createNotification.isPending}>
                    {createEventRespondent.isPending ? 'Sending...' : 'Send Invite'}
                  </Button>
                </DialogFooter>
              }
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default SendEventInviteModal;
