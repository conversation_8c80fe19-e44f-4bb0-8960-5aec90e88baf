import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useCreateEvent } from '@/hooks/generated/event';
import { toast } from '@/hooks/use-toast';
import { dateFromDateAndTimeFields } from '@suiteapi/models';
import { PlusIcon } from 'lucide-react';
import { useState } from 'react';
import { FieldValues } from 'react-hook-form';
import { InferType } from 'yup';

import { createEventFormConfig } from '../_lib/create-event-form.config';
import { createEventFormSchema } from '../_lib/create-event-form.config';

type CreateEventFormData = InferType<typeof createEventFormSchema>;

const CreateEventModal = () => {
  const [open, setOpen] = useState(false);
  const createEvent = useCreateEvent();

  // Default form values with nested structure
  const defaultValues = {
    name: '',
    description: '',
    format: 'virtual',
    timeGroup: {
      dateStart: new Date(new Date().setDate(new Date().getDate() + 7)).toISOString(),
      startTime: '09:00',
      endTime: '10:00',
    },
    location: '',
    joinUrl: '',
    maxAttendees: 100,
  };

  const handleSubmit = async (data: FieldValues) => {
    try {
      const validatedData = createEventFormSchema.cast(data) as CreateEventFormData;

      const startDate = dateFromDateAndTimeFields(validatedData.timeGroup.dateStart, validatedData.timeGroup.startTime);
      const endDate = dateFromDateAndTimeFields(validatedData.timeGroup.dateStart, validatedData.timeGroup.endTime);

      if (endDate <= startDate) {
        throw new Error('End time must be after start time');
      }

      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        throw new Error('Invalid date format');
      }

      await createEvent.mutateAsync({
        data: {
          event_title: validatedData.name,
          description: validatedData.description,
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
          location: validatedData.location,
          join_url: validatedData.format === 'virtual' ? validatedData.joinUrl : null,
          max_attendees: validatedData.maxAttendees,
        },
      });

      toast({
        title: 'Success',
        description: 'The event has been successfully created',
        variant: 'success',
      });

      setOpen(false);
    } catch (error) {
      console.error('Error creating event:', error);
      toast({
        title: 'Error',
        description: 'There was an error creating the event. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <>
      <Button onClick={() => setOpen(true)} className='bg-emaBrandSecondary text-white hover:bg-emaBrandSecondary/90'>
        <PlusIcon className='mr-2 h-4 w-4' />
        Create Event
      </Button>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className='max-w-3xl'>
          <DialogHeader>
            <DialogTitle>Create Event</DialogTitle>
          </DialogHeader>
          <FormFactory
            fields={createEventFormConfig}
            schema={createEventFormSchema}
            onSubmit={handleSubmit}
            defaultValues={defaultValues}
            actionButtonsComponent={
              <DialogFooter>
                <Button variant='outline' onClick={() => setOpen(false)}>
                  Cancel
                </Button>
                <Button type='submit' disabled={createEvent.isPending}>
                  {createEvent.isPending ? 'Creating Event...' : 'Create Event'}
                </Button>
              </DialogFooter>
            }
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default CreateEventModal;
