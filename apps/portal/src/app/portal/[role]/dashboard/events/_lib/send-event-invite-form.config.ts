import { FieldConfig, FormValues } from '@/types/form-field';
import * as yup from 'yup';

export type Attendee = {
  id: string;
  email: string | null;
  phone: string | null;
  firstName: string;
  lastName: string;
};

export const sendInviteFormSchema = yup.object().shape({
  inviteType: yup.string().oneOf(['existing', 'custom']).required('Invite type is required'),

  // Fields for existing attendee selection
  existingAttendeeId: yup.string().when('inviteType', {
    is: 'existing',
    then: (schema) => schema.required('Please select an existing attendee'),
    otherwise: (schema) => schema.optional(),
  }),

  // Fields for custom invite
  customAttendeeName: yup.string().when('inviteType', {
    is: 'custom',
    then: (schema) =>
      schema
        .required('Name is required')
        .matches(/^[\p{L}]+(?: [\p{L}]+)+$/u, 'Please enter both first and last name')
        .min(4, 'Name must be at least 4 characters long'),
    otherwise: (schema) => schema.optional(),
  }),

  // Required for both custom invites and existing attendees
  email: yup.string().email('Please enter a valid email address').required('Email is required'),

  // Phone is optional but must be properly formatted if provided
  phone: yup
    .string()
    .test('phone', 'Please enter a valid phone number (e.g., (*************)', (value) => {
      if (!value) return true; // Handle empty/null case

      // Remove +1 prefix if present
      let testValue = value;
      if (testValue.startsWith('+1')) {
        testValue = testValue.substring(2);
      }

      // Strip any non-numeric characters
      const numbers = testValue.replace(/\D/g, '');

      // Accept either a raw 10-digit number or properly formatted number
      return numbers.length === 10 || /^\(\d{3}\) \d{3}-\d{4}$/.test(testValue);
    })
    .transform((value) => {
      if (!value) return value;

      // Remove +1 prefix if present
      if (value.startsWith('+1')) {
        value = value.substring(2);
      }

      // Strip any non-numeric characters
      const numbers = value.replace(/\D/g, '');

      // If it's a 10-digit number, format it
      if (numbers.length === 10) {
        return `(${numbers.slice(0, 3)}) ${numbers.slice(3, 6)}-${numbers.slice(6)}`;
      }
      return value;
    })
    .nullable(),

  needsTransportation: yup
    .string()
    .oneOf(['yes', 'no'], 'Please select yes or no')
    .required('Please indicate if transportation is needed'),

  childrenCount: yup
    .number()
    .typeError('Please enter a number')
    .min(0, 'Number of children cannot be negative')
    .max(10, 'Maximum of 10 children allowed')
    .required('Number of children is required'),
});

export type SendInviteFormData = yup.InferType<typeof sendInviteFormSchema>;

export const sendInviteFormConfig = (attendees: Attendee[]): FieldConfig[] => [
  {
    name: 'inviteType',
    label: 'Invite Type',
    type: 'button-radio-group',
    options: [
      { value: 'existing', label: 'Existing User' },
      { value: 'custom', label: 'Custom Invite' },
    ],
    defaultValue: 'existing',
  },
  {
    name: 'existingAttendeeId',
    label: 'Attendee',
    type: 'combo-box',
    placeholder: 'Select or enter attendee name',
    options: attendees.map((attendee) => ({
      label: `${attendee.firstName} ${attendee.lastName} (${attendee.email || 'No email'})`,
      value: attendee.id,
    })),
    defaultValue: '',
    customOptionLabel: 'Invite ',
    hidden: (values?: FormValues) => values?.inviteType === 'custom',
  },
  {
    name: 'customAttendeeName',
    label: 'Attendee Name',
    type: 'text',
    placeholder: 'Enter full name (First Last)',
    defaultValue: '',
    hidden: (values?: FormValues) => values?.inviteType === 'existing',
  },
  {
    name: 'email',
    label: 'Email',
    type: 'email',
    placeholder: 'Enter email address',
    required: true,
  },
  {
    name: 'phone',
    label: 'Phone',
    type: 'tel',
    placeholder: 'Enter phone number (e.g., (*************)',
  },
  {
    name: 'needsTransportation',
    label: 'Needs Transportation',
    type: 'button-radio-group',
    options: [
      { value: 'yes', label: 'Yes' },
      { value: 'no', label: 'No' },
    ],
    defaultValue: 'no',
    required: true,
  },
  {
    name: 'childrenCount',
    label: 'Number of Children',
    type: 'number',
    placeholder: '0',
    defaultValue: 0,
    required: true,
  },
];
