import type { EventRespondent } from '@/hooks/generated/__types';
import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const editAttendeeFormSchema = yup.object().shape({
  name: yup.string().required('Name is required'),
  email: yup.string().email('Invalid email').required('Email is required'),
  phone: yup.string().required('Phone number is required'),
  address: yup.string().required('Address is required'),
  needsTransport: yup.string().oneOf(['yes', 'no']).required('Transportation preference is required'),
  childrenCount: yup
    .number()
    .min(0, 'Number of children cannot be negative')
    .required('Number of children is required'),
});

export const editAttendeeFormConfig = (
  attendee: EventRespondent & {
    user: {
      firstName: string;
      lastName: string;
    } | null;
    mom: {
      first_name: string;
      last_name: string;
    } | null;
  },
): FieldConfig[] => {
  return [
    {
      name: 'name',
      label: 'Name',
      type: 'text',
      placeholder: 'Name',
      defaultValue: attendee.name || '',
    },
    {
      name: 'email',
      label: 'Email',
      type: 'email',
      placeholder: 'Email',
      defaultValue: attendee.email || '',
    },
    {
      name: 'phone',
      label: 'Phone',
      type: 'tel',
      placeholder: 'Phone',
      defaultValue: attendee.phone_number || '',
    },
    {
      name: 'address',
      label: 'Address',
      type: 'text',
      placeholder: 'Address',
      defaultValue: attendee.address || '',
    },
    {
      name: 'needsTransport',
      label: 'Needs Transportation',
      type: 'button-radio-group',
      options: [
        { value: 'yes', label: 'Yes' },
        { value: 'no', label: 'No' },
      ],
      defaultValue: attendee.needsTransport ? 'yes' : 'no',
    },
    {
      name: 'childrenCount',
      label: 'Number of Children',
      type: 'number',
      placeholder: '1',
      defaultValue: attendee.childrenCount || 0,
    },
  ];
};
