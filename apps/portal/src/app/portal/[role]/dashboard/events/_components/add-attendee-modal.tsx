'use client';

import { FormFactoryRef } from '@/app/portal/types';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogFooter, <PERSON><PERSON>Header, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useFindUniqueEvent } from '@/hooks/generated/event';
import { useCreateEventRespondent } from '@/hooks/generated/event-respondent';
import { toast } from '@/hooks/use-toast';
import { PlusIcon } from 'lucide-react';
import { useRef, useState } from 'react';
import { FieldValues } from 'react-hook-form';

import { addAttendeeFormConfig, addAttendeeFormSchema } from '../_lib/add-attendee-form.config';
import { getInviteType, useAvailableAttendees } from './useAvailableAttendees';

const AddAttendeeModal = ({
  eventId,
  attendees,
}: {
  eventId: string;
  attendees: { user_id: string | null; mom_id: string | null }[];
}) => {
  const lastAttendeeValue = useRef('');
  const [open, setOpen] = useState(false);
  const formRef = useRef<FormFactoryRef>(null);
  const createEventRespondent = useCreateEventRespondent();
  const availableAttendees = useAvailableAttendees({ attendees });

  // Fetch event details to get max attendees and current attendees
  const { data: eventData } = useFindUniqueEvent({
    where: { id: eventId },
    include: { eventRespondents: true },
  });

  // Function to calculate total seats taken including children
  const calculateTotalSeatsTaken = () => {
    if (!eventData?.eventRespondents) return 0;
    return eventData.eventRespondents.reduce((total, attendee) => {
      return total + 1 + (attendee.childrenCount || 0);
    }, 0);
  };

  const handleAddAttendee = async (data: FieldValues) => {
    const { userId, momId, name } = getInviteType(data.attendeeNameOrId, availableAttendees);

    try {
      // Check if adding this attendee would exceed the max capacity
      const currentAttendees = calculateTotalSeatsTaken();
      const childrenCount = parseInt(data.childrenCount || '0');
      const registrantCount = 1 + childrenCount; // Person + children
      const maxAttendees = eventData?.max_attendees || 0;

      if (maxAttendees > 0 && currentAttendees + registrantCount > maxAttendees) {
        toast({
          title: 'Capacity Exceeded',
          description: `Adding this attendee (${name || 'Unknown'} + ${childrenCount} children) would exceed the event capacity. 
            Only ${maxAttendees - currentAttendees} seats are available.`,
          variant: 'destructive',
        });
        return;
      }

      // Check if a user or mom is already an attendee
      if (userId && attendees.some((a) => a.user_id === userId)) {
        toast({
          title: 'Duplicate Attendee',
          description: 'This user is already attending the event',
          variant: 'destructive',
        });
        return;
      }

      if (momId && attendees.some((a) => a.mom_id === momId)) {
        toast({
          title: 'Duplicate Attendee',
          description: 'This mom is already attending the event',
          variant: 'destructive',
        });
        return;
      }

      await createEventRespondent.mutateAsync({
        data: {
          event: {
            connect: {
              id: eventId,
            },
          },
          user: userId
            ? {
                connect: {
                  id: userId,
                },
              }
            : undefined,
          mom: momId
            ? {
                connect: {
                  id: momId,
                },
              }
            : undefined,
          name,
          email: data.email,
          phone_number: data.phone,
          needsTransport: data.needsTransportation === 'yes',
          childrenCount: data.childrenCount,
          hasBeenInvited: true,
          didCheckin: true,
          didRsvp: true,
        },
      });

      toast({
        title: 'Success',
        description: 'The attendee has been successfully added to the event',
        variant: 'success',
      });
      setOpen(false);
    } catch (error) {
      console.error(error);
      toast({
        title: 'Error',
        description: 'There was an error adding the attendee to the event. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant='ghost' size='sm' onClick={() => setOpen(true)} className='flex items-center gap-2'>
          <PlusIcon className='h-4 w-4' />
          Quick Add
        </Button>
      </DialogTrigger>
      <DialogContent aria-describedby={undefined}>
        <DialogHeader>
          <DialogTitle>Add Attendee</DialogTitle>
        </DialogHeader>

        <FormFactory
          ref={formRef}
          fields={addAttendeeFormConfig(availableAttendees)}
          schema={addAttendeeFormSchema}
          onSubmit={handleAddAttendee}
          onChange={(values: FieldValues) => {
            if (values.attendeeNameOrId !== lastAttendeeValue.current) {
              lastAttendeeValue.current = values.attendeeNameOrId;

              const attendee = availableAttendees.find((attendee) => attendee.id === values.attendeeNameOrId);

              formRef.current?.form.setValue('phone', attendee?.phone || '');
              formRef.current?.form.setValue('email', attendee?.email || '');
            }
          }}
          actionButtonsComponent={
            <DialogFooter>
              <Button variant='outline' onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button type='submit' disabled={createEventRespondent.isPending}>
                {createEventRespondent.isPending ? 'Adding...' : 'Add Attendee'}
              </Button>
            </DialogFooter>
          }
        />
      </DialogContent>
    </Dialog>
  );
};

export default AddAttendeeModal;
