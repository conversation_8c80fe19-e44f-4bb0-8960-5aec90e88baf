import { FieldConfig, FormValues } from '@/types/form-field';
import * as yup from 'yup';

export const createEventFormSchema = yup.object().shape({
  name: yup.string().required('Event name is required'),
  description: yup.string().required('Event description is required'),
  format: yup.string().oneOf(['virtual', 'in-person']).required('Event format is required'),
  timeGroup: yup.object().shape({
    dateStart: yup.string().required('Start date is required'),
    startTime: yup.string().required('Start time is required'),
    endTime: yup.string().required('End time is required'),
  }),
  location: yup.string().when('format', {
    is: 'in-person',
    then: (schema) => schema.required('Event location is required for in-person events'),
    otherwise: (schema) => schema.nullable(),
  }),
  joinUrl: yup.string().when('format', {
    is: 'virtual',
    then: (schema) =>
      schema
        .required('Join URL is required for virtual events')
        .matches(/^(https?:\/\/)/, 'Join URL must start with http:// or https://')
        .url('Please enter a valid URL (e.g., https://zoom.us/j/1234567890)'),
    otherwise: (schema) => schema.nullable(),
  }),
  maxAttendees: yup
    .number()
    .required('Maximum number of attendees is required')
    .min(1, 'Maximum number of attendees must be at least 1')
    .integer('Maximum number of attendees must be a whole number'),
});

export const createEventFormConfig: FieldConfig[] = [
  {
    name: 'name',
    label: 'Event Name',
    type: 'text',
    placeholder: 'Event Name',
    required: true,
  },
  {
    name: 'description',
    label: 'Event Description',
    type: 'textarea',
    placeholder: 'Enter event description',
    required: true,
  },
  {
    name: 'timeGroup',
    type: 'row-group',
    label: '',
    subFields: [
      {
        name: 'timeGroup.dateStart',
        label: 'Event Start Date',
        type: 'date',
        placeholder: 'Select a date',
        required: true,
        defaultValue: new Date(new Date().setDate(new Date().getDate() + 7)).toISOString(),
      },
      {
        name: 'timeGroup.startTime',
        label: 'Start Time',
        type: 'time',
        placeholder: 'Select a time',
        required: true,
        defaultValue: '09:00',
      },
      {
        name: 'timeGroup.endTime',
        label: 'End Time',
        type: 'time',
        placeholder: 'Select a time',
        required: true,
        defaultValue: '10:00',
      },
    ],
  },
  {
    name: 'format',
    label: 'Event Format',
    type: 'button-radio-group',
    options: [
      { value: 'virtual', label: 'Virtual (Online)' },
      { value: 'in-person', label: 'In Person' },
    ],
    required: true,
    defaultValue: 'virtual',
  },
  {
    name: 'location',
    label: 'Event Location',
    type: 'text',
    placeholder: 'Enter location',
    required: true,
    hidden: (values?: FormValues) => values?.format !== 'in-person',
  },
  {
    name: 'joinUrl',
    label: 'Join URL',
    type: 'text',
    placeholder: 'Enter join URL (e.g., https://zoom.us/j/1234567890)',
    required: true,
    hidden: (values?: FormValues) => values?.format !== 'virtual',
    defaultValue: '',
  },
  {
    name: 'maxAttendees',
    label: 'Maximum Attendees',
    type: 'number',
    placeholder: 'Enter maximum number of attendees',
    required: true,
    defaultValue: 100,
  },
];
