import { useFindManyMom } from '@/hooks/generated/mom';
import { useFindManyUser } from '@/hooks/generated/user';

import { Attendee } from '../_lib/send-event-invite-form.config';

export const useAvailableAttendees = ({
  attendees,
}: {
  attendees?: { user_id: string | null; mom_id: string | null }[];
}) => {
  const { data: users } = useFindManyUser();
  const { data: moms } = useFindManyMom();

  const availableUsers = users?.filter((user) => !attendees?.some((attendee) => attendee.user_id === user.id));

  const availableMoms = moms?.filter((mom) => !attendees?.some((attendee) => attendee.mom_id === mom.id));

  const availableAttendees: Attendee[] = [
    ...(availableUsers?.map((user) => ({
      id: `user-${user.id}`,
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      email: user.email,
      phone: user.phone,
    })) || []),
    ...(availableMoms?.map((mom) => ({
      id: `mom-${mom.id}`,
      firstName: mom.first_name || '',
      lastName: mom.last_name || '',
      email: mom.email1,
      phone: mom.phone_other,
    })) || []),
  ];

  return availableAttendees;
};

export const getInviteType = (selectedId: string, availableAttendees: Attendee[]) => {
  // For custom invites, this will be the full name
  if (selectedId && !selectedId.includes('-')) {
    return {
      userId: null,
      momId: null,
      name: selectedId,
    };
  }

  const selectedAttendee = availableAttendees.find((attendee) => attendee.id === selectedId);

  if (!selectedAttendee) {
    return {
      userId: null,
      momId: null,
      name: selectedId,
    };
  }

  const [type, id] = selectedAttendee.id.split('-');
  return {
    userId: type === 'user' ? id : null,
    momId: type === 'mom' ? id : null,
    name: `${selectedAttendee.firstName} ${selectedAttendee.lastName}`.trim(),
  };
};
