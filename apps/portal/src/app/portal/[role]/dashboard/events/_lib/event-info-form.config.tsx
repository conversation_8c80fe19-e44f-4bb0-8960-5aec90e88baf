import { FieldConfig, FormValues } from '@/types/form-field';
import * as yup from 'yup';

export const eventInfoFormSchema = yup.object().shape({
  name: yup.string().required('Event name is required'),
  description: yup.string().nullable(),
  format: yup.string().oneOf(['virtual', 'in-person']).required('Event format is required'),
  dateStart: yup.string().required('Start date is required'),
  startTime: yup.string().required('Start time is required'),
  endTime: yup.string().required('End time is required'),
  location: yup.string().when('format', {
    is: 'in-person',
    then: (schema) => schema.required('Location is required for in-person events'),
    otherwise: (schema) => schema.optional().nullable(),
  }),
  joinUrl: yup.string().when('format', {
    is: 'virtual',
    then: (schema) =>
      schema
        .required('Join URL is required for virtual events')
        .matches(/^(https?:\/\/)/, 'Join URL must start with http:// or https://')
        .url('Please enter a valid URL (e.g., https://zoom.us/j/1234567890)'),
    otherwise: (schema) => schema.optional().nullable(),
  }),
  maxAttendees: yup
    .number()
    .typeError('Please enter a valid number')
    .required('Maximum attendees is required')
    .min(1, 'Must be at least 1 attendee')
    .transform((value) => (isNaN(value) ? undefined : value)),
});

export const eventInfoFormConfig: FieldConfig[] = [
  {
    name: 'name',
    label: 'Event Name',
    type: 'text',
    placeholder: 'Enter event name',
    required: true,
  },
  {
    name: 'description',
    label: 'Event Description',
    type: 'textarea',
    placeholder: 'Enter event description',
    required: true,
  },
  {
    name: 'format',
    label: 'Event Format',
    type: 'select',
    options: [
      { value: 'virtual', label: 'Virtual' },
      { value: 'in-person', label: 'In-Person' },
    ],
    required: true,
  },
  {
    name: 'timeGroup',
    type: 'row-group',
    label: '',
    subFields: [
      {
        name: 'dateStart',
        label: 'Event Start Date',
        type: 'date',
        placeholder: 'Select start date',
        required: true,
      },
      {
        name: 'startTime',
        label: 'Start Time',
        type: 'time',
        placeholder: 'Select start time',
        required: true,
      },
      {
        name: 'endTime',
        label: 'End Time',
        type: 'time',
        placeholder: 'Select end time',
        required: true,
      },
    ],
  },
  {
    name: 'location',
    label: 'Event Location',
    type: 'text',
    placeholder: 'Enter event location',
    required: true,
    hidden: (values?: FormValues) => values?.format !== 'in-person',
  },
  {
    name: 'joinUrl',
    label: 'Join URL',
    type: 'text',
    placeholder: 'Enter join URL (e.g., https://zoom.us/j/1234567890)',
    required: true,
    hidden: (values?: FormValues) => values?.format !== 'virtual',
  },
  {
    name: 'maxAttendees',
    label: 'Maximum Attendees',
    type: 'number',
    placeholder: 'Enter maximum number of attendees',
    required: true,
  },
];
