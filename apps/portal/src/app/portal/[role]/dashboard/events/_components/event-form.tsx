import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { convertTo24Hour } from '@suiteapi/models';
import { useRouter } from 'next/navigation';
import { FieldValues } from 'react-hook-form';

import { eventInfoFormConfig, eventInfoFormSchema } from '../_lib/event-info-form.config';
import { WorkshopEvent } from './events-table';

interface EventFormData {
  name: string;
  description?: string;
  format: 'virtual' | 'in-person';
  timeGroup: {
    dateStart: string;
    startTime: string;
    endTime: string;
  };
  location?: string;
  joinUrl?: string;
  maxAttendees?: number;
}

interface EventFormProps {
  event: WorkshopEvent;
  onSubmit: (formData: EventFormData) => Promise<void>;
  isSubmitting: boolean;
  portalRole: string;
}

const EventForm = ({ event, onSubmit, isSubmitting, portalRole }: EventFormProps) => {
  const router = useRouter();

  // Transform event data to match form schema
  const transformedEvent = {
    name: event.name || '',
    description: event.description || '',
    format: event.format || 'virtual',
    // Flatten timeGroup fields to match FormFactory's row-group handling
    dateStart: event.start_date
      ? new Date(event.start_date).toISOString().split('T')[0]
      : new Date().toISOString().split('T')[0],
    startTime: event.startTime ? convertTo24Hour(event.startTime) : '09:00',
    endTime: event.endTime ? convertTo24Hour(event.endTime) : '10:00',
    // Handle conditional requirements based on format
    location: event.format === 'in-person' ? event.location || '' : '',
    joinUrl: event.format === 'virtual' ? event.join_url || '' : '',
    maxAttendees: event.max_attendees || 100,
  };

  const handleSubmit = async (data: FieldValues) => {
    try {
      // Validate the data against the schema
      await eventInfoFormSchema.validate(data);

      await onSubmit({
        name: data.name,
        description: data.description,
        format: data.format,
        timeGroup: {
          dateStart: data.dateStart,
          startTime: data.startTime,
          endTime: data.endTime,
        },
        location: data.location,
        joinUrl: data.joinUrl,
        maxAttendees: data.maxAttendees,
      });

      toast({
        title: 'Success',
        description: 'Event details have been saved successfully.',
        variant: 'default',
      });
    } catch (error) {
      console.error('Form validation error:', error);
      toast({
        title: 'Error',
        description: 'There was an error saving the changes. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className='border-emaBorder rounded-lg border bg-white p-6'>
      <FormFactory
        fields={eventInfoFormConfig}
        schema={eventInfoFormSchema}
        onSubmit={handleSubmit}
        defaultValues={transformedEvent}
        actionButtonsComponent={
          <div className='flex justify-end gap-4'>
            <Button type='submit' disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </Button>
            <Button
              onClick={() => router.push(`/portal/${portalRole}/dashboard/events/${event.id}?tab=attendees`)}
              className='bg-emaBrandSecondary text-white hover:bg-emaBrandSecondary/90'
            >
              Next: Attendees
            </Button>
          </div>
        }
      />
    </div>
  );
};

export default EventForm;
