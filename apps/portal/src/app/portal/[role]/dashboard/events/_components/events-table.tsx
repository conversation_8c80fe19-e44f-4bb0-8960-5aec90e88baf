'use client';

import GenericTable from '@/components/custom/generic-table';
import { TableCell } from '@/components/ui/table';
import { TableRow } from '@/components/ui/table';
import { useFindManyEvent } from '@/hooks/generated/event';
import { toast } from '@/hooks/use-toast';
import { Copy } from 'lucide-react';
import { AlertCircle } from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';

import CreateEventModal from './create-event-modal';
import SendEventInviteModal from './send-event-invite-modal';

interface EventRespondent {
  id: string;
  event_id: string;
  user_id: string | null;
  status?: string;
  created_at?: Date;
  updated_at?: Date;
  name?: string | null;
  email?: string | null;
  mom_id?: string | null;
  [key: string]: unknown;
}

export interface WorkshopEvent {
  name: string;
  date?: string;
  startTime?: string;
  endTime?: string;
  attendees?: number;
  format?: string;
  id: string;
  description?: string;
  location?: string;
  status?: string;
  createdAt?: string;
  updatedAt?: string;
  audience?: string;
  max_attendees: number;
  signUpCutOff?: string;
  kidsWelcome?: boolean;
  trainingSession?: boolean;
  eventRespondents?: EventRespondent[];
  event_title: string;
  start_date: Date | null;
  end_date: Date | null;
  join_url: string | null;
  [key: string]: unknown;
}

export default function EventsTable(): React.ReactNode {
  const pathname = usePathname();
  const router = useRouter();

  const {
    data: events,
    isLoading,
    error,
  } = useFindManyEvent({
    include: {
      eventRespondents: true,
    },
  });

  if (error) {
    return (
      <div className='border-emaBorder rounded-lg border bg-white p-6'>
        <div className='flex flex-col items-center justify-center py-12 text-center'>
          <div className='bg-emaError/10 mb-4 flex h-12 w-12 items-center justify-center rounded-full'>
            <AlertCircle className='text-emaError h-6 w-6' />
          </div>
          <h2 className='mb-2 text-xl font-semibold text-emaTextPrimary'>Unable to Load Events</h2>
          <p className='max-w-md text-emaTextQuaternary'>
            We couldn&apos;t load the events list. Please try refreshing the page or contact support if the issue
            persists.
          </p>
        </div>
      </div>
    );
  }

  const formattedEvents =
    events?.map(
      (event) =>
        ({
          id: event.id,
          name: event.event_title,
          description: event.description || '',
          format: event.join_url ? 'Virtual' : 'In Person',
          date: event.start_date ? new Date(event.start_date).toLocaleDateString() : '',
          startTime: event.start_date
            ? new Date(event.start_date).toLocaleTimeString('en-US', {
                hour12: true,
                hour: 'numeric',
                minute: '2-digit',
              })
            : '',
          endTime: event.end_date
            ? new Date(event.end_date).toLocaleTimeString('en-US', { hour12: true, hour: 'numeric', minute: '2-digit' })
            : '',
          location: event.location || '',
          join_url: event.join_url || '',
          max_attendees: event.max_attendees || 100,
          attendees:
            event.eventRespondents?.reduce((total, respondent) => {
              // Count the attendee (1) plus their children (if any)
              return total + 1 + (respondent.childrenCount || 0);
            }, 0) || 0,
          eventRespondents: event.eventRespondents,
          event_title: event.event_title,
          start_date: event.start_date || new Date(),
          end_date: event.end_date || new Date(),
        }) as WorkshopEvent,
    ) || [];

  const headers = ['Event Name', 'Date & Time', 'Attendees', 'Format', 'Actions'];

  const handleCopy = (e: React.MouseEvent, event: WorkshopEvent) => {
    e.stopPropagation();
    if (event.join_url) {
      navigator.clipboard.writeText(event.join_url);
      toast({
        title: 'Success',
        description: 'The invite link has been successfully copied to clipboard',
        variant: 'success',
      });
    }
  };

  const renderRow = (event: WorkshopEvent) => (
    <TableRow key={event.id} className='cursor-pointer' onClick={() => router.push(`${pathname}/${event.id}`)}>
      <TableCell className='font-medium'>{event.name}</TableCell>
      <TableCell>
        <div className='flex flex-col'>
          <span>{event.date}</span>
          <span className='text-xs text-gray-600'>
            {event.startTime} - {event.endTime}
          </span>
        </div>
      </TableCell>
      <TableCell>
        {event.attendees}/{event.max_attendees}
      </TableCell>
      <TableCell>{event.format}</TableCell>
      <TableCell>
        <div className='flex items-center gap-2'>
          <div onClick={(e) => e.stopPropagation()}>
            <SendEventInviteModal
              eventId={event.id}
              attendees={event.eventRespondents?.map((respondent) => ({
                user_id: respondent.user_id,
                mom_id: respondent.mom_id || null,
                email: respondent.email || null,
              }))}
              eventTitle={event.name || 'Event'}
              eventDate={event.date || new Date().toLocaleDateString()}
              eventStartTime={event.startTime || '00:00'}
              eventEndTime={event.endTime || '00:00'}
              eventLocation={event.location || undefined}
              eventJoinUrl={event.join_url || undefined}
            />
          </div>
          <Copy
            className={`h-5 w-5 cursor-pointer ${event.join_url ? 'hover:text-gray-600' : 'cursor-not-allowed text-gray-300'}`}
            onClick={(e) => handleCopy(e, event)}
            style={{ pointerEvents: event.join_url ? 'auto' : 'none' }}
          />
        </div>
      </TableCell>
    </TableRow>
  );

  const renderMobileItem = (event: WorkshopEvent) => (
    <div className='space-y-2' onClick={() => router.push(`${pathname}/${event.id}`)}>
      <div className='flex justify-between'>
        <span className='font-medium'>{event.name}</span>
        <span className='text-gray-600'>{event.format}</span>
      </div>
      <div className='text-sm text-gray-600'>
        {event.date} at {event.startTime}
      </div>
      <div className='flex items-center justify-between'>
        <span className='text-sm'>
          {event.attendees}/{event.max_attendees} attendees
        </span>
        <div className='flex items-center gap-1'>
          <div onClick={(e) => e.stopPropagation()}>
            <SendEventInviteModal
              eventId={event.id}
              attendees={event.eventRespondents?.map((respondent) => ({
                user_id: respondent.user_id,
                mom_id: respondent.mom_id || null,
                email: respondent.email || null,
              }))}
              eventTitle={event.name || 'Event'}
              eventDate={event.date || new Date().toLocaleDateString()}
              eventStartTime={event.startTime || '00:00'}
              eventEndTime={event.endTime || '00:00'}
              eventLocation={event.location || undefined}
              eventJoinUrl={event.join_url || undefined}
            />
          </div>
          <Copy
            className={`h-5 w-5 cursor-pointer ${event.join_url ? 'hover:text-gray-600' : 'cursor-not-allowed text-gray-300'}`}
            onClick={(e) => handleCopy(e, event)}
            style={{ pointerEvents: event.join_url ? 'auto' : 'none' }}
          />
        </div>
      </div>
    </div>
  );

  // Define column widths to match the UI layout
  const columnWidths = [30, 20, 10, 15, 25];

  return (
    <div className='p-6'>
      <div className='mb-4 flex items-center justify-between'>
        <h1 className='text-2xl font-semibold'>Events and Workshops</h1>
        <CreateEventModal />
      </div>
      {error ? (
        <div className='flex flex-col items-center justify-center rounded-md bg-red-50 p-6'>
          <p className='text-red-600'>Error loading events. Please try again later.</p>
        </div>
      ) : (
        <GenericTable
          data={formattedEvents}
          headers={headers}
          columnWidths={columnWidths}
          tableHeaderClassName='text-gray-600 font-medium'
          isLoading={isLoading}
          rowRenderer={renderRow}
          mobileRenderer={renderMobileItem}
          shouldUseCustomRowComponent={true}
          emptyMessage='There are currently no events to display.'
        />
      )}
    </div>
  );
}
