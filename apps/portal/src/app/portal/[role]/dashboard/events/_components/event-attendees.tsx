import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import type { EventRespondent } from '@/hooks/generated/__types';
import { useFindManyEventRespondent } from '@/hooks/generated/event-respondent';
import {
  useDeleteEventRespondent,
  useUpdateEventRespondent,
  useUpdateManyEventRespondent,
} from '@/hooks/generated/event-respondent';
import { toast } from '@/hooks/use-toast';
import { formatPhoneNumber } from '@/lib/utils';
import { CheckCircle, Pencil, Trash2 } from 'lucide-react';
import { useState } from 'react';

import AddAttendeeModal from './add-attendee-modal';
import EditAttendeeModal from './edit-attendee-modal';
import { WorkshopEvent } from './events-table';
import SendEventInviteModal from './send-event-invite-modal';

interface EventAttendeesProps {
  event: WorkshopEvent;
}

type EventRespondentWithAssociations = EventRespondent & {
  user: {
    firstName: string;
    lastName: string;
  } | null;
  mom: {
    first_name: string;
    last_name: string;
  } | null;
};

const EventAttendees = ({ event }: EventAttendeesProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [attendeeToDelete, setAttendeeToDelete] = useState<EventRespondentWithAssociations | null>(null);
  const [attendeeToEdit, setAttendeeToEdit] = useState<EventRespondentWithAssociations | null>(null);
  const updateEventRespondent = useUpdateEventRespondent();
  const updateManyEventRespondent = useUpdateManyEventRespondent();
  const deleteEventRespondent = useDeleteEventRespondent();

  const { data: attendees, isLoading: isLoadingAttendees } = useFindManyEventRespondent({
    where: {
      event_id: event.id,
    },
    include: {
      user: true,
      mom: true,
    },
  });

  const handleCheckIn = async (attendee: EventRespondent, checked: boolean) => {
    setIsLoading(true);
    try {
      await updateEventRespondent.mutateAsync({
        where: { id: attendee.id },
        data: {
          didCheckin: checked,
        },
      });
    } catch (error) {
      console.error('Error updating attendee status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleMarkAllPresent = async () => {
    if (!attendees || attendees.length === 0) {
      toast({
        title: 'No Attendees',
        description: 'There are no attendees to mark as present.',
        variant: 'success',
      });
      return;
    }

    setIsLoading(true);
    try {
      // Use updateMany to update all attendees in a single operation
      await updateManyEventRespondent.mutateAsync({
        where: {
          event_id: event.id,
        },
        data: {
          didCheckin: true,
        },
      });

      toast({
        title: 'Success',
        description: `All ${attendees.length} attendees have been marked as present.`,
        variant: 'success',
      });
    } catch (error) {
      console.error('Error marking all attendees as present:', error);
      toast({
        title: 'Error',
        description: 'There was an error marking all attendees as present. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteAttendee = async () => {
    if (!attendeeToDelete) return;

    try {
      await deleteEventRespondent.mutateAsync({
        where: { id: attendeeToDelete.id },
      });
      toast({
        title: 'Attendee Removed',
        description: 'The attendee has been successfully removed from the event',
        variant: 'success',
      });
      setAttendeeToDelete(null);
    } catch (error) {
      console.error('Error deleting attendee:', error);
      toast({
        title: 'Failed to Remove Attendee',
        description: 'There was an error removing the attendee from the event. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const getStatusLabel = (attendee: EventRespondent) => {
    if (attendee.didCheckin) {
      return 'Checked In';
    }
    if (attendee.didRsvp) {
      return 'RSVP Yes';
    }
    if (attendee.hasBeenInvited) {
      return 'Invited';
    }
    return 'Not Invited';
  };

  const getStatusColor = (attendee: EventRespondent) => {
    if (attendee.didCheckin) {
      return 'bg-green-100 text-green-800';
    }
    if (attendee.didRsvp) {
      return 'bg-blue-100 text-blue-800';
    }
    if (attendee.hasBeenInvited) {
      return 'bg-gray-100 text-gray-800';
    }
    return 'bg-gray-100 text-gray-800';
  };

  const calculateTotalSeatsTaken = () => {
    if (!attendees) return 0;
    // Count both attendees and their children
    return attendees.reduce((total, attendee) => {
      return total + 1 + (attendee.childrenCount || 0);
    }, 0);
  };

  if (isLoadingAttendees) {
    return <div>Loading attendees...</div>;
  }

  return (
    <div className='border-emaBorder rounded-lg border bg-white p-6'>
      <div className='mb-6 flex items-start justify-between'>
        <div>
          <h2 className='text-lg font-semibold'>Event Attendees</h2>
          <p className='mt-1 text-sm text-gray-500'>
            Seats Available:{' '}
            {event.max_attendees ? `${event.max_attendees - calculateTotalSeatsTaken()}/${event.max_attendees}` : 'N/A'}
          </p>
        </div>
        <div className='flex gap-2'>
          <AddAttendeeModal
            eventId={event.id}
            attendees={
              attendees?.map((attendee) => ({
                user_id: attendee.user?.id || null,
                mom_id: attendee.mom?.id || null,
              })) || []
            }
          />
          <SendEventInviteModal
            eventId={event.id}
            attendees={
              attendees?.map((attendee) => ({
                user_id: attendee.user?.id || null,
                mom_id: attendee.mom?.id || null,
                email: attendee.email || null,
              })) || []
            }
            eventTitle={event.name || 'Event'}
            eventDate={event.date || new Date().toLocaleDateString()}
            eventStartTime={event.startTime || '00:00'}
            eventEndTime={event.endTime || '00:00'}
            eventLocation={event.location || undefined}
            eventJoinUrl={event.join_url || undefined}
          />
          <Button
            variant='outline'
            size='sm'
            onClick={handleMarkAllPresent}
            disabled={isLoading || !attendees || attendees.length === 0}
          >
            <CheckCircle className='mr-2 h-4 w-4' /> Mark All as Present
          </Button>
        </div>
      </div>

      {attendees && attendees.length > 0 ? (
        <div className='overflow-x-auto'>
          <table className='min-w-full divide-y divide-gray-200'>
            <thead>
              <tr>
                <th className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500'>Name</th>
                <th className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500'>Status</th>
                <th className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500'>Children</th>
                <th className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500'>Transport</th>
                <th className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500'>Check In</th>
                <th className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500'>Actions</th>
              </tr>
            </thead>
            <tbody className='divide-y divide-gray-200 bg-white'>
              {attendees?.map((attendee) => (
                <tr key={attendee.id}>
                  <td className='whitespace-nowrap px-6 py-4'>
                    <div className='flex flex-col'>
                      <div className='text-sm font-medium'>
                        {attendee.user
                          ? `${attendee.user.firstName} ${attendee.user.lastName}`
                          : attendee.mom
                            ? `${attendee.mom.first_name} ${attendee.mom.last_name}`
                            : attendee.name || 'Unknown Attendee'}
                      </div>
                      <div className='flex flex-col gap-0.5 text-xs text-gray-500'>
                        {attendee.email && <div>{attendee.email}</div>}
                        {attendee.phone_number && <div>{formatPhoneNumber(attendee.phone_number)}</div>}
                      </div>
                    </div>
                  </td>
                  <td className='whitespace-nowrap px-6 py-4'>
                    <span className={`rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(attendee)}`}>
                      {getStatusLabel(attendee)}
                    </span>
                  </td>
                  <td className='whitespace-nowrap px-6 py-4'>{attendee.childrenCount}</td>
                  <td className='whitespace-nowrap px-6 py-4'>{attendee.needsTransport ? 'Yes' : 'No'}</td>
                  <td className='whitespace-nowrap px-6 py-4'>
                    <input
                      type='checkbox'
                      className='h-4 w-4 rounded border-gray-300 text-emaBrandPrimary focus:ring-emaBrandPrimary'
                      checked={attendee.didCheckin}
                      onChange={(e) => handleCheckIn(attendee, e.target.checked)}
                      disabled={isLoading}
                    />
                  </td>
                  <td className='whitespace-nowrap px-6 py-4'>
                    <div className='flex items-center gap-2'>
                      <Button
                        variant='ghost'
                        size='sm'
                        onClick={() => setAttendeeToEdit(attendee as unknown as EventRespondentWithAssociations)}
                      >
                        <Pencil className='h-4 w-4' />
                      </Button>
                      <Button
                        variant='ghost'
                        size='sm'
                        onClick={() => setAttendeeToDelete(attendee as unknown as EventRespondentWithAssociations)}
                      >
                        <Trash2 className='h-4 w-4' />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className='text-center text-gray-500'>No attendees yet</div>
      )}

      <Dialog open={!!attendeeToDelete} onOpenChange={() => setAttendeeToDelete(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Remove Attendee</DialogTitle>
            <DialogDescription>
              Are you sure you want to remove{' '}
              {attendeeToDelete?.user
                ? `${attendeeToDelete.user.firstName} ${attendeeToDelete.user.lastName}`
                : attendeeToDelete?.mom
                  ? `${attendeeToDelete.mom.first_name} ${attendeeToDelete.mom.last_name}`
                  : 'this attendee'}{' '}
              from this event? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant='outline' onClick={() => setAttendeeToDelete(null)}>
              Cancel
            </Button>
            <Button variant='destructive' onClick={handleDeleteAttendee}>
              Remove
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {attendeeToEdit && <EditAttendeeModal attendee={attendeeToEdit} onClose={() => setAttendeeToEdit(null)} />}
    </div>
  );
};

export default EventAttendees;
