'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useDeleteDocument, useFindManyDocument, useFindManyTag, useFindManyTrack } from '@/hooks/generated';
import { LanguageType } from '@/hooks/generated/__types';
import { useToast } from '@/hooks/use-toast';
import { getFileIcon } from '@/lib/file-utils';
import { downloadDocument } from '@/lib/portal';
import { cn } from '@/lib/utils';
import { formatDateFromString } from '@suiteapi/models';
import { DownloadCloudIcon, ExternalLinkIcon, TrashIcon } from 'lucide-react';
import { useState } from 'react';

export default function ResourcesPage(): React.ReactNode {
  const { data: tags, isLoading: isTagsLoading } = useFindManyTag({
    select: {
      id: true,
      name: true,
    },
  });

  // Query all tracks to be able to associate standalone documents with tracks
  const { data: tracks, isLoading: isTracksLoading } = useFindManyTrack({
    select: {
      id: true,
      title: true,
      language_type: true,
    },
  });

  // Query documents directly and include their tags
  const {
    data: documents,
    isLoading: isDocumentsLoading,
    refetch: refetchDocuments,
  } = useFindManyDocument({
    select: {
      id: true,
      document_name: true,
      external_url_c: true,
      filename: true,
      description: true,
      created_at: true,
      updated_at: true,
      documentTags: {
        select: {
          tag_id: true,
          tag: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
      lessonTemplate: {
        select: {
          id: true,
          title: true,
          track: {
            select: {
              id: true,
              title: true,
              language_type: true,
            },
          },
        },
      },
    },
  });

  // Define type for resource items
  interface ResourceItem {
    id: string;
    documentName: string;
    filename: string | null;
    externalUrl: string | null;
    date: Date | null;
    trackTitle: string;
    templateTitle: string;
    document: {
      id: string;
      document_name: string | null;
      external_url_c: string | null;
      filename: string | null;
      description: string | null;
      created_at: Date | null;
      updated_at: Date | null;
      documentTags: Array<{
        tag_id: string;
        tag: {
          id: string;
          name: string;
        };
      }>;
      lessonTemplate: {
        id: string;
        title: string;
        track?: {
          id: string;
          title: string;
          language_type: LanguageType | null;
        } | null;
      } | null;
    };
  }

  const [selectedTrack, setSelectedTrack] = useState<string | null>('all');
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>('all');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null);
  const { toast } = useToast();

  // Delete document mutation
  const deleteDocument = useDeleteDocument({
    onSuccess: () => {
      refetchDocuments();
      toast({
        title: 'Document Deleted',
        description: 'The document was successfully deleted.',
        variant: 'success',
      });
      setDeleteDialogOpen(false);
      setDocumentToDelete(null);
    },
    onError: (error) => {
      console.error('Error deleting document:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete document. Please try again.',
        variant: 'destructive',
      });
    },
  });

  if (isTagsLoading || isDocumentsLoading || isTracksLoading) {
    return (
      <div className='space-y-6'>
        <h1 className='text-2xl font-bold'>Resources</h1>
        <div>Loading resources...</div>
      </div>
    );
  }

  if (!tags || !documents || documents.length === 0) {
    return (
      <div className='space-y-6'>
        <h1 className='text-2xl font-bold'>Resources</h1>
        <div>No resources available.</div>
      </div>
    );
  }

  // Initialize selected track if it hasn't been set yet
  if (!selectedTrack && tracks && tracks.length > 0) {
    setSelectedTrack('all');
  }

  // Function to get documents by tag and organize them
  const getDocumentsByTag = (tagId: string) => {
    if (!documents) return [];

    // If "all" is selected, use all documents
    const docsWithTag =
      tagId === 'all'
        ? documents
        : documents.filter((doc) => doc.documentTags.some((docTag) => docTag.tag_id === tagId));

    // Group documents by lesson template and track
    const documentGroups = docsWithTag.reduce(
      (acc, doc) => {
        // For documents associated with a lesson template
        if (doc.lessonTemplate) {
          const trackId = doc.lessonTemplate.track?.id || 'no-track';
          const trackTitle = doc.lessonTemplate.track?.title || 'No Track';
          const trackLanguage = doc.lessonTemplate.track?.language_type;
          const templateId = doc.lessonTemplate.id;
          const templateTitle = doc.lessonTemplate.title;

          // If this track doesn't exist in accumulator yet, create it
          if (!acc[trackId]) {
            acc[trackId] = {
              trackTitle,
              trackLanguage,
              templates: {},
            };
          }

          // If this template doesn't exist in this track yet, create it
          if (!acc[trackId].templates[templateId]) {
            acc[trackId].templates[templateId] = {
              templateTitle,
              documents: [],
            };
          }

          // Add document to this template
          acc[trackId].templates[templateId].documents.push(doc);
        }
        // For standalone documents that might be linked to tracks (based on ID format "doc-{trackId}")
        else if (doc.id.startsWith('doc-') && tracks) {
          const possibleTrackId = doc.id.substring(4); // Remove 'doc-' prefix
          const matchingTrack = tracks.find((track) => track.id === possibleTrackId);

          if (matchingTrack) {
            const trackId = matchingTrack.id;
            const trackTitle = matchingTrack.title;
            const trackLanguage = matchingTrack.language_type;
            const templateId = `track-documents-${trackId}`;
            const templateTitle = `${trackTitle} Resources`;

            // If this track doesn't exist in accumulator yet, create it
            if (!acc[trackId]) {
              acc[trackId] = {
                trackTitle,
                trackLanguage,
                templates: {},
              };
            }

            // If this template doesn't exist in this track yet, create it
            if (!acc[trackId].templates[templateId]) {
              acc[trackId].templates[templateId] = {
                templateTitle,
                documents: [],
              };
            }

            // Add document to this template
            acc[trackId].templates[templateId].documents.push(doc);
          } else {
            // If no matching track was found, fall back to standalone
            addToStandalone(acc, doc);
          }
        }
        // For truly standalone documents (not associated with anything)
        else {
          addToStandalone(acc, doc);
        }

        return acc;
      },
      {} as Record<
        string,
        {
          trackTitle: string;
          trackLanguage?: LanguageType | null;
          templates: Record<
            string,
            {
              templateTitle: string;
              documents: (typeof docsWithTag)[0][];
            }
          >;
        }
      >,
    );

    // Helper function to add document to the standalone category
    function addToStandalone(
      acc: Record<
        string,
        {
          trackTitle: string;
          trackLanguage?: LanguageType | null;
          templates: Record<
            string,
            {
              templateTitle: string;
              documents: (typeof docsWithTag)[0][];
            }
          >;
        }
      >,
      doc: (typeof docsWithTag)[0],
    ) {
      const trackId = 'standalone';
      const trackTitle = 'General Resources';
      const trackLanguage = null;
      const templateId = 'standalone';
      const templateTitle = 'Additional Resources';

      // If this track doesn't exist in accumulator yet, create it
      if (!acc[trackId]) {
        acc[trackId] = {
          trackTitle,
          trackLanguage,
          templates: {},
        };
      }

      // If this template doesn't exist in this track yet, create it
      if (!acc[trackId].templates[templateId]) {
        acc[trackId].templates[templateId] = {
          templateTitle,
          documents: [],
        };
      }

      // Add document to this template
      acc[trackId].templates[templateId].documents.push(doc);
    }

    // Convert the nested objects to arrays for easier rendering
    const result = Object.entries(documentGroups).map(([trackId, trackData]) => ({
      trackId,
      trackTitle: trackData.trackTitle,
      trackLanguage: trackData.trackLanguage,
      templates: Object.entries(trackData.templates).map(([templateId, templateData]) => ({
        templateId,
        templateTitle: templateData.templateTitle,
        documents: templateData.documents,
      })),
    }));

    // Always show Standalone/Additional Resources first if they exist
    return result.sort((a, b) => {
      if (a.trackId === 'standalone') return -1;
      if (b.trackId === 'standalone') return 1;
      return 0;
    });
  };

  // Flatten documents for table view
  const getResourcesAsFilteredList = (tagId: string, trackId: string | null) => {
    // If no category is selected, return empty list
    if (!tagId) return [];

    const groupedDocs = getDocumentsByTag(tagId);
    const flatList: ResourceItem[] = [];

    // Filter groupedDocs by selected track if one is selected
    const filteredGroups =
      trackId === 'all' || !trackId ? groupedDocs : groupedDocs.filter((group) => group.trackId === trackId);

    filteredGroups.forEach((trackGroup) => {
      trackGroup.templates.forEach((template) => {
        template.documents.forEach((doc) => {
          flatList.push({
            id: doc.id,
            documentName: doc.document_name || 'Unnamed Document',
            filename: doc.filename,
            externalUrl: doc.external_url_c,
            date: doc.created_at || doc.updated_at || null,
            trackTitle: trackGroup.trackTitle,
            templateTitle: template.templateTitle,
            document: doc,
          });
        });
      });
    });

    return flatList;
  };

  // Handle document deletion
  const handleDeleteClick = (documentId: string) => {
    setDocumentToDelete(documentId);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (documentToDelete) {
      deleteDocument.mutate({
        where: {
          id: documentToDelete,
        },
      });
    }
  };

  // Get all unique tracks from documents for the selected category
  const getAllTracksFromDocs = () => {
    if (!tracks) return [];

    // If "all" is selected, return all tracks
    if (selectedCategory === 'all') {
      const allTracks = [...tracks];
      allTracks.push({
        id: 'standalone',
        title: 'General Resources',
        language_type: null as LanguageType | null,
      });
      return allTracks;
    }

    if (!selectedCategory) return [];

    // Get documents that have the selected tag
    const docsWithSelectedTag =
      documents?.filter((doc) => doc.documentTags.some((docTag) => docTag.tag_id === selectedCategory)) || [];

    // Get unique track IDs from filtered documents
    const uniqueTrackIds = new Set<string>();

    docsWithSelectedTag.forEach((doc) => {
      if (doc.lessonTemplate?.track?.id) {
        uniqueTrackIds.add(doc.lessonTemplate.track.id);
      } else if (doc.id.startsWith('doc-') && tracks) {
        const possibleTrackId = doc.id.substring(4);
        const matchingTrack = tracks.find((track) => track.id === possibleTrackId);
        if (matchingTrack) {
          uniqueTrackIds.add(matchingTrack.id);
        } else {
          uniqueTrackIds.add('standalone');
        }
      } else {
        uniqueTrackIds.add('standalone');
      }
    });

    // If there are no documents with this tag, return all tracks
    if (uniqueTrackIds.size === 0) {
      const allTracks = [...tracks];
      allTracks.push({
        id: 'standalone',
        title: 'General Resources',
        language_type: null as LanguageType | null,
      });
      return allTracks;
    }

    // Filter tracks to only those that are in the uniqueTrackIds
    const filteredTracks = tracks.filter((track) => uniqueTrackIds.has(track.id));

    // Add standalone if needed
    if (uniqueTrackIds.has('standalone')) {
      filteredTracks.push({
        id: 'standalone',
        title: 'General Resources',
        language_type: null as LanguageType | null,
      });
    }

    return filteredTracks;
  };

  // When category changes, reset track selection
  const handleCategoryChange = (value: string) => {
    setSelectedCategory(value);
    setSelectedTrack(null);
  };

  const handleDownload = async (resource: ResourceItem) => {
    try {
      if (!resource.id) {
        throw new Error('Document ID is required for download');
      }
      const filename = resource.filename || resource.documentName || 'document';
      await downloadDocument(resource.id, filename);
    } catch (error) {
      console.error('Error downloading file:', error);
      toast({
        title: 'Failed to download document',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive',
      });
    }
  };

  const handleExternalLink = (url: string | null) => {
    if (url) {
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <div className='space-y-6'>
      <h1 className='text-2xl font-bold'>Resources & Curriculum</h1>
      <p className='text-sm text-emaTextSecondary'>Find your content and lessons</p>

      <Tabs defaultValue='all' value={selectedCategory} onValueChange={handleCategoryChange} className='space-y-4'>
        <div>
          <TabsList>
            <TabsTrigger key='all' value='all'>
              All
            </TabsTrigger>
            {tags?.map((tag) => (
              <TabsTrigger key={tag.id} value={tag.id}>
                {tag.name}
              </TabsTrigger>
            ))}
          </TabsList>
        </div>

        <div className='flex gap-6'>
          {/* Left sidebar for tracks */}
          <div className='w-64 rounded-md border border-emaBorderSecondary bg-white p-4'>
            <h3 className='mb-4 font-medium'>Tracks</h3>
            <ul className='space-y-1'>
              <li>
                <button
                  onClick={() => setSelectedTrack('all')}
                  className={cn(
                    'w-full rounded-md px-3 py-2 text-left text-sm font-medium transition-colors',
                    selectedTrack === 'all' ? 'bg-blue-100 text-blue-800' : 'text-gray-700 hover:bg-gray-100',
                  )}
                >
                  All Tracks
                </button>
              </li>
              {getAllTracksFromDocs().map((track) => (
                <li key={track.id}>
                  <button
                    onClick={() => setSelectedTrack(track.id)}
                    className={cn(
                      'w-full rounded-md px-3 py-2 text-left text-sm font-medium transition-colors',
                      selectedTrack === track.id ? 'bg-blue-100 text-blue-800' : 'text-gray-700 hover:bg-gray-100',
                    )}
                  >
                    {track.title}
                    {track.language_type && (
                      <span className='ml-2 text-xs text-gray-500'>
                        ({track.language_type === 'english' ? 'EN' : 'ES'})
                      </span>
                    )}
                  </button>
                </li>
              ))}
              {getAllTracksFromDocs().length === 0 && (
                <li className='px-3 py-2 text-sm text-gray-500'>No tracks available</li>
              )}
            </ul>
          </div>

          {/* Right side resources table */}
          <div className='flex-1 rounded-md border border-emaBorderSecondary bg-white p-4'>
            <TabsContent key='all' value='all' className='space-y-4'>
              <div>
                <h2 className='mb-4 text-xl font-semibold'>
                  All Resources
                  {selectedTrack && tracks && (
                    <span className='ml-2 text-base font-normal text-gray-500'>
                      -{' '}
                      {selectedTrack === 'all'
                        ? 'All Tracks'
                        : tracks.find((t) => t.id === selectedTrack)?.title || 'General Resources'}
                    </span>
                  )}
                </h2>
                <div className='rounded-md border'>
                  <table className='w-full'>
                    <thead>
                      <tr className='border-b bg-gray-50 text-left text-xs uppercase text-gray-500'>
                        <th className='px-4 py-2'>File</th>
                        <th className='px-4 py-2'>Date</th>
                        <th className='px-4 py-2 text-right'>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {getResourcesAsFilteredList('all', selectedTrack).map((resource) => (
                        <tr key={resource.id} className='border-b'>
                          <td className='px-4 py-3'>
                            <div className='flex items-center gap-3'>
                              <div className='relative'>
                                {resource.externalUrl ? (
                                  <ExternalLinkIcon className='h-5 w-5 text-blue-500' />
                                ) : (
                                  getFileIcon(resource.filename)
                                )}
                              </div>
                              <span className='font-medium'>{resource.documentName}</span>
                            </div>
                          </td>
                          <td className='px-4 py-3 text-sm text-gray-600'>
                            {resource.date ? formatDateFromString(resource.date.toISOString()) : 'N/A'}
                          </td>
                          <td className='px-4 py-3 text-right'>
                            <div className='flex justify-end gap-2'>
                              <Button
                                variant='ghost'
                                size='sm'
                                className='h-8 w-8 p-0'
                                title={resource.externalUrl ? 'Open external link' : 'Download'}
                                onClick={() =>
                                  resource.externalUrl
                                    ? handleExternalLink(resource.externalUrl)
                                    : handleDownload(resource)
                                }
                              >
                                {resource.externalUrl ? (
                                  <ExternalLinkIcon className='h-4 w-4' />
                                ) : (
                                  <DownloadCloudIcon className='h-4 w-4' />
                                )}
                              </Button>
                              <Button
                                variant='ghost'
                                size='sm'
                                className='h-8 w-8 p-0 text-red-500 hover:bg-red-50 hover:text-red-700'
                                onClick={() => handleDeleteClick(resource.id)}
                                title='Delete'
                              >
                                <TrashIcon className='h-4 w-4' />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                      {getResourcesAsFilteredList('all', selectedTrack).length === 0 && (
                        <tr>
                          <td colSpan={3} className='px-4 py-4 text-center text-sm text-gray-500'>
                            No resources found with this combination of tag and track.
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </TabsContent>
            {tags?.map((tag) => (
              <TabsContent key={tag.id} value={tag.id} className='space-y-4'>
                {selectedCategory && (
                  <div>
                    <h2 className='mb-4 text-xl font-semibold'>
                      {selectedCategory && tags && tags.find((t) => t.id === selectedCategory)?.name}
                      {selectedTrack && tracks && (
                        <span className='ml-2 text-base font-normal text-gray-500'>
                          -{' '}
                          {selectedTrack === 'all'
                            ? 'All Tracks'
                            : tracks.find((t) => t.id === selectedTrack)?.title || 'General Resources'}
                        </span>
                      )}
                    </h2>
                    <div className='rounded-md border'>
                      <table className='w-full'>
                        <thead>
                          <tr className='border-b bg-gray-50 text-left text-xs uppercase text-gray-500'>
                            <th className='px-4 py-2'>File</th>
                            <th className='px-4 py-2'>Date</th>
                            <th className='px-4 py-2 text-right'>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {getResourcesAsFilteredList(selectedCategory, selectedTrack).map((resource) => (
                            <tr key={resource.id} className='border-b'>
                              <td className='px-4 py-3'>
                                <div className='flex items-center gap-3'>
                                  <div className='relative'>
                                    {resource.externalUrl ? (
                                      <ExternalLinkIcon className='h-5 w-5 text-blue-500' />
                                    ) : (
                                      getFileIcon(resource.filename)
                                    )}
                                  </div>
                                  <span className='font-medium'>{resource.documentName}</span>
                                </div>
                              </td>
                              <td className='px-4 py-3 text-sm text-gray-600'>
                                {resource.date ? formatDateFromString(resource.date.toISOString()) : 'N/A'}
                              </td>
                              <td className='px-4 py-3 text-right'>
                                <div className='flex justify-end gap-2'>
                                  <Button
                                    variant='ghost'
                                    size='sm'
                                    className='h-8 w-8 p-0'
                                    title={resource.externalUrl ? 'Open external link' : 'Download'}
                                    onClick={() =>
                                      resource.externalUrl
                                        ? handleExternalLink(resource.externalUrl)
                                        : handleDownload(resource)
                                    }
                                  >
                                    {resource.externalUrl ? (
                                      <ExternalLinkIcon className='h-4 w-4' />
                                    ) : (
                                      <DownloadCloudIcon className='h-4 w-4' />
                                    )}
                                  </Button>
                                  <Button
                                    variant='ghost'
                                    size='sm'
                                    className='h-8 w-8 p-0 text-red-500 hover:bg-red-50 hover:text-red-700'
                                    onClick={() => handleDeleteClick(resource.id)}
                                    title='Delete'
                                  >
                                    <TrashIcon className='h-4 w-4' />
                                  </Button>
                                </div>
                              </td>
                            </tr>
                          ))}
                          {getResourcesAsFilteredList(selectedCategory, selectedTrack).length === 0 && (
                            <tr>
                              <td colSpan={3} className='px-4 py-4 text-center text-sm text-gray-500'>
                                No resources found with this combination of tag and track.
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </TabsContent>
            ))}
          </div>
        </div>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you sure?</DialogTitle>
            <DialogDescription>
              This will permanently delete this resource. This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant='outline' onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant='destructive' onClick={confirmDelete} className='bg-red-600 hover:bg-red-700'>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
