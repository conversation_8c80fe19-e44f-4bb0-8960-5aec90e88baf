'use client';

import { FileUpload } from '@/components/custom/file-upload';
import { useToast } from '@/hooks/use-toast';
import { CircleUserRound } from 'lucide-react';
import { useState } from 'react';

export default function FileUploadExample() {
  const [isUploading, setIsUploading] = useState(false);
  const { toast } = useToast();

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleFileSelect = async (files: File | File[]) => {
    setIsUploading(true);

    setTimeout(() => {
      setIsUploading(false);
      toast({
        title: 'Success',
        description: 'File upload demonstration complete',
        variant: 'success',
      });
    }, 1500);
  };

  return (
    <div className='space-y-8'>
      <div>
        <h3 className='mb-4 text-lg font-medium'>Compact Layout (fullWidth=false)</h3>
        <FileUpload
          onFileSelect={handleFileSelect}
          acceptedFileTypes={['image/jpeg', 'image/png', 'image/gif']}
          maxFileSize={5 * 1024 * 1024}
          buttonLabel='Upload Photo'
          dropText='Drop image here to upload'
          isUploading={isUploading}
          helperText={<p className='text-xs text-muted-foreground'>JPG, PNG or GIF. Max 5MB.</p>}
          enableDragDrop={true}
          multiple={false}
          fullWidth={false}
        >
          <CircleUserRound className='h-28 w-28 text-muted-foreground' />
        </FileUpload>
      </div>

      <div>
        <h3 className='mb-4 text-lg font-medium'>Full Width Layout (fullWidth=true)</h3>
        <FileUpload
          onFileSelect={handleFileSelect}
          acceptedFileTypes={['image/jpeg', 'image/png', 'image/gif']}
          maxFileSize={5 * 1024 * 1024}
          buttonLabel='Upload Photo'
          dropText='Drop image here to upload'
          isUploading={isUploading}
          helperText={<p className='text-xs text-muted-foreground'>JPG, PNG or GIF. Max 5MB.</p>}
          enableDragDrop={true}
          multiple={false}
          fullWidth={true}
        >
          <CircleUserRound className='h-28 w-28 text-muted-foreground' />
        </FileUpload>
      </div>
    </div>
  );
}
