import { languageOptions } from '@/lib/constants';
import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

import { Affiliate } from '../../admin/_lib/create-user-config';

export const ActiveInactiveStatusOptions = [
  { label: 'Active', value: 'Active' },
  { label: 'Inactive', value: 'Inactive' },
];
export const timezoneOptions = [
  {
    label: 'Eastern Time (ET)',
    value: 'america/new_york',
  },
  {
    label: 'Central Time (CT)',
    value: 'america/chicago',
  },
  {
    label: 'Mountain Time (MT)',
    value: 'america/denver',
  },
  {
    label: 'Pacific Time (PT)',
    value: 'america/los_angeles',
  },
  {
    label: 'Alaska Time (AKT)',
    value: 'america/anchorage',
  },
  {
    label: 'Hawaii-Aleutian Time (HAT)',
    value: 'pacific/honolulu',
  },
];

export const coordinatorBasicInfoFormConfig = (affiliateOptions: Affiliate[]): FieldConfig[] => [
  {
    name: 'nameGroup',
    label: '',
    type: 'row-group',
    subFields: [
      {
        label: 'First Name',
        name: 'firstName',
        type: 'text',
        placeholder: 'First Name',
      },
      {
        name: 'lastName',
        label: 'Last Name',
        type: 'text',
        placeholder: 'Last Name',
      },
    ],
  },
  {
    name: 'home_church',
    label: 'Home Church',
    type: 'text',
    placeholder: 'Home Church',
  },
  {
    name: 'email',
    label: 'Email Address',
    type: 'email',
    placeholder: 'Email Address',
  },
  {
    name: 'secondary_email',
    label: 'Secondary Email Address',
    type: 'email',
    placeholder: 'Secondary Email Address',
  },
  {
    name: 'phone_mobile',
    label: 'Mobile Phone Number',
    type: 'tel',
    placeholder: 'Mobile Phone Number',
  },
  {
    name: 'secondary_phone',
    label: 'Secondary Phone Number',
    type: 'tel',
    placeholder: 'Secondary Phone Number',
  },
  {
    name: 'communication_preference',
    label: 'Communication Preference',
    type: 'button-radio-group',
    placeholder: 'Select',
    options: [
      {
        label: 'Text Message',
        value: 'text_message',
      },
      {
        label: 'Email',
        value: 'email',
      },
      {
        label: 'Both',
        value: 'both',
      },
    ],
  },
  {
    name: 'sms_message_opt_in',
    label: '',
    type: 'checkbox',
    checkboxLabel:
      'Opt in for SMS messages with updates about your account. Message & data rates apply. 1-5 messages per week.',
  },
  {
    name: 'date_of_birth',
    label: 'Date of Birth',
    type: 'date',
    placeholder: 'Date of Birth',
    captionLayout: 'dropdown',
  },
  {
    name: 'timezone',
    label: 'Time Zone',
    type: 'select',
    options: timezoneOptions,
    placeholder: 'Select',
  },
  {
    name: 'affiliateId',
    label: 'Affiliate',
    type: 'select',
    placeholder: 'Assign Affiliate',
    options: affiliateOptions.map((affiliateOption) => ({
      label: affiliateOption.name ?? '',
      value: affiliateOption.id,
    })),
  },
  {
    name: 'status',
    label: 'Current Status',
    type: 'select',
    options: ActiveInactiveStatusOptions,
  },
  {
    name: 'availability',
    label: 'Availability',
    type: 'text',
    placeholder: 'Availability',
  },
  {
    name: 'language_preference_c',
    label: 'Language Preference',
    labelDescription: 'The language preference for the user',
    type: 'select',
    options: languageOptions,
  },
  {
    name: 'languages_c',
    label: 'Languages',
    labelDescription: 'The languages the user speaks',
    type: 'multi-select',
    options: languageOptions,
  },
];

export const coordinatorBasicInfoFormSchema = yup.object().shape({
  firstName: yup.string().required('First name is required'),
  lastName: yup.string().required('Last name is required'),
  home_church: yup.string().nullable().optional(),
  email: yup.string().email('Invalid email format').required('Email is required'),
  secondary_email: yup.string().email('Invalid email format').nullable().optional(),
  phone_mobile: yup.string().nullable().optional(),
  secondary_phone: yup.string().nullable().optional(),
  communication_preference: yup.string().oneOf(['text_message', 'email', 'both']).nullable().optional(),
  sms_message_opt_in: yup.boolean().nullable().optional(),
  date_of_birth: yup.date().nullable().optional(),
  timezone: yup.string().nullable().optional(),
  affiliateId: yup.string().nullable().optional(),
  status: yup.string().oneOf(['Active', 'Inactive']).nullable().optional(),
  availability: yup.string().nullable().optional(),
  language_preference_c: yup.string().nullable(),
  languages_c: yup
    .array()
    .of(
      yup.object().shape({
        label: yup.string().required(),
        value: yup.string().required(),
      }),
    )
    .nullable(),
});
