'use client';

import { PhotoUpload } from '@/components/custom/photo-upload';
import { useUserInfo } from '@/context/user-info';
import { useFindUniqueUser } from '@/hooks/generated/user';
import { removeUserPhoto, uploadUserPhoto } from '@/lib/portal';
import { useQueryClient } from '@tanstack/react-query';
import { CircleUserRound } from 'lucide-react';
import { useEffect, useState } from 'react';

// Use the same event name defined in the sidebar
const USER_PHOTO_UPDATED_EVENT = 'user-photo-updated';

export default function UserPhoto() {
  const { userData } = useUserInfo();
  const userId = userData?.sub;
  const queryClient = useQueryClient();
  const [refreshKey, setRefreshKey] = useState(Date.now());

  // Fetch the user data to get the photo URL using the generated hook
  const { data: user, refetch } = useFindUniqueUser({
    where: { id: userId },
  });

  // Listen for photo updates from other components
  useEffect(() => {
    const handlePhotoUpdate = () => {
      // Force refetch user data when photo is updated elsewhere
      refetch();
    };

    window.addEventListener('photo-updated', handlePhotoUpdate);
    return () => {
      window.removeEventListener('photo-updated', handlePhotoUpdate);
    };
  }, [refetch]);

  // Get the photo URL from the user data
  const userPhoto = user?.photoUrl || undefined;
  // Safely access thumbnailUrl, which might not be in the type yet
  const userThumbnail = user?.thumbnailUrl || undefined;

  return (
    <div key={refreshKey}>
      <PhotoUpload
        id={userId}
        photoUrl={userPhoto}
        thumbnailUrl={userThumbnail}
        uploadFunction={
          uploadUserPhoto as (
            id: string,
            file: File,
          ) => Promise<{ id: string; photoUrl?: string; thumbnailUrl?: string }>
        }
        removeFunction={
          removeUserPhoto as (id: string) => Promise<{ id: string; photoUrl?: string; thumbnailUrl?: string }>
        }
        queryKey='user'
        additionalInvalidations={['user-data']}
        entityName='user'
        placeholderComponent={<CircleUserRound className='h-28 w-28 text-muted-foreground' />}
        onSuccess={() => {
          // Invalidate the user data query to force a refresh
          queryClient.invalidateQueries({ queryKey: ['user-data'] });
          queryClient.invalidateQueries({ queryKey: ['user-data', userId] });

          // Force refetch immediately
          refetch();

          // Force complete component remount by updating key
          setRefreshKey(Date.now());

          // Dispatch general event with entity info for backward compatibility
          const photoUpdatedEvent = new CustomEvent('photo-updated', {
            detail: {
              entityType: 'user',
              entityId: userId,
            },
          });
          window.dispatchEvent(photoUpdatedEvent);

          // Dispatch specific user photo event that only the sidebar listens for
          window.dispatchEvent(new Event(USER_PHOTO_UPDATED_EVENT));
        }}
      />
    </div>
  );
}
