import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const availabilityFormFields: FieldConfig[] = [
  {
    name: 'sunday',
    label: 'Sunday',
    type: 'checkbox-group',
    options: [
      { label: 'Morning', value: 'morning' },
      { label: 'Afternoon', value: 'afternoon' },
      { label: 'Evening', value: 'evening' },
    ],
  },
  {
    name: 'monday',
    label: 'Monday',
    type: 'checkbox-group',
    options: [
      { label: 'Morning', value: 'morning' },
      { label: 'Afternoon', value: 'afternoon' },
      { label: 'Evening', value: 'evening' },
    ],
  },
  {
    name: 'tuesday',
    label: 'Tuesday',
    type: 'checkbox-group',
    options: [
      { label: 'Morning', value: 'morning' },
      { label: 'Afternoon', value: 'afternoon' },
      { label: 'Evening', value: 'evening' },
    ],
  },
  {
    name: 'wednesday',
    label: 'Wednesday',
    type: 'checkbox-group',
    options: [
      { label: 'Morning', value: 'morning' },
      { label: 'Afternoon', value: 'afternoon' },
      { label: 'Evening', value: 'evening' },
    ],
  },
  {
    name: 'thursday',
    label: 'Thursday',
    type: 'checkbox-group',
    options: [
      { label: 'Morning', value: 'morning' },
      { label: 'Afternoon', value: 'afternoon' },
      { label: 'Evening', value: 'evening' },
    ],
  },
  {
    name: 'friday',
    label: 'Friday',
    type: 'checkbox-group',
    options: [
      { label: 'Morning', value: 'morning' },
      { label: 'Afternoon', value: 'afternoon' },
      { label: 'Evening', value: 'evening' },
    ],
  },
  {
    name: 'saturday',
    label: 'Saturday',
    type: 'checkbox-group',
    options: [
      { label: 'Morning', value: 'morning' },
      { label: 'Afternoon', value: 'afternoon' },
      { label: 'Evening', value: 'evening' },
    ],
  },
];

export const availabilityFormSchema = yup.object().shape({
  sunday: yup.array().of(yup.string()).optional(),
  monday: yup.array().of(yup.string()).optional(),
  tuesday: yup.array().of(yup.string()).optional(),
  wednesday: yup.array().of(yup.string()).optional(),
  thursday: yup.array().of(yup.string()).optional(),
  friday: yup.array().of(yup.string()).optional(),
  saturday: yup.array().of(yup.string()).optional(),
});
