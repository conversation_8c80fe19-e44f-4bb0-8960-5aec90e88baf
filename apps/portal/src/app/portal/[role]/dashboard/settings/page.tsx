'use client';

import ChangePassword from '@/app/login/change-password/_components/change-password';
import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useFindManyAffiliate, useFindUniqueUser, useUpdateUser } from '@/hooks/generated';
import { toast } from '@/hooks/use-toast';
import { Language } from '@/lib/constants';
import { ChevronDown } from 'lucide-react';
import React from 'react';
import { useState } from 'react';
import { FieldValues, SubmitHandler } from 'react-hook-form';

import { mapLanguageCodesToOptions } from '../people/_utils/language-utils';
import UserPhoto from './_components/user-photo';
import { advocateBasicInfoFormConfig, advocateBasicInfoFormSchema } from './_lib/advocate-basic-info-form.config';
import {
  coordinatorBasicInfoFormConfig,
  coordinatorBasicInfoFormSchema,
} from './_lib/coordinator-basic-info-form.config';
import { supervisorBasicInfoFormConfig, supervisorBasicInfoFormSchema } from './_lib/supervisor-basic-info-form.config';

export default function SettingsPage({ params }: { params: { role: string } }): React.ReactNode {
  const [activeTab, setActiveTab] = useState('personal');
  const role = params.role;

  const { profile } = useDashboardContext();
  const userId = profile?.id;
  const [savingUserProfileChanges, setSavingUserProfileChanges] = useState(false);

  const { data: user } = useFindUniqueUser({
    where: {
      id: userId,
    },
  });

  // Transform the user data into the form's expected format
  const defaultPersonalInfoValues = React.useMemo(() => {
    if (!user) return {};
    return {
      firstName: user.firstName,
      lastName: user.lastName,
      home_church: user.home_church,
      email: user.email,
      secondary_email: user.secondary_email,
      phone_mobile: user.phone_mobile,
      // causing a form validation error b/c formating like `************** x95011`
      secondary_phone: user.secondary_phone ? user.secondary_phone.replace(/\D/g, '') : undefined,
      communication_preference: user.communication_preference,
      sms_message_opt_in: user.sms_message_opt_in,
      date_of_birth: user.date_of_birth,
      timezone: user.timezone,
      affiliateId: user.affiliateId,
      status: user.status,
      availability: user.availability,
      language_preference_c: user.language_preference_c,
      languages_c: user.languages_c ? mapLanguageCodesToOptions(user.languages_c) : [],
    };
  }, [user]);

  const tabs = [
    {
      id: 'personal',
      label: 'Personal Information',
      description: 'Your basic information',
    },
    {
      id: 'photo',
      label: 'Profile Photo',
      description: 'Your profile photo',
    },
    {
      id: 'password',
      label: 'Password',
      description: 'Update your password',
    },
  ];

  const { data: affiliates } = useFindManyAffiliate();

  const getBasicInfoConfig = () => {
    switch (role) {
      case 'supervisor':
        return {
          fields: supervisorBasicInfoFormConfig(affiliates ?? []),
          schema: supervisorBasicInfoFormSchema,
        };
      case 'coordinator':
        return {
          fields: coordinatorBasicInfoFormConfig(affiliates ?? []),
          schema: coordinatorBasicInfoFormSchema,
        };
      case 'advocate':
        return {
          fields: advocateBasicInfoFormConfig(affiliates ?? []),
          schema: advocateBasicInfoFormSchema,
        };
      default:
        return {
          fields: coordinatorBasicInfoFormConfig(affiliates ?? []),
          schema: coordinatorBasicInfoFormSchema,
        };
    }
  };

  const updateUser = useUpdateUser();

  const handleSaveBasicInfo = async (data: Record<string, unknown>) => {
    setSavingUserProfileChanges(true);
    try {
      // destructure affiliateId and languages_c from data so we can handle them separately
      // vs using them as a flat structure in the updateUser mutation
      // This allows us to connect the affiliate and format languages_c correctly
      const { affiliateId, languages_c, ...rest } = data as {
        affiliateId?: string | null;
        languages_c?: Array<{ value: string }> | null;
        [key: string]: unknown;
      };

      // Only add keys if the property is not null or undefined
      const updateData: Record<string, unknown> = {};
      Object.entries(rest).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          updateData[key] = value;
        }
      });

      if (languages_c && languages_c?.length > 0) {
        updateData.languages_c = { set: languages_c.map((lang) => lang.value) };
      }

      await updateUser.mutateAsync({
        where: {
          id: userId,
        },
        data: {
          ...updateData,

          // Handle affiliate connection if affiliateId exists
          ...(affiliateId && {
            affiliate: {
              connect: { id: affiliateId },
            },
          }),
        },
      });

      toast({
        title: 'Success',
        description: 'The personal information has been successfully updated',
        variant: 'success',
      });
    } catch (error) {
      console.error(error);
      toast({
        title: 'Error',
        description: 'There was an error updating the personal information. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSavingUserProfileChanges(false);
    }
  };

  return (
    <div>
      <h1 className='mb-2 text-3xl font-bold'>Settings</h1>
      <p className='mb-6 border-b border-emaBorderSecondary pb-6 text-emaTextTertiary'>
        Update your personal information
      </p>
      <div className='flex flex-col gap-6 md:flex-row'>
        <div className='md:w-64'>
          <div className='mb-4 md:hidden'>
            <DropdownMenu>
              <DropdownMenuTrigger className='flex w-full items-center justify-between rounded-lg border bg-white p-2'>
                {tabs.find((tab) => tab.id === activeTab)?.label}
                <ChevronDown className='h-4 w-4 opacity-50' />
              </DropdownMenuTrigger>

              <DropdownMenuContent className='w-[calc(100vw-20px)]'>
                {tabs.map((tab) => (
                  <DropdownMenuItem key={tab.id} onClick={() => setActiveTab(tab.id)}>
                    {tab.label}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className='hidden whitespace-nowrap md:flex md:flex-col'>
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`p-2 font-semibold md:border-l-2 ${
                  activeTab === tab.id ? 'border-black' : 'border-transparent'
                } min-w-fit text-left`}
              >
                {tab.label}
                <span className='block whitespace-normal break-words text-sm font-light text-emaTextTertiary'>
                  {tab.description}
                </span>
              </button>
            ))}
          </div>
        </div>

        <div className='max-w-[800px] flex-1 rounded-lg border border-emaBorderPrimary bg-white p-4 md:pb-8'>
          {activeTab === 'personal' && (
            <FormFactory
              key={user ? `form-${user.id}-personal-info-form` : 'loading'}
              fields={getBasicInfoConfig().fields}
              schema={getBasicInfoConfig().schema}
              formWrapperClassName='flex flex-col gap-2'
              formFieldElClass='w-full'
              onSubmit={handleSaveBasicInfo as SubmitHandler<FieldValues>}
              defaultValues={defaultPersonalInfoValues}
              actionButtonsComponent={
                <div className='flex w-full justify-end'>
                  <Button type='submit' disabled={savingUserProfileChanges}>
                    {savingUserProfileChanges ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              }
            />
          )}
          {activeTab === 'photo' && (
            <div>
              <UserPhoto />
            </div>
          )}
          {activeTab === 'password' && (
            <div className='mx-auto max-w-[400px] pt-4'>
              <ChangePassword
                actionItemsButton={
                  <div className='flex w-full justify-end'>
                    <Button type='submit'>Save Changes</Button>
                  </div>
                }
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
