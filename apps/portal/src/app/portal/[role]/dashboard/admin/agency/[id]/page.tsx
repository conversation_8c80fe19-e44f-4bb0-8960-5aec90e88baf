'use client';

import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { useFindUniqueAgency, useUpdateAgency } from '@/hooks/generated';
import { useToast } from '@/hooks/use-toast';
import { ArrowLeft } from 'lucide-react';
import { Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';
import { FieldValues, SubmitHandler } from 'react-hook-form';

import { editAgencySchema } from '../../_lib/edit-agency.config';
import { editAgencyConfig } from '../../_lib/edit-agency.config';

const AgencyPage = ({ params }: { params: { id: string } }) => {
  const { toast } = useToast();
  const { id } = params;
  const router = useRouter();

  const { data: agency, isLoading, refetch } = useFindUniqueAgency({ where: { id } });

  const updateAgency = useUpdateAgency();
  const handleEditAgency: SubmitHandler<FieldValues> = async (data) => {
    try {
      await updateAgency.mutateAsync({
        where: { id },
        data: {
          ...data,
        },
      });
      toast({
        title: 'Agency updated successfully',
        className: 'bg-green-500 text-white',
      });
      refetch();
    } catch (error) {
      console.error(error);
      toast({
        title: 'Error',
        description: `${error}`,
        variant: 'destructive',
      });
    }
  };

  return (
    <div className='p-6'>
      <Button onClick={() => router.back()} variant='link' className='mb-4'>
        <ArrowLeft className='mr-2 h-4 w-4' /> Back
      </Button>
      <div className='mb-6 border-b border-emaBorderSecondary pb-4'>
        <h1 className='text-2xl font-bold'>Agency Management</h1>
        <p className='text-sm text-gray-500'>Edit agency details</p>
      </div>
      {isLoading ? (
        <p className='flex flex-row items-center gap-2'>
          <Loader2 className='h-4 w-4 animate-spin' /> Loading...
        </p>
      ) : (
        <div className='max-w-[600px] rounded-lg border bg-white p-4'>
          <h2 className='text-xl font-semibold'>{agency?.agency_name}</h2>

          <FormFactory
            fields={editAgencyConfig}
            schema={editAgencySchema}
            defaultValues={agency}
            onSubmit={handleEditAgency}
            actionButtonsComponent={
              <div className='flex w-full flex-row justify-end gap-2'>
                <Button type='submit' variant='default'>
                  Save Changes
                </Button>
              </div>
            }
          />
        </div>
      )}
    </div>
  );
};

export default AgencyPage;
