'use client';

import { But<PERSON> } from '@/components/ui/button';
import { useFindManyAffiliate, useFindUniqueTrack } from '@/hooks/generated';
import { YupSchemas } from '@suiteapi/models';
import { ArrowLeft, PencilIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';

import EditTrackModal from '../../_components/edit-track-modal';
import LessonTemplateSection from '../../_components/lesson-template-section';

export interface Lesson {
  id: string;
  title: string;
  description?: string;
  priority?: number;
  order?: number;
  duration_days?: number;
  status?: string;
  dependency?: string;
  dueDate?: string;
  importance?: string;
  files?: File[];
}

// Define interface for affiliate structure
interface Affiliate {
  id: string;
  name: string;
}

// Create an extended track type that includes the affiliates array
type TrackWithAffiliates = YupSchemas.TrackSchema & {
  affiliates: Affiliate[];
  created_at: string | Date;
  updated_at: string | Date;
  created_by_id?: string;
  created_by_name?: string;
  deleted_at?: number | bigint;
};

const TrackPage = ({ params }: { params: { id: string } }) => {
  const { id } = params;
  const router = useRouter();
  const [localTrack, setLocalTrack] = React.useState<TrackWithAffiliates | null>(null);

  // Fetch the track data including affiliates
  const {
    data: track,
    isLoading,
    refetch,
    error,
  } = useFindUniqueTrack({
    where: { id },
    include: {
      affiliates: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });

  // Separate query to get all affiliates for the dropdown
  const { data: affiliates, isLoading: isAffiliatesLoading } = useFindManyAffiliate({
    select: {
      id: true,
      name: true,
    },
  });

  // Update localTrack when track data is fetched
  React.useEffect(() => {
    if (track) {
      // Create a track object with the fetched affiliates
      const trackWithAffiliates: TrackWithAffiliates = {
        ...(track as TrackWithAffiliates), // Cast to TrackWithAffiliates to include all properties
        affiliates: track.affiliates || [],
      };

      setLocalTrack(trackWithAffiliates);
    }
  }, [track]);

  const handleEditTrack = (updatedTrackData: YupSchemas.TrackSchema) => {
    // Update localTrack with the updated track data including affiliates
    if (updatedTrackData) {
      // Type assertion to handle the affiliates property
      const trackWithAffiliates = updatedTrackData as unknown as TrackWithAffiliates;

      // Make sure affiliates is present (even if empty)
      if (!Array.isArray(trackWithAffiliates.affiliates)) {
        trackWithAffiliates.affiliates = [];
      }

      // Update local state
      setLocalTrack(trackWithAffiliates);
    }
    // Refresh track data from server
    refetch();
  };

  if (isLoading || isAffiliatesLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  if (!localTrack) {
    return <div>No track data available</div>;
  }

  const formatDate = (date: Date | string) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className='p-6'>
      <div className='flex justify-between'>
        <Button
          variant='outline'
          size='icon'
          className='h-10 w-10'
          onClick={() => router.push('/portal/advocate/dashboard/admin/track')}
        >
          <ArrowLeft className='h-5 w-5' />
        </Button>
        <div className='flex items-center gap-4'>
          <EditTrackModal
            track={localTrack}
            onEditTrack={handleEditTrack}
            affiliates={affiliates || []}
            trigger={
              <Button variant='outline' className='flex items-center gap-2 rounded-lg px-4'>
                <PencilIcon size={16} />
                Edit Track
              </Button>
            }
          />
        </div>
      </div>
      <div className='mb-4 flex flex-row items-center justify-between'>
        <h1 className='mb-6 text-2xl font-bold'>Track Management</h1>
      </div>
      <div className='mb-6'>
        <div className='mb-4 flex flex-row items-center justify-between'>
          <h2 className='mb-2 text-xl font-semibold'>Track: {localTrack.title}</h2>
        </div>
        <div className='space-y-6 rounded border bg-white p-4'>
          {/* Basic track info */}
          <div>
            <h3 className='mb-2 font-semibold'>Track Description</h3>
            <p className='mb-4'>{localTrack.description || 'No description available'}</p>
          </div>

          {/* Metadata */}
          <div>
            <h3 className='mb-2 font-semibold'>Track Details</h3>
            <div className='grid grid-cols-1 gap-4 md:grid-cols-3'>
              <div>
                <p className='text-sm font-medium text-gray-500'>Language Type</p>
                <p className='capitalize'>{localTrack.language_type || 'Not specified'}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-gray-500'>Created</p>
                <p>{formatDate(localTrack.created_at)}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-gray-500'>Last Updated</p>
                <p>{formatDate(localTrack.updated_at)}</p>
              </div>
              {localTrack.created_by_name && (
                <div>
                  <p className='text-sm font-medium text-gray-500'>Created By</p>
                  <p>{localTrack.created_by_name}</p>
                </div>
              )}
            </div>
          </div>

          {/* Summaries */}
          <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
            <div>
              <h3 className='mb-2 font-semibold'>Track Summary</h3>
              <p>{localTrack.track_summary || 'No track summary available'}</p>
            </div>
            <div>
              <h3 className='mb-2 font-semibold'>Mom Summary</h3>
              <p>{localTrack.mom_summary || 'No mom summary available'}</p>
            </div>
          </div>

          {/* Connection Description */}
          <div>
            <h3 className='mb-2 font-semibold'>Connection Description</h3>
            <p>{localTrack.connection_description || 'No connection description available'}</p>
          </div>

          {/* Affiliates section */}
          <div>
            <h3 className='mb-2 font-semibold'>Affiliates</h3>
            {localTrack.affiliates && localTrack.affiliates.length > 0 ? (
              <ul className='list-disc pl-5'>
                {localTrack.affiliates.map((affiliate) => (
                  <li key={affiliate.id}>{affiliate.name}</li>
                ))}
              </ul>
            ) : (
              <p>No affiliates associated with this track.</p>
            )}
          </div>
        </div>
      </div>
      <LessonTemplateSection trackId={localTrack.id} />
    </div>
  );
};

export default TrackPage;
