import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const createAgencyConfig: FieldConfig[] = [
  {
    name: 'contact_first_name',
    label: 'Contact First Name',
    type: 'text',
  },
  {
    name: 'contact_last_name',
    label: 'Contact Last Name',
    type: 'text',
  },
  {
    name: 'contact_email',
    label: 'Contact Email',
    type: 'email',
  },
  {
    name: 'contact_phone',
    label: 'Contact Phone',
    type: 'tel',
  },
  {
    name: 'agency_name',
    label: 'Agency Name',
    type: 'text',
  },
  {
    name: 'address_city',
    label: 'Address City',
    type: 'text',
  },
  {
    name: 'address_state',
    label: 'Address State',
    type: 'text',
  },
];

export const createAgencySchema = yup.object().shape({
  contact_first_name: yup.string().required('Contact first name is required'),
  contact_last_name: yup.string().required('Contact last name is required'),
  contact_email: yup.string().optional().nullable(),
  contact_phone: yup.string().optional().nullable(),
  agency_name: yup.string().required('Agency name is required'),
  address_city: yup.string().required('Address city is required'),
  address_state: yup.string().required('Address state is required'),
});
