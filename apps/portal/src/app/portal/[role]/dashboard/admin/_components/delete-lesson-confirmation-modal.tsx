import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useDeleteLesson } from '@/hooks/generated/lesson';
import { Trash2 } from 'lucide-react';
import React from 'react';
import { useState } from 'react';

// TODO: This modal does not work yet and needs to be wired up. Much of this is stubbed out for the next developer to finish implementation
const DeleteLessonConfirmationModal = ({
  itemToDelete,
  onSuccess,
  title,
}: {
  itemToDelete: string | number | null;
  onSuccess?: (id: string | number | null) => Promise<void>;
  title: string;
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const deleteLesson = useDeleteLesson({
    onSuccess: () => {
      onSuccess?.(itemToDelete);
      setIsOpen(false);
    },
  });

  const handleDelete = () => {
    if (itemToDelete) {
      deleteLesson.mutate({
        where: { id: itemToDelete.toString() },
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          size='icon'
          variant='ghost'
          onClick={() => {
            setIsOpen(true);
          }}
        >
          <Trash2 className='mr-2 h-6 w-6 text-red-500' />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant='outline'
            onClick={() => {
              setIsOpen(false);
            }}
          >
            Cancel
          </Button>
          <Button variant='destructive' onClick={handleDelete} disabled={deleteLesson.isPending}>
            {deleteLesson.isPending ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteLessonConfirmationModal;
