import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const trackLessonSchema = yup.object().shape({
  title: yup.string().required('Title is required'),
  description: yup.string().required('Description is required'),
  priority: yup
    .number()
    .required('Priority is required')
    .positive('Priority must be positive')
    .integer('Priority must be an integer'),
  order: yup.number().optional().positive('Order must be positive').integer('Order must be an integer'),
  duration_days: yup.number().optional().positive('Duration must be positive').integer('Duration must be an integer'),
});

export const trackLessonConfig: FieldConfig[] = [
  {
    name: 'title',
    label: 'Title',
    type: 'text',
    placeholder: 'Enter title',
  },
  {
    name: 'description',
    label: 'Description',
    type: 'text',
    placeholder: 'Enter description',
  },
  {
    name: 'priority',
    label: 'Priority',
    type: 'number',
    placeholder: 'Enter priority',
  },
  {
    name: 'order',
    label: 'Order',
    type: 'number',
    placeholder: 'Enter order',
  },
  {
    name: 'duration_days',
    label: 'Duration (Days)',
    type: 'number',
    placeholder: 'Enter duration',
  },
];
