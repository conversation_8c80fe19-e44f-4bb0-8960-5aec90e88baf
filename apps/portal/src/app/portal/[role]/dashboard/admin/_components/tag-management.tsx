'use client';

import GenericDeleteConfirmationModal from '@/components/custom/generic-delete-confirmation-modal';
import GenericFormModal from '@/components/custom/generic-form-modal';
import GenericTable from '@/components/custom/generic-table';
import { Button } from '@/components/ui/button';
import { TableCell } from '@/components/ui/table';
import { useCreateTag, useDeleteTag, useFindManyTag, useUpdateTag } from '@/hooks/generated/tag';
import { toast } from '@/hooks/use-toast';
import { YupSchemas } from '@suiteapi/models';
import { PlusIcon } from 'lucide-react';
import { useState } from 'react';
import { FieldValues, SubmitHandler } from 'react-hook-form';

import { createOrEditTagConfig, createOrEditTagSchema } from '../_lib/create-or-edit-tag.config';

const TagManagement = () => {
  const [isAddTagModalOpen, setIsAddTagModalOpen] = useState(false);
  const [isEditTagModalOpen, setIsEditTagModalOpen] = useState(false);
  const [selectedTag, setSelectedTag] = useState<YupSchemas.TagSchema | null>(null);
  const {
    data: tags,
    isLoading,
    refetch,
  } = useFindManyTag({
    select: {
      id: true,
      name: true,
    },
  });
  const rowRenderer = (tag: YupSchemas.TagSchema) => {
    return (
      <>
        <TableCell>{tag.name}</TableCell>
        <TableCell>
          <div className='flex items-center justify-between gap-2'>
            <Button
              variant='outline'
              onClick={() => {
                setSelectedTag(tag);
                setIsEditTagModalOpen(true);
              }}
            >
              Edit
            </Button>
            <GenericDeleteConfirmationModal
              title='Are you sure you want to delete this tag?'
              handleDelete={() => {
                handleDeleteTag(tag.id);
              }}
              isDeleting={deleteTag.isPending}
            />
          </div>
        </TableCell>
      </>
    );
  };
  const mobileRowRenderer = (tag: YupSchemas.TagSchema) => {
    return (
      <div className='flex items-center justify-between gap-4'>
        <div>{tag.name}</div>
        <div>
          <Button
            variant='outline'
            onClick={() => {
              setSelectedTag(tag);
              setIsEditTagModalOpen(true);
            }}
          >
            Edit
          </Button>
          <GenericDeleteConfirmationModal
            title='Are you sure you want to delete this tag?'
            handleDelete={() => {
              handleDeleteTag(tag.id);
            }}
          />
        </div>
      </div>
    );
  };
  const deleteTag = useDeleteTag({
    onSuccess: () => {
      refetch();
    },
  });

  const createTag = useCreateTag();

  const handleCreateTag: SubmitHandler<FieldValues> = async (data) => {
    try {
      await createTag.mutateAsync({
        data: {
          name: data.name,
        },
      });
      toast({
        title: 'Success',
        description: 'The tag has been successfully created',
        variant: 'success',
      });
    } catch (error) {
      console.error('Error creating tag:', error);
      toast({
        title: 'Error',
        description: 'There was an error creating the tag. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsAddTagModalOpen(false);
      refetch();
    }
  };

  const handleDeleteTag = (id: string) => {
    deleteTag.mutate({ where: { id } });
  };

  const updateTag = useUpdateTag({
    onSuccess: () => {
      refetch();
    },
  });

  const handleUpdateTag: SubmitHandler<FieldValues> = async (data) => {
    try {
      await updateTag.mutateAsync({
        where: { id: data.id },
        data: {
          name: data.name,
        },
      });
      toast({
        title: 'Tag Updated',
        description: 'The tag has been successfully updated',
        variant: 'success',
      });
    } catch (error) {
      console.error('Error updating tag:', error instanceof Error ? error : String(error));
      toast({
        title: 'Failed to Update Tag',
        description: 'There was an error updating the tag. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsEditTagModalOpen(false);
      refetch();
    }
  };

  return (
    <div>
      <div className='mb-4 flex items-center justify-between'>
        <div>
          <h2 className='text-xl font-semibold'>Current Tags</h2>
          <p className='text-sm text-gray-500'>
            Tags can be attached to documents for organization and search purposes.
          </p>
        </div>
        <GenericFormModal
          modalTitle='Add Tag'
          schema={createOrEditTagSchema}
          fields={createOrEditTagConfig}
          onSubmit={handleCreateTag}
          open={isAddTagModalOpen}
          onOpenChange={setIsAddTagModalOpen}
          trigger={
            <Button variant='outline'>
              <PlusIcon className='mr-2 h-4 w-4' />
              Add Tag
            </Button>
          }
        />
      </div>
      <div>
        <GenericTable
          headers={['Name', 'Actions']}
          columnWidths={[100, 100]}
          itemsPerPage={8}
          data={tags ?? []}
          rowRenderer={rowRenderer}
          mobileRenderer={mobileRowRenderer}
          isLoading={isLoading}
          emptyMessage='There are currently no tags to display.'
          loadingMessage='Loading tags...'
        />
      </div>
      <GenericFormModal
        modalTitle='Edit Tag'
        schema={createOrEditTagSchema}
        fields={createOrEditTagConfig}
        onSubmit={handleUpdateTag}
        open={isEditTagModalOpen}
        onOpenChange={setIsEditTagModalOpen}
        defaultValues={selectedTag ? { id: selectedTag.id, name: selectedTag.name } : undefined}
        trigger={null}
      />
    </div>
  );
};

export default TagManagement;
