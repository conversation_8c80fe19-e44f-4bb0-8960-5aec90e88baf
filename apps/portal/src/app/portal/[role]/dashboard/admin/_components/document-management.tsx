'use client';

import GenericFormModal from '@/components/custom/generic-form-modal';
import RenderLinkOrDocument from '@/components/custom/render-link-or-document';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useCreateDocument, useDeleteDocument, useFindManyDocument, useFindManyTag } from '@/hooks/generated';
import { useToast } from '@/hooks/use-toast';
import { sfetch } from '@/lib/sfetch';
import { YupSchemas } from '@suiteapi/models';
import { ExternalLink, PlusIcon } from 'lucide-react';
import { useEffect, useState } from 'react';

import { addLinkToDocumentTableConfig, documentLinkSchema } from '../_lib/add-link-to-document-table.config';
import DocumentActionManagement from './document-action-management';

const DocumentManagement = () => {
  const { toast } = useToast();
  const [documents, setDocuments] = useState<YupSchemas.DocumentSchema[]>([]);
  const [selectedTab, setSelectedTab] = useState<string>('all');
  const [isAddLinkModalOpen, setIsAddLinkModalOpen] = useState(false);

  // Fetch tags
  const { data: tags, isLoading: isTagsLoading } = useFindManyTag({
    select: {
      id: true,
      name: true,
    },
  });

  // Create document mutation
  const createDocument = useCreateDocument({
    onSuccess: () => {
      fetchDocuments();
    },
  });

  // Delete document mutation
  const deleteDocument = useDeleteDocument({
    onSuccess: () => {
      fetchDocuments();
      toast({
        title: 'Document Deleted',
        description: 'The document was successfully deleted.',
        variant: 'success',
      });
    },
  });

  const handleDeleteDocument = (id: string) => {
    deleteDocument.mutate({
      where: {
        id,
      },
    });
  };

  const {
    data: rawDocuments,
    isLoading,
    refetch: fetchDocuments,
  } = useFindManyDocument({
    orderBy: {
      created_at: 'desc',
    },
    include: {
      documentTags: {
        include: {
          tag: true,
        },
      },
      lessonTemplate: true,
    },
  });

  useEffect(() => {
    if (rawDocuments) {
      const documentsArray = Array.isArray(rawDocuments) ? rawDocuments : [rawDocuments];

      // Transform the raw documents to match the expected YupSchemas.DocumentSchema type
      const transformedDocs = documentsArray.map((doc) => {
        const transformed: YupSchemas.DocumentSchema = {
          id: doc.id,
          document_name: doc.document_name || undefined,
          filecontents: doc.filecontents || undefined,
          filename: doc.filename || undefined,
          mimeType: doc.mimeType || undefined,
          description: doc.description || undefined,
          external_url_c: doc.external_url_c || undefined,
          created_at: doc.created_at,
          s3_file_name: doc.s3_file_name || undefined,
          lesson_template_id: doc.lesson_template_id || undefined,
          is_primary_lesson_resource: doc.is_primary_lesson_resource || false,
          documentTags:
            doc.documentTags?.map((tag) => ({
              id: tag.id,
              tag_id: tag.tag_id,
              document_id: tag.document_id,
              tag: {
                id: tag.tag?.id,
                name: tag.tag?.name,
              },
            })) || [],
        };

        // Store lessonTemplate info in a custom property
        if (doc.lessonTemplate) {
          (transformed as { lessonTemplate: typeof doc.lessonTemplate }).lessonTemplate = doc.lessonTemplate;
        }

        return transformed;
      });

      setDocuments(transformedDocs);
    } else {
      setDocuments([]);
    }
  }, [rawDocuments]);

  const handleFileUpload = async (file: File) => {
    try {
      const fileReader = new FileReader();
      fileReader.onload = async () => {
        const base64String = fileReader.result as string;
        const filecontents = base64String.split(',')[1] || base64String;

        const documentData = {
          filecontents,
          filename: file.name,
          mimeType: file.type,
          document_name: file.name,
        };

        const response = await sfetch('/v1/document', {
          method: 'POST',
          body: JSON.stringify(documentData),
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to upload file');
        }

        // Get the newly created document from the response
        await response.json(); // Response processed but not stored

        toast({
          title: 'Document Uploaded',
          description: 'The document was successfully uploaded.',
          variant: 'success',
        });

        fetchDocuments();
      };

      fileReader.readAsDataURL(file);
    } catch (error) {
      console.error('Error uploading file:', error);
      toast({
        title: 'Failed to Upload Document',
        description: 'There was an error uploading the document. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleAddExternalLink = async (data: YupSchemas.DocumentSchema) => {
    try {
      await createDocument.mutateAsync({
        data: {
          document_name: data.document_name,
          external_url_c: data.external_url_c,
        },
      });

      toast({
        title: 'Link Added',
        description: 'The external link was successfully added.',
        variant: 'success',
      });

      setIsAddLinkModalOpen(false);
    } catch (error) {
      console.error('Error adding external link:', error);
      toast({
        title: 'Failed to Add Link',
        description: 'There was an error adding the external link. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Function to filter documents by tag
  const getDocumentsByTag = (tagId: string) => {
    if (!tagId || !documents) return [];
    if (tagId === 'all') return documents;

    // Use index access to bypass type checking
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return documents.filter((doc) =>
      doc.documentTags?.some((docTag) => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const tagObject = (docTag as any).tag;
        return tagObject && tagObject.id === tagId;
      }),
    );
  };

  // Function to get standalone documents (not connected to a lesson)
  const getStandaloneDocuments = () => {
    return documents.filter((doc) => !doc.lesson_template_id);
  };

  // Generate tab data dynamically
  const allTabs = [
    { id: 'all', name: 'All Resources' },
    { id: 'standalone', name: 'Standalone Resources' },
    ...(tags?.map((tag) => ({ id: tag.id, name: tag.name })) || []),
  ];

  if (isLoading || isTagsLoading) {
    return (
      <div>
        <div className='mb-4'>
          <h2 className='text-xl font-semibold'>Documents</h2>
          <p className='text-sm text-gray-500'>Manage program documents and resources</p>
        </div>
        <div>Loading documents and tags...</div>
      </div>
    );
  }

  const renderDocumentItem = (document: YupSchemas.DocumentSchema) => (
    <div
      key={document.id}
      className='flex w-full flex-col justify-between gap-2 rounded-md border border-emaBorderSecondary p-2 shadow-sm md:flex-row md:items-center'
    >
      {/* Document Information Column */}
      <div className='flex-grow'>
        <div className='flex items-center'>
          <RenderLinkOrDocument
            document={{
              id: document?.id || null,
              external_url_c: document?.external_url_c || null,
              filename: document?.filename || null,
              document_name: document?.document_name || null,
            }}
          />
        </div>
      </div>

      {/* Tags Column */}
      <div className='flex flex-wrap gap-1 md:w-1/3'>
        {document.documentTags?.map((tag) => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const tagData = (tag as any).tag;
          if (!tagData) return null;

          return (
            <span
              key={tagData.id}
              className='inline-flex items-center rounded-md bg-orange-100 px-2 py-1 text-xs font-medium text-orange-800'
            >
              {tagData.name}
            </span>
          );
        })}
        {(!document.documentTags || document.documentTags.length === 0) && (
          <span className='text-sm italic text-gray-400'>No tags</span>
        )}
      </div>

      {/* Actions Column */}
      <div className='flex items-center justify-end gap-2'>
        <DocumentActionManagement
          document={document}
          handleDeleteDocument={handleDeleteDocument}
          handleTogglePrimaryResource={() => {}}
          fetchDocuments={fetchDocuments}
          deleteDocument={deleteDocument}
          editablePrimaryResource={false}
        />
      </div>
    </div>
  );

  return (
    <div>
      <div className='mb-4'>
        <h2 className='text-xl font-semibold'>Documents</h2>
        <p className='text-sm text-gray-500'>Manage program documents and resources</p>
      </div>

      <div className='mb-4 flex flex-wrap gap-2'>
        <Button variant='outline' onClick={() => setIsAddLinkModalOpen(true)}>
          <ExternalLink className='mr-2 h-4 w-4' />
          Add External Link
        </Button>

        <div className='relative'>
          <input
            type='file'
            id='file-input'
            onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0])}
            className='hidden'
            accept='.jpeg,.png,.pdf,.jpg'
          />
          <Button variant='outline' onClick={() => document.getElementById('file-input')?.click()}>
            <PlusIcon className='mr-2 h-4 w-4' />
            Upload Document
          </Button>
        </div>
      </div>

      <div className='space-y-6'>
        <Tabs defaultValue='all' value={selectedTab} onValueChange={setSelectedTab} className='space-y-4'>
          <div>
            <h2 className='mb-2 text-lg font-semibold'>Resources by Category</h2>
            <TabsList>
              {allTabs.map((tab) => (
                <TabsTrigger key={tab.id} value={tab.id}>
                  {tab.name}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>

          <div className='rounded-md border border-emaBorderSecondary bg-white p-4'>
            {allTabs.map((tab) => (
              <TabsContent key={tab.id} value={tab.id} className='space-y-4'>
                {tab.id === 'standalone' ? (
                  <div className='space-y-4'>
                    <h3 className='text-xl font-semibold'>Standalone Resources</h3>
                    {getStandaloneDocuments().length > 0 ? (
                      <div className='space-y-2'>{getStandaloneDocuments().map(renderDocumentItem)}</div>
                    ) : (
                      <p className='text-muted-foreground'>No standalone resources found.</p>
                    )}
                  </div>
                ) : (
                  <div className='space-y-4'>
                    <h3 className='text-xl font-semibold'>{tab.name}</h3>
                    {getDocumentsByTag(tab.id).length > 0 ? (
                      <Accordion type='multiple' className='space-y-2'>
                        <AccordionItem value='all-docs'>
                          <AccordionTrigger className='hover:no-underline'>
                            <div className='text-left'>
                              <h4 className='font-semibold'>All Documents ({getDocumentsByTag(tab.id).length})</h4>
                            </div>
                          </AccordionTrigger>
                          <AccordionContent>
                            <div className='space-y-2'>{getDocumentsByTag(tab.id).map(renderDocumentItem)}</div>
                          </AccordionContent>
                        </AccordionItem>

                        <AccordionItem value='standalone-docs'>
                          <AccordionTrigger className='hover:no-underline'>
                            <div className='text-left'>
                              <h4 className='font-semibold'>
                                Standalone Documents (
                                {getDocumentsByTag(tab.id).filter((doc) => !doc.lesson_template_id).length})
                              </h4>
                            </div>
                          </AccordionTrigger>
                          <AccordionContent>
                            <div className='space-y-2'>
                              {getDocumentsByTag(tab.id)
                                .filter((doc) => !doc.lesson_template_id)
                                .map(renderDocumentItem)}
                            </div>
                          </AccordionContent>
                        </AccordionItem>

                        <AccordionItem value='lesson-docs'>
                          <AccordionTrigger className='hover:no-underline'>
                            <div className='text-left'>
                              <h4 className='font-semibold'>
                                Lesson Documents (
                                {getDocumentsByTag(tab.id).filter((doc) => doc.lesson_template_id).length})
                              </h4>
                            </div>
                          </AccordionTrigger>
                          <AccordionContent>
                            <div className='space-y-2'>
                              {getDocumentsByTag(tab.id)
                                .filter((doc) => doc.lesson_template_id)
                                .map(renderDocumentItem)}
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                    ) : (
                      <p className='text-muted-foreground'>No resources found with this filter.</p>
                    )}
                  </div>
                )}
              </TabsContent>
            ))}
          </div>
        </Tabs>
      </div>

      {/* Modal for adding external links */}
      <GenericFormModal
        modalTitle='Add External Link'
        description='Add an external link as a resource'
        fields={addLinkToDocumentTableConfig}
        schema={documentLinkSchema}
        open={isAddLinkModalOpen}
        onOpenChange={setIsAddLinkModalOpen}
        onSubmit={handleAddExternalLink}
        trigger={null}
      />
    </div>
  );
};

export default DocumentManagement;
