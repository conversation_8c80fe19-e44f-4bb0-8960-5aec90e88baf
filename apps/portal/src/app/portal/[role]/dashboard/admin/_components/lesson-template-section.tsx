import GenericDeleteConfirmationModal from '@/components/custom/generic-delete-confirmation-modal';
import GenericFormModal from '@/components/custom/generic-form-modal';
import { Button } from '@/components/ui/button';
import {
  useCreateLessonTemplate,
  useDeleteLessonTemplate,
  useFindManyLessonTemplate,
  useUpdateLessonTemplate,
} from '@/hooks/generated';
import { toast } from '@/hooks/use-toast';
import { Plus } from 'lucide-react';
import { PencilIcon } from 'lucide-react';
import { useState } from 'react';
import { FieldValues } from 'react-hook-form';

import {
  createOrEditLessonTemplateConfig,
  createOrEditLessonTemplateSchema,
} from '../_lib/create-lesson-template.config';
import LessonTemplateDocuments from './lesson-template-documents';

const LessonTemplateSection = ({ trackId }: { trackId: string | undefined }) => {
  const createLessonTemplate = useCreateLessonTemplate();
  const {
    data: lessonTemplates,
    refetch,
    isLoading,
  } = useFindManyLessonTemplate({
    where: {
      track_id: trackId,
    },
  });
  const [isAddLessonTemplateOpen, setIsAddLessonTemplateOpen] = useState(false);
  const [editingTemplateId, setEditingTemplateId] = useState<string | null>(null);

  const handleAddLessonTemplate = async (data: FieldValues) => {
    try {
      await createLessonTemplate.mutateAsync({
        data: {
          title: String(data.title),
          description: String(data.description),
          priority: data.priority ? Number(data.priority) : null,
          order: data.order ? Number(data.order) : null,
          duration_days: data.duration_days ? Number(data.duration_days) : null,
          track_id: trackId ?? '',
        },
      });

      toast({
        title: 'Success',
        description: 'The lesson template has been successfully added',
        variant: 'success',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'There was an error adding the lesson template. Please try again.',
        variant: 'destructive',
      });
      console.error(error);
    } finally {
      setIsAddLessonTemplateOpen(false);
      refetch();
    }
  };

  const updateLessonTemplate = useUpdateLessonTemplate({
    onSuccess: () => {
      refetch();
    },
  });

  const handleEditLessonTemplate = async (data: FieldValues, id: string) => {
    try {
      await updateLessonTemplate.mutateAsync({
        data: {
          id: id,
          title: data.title,
          description: data.description,
          priority: data.priority ? Number(data.priority) : null,
          order: data.order ? Number(data.order) : null,
          duration_days: data.duration_days ? Number(data.duration_days) : null,
        },
        where: {
          id: data.id,
        },
      });

      toast({
        title: 'Success',
        description: 'The lesson template has been successfully updated',
        variant: 'success',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'There was an error updating the lesson template. Please try again.',
        variant: 'destructive',
      });
      console.error(error);
    } finally {
      refetch();
      setEditingTemplateId(null);
    }
  };

  const deleteLessonTemplate = useDeleteLessonTemplate();

  const handleDeleteLessonTemplate = async (id: string) => {
    try {
      await deleteLessonTemplate.mutateAsync({
        where: { id },
      });

      toast({
        title: 'Success',
        description: 'The lesson template has been successfully deleted',
        variant: 'success',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'There was an error deleting the lesson template. Please try again.',
        variant: 'destructive',
      });
      console.error(error);
    }
  };

  return (
    <>
      <div className='mb-6'>
        <div className='mb-4 flex items-center justify-between'>
          <h2 className='text-xl font-semibold'>Lesson Templates</h2>
          <div className='flex flex-row gap-2'>
            <GenericFormModal
              modalTitle='Add a Lesson Template'
              description='This template would be used to create lessons for this track.'
              onSubmit={handleAddLessonTemplate}
              trigger={
                <Button>
                  <Plus className='mr-2 h-4 w-4' />
                  Add Lesson Template
                </Button>
              }
              fields={createOrEditLessonTemplateConfig}
              schema={createOrEditLessonTemplateSchema}
              open={isAddLessonTemplateOpen}
              onOpenChange={setIsAddLessonTemplateOpen}
            />
          </div>
        </div>
      </div>
      {isLoading ? (
        <div>Loading...</div>
      ) : (
        <div>
          {lessonTemplates?.map((lessonTemplate) => (
            <div
              key={lessonTemplate.id}
              className='mb-5 rounded-lg border border-emaBorderSecondary bg-white p-4 shadow-sm transition-shadow hover:shadow-md md:p-6'
            >
              <div className='flex flex-row items-start justify-between'>
                <h3 className='text-lg font-semibold text-gray-900'>{lessonTemplate.title}</h3>
                <div className='flex flex-row items-center gap-2'>
                  <GenericFormModal
                    modalTitle='Edit Lesson Template'
                    onSubmit={(data) => handleEditLessonTemplate(data, lessonTemplate.id)}
                    trigger={
                      <Button variant='outline' size='sm'>
                        <PencilIcon className='h-4 w-4' />
                        <span className='ml-2 hidden md:block'>Edit</span>
                      </Button>
                    }
                    fields={createOrEditLessonTemplateConfig}
                    schema={createOrEditLessonTemplateSchema}
                    defaultValues={lessonTemplate}
                    open={editingTemplateId === lessonTemplate.id}
                    onOpenChange={(open) => setEditingTemplateId(open ? lessonTemplate.id : null)}
                  />
                  <GenericDeleteConfirmationModal
                    title='Delete Lesson Template'
                    description='Are you sure you want to delete this lesson template?'
                    handleDelete={() => handleDeleteLessonTemplate(lessonTemplate.id)}
                    isDeleting={deleteLessonTemplate.isPending}
                  />
                </div>
              </div>

              <div className='mt-4 grid gap-6 md:grid-cols-2'>
                <div className='space-y-2'>
                  <p className='text-sm text-emaTextSecondary'>
                    <span className='font-medium text-gray-700'>Description:</span> {lessonTemplate.description}
                  </p>
                </div>
                <div className='space-y-2'>
                  <p className='text-sm text-emaTextSecondary'>
                    <span className='font-medium text-gray-700'>Duration:</span> {lessonTemplate.duration_days} days
                  </p>
                  <p className='text-sm text-emaTextSecondary'>
                    <span className='font-medium text-gray-700'>Priority:</span> {lessonTemplate.priority}
                  </p>
                  <p className='text-sm text-emaTextSecondary'>
                    <span className='font-medium text-gray-700'>Order:</span> {lessonTemplate.order}
                  </p>
                </div>
              </div>

              <LessonTemplateDocuments templateId={lessonTemplate.id} onRefetch={refetch} />
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default LessonTemplateSection;
