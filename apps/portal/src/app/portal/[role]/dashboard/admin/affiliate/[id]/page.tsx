'use client';

import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { useFindManyAgency, useFindUniqueAffiliate, useUpdateAffiliate } from '@/hooks/generated';
import { useToast } from '@/hooks/use-toast';
import { YupSchemas } from '@suiteapi/models';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';
import { SubmitHandler } from 'react-hook-form';
import { FieldValues } from 'react-hook-form';

import {
  affiliateValidationSchema,
  editAffiliateFieldsConfig,
} from '../../_lib/affiliate-management-edit-affiliate.config';

const AffiliatePage = ({ params }: { params: { id: string } }) => {
  const { id } = params;
  const router = useRouter();
  const updateAffiliate = useUpdateAffiliate();
  const { toast } = useToast();
  const { data: affiliate, isLoading } = useFindUniqueAffiliate({
    where: { id: id },
    include: {
      affiliateAgencies: {
        include: {
          agency: true,
        },
      },
    },
  });
  const { data: agencies } = useFindManyAgency({
    select: {
      id: true,
      name: true,
      agency_name: true,
      contact_first_name: true,
      contact_last_name: true,
      address_city: true,
      address_state: true,
      address_postalcode: true,
      contact_email: true,
      contact_phone: true,
      agency_phone: true,
      address: true,
    },
  });

  const formattedDefaultValues = {
    ...affiliate,
    agency_id: affiliate?.affiliateAgencies.map((agency) => ({
      label: agency.agency.name,
      value: agency.agency.id,
    })),
  };

  const handleEditAffiliate = async (affiliateData: YupSchemas.Affiliate) => {
    try {
      // Remove agency_id from the payload as it's not a database field
      const { agency_id, affiliateAgencies, ...affiliateUpdateData } = affiliateData;

      // Get the agency IDs from the selected options
      const selectedAgencyIds = agency_id?.map((agency: { value: string }) => agency.value) || [];

      const existingAgencies = affiliate?.affiliateAgencies || [];
      const agenciesToDelete = existingAgencies.filter((agency) => !selectedAgencyIds.includes(agency.agency_id));
      const agenciesToCreate = selectedAgencyIds.filter(
        (agencyId: string) => !existingAgencies.some((ag) => ag.agency_id === agencyId),
      );

      // Update affiliate with a single mutation
      await updateAffiliate.mutateAsync({
        where: { id },
        data: {
          ...affiliateUpdateData,
          billing_address_street_2: affiliateUpdateData.billing_address_street_2 || null,
          affiliateAgencies: {
            deleteMany:
              agenciesToDelete.length > 0
                ? {
                    AND: [
                      { affiliate_id: id },
                      { agency_id: { in: agenciesToDelete.map((agency) => agency.agency_id) } },
                    ],
                  }
                : undefined,
            create: agenciesToCreate.map((agencyId: string) => ({
              agency: {
                connect: { id: agencyId },
              },
            })),
          },
        },
        include: {
          affiliateAgencies: true,
        },
      });

      toast({
        title: 'Success',
        description: 'The affiliate has been successfully updated',
        variant: 'success',
      });
    } catch (error) {
      console.error(error);
      toast({
        title: 'Error',
        description: 'There was an error updating the affiliate. Please try again.',
        variant: 'destructive',
      });
    }
  };
  return (
    <div>
      <Button variant='link' onClick={() => router.back()}>
        <ArrowLeft className='mr-2 h-4 w-4' /> Back
      </Button>
      <div className='mb-4 border-b border-emaBorderSecondary pb-4'>
        <h2 className='text-xl font-semibold'>{affiliate?.name}</h2>
        <p className='text-sm text-gray-500'>Edit affiliate details</p>
      </div>
      <div className='max-w-[720px] rounded-lg border border-emaBorderSecondary bg-white p-4'>
        {isLoading ? (
          <div>Loading...</div>
        ) : (
          <FormFactory
            fields={editAffiliateFieldsConfig(agencies ?? [])}
            schema={affiliateValidationSchema}
            onSubmit={handleEditAffiliate as SubmitHandler<FieldValues>}
            defaultValues={formattedDefaultValues}
            actionButtonsComponent={
              <div className='flex w-full flex-row justify-end gap-2'>
                <Button type='submit' variant='default'>
                  Save Changes
                </Button>
              </div>
            }
          />
        )}
      </div>
    </div>
  );
};

export default AffiliatePage;
