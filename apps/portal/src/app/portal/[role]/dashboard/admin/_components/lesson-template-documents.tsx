import GenericFormModal from '@/components/custom/generic-form-modal';
import GenericTag from '@/components/custom/generic-tag';
import RenderLinkOrDocument from '@/components/custom/render-link-or-document';
import { Button } from '@/components/ui/button';
import { useCreateDocument, useDeleteDocument, useUpdateDocument, useUpdateManyDocument } from '@/hooks/generated';
import { toast } from '@/hooks/use-toast';
import { sfetch } from '@/lib/sfetch';
import { YupSchemas } from '@suiteapi/models';
import { PlusIcon } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import { documentLinkSchema } from '../_lib/add-link-to-document-table.config';
import { addLinkToDocumentTableConfig } from '../_lib/add-link-to-document-table.config';
import DocumentActionManagement from './document-action-management';

interface LessonTemplateDocumentsProps {
  templateId: string;
  onRefetch: () => void;
}

// Interface for raw document tags from API
interface RawDocumentTag {
  id: string;
  tag_id: string;
  tag: {
    id: string;
    name: string;
  };
}

// Interface for mapped document tags used in the UI
interface MappedDocumentTag {
  id?: string;
  name?: string;
}

const LessonTemplateDocuments = ({ templateId, onRefetch }: LessonTemplateDocumentsProps) => {
  const [areDocumentsLoading, setAreDocumentsLoading] = useState(false);
  const [documents, setDocuments] = useState<YupSchemas.DocumentSchema[]>([]);
  const [isAddLessonResourceModalOpen, setIsAddLessonResourceModalOpen] = useState(false);

  const deleteDocument = useDeleteDocument({
    onSuccess: () => {
      onRefetch();
    },
  });

  const fetchDocuments = useCallback(async () => {
    setAreDocumentsLoading(true);
    try {
      const response = await sfetch(`/v1/document/lesson-template/${templateId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch documents: ${response.statusText}`);
      }
      const data = await response.json();

      const documentsArray = Array.isArray(data) ? data : data.length ? [data] : [];

      const mappedDocs = documentsArray.map((doc) => ({
        id: doc.id,
        filename: doc.document_name || doc.filename,
        mimeType: doc.mimeType,
        external_url_c: doc.external_url_c ? doc.external_url_c : undefined,
        is_primary_lesson_resource: doc.is_primary_lesson_resource,
        documentTags:
          doc.documentTags?.map((dt: RawDocumentTag) => ({
            id: dt.tag_id, // this is the id of the specific tag, not the id of the document tag
            name: dt.tag.name, // this is the name of the specific tag
          })) || [],
      }));

      setDocuments(mappedDocs);
    } catch (error) {
      console.error(`Error fetching documents:`, error);
    } finally {
      setAreDocumentsLoading(false);
    }
  }, [templateId]);

  useEffect(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  const onFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) handleFileUpload(file);
    event.target.value = '';
  };

  const handleFileUpload = async (file: File) => {
    try {
      const fileReader = new FileReader();
      fileReader.onload = async () => {
        const base64String = fileReader.result as string;
        const filecontents = base64String.split(',')[1] || base64String;

        const documentData = {
          filecontents,
          filename: file.name,
          mimeType: file.type,
          lesson_template_id: templateId,
          document_name: file.name,
        };

        const response = await sfetch(`/v1/document`, {
          method: 'POST',
          body: JSON.stringify(documentData),
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to upload file');
        }

        const newDocument = await response.json();
        setDocuments((prev) => [
          ...prev,
          {
            id: newDocument.id,
            filename: newDocument.document_name || newDocument.filename,
            created_at: newDocument.created_at ? new Date(newDocument.created_at) : new Date(),
            mimeType: newDocument.mimeType,
          },
        ]);
      };

      fileReader.readAsDataURL(file);
    } catch (error) {
      console.error('Error uploading file:', error);
    }
  };

  const handleDeleteDocument = async (documentId: string) => {
    try {
      await deleteDocument.mutateAsync({
        where: { id: documentId },
      });
      setDocuments((prev) => prev.filter((doc) => doc.id !== documentId));
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Error deleting document',
      });
      console.error(error);
    }
  };

  const createDocument = useCreateDocument({
    onSuccess: () => {
      onRefetch();
    },
  });

  const handleAddLinkToDocumentTable = async (data: YupSchemas.DocumentSchema) => {
    try {
      await createDocument.mutateAsync({
        data: {
          document_name: data.document_name,
          external_url_c: data.external_url_c,
          lesson_template_id: templateId,
          coordinator: undefined, // this is being explicity asked for in the schema
          is_primary_lesson_resource: data.is_primary_lesson_resource,
        },
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Error creating document link',
      });
      console.error(error);
    } finally {
      fetchDocuments();
      setIsAddLessonResourceModalOpen(false);
    }
  };

  const updateDocument = useUpdateDocument({
    onSuccess: () => {
      onRefetch();
    },
  });

  const updateManyDocument = useUpdateManyDocument({
    onSuccess: () => {
      onRefetch();
    },
  });

  const handleTogglePrimaryResource = async (documentId: string, isPrimary: boolean) => {
    if (!documentId) return;
    try {
      if (!isPrimary) {
        // First, set all other primary resources to false using updateMany
        await updateManyDocument.mutateAsync({
          where: {
            AND: [
              { lesson_template_id: templateId },
              { id: { not: documentId } },
              { is_primary_lesson_resource: true },
            ],
          },
          data: {
            is_primary_lesson_resource: false,
          },
        });

        // Then set the selected document to true using single update
        await updateDocument.mutateAsync({
          where: { id: documentId },
          data: {
            is_primary_lesson_resource: true,
          },
        });
      } else {
        // Just update the single document to false
        await updateDocument.mutateAsync({
          where: { id: documentId },
          data: {
            is_primary_lesson_resource: false,
          },
        });
      }

      fetchDocuments();
    } catch (error) {
      console.error('Error toggling primary resource:', error);
      toast({
        title: 'Error',
        description: 'Failed to update primary resource status',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className='mt-6 border-t pt-4'>
      <div className='mb-3 flex flex-col gap-4'>
        <div>
          <GenericFormModal
            modalTitle='Upload Link'
            description='Upload a link to the lesson template'
            fields={addLinkToDocumentTableConfig}
            schema={documentLinkSchema}
            open={isAddLessonResourceModalOpen}
            onOpenChange={setIsAddLessonResourceModalOpen}
            trigger={
              <Button variant='secondary' size='sm'>
                <PlusIcon className='mr-2 h-4 w-4' />
                Add Resource Link
              </Button>
            }
            onSubmit={handleAddLinkToDocumentTable}
          />
        </div>

        <input
          type='file'
          id={`file-input-${templateId}`}
          onChange={onFileSelect}
          className='hidden'
          accept='.jpeg,.png,.pdf,.jpg'
        />
        <Button
          className='w-fit'
          variant='secondary'
          size='sm'
          onClick={() => document.getElementById(`file-input-${templateId}`)?.click()}
        >
          <PlusIcon className='mr-2 h-4 w-4' />
          Add Document
        </Button>
        <p className='text-sm text-emaTextSecondary'>
          Accepted files: <span className='font-medium text-gray-700'>jpeg, png, pdf, jpg</span>
          <span className='mx-2'>•</span>
          Max size: <span className='font-medium text-gray-700'>5MB</span>
        </p>
      </div>
      {areDocumentsLoading ? (
        <div className='min-h-[20px]'>Loading...</div>
      ) : (
        <div className='mt-4 space-y-4 md:space-y-2'>
          {documents.map((document) => (
            <div
              key={document.id}
              className='flex w-full flex-col justify-between gap-2 rounded-md border border-emaBorderSecondary p-2 shadow-sm md:flex-row md:items-center md:border-none md:pb-0 md:shadow-none'
            >
              {/* Document Information Column */}
              <div className='flex-grow'>
                <RenderLinkOrDocument
                  document={{
                    external_url_c: document.external_url_c || null,
                    filename: document.filename || null,
                    document_name: document.document_name || null,
                    id: document.id || null,
                  }}
                />
              </div>

              {/* Tags Column */}
              <div className='flex flex-wrap gap-1 md:w-1/3'>
                {document.documentTags?.map((tag: MappedDocumentTag) => (
                  <GenericTag key={tag.id || `tag-${Math.random()}`} tag={tag.name || 'Unnamed'} color='orange' />
                ))}
                {(!document.documentTags || document.documentTags.length === 0) && (
                  <span className='text-sm italic text-gray-400'>No tags</span>
                )}
              </div>

              {/* Actions Column */}
              <div className='flex items-center justify-end gap-2'>
                <DocumentActionManagement
                  document={document}
                  handleDeleteDocument={handleDeleteDocument}
                  handleTogglePrimaryResource={handleTogglePrimaryResource}
                  fetchDocuments={fetchDocuments}
                  deleteDocument={deleteDocument}
                  editablePrimaryResource={true}
                />
              </div>
            </div>
          ))}
          {documents.length === 0 && <div className='text-sm text-gray-500'>No documents available</div>}
        </div>
      )}
    </div>
  );
};

export default LessonTemplateDocuments;
