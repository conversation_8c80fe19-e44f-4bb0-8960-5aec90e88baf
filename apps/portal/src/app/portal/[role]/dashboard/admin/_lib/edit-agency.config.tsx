import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

import { createAgencyConfig } from './create-agency.config';

export const editAgencyConfig: FieldConfig[] = [
  ...createAgencyConfig,
  {
    name: 'address_postalcode',
    label: 'Address Postal Code',
    type: 'text',
  },
  {
    name: 'agency_phone',
    label: 'Agency Phone',
    type: 'tel',
  },
  {
    name: 'address',
    label: 'Address',
    type: 'text',
  },
];

export const editAgencySchema = yup.object().shape({
  name: yup.string().required(),
  address_city: yup.string().required().nullable(),
  address_state: yup.string().required().nullable(),
  address_postalcode: yup
    .string()
    .nullable()
    .transform((value) => (value === '' ? null : value))
    .test('zip', 'Please enter a valid ZIP code', (value) => {
      if (!value) return true;
      return /^\d{5}(-\d{4})?$/.test(value);
    }),
  contact_first_name: yup.string().required(),
  contact_last_name: yup.string().required(),
  contact_email: yup.string().optional().nullable(),
  contact_phone: yup.string().optional().nullable(),
  agency_phone: yup.string().optional().nullable(),
  address: yup.string().optional().nullable(),
  agency_postalcode: yup.string().optional().nullable(),
});
