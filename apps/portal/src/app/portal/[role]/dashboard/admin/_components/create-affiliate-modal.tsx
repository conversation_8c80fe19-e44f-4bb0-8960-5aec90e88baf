import FormFactory from '@/components/custom/form-factory';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useCreateAffiliate } from '@/hooks/generated';
import { YupSchemas } from '@suiteapi/models';
import { useState } from 'react';
import { FieldValues, SubmitHandler } from 'react-hook-form';

import { createAffiliateConfig } from '../_lib/create-affiliate.config';
import { createAffiliateSchema } from '../_lib/create-affiliate.config';

interface CreateAffiliateModalProps {
  onSubmit: SubmitHandler<FieldValues>;
  trigger: React.ReactNode;
  defaultValues?: YupSchemas.Affiliate;
  saveButtonText?: string;
  modalTitle?: string;
  onCreateAffiliate?: () => void;
}

const CreateAffiliateModal = ({
  onCreateAffiliate,
  trigger,
  defaultValues,
  saveButtonText = 'Create',
  modalTitle = 'Create Affiliate',
}: Omit<CreateAffiliateModalProps, 'onSubmit'>) => {
  const createAffiliate = useCreateAffiliate();
  const [open, setOpen] = useState(false);

  const handleSubmit: SubmitHandler<FieldValues> = async (data) => {
    try {
      await createAffiliate.mutateAsync({
        data: {
          name: data.name as string,
          description: data.description as string,
          website: data.website as string,
          status: data.status as 'Active' | 'Inactive',
        },
      });
      setOpen(false);
      onCreateAffiliate?.();
    } catch (error) {
      console.error('Error creating affiliate:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>

      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>{modalTitle}</DialogTitle>
        </DialogHeader>

        <FormFactory
          fields={createAffiliateConfig}
          schema={createAffiliateSchema}
          onSubmit={handleSubmit}
          actionButtonsComponent={
            <div className='flex w-full flex-row justify-end gap-2'>
              <Button type='submit'>{saveButtonText}</Button>
              <Button variant='outline' onClick={() => setOpen(false)}>
                Cancel
              </Button>
            </div>
          }
          defaultValues={defaultValues}
        />
      </DialogContent>
    </Dialog>
  );
};

export default CreateAffiliateModal;
