import { FieldConfig } from '@/types/form-field';
import * as Yup from 'yup';

export const createOrEditLessonTemplateConfig: FieldConfig[] = [
  {
    name: 'title',
    label: 'Title',
    type: 'text',
  },
  {
    name: 'description',
    label: 'Description',
    type: 'text',
  },
  {
    name: 'order',
    label: 'Order',
    type: 'number',
  },
  {
    name: 'duration_days',
    label: 'Duration (Days)',
    type: 'number',
  },
  {
    name: 'priority',
    label: 'Priority',
    type: 'number',
  },
];

export const createOrEditLessonTemplateSchema = Yup.object().shape({
  title: Yup.string().required('Title is required'),
  description: Yup.string().required('Description is required'),
  order: Yup.number().optional().positive('Order must be positive').integer('Order must be an integer'),
  duration_days: Yup.number().optional().positive('Duration must be positive').integer('Duration must be an integer'),
  priority: Yup.number().optional().positive('Priority must be positive').integer('Priority must be an integer'),
});
