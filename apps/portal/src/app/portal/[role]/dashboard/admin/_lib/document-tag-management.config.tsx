import { Tag } from '@/hooks/generated/__types';
import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export interface TagOption {
  label: string;
  value: string;
}

export const documentTagManagementConfig = (tags: Tag[]): FieldConfig[] => {
  // Ensure tags have proper label/value format for the multi-select
  const tagOptions = (tags || [])
    .filter((tag) => tag && tag.id && tag.name) // Filter out any invalid tags
    .map((tag) => ({
      label: tag.name,
      value: tag.id,
    }));

  return [
    {
      name: 'documentTags',
      label: 'Document Tags',
      type: 'multi-select',
      placeholder: 'Select tags',
      options: tagOptions,
      overrideStrings: {
        allItemsAreSelected: 'All tags are selected',
        selectAll: 'Select all tags',
        search: 'Search tags',
        selectSomeItems: 'Select tags',
      },
    },
  ];
};

// Schema validation that accepts both string values and objects with label/value
export const documentTagManagementSchema = yup.object().shape({
  documentTags: yup
    .array()
    .of(
      yup.lazy((value) => {
        if (typeof value === 'string') {
          return yup.string();
        }
        return yup.object().shape({
          value: yup.string().required(),
          label: yup.string().required(),
        });
      }),
    )
    .required('Please select at least one tag'),
});
