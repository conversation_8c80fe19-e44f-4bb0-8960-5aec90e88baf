import { FieldConfig } from '@/types/form-field';
import { YupSchemas } from '@suiteapi/models';
import * as yup from 'yup';

import { ActiveInactiveStatusOptions } from '../../settings/_lib/coordinator-basic-info-form.config';

export const editAffiliateFieldsConfig = (agencyOptions: YupSchemas.Agency[]): FieldConfig[] => [
  {
    name: 'name',
    label: 'Affiliate Name',
    type: 'text',
  },
  {
    name: 'agency_id',
    label: 'Agency',
    type: 'multi-select',
    options: agencyOptions.map((agency) => ({
      value: agency.id ?? '',
      label: agency.name ?? '',
    })),
  },
  {
    name: 'description',
    label: 'Description',
    type: 'text',
  },
  {
    name: 'status',
    label: 'Status',
    type: 'select',

    options: ActiveInactiveStatusOptions,
  },
  {
    name: 'phone_office',
    label: 'Phone',
    type: 'tel',
  },
  {
    name: 'billing_address_street',
    label: 'Street Address',
    type: 'text',
  },
  {
    name: 'billing_address_street_2',
    label: 'Suite or Unit',
    type: 'text',
  },
  {
    name: 'billing_address_city',
    label: 'City',
    type: 'text',
  },
  {
    name: 'billing_address_state',
    label: 'State',
    type: 'text',
  },
  {
    name: 'billing_address_postalcode',
    label: 'Zip',
    type: 'text',
  },
  {
    name: 'website',
    label: 'Website',
    type: 'text',
  },
  {
    name: 'contact_name',
    label: 'Contact Name',
    type: 'text',
  },
  {
    name: 'email1',
    label: 'Contact Email',
    type: 'email',
  },
];

export const affiliateValidationSchema = yup.object().shape({
  name: yup.string().required('Agency Name is required'),
  description: yup.string().optional().nullable(),
  status: yup.string().required('Status is required').oneOf(['Active', 'Inactive']),
  phone_office: yup.string().required('Phone is required'),
  billing_address_street: yup.string().required('Street Address is required'),
  billing_address_street_2: yup.string().optional().nullable(),
  billing_address_city: yup.string().required('City is required'),
  billing_address_state: yup
    .string()
    .required('State is required')
    .matches(/^[A-Z]{2}$/, 'Please enter a valid 2-letter state code'),
  billing_address_postalcode: yup
    .string()
    .required('Zip is required')
    .matches(/^\d{5}(-\d{4})?$/, 'Please enter a valid ZIP code'),
  website: yup.string().required('Website is required'),
  contact_name: yup.string().required('Contact Name is required'),
  email1: yup.string().required('Contact Email is required').email('Please enter a valid email address'),
  agency_id: yup
    .array()
    .of(yup.object().shape({ value: yup.string(), label: yup.string() }))
    .required(),
});
