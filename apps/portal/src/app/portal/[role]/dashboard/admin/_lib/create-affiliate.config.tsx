import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

import { ActiveInactiveStatusOptions } from '../../settings/_lib/coordinator-basic-info-form.config';

export const createAffiliateConfig: FieldConfig[] = [
  {
    name: 'name',
    label: 'Name',
    type: 'text',
  },
  {
    name: 'description',
    label: 'Description',
    type: 'text',
  },
  {
    name: 'website',
    label: 'Website',
    type: 'text',
  },
  {
    name: 'status',
    label: 'Status',
    type: 'select',
    options: ActiveInactiveStatusOptions,
  },
];

export const createAffiliateSchema = yup.object().shape({
  name: yup.string().required('Name is required'),
  description: yup.string().required('Description is required'),
  website: yup.string().required('Website is required'),
  status: yup
    .string()
    .oneOf(['Active', 'Inactive'], 'Status must be either Active or Inactive')
    .required('Status is required'),
});
