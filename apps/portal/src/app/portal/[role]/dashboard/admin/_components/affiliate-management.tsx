'use client';

import GenericDeleteConfirmationModal from '@/components/custom/generic-delete-confirmation-modal';
import GenericTable from '@/components/custom/generic-table';
import { Button } from '@/components/ui/button';
import { TableCell } from '@/components/ui/table';
import { useDeleteAffiliate, useFindManyAffiliate } from '@/hooks/generated';
import { YupSchemas } from '@suiteapi/models';
import { Plus } from 'lucide-react';
import { usePathname } from 'next/navigation';
import { useRouter } from 'next/navigation';

import CreateAffiliateModal from './create-affiliate-modal';

const AffiliateManagement = () => {
  const router = useRouter();
  const pathname = usePathname();
  const {
    data: affiliates,
    isLoading,
    refetch,
  } = useFindManyAffiliate({
    select: {
      id: true,
      name: true,
      billing_address_street: true,
      billing_address_street_2: true,
      billing_address_street_3: true,
      billing_address_street_4: true,
      billing_address_city: true,
      billing_address_state: true,
      billing_address_postalcode: true,
      billing_address_country: true,
      phone_office: true,
      website: true,
      email1: true,
      description: true,
      created_at: true,
      updated_at: true,
    },
  });

  const rowRenderer = (affiliate: YupSchemas.Affiliate) => (
    <>
      <TableCell className='py-4'>{affiliate.name}</TableCell>
      <TableCell className='py-4'>
        <div className='text-sm text-emaTextQuaternary'>
          {affiliate.billing_address_city}
          {affiliate.billing_address_city && affiliate.billing_address_state ? ', ' : ''}
          {affiliate.billing_address_state}
        </div>
      </TableCell>

      <TableCell className='py-4'>
        <div className='flex flex-row items-center gap-2'>
          <Button variant='outline' onClick={() => router.push(`${pathname}/affiliate/${affiliate.id}`)}>
            View
          </Button>
          <GenericDeleteConfirmationModal
            title='Are you sure you want to delete this affiliate?'
            handleDelete={() => handleDeleteAffiliate(affiliate.id)}
            isDeleting={deleteAffiliate.isPending}
          />
        </div>
      </TableCell>
    </>
  );

  const deleteAffiliate = useDeleteAffiliate({
    onSuccess: () => {
      refetch();
    },
  });

  const handleDeleteAffiliate = (id: string | undefined) => {
    if (id) {
      deleteAffiliate.mutate({
        where: { id: id.toString() },
      });
    }
  };
  const mobileRenderer = (affiliate: YupSchemas.Affiliate) => (
    <div className='space-y-2'>
      <div className='font-medium'>{affiliate.name}</div>
      <div className='text-sm text-emaTextQuaternary'>
        {affiliate.billing_address_city}
        {affiliate.billing_address_city && affiliate.billing_address_state ? ', ' : ''}
        {affiliate.billing_address_state}
      </div>

      <div className='flex flex-row items-center gap-4'>
        <Button variant='outline' size='sm' onClick={() => router.push(`${pathname}/affiliate/${affiliate.id}`)}>
          View
        </Button>
        <GenericDeleteConfirmationModal
          title='Are you sure you want to delete this affiliate?'
          handleDelete={() => handleDeleteAffiliate(affiliate.id)}
          isDeleting={deleteAffiliate.isPending}
        />
      </div>
    </div>
  );

  return (
    <div>
      <div className='mb-4 flex items-center justify-between'>
        <div>
          <h2 className='text-xl font-semibold text-emaTextPrimary'>Affiliates</h2>
          <p className='text-sm text-emaTextQuaternary'>Manage system affiliates and their information</p>
        </div>
        <CreateAffiliateModal
          trigger={
            <Button variant='outline'>
              <Plus className='mr-2 h-4 w-4' /> Add Affiliate
            </Button>
          }
          onCreateAffiliate={() => {
            refetch();
          }}
        />
      </div>
      <div>
        <GenericTable
          data={
            affiliates?.map((affiliate) => ({
              id: affiliate.id,
              name: affiliate.name,
              billing_address_city: affiliate.billing_address_city ?? undefined,
              billing_address_state: affiliate.billing_address_state ?? undefined,
            })) ?? []
          }
          headers={['Name', 'Location', 'Actions']}
          rowRenderer={rowRenderer}
          mobileRenderer={mobileRenderer}
          isLoading={isLoading}
          columnWidths={[50, 35, 15]}
        />
      </div>
    </div>
  );
};

export default AffiliateManagement;
