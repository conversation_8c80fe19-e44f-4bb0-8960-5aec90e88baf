import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useFindManyTag, useFindUniqueDocument } from '@/hooks/generated';
import { useUpdateDocument } from '@/hooks/generated';
import { toast } from '@/hooks/use-toast';
import { useState } from 'react';

import { documentTagManagementSchema } from '../_lib/document-tag-management.config';
import { documentTagManagementConfig } from '../_lib/document-tag-management.config';

// Define interfaces for tag structures
interface TagOption {
  label: string;
  value: string;
}

interface SimpleTag {
  id: string;
  name: string;
}

interface DocumentTag {
  tag: {
    id: string;
    name: string;
  };
}

type TagType = TagOption | SimpleTag | DocumentTag;

const ManageTagsModal = ({
  onClose,
  documentId,
  currentTags = [],
  onSuccess,
}: {
  isOpen?: boolean;
  onClose?: () => void;
  documentId: string | undefined;
  // Accept various tag formats that might come from different parts of the app
  currentTags: TagType[];
  onSuccess?: () => void;
}) => {
  const [open, setOpen] = useState(false);
  const updateDocument = useUpdateDocument();
  const { data: tagsData } = useFindManyTag();
  const { data: document } = useFindUniqueDocument({
    where: {
      id: documentId,
    },
    include: {
      documentTags: {
        include: {
          tag: true,
        },
      },
    },
  });

  // Process the tags in a way that works for the multi-select component
  const processedTags = currentTags
    .map((tag): TagOption => {
      // Handle tag object with label/value already properly formatted
      if (typeof tag === 'object' && tag !== null && 'label' in tag && 'value' in tag) {
        return {
          label: String(tag.label || ''),
          value: String(tag.value || ''),
        };
      }

      // Handle documentTag with nested tag property
      if (typeof tag === 'object' && tag !== null && 'tag' in tag && tag.tag && typeof tag.tag === 'object') {
        return {
          label: String(tag.tag.name || ''),
          value: String(tag.tag.id || ''),
        };
      }

      // Handle simple tag with id/name
      if (typeof tag === 'object' && tag !== null && 'id' in tag && 'name' in tag) {
        return {
          label: String(tag.name || ''),
          value: String(tag.id || ''),
        };
      }

      // Fallback for unexpected formats
      return {
        label: '',
        value: '',
      };
    })
    .filter((tag) => tag.label && tag.value); // Remove any empty/invalid tags

  const formattedDefaultValues = {
    documentTags: processedTags,
  };

  const handleCancel = () => {
    setOpen(false);
    if (onClose) onClose();
  };

  const handleSave = async (data: { documentTags?: Array<TagOption | string> }) => {
    try {
      const existingTags = document?.documentTags || [];

      // Process the submitted tag values
      const newTagValues = Array.isArray(data.documentTags)
        ? data.documentTags
            .map((tag) => {
              if (typeof tag === 'object' && tag !== null && 'value' in tag) {
                return tag.value;
              }
              return typeof tag === 'string' ? tag : '';
            })
            .filter(Boolean)
        : [];

      const documentTagsToDelete = existingTags.filter((tag) => !newTagValues.includes(tag.tag?.id));

      const tagsToCreate = newTagValues.filter((tagId: string) => !existingTags.some((dt) => dt.tag?.id === tagId));

      // First, delete tags that were unchecked
      if (documentTagsToDelete.length > 0) {
        await updateDocument.mutateAsync({
          where: {
            id: documentId,
          },
          data: {
            documentTags: {
              deleteMany: {
                AND: [
                  { document_id: documentId },
                  { tag_id: { in: documentTagsToDelete.map((dt) => dt.tag?.id).filter(Boolean) } },
                ],
              },
            },
          },
        });
      }

      // Then, create new tag relationships
      if (tagsToCreate.length > 0) {
        await updateDocument.mutateAsync({
          where: {
            id: documentId,
          },
          data: {
            documentTags: {
              create: tagsToCreate.map((tagId: string) => ({
                tag: {
                  connect: {
                    id: tagId,
                  },
                },
              })),
            },
          },
          include: {
            documentTags: true,
          },
        });
      }

      toast({
        title: 'Success',
        description: 'Tags updated successfully',
        className: 'bg-green-500 text-white',
      });

      setOpen(false);
      onSuccess?.();
    } catch (error) {
      console.error('Error updating tags:', error);
      toast({
        title: 'Error',
        description: 'Failed to update tags',
        variant: 'destructive',
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant='secondary' size='sm'>
          Manage Tags
        </Button>
      </DialogTrigger>
      <DialogContent className=''>
        <DialogHeader>
          <DialogTitle>Manage Tags</DialogTitle>
          <DialogDescription>Select tags for this document</DialogDescription>
        </DialogHeader>
        {!tagsData ? (
          <div>Please add tags in the admin</div>
        ) : (
          <FormFactory
            key={`tag-form-${documentId}-${processedTags.length}`}
            fields={documentTagManagementConfig(tagsData ?? [])}
            schema={documentTagManagementSchema}
            onSubmit={handleSave}
            defaultValues={formattedDefaultValues}
            actionButtonsComponent={
              <DialogFooter>
                <Button variant='outline' type='button' onClick={handleCancel}>
                  Cancel
                </Button>
                <Button type='submit'>Save Changes</Button>
              </DialogFooter>
            }
          />
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ManageTagsModal;
