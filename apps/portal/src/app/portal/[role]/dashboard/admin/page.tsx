'use client';

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { useState } from 'react';

import CreateEditUser from './_components/admin-user-management';
import AffiliateManagement from './_components/affiliate-management';
import AgencyManagement from './_components/agency-management';
import DocumentManagement from './_components/document-management';
import TagManagement from './_components/tag-management';
import TrackManagement from './_components/track-management';

interface TabItem {
  label: string;
  value: string;
  component: React.ComponentType;
}

const tabItems: TabItem[] = [
  { label: 'Tracks', value: 'tracks', component: TrackManagement },
  { label: 'Users', value: 'users', component: CreateEditUser },
  { label: 'Resources', value: 'resources', component: DocumentManagement },
  { label: 'Affiliates', value: 'affiliates', component: AffiliateManagement },
  { label: 'Tags', value: 'tags', component: TagManagement },
  { label: 'Agencies', value: 'agencies', component: AgencyManagement },
];

const AdminPage = () => {
  const [selectedSection, setSelectedSection] = useState<string>('tracks');

  return (
    <div className='space-y-6'>
      <h1 className='text-2xl font-bold'>Admin Dashboard</h1>
      <p className='text-sm text-emaTextSecondary'>Manage your system settings and content</p>

      <Tabs defaultValue='tracks' value={selectedSection} onValueChange={setSelectedSection} className='space-y-4'>
        <div>
          <TabsList className='justify-start'>
            {tabItems.map((tab) => (
              <TabsTrigger key={tab.value} value={tab.value}>
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
        </div>

        <div className='rounded-md border border-emaBorderSecondary bg-white p-4'>
          {tabItems.map((tab) => (
            <TabsContent key={tab.value} value={tab.value}>
              <div>
                <tab.component />
              </div>
            </TabsContent>
          ))}
        </div>
      </Tabs>
    </div>
  );
};

export default AdminPage;
