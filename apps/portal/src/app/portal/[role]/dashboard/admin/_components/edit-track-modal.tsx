import FormFactory from '@/components/custom/form-factory';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useFindUniqueTrack, useUpdateTrack } from '@/hooks/generated';
import { toast } from '@/hooks/use-toast';
import { TrackSchema } from '@/types/schemas/track';
import { Loader2 } from 'lucide-react';
import React from 'react';
import { FieldValues, SubmitHandler } from 'react-hook-form';

import { editTrackConfig, editTrackSchema } from '../_lib/track-config';

// Define enum to match what's expected by the API
enum LanguageType {
  english = 'english',
  spanish = 'spanish',
}

interface Affiliate {
  id: string;
  name: string;
}

interface TrackUpdateData {
  title: string;
  description: string | null;
  track_summary: string | null;
  mom_summary: string | null;
  connection_description: string | null;
  language_type: LanguageType | null;
  affiliates?: {
    set: Array<{ id: string }> | [];
  };
}

interface EditTrackModalProps {
  track?: TrackSchema | null;
  onEditTrack: (track: TrackSchema) => void;
  affiliates?: Affiliate[];
  trigger: React.ReactNode;
  defaultValues?: TrackSchema | null;
  saveButtonText?: string;
  modalTitle?: string;
}

const EditTrackModal = ({
  track,
  onEditTrack,
  affiliates = [],
  trigger,
  defaultValues,
  saveButtonText = 'Save',
  modalTitle = 'Edit Track',
}: EditTrackModalProps) => {
  const [open, setOpen] = React.useState(false);
  const [selectedAffiliates, setSelectedAffiliates] = React.useState<{ label: string; value: string }[]>([]);
  const [formKey, setFormKey] = React.useState(0);
  const [isSaving, setIsSaving] = React.useState(false);
  const updateTrack = useUpdateTrack();

  // Use the generated hook to fetch track with its relationships
  const { data: trackData, isLoading } = useFindUniqueTrack(
    {
      where: { id: track?.id || '' },
      include: { affiliates: true },
    },
    {
      enabled: !!track?.id && open,
    },
  );

  // Initialize selectedAffiliates when track changes or when trackData is loaded
  React.useMemo(() => {
    // Skip updates during save operation
    if (isSaving) return;

    const dataTrack = trackData?.affiliates?.length ? trackData : track;

    if (dataTrack?.affiliates?.length) {
      const trackAffiliates =
        'affiliates' in dataTrack &&
        dataTrack.affiliates?.map((affiliate: Affiliate) => ({
          label: affiliate.name,
          value: affiliate.id,
        }));
      setSelectedAffiliates(trackAffiliates || []);
      // Force form re-render when selectedAffiliates changes
      setFormKey((prev) => prev + 1);
    } else if (!selectedAffiliates.length) {
      // Only clear affiliates if we don't already have some selected
      setSelectedAffiliates([]);
    }
  }, [track, trackData, selectedAffiliates.length, isSaving]);

  React.useEffect(() => {
    // Reset form when modal opens
    if (open) {
      // Force form to re-render with current values when modal opens
      setFormKey((prev) => prev + 1);
    }
  }, [open]);

  const handleSubmit: SubmitHandler<FieldValues> = async (data) => {
    try {
      setIsSaving(true);
      if (!track?.id) {
        throw new Error('Track ID is required for update');
      }

      const { affiliates, ...trackData } = data;

      // Get the selected affiliate IDs
      const newAffiliateIds: string[] = affiliates ? affiliates.map((a: { value: string }) => a.value) : [];

      // Basic track data including affiliates
      const submitData: TrackUpdateData = {
        title: trackData.title,
        description: trackData.description || null,
        track_summary: trackData.track_summary || null,
        mom_summary: trackData.mom_summary || null,
        connection_description: trackData.connection_description || null,
        language_type: (trackData.language_type?.toLowerCase() as LanguageType) || null,
      };

      // Now that we have the proper join table, we can include the affiliates
      if (newAffiliateIds.length > 0) {
        submitData.affiliates = {
          set: newAffiliateIds.map((id) => ({ id })),
        };
      } else {
        // If no affiliates are selected, disconnect all
        submitData.affiliates = {
          set: [],
        };
      }

      // Update track with affiliates
      const result = await updateTrack.mutateAsync({
        where: { id: track.id.toString() },
        data: submitData,
      });

      // Extract affiliate objects from form data for the UI
      const selectedAffiliateObjects = affiliates
        ? affiliates.map((item: { value: string; label: string }) => ({
            id: item.value,
            name: item.label,
          }))
        : [];

      // Store selected affiliates in state
      setSelectedAffiliates(affiliates || []);

      // Update the track with selected affiliates for the UI
      const updatedTrack = {
        ...result,
        affiliates: selectedAffiliateObjects,
      };

      // Pass the updated track back to the parent component
      onEditTrack(updatedTrack as TrackSchema);

      toast({
        title: 'Success',
        description: 'Track updated successfully',
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update track';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
      setOpen(false);
    }
  };

  // Ensure all track fields are included in the form defaults
  const formDefaultValues = React.useMemo(
    () => ({
      title: track?.title || '',
      description: track?.description || '',
      track_summary: track?.track_summary || '',
      mom_summary: track?.mom_summary || '',
      connection_description: track?.connection_description || '',
      language_type: track?.language_type || '',
      affiliates: selectedAffiliates,
      ...(defaultValues || {}),
    }),
    [track, selectedAffiliates, defaultValues],
  );

  const formFieldsWithDynamicOptions = editTrackConfig.map((field) => {
    if (field.dynamicOptionsKey === 'affiliates') {
      return {
        ...field,
        options: affiliates.map((affiliate) => ({
          label: affiliate.name,
          value: affiliate.id,
        })),
      };
    }
    return field;
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>{modalTitle}</DialogTitle>
          <DialogDescription>Update the track details and affiliates associated with this track.</DialogDescription>
        </DialogHeader>
        {isLoading ? (
          <div className='flex items-center justify-center py-8'>
            <Loader2 className='h-8 w-8 animate-spin text-primary' />
          </div>
        ) : (
          <FormFactory
            key={formKey}
            fields={formFieldsWithDynamicOptions}
            schema={editTrackSchema}
            onSubmit={handleSubmit}
            actionButtonsComponent={<Button type='submit'>{saveButtonText}</Button>}
            defaultValues={formDefaultValues}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};

export default EditTrackModal;
