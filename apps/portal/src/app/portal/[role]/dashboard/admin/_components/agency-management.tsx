import GenericDeleteConfirmationModal from '@/components/custom/generic-delete-confirmation-modal';
import GenericFormModal from '@/components/custom/generic-form-modal';
import GenericTable from '@/components/custom/generic-table';
import { Button } from '@/components/ui/button';
import { TableCell } from '@/components/ui/table';
import { useCreateAgency, useDeleteAgency, useFindManyAgency } from '@/hooks/generated';
import { toast } from '@/hooks/use-toast';
import { YupSchemas } from '@suiteapi/models';
import { PlusIcon } from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';
import { FieldValues, SubmitHandler } from 'react-hook-form';

import { createAgencyConfig, createAgencySchema } from '../_lib/create-agency.config';

const AgencyManagement = () => {
  const [isCreateAgencyModalOpen, setIsCreateAgencyModalOpen] = useState(false);
  const [agencyToDelete, setAgencyToDelete] = useState<string | null | undefined>(null);
  const router = useRouter();
  const pathname = usePathname();
  const {
    data: agencies,
    isLoading,
    refetch,
  } = useFindManyAgency({
    select: {
      id: true,
      agency_name: true,
      address_city: true,
      address_state: true,
      contact_first_name: true,
      contact_last_name: true,
    },
  });

  const createAgency = useCreateAgency();

  const handleCreateAgency: SubmitHandler<FieldValues> = async (data) => {
    try {
      await createAgency.mutateAsync({
        data: {
          name: data.agency_name,
          agency_name: data.agency_name,
          address_city: data.address_city ?? undefined,
          address_state: data.address_state ?? undefined,
          address_postalcode: data.address_postalcode ?? undefined,
          contact_first_name: data.contact_first_name,
          contact_last_name: data.contact_last_name,
          contact_email: data.contact_email ?? undefined,
          contact_phone: data.contact_phone ?? undefined,
        },
      });
      toast({
        title: 'Agency created successfully',
        className: 'bg-green-500 text-white',
      });
      refetch();
      setIsCreateAgencyModalOpen(false);
    } catch (error) {
      console.error('Error creating agency:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while creating the agency',
        variant: 'destructive',
      });
    }
  };

  const deleteAgency = useDeleteAgency({
    onSuccess: () => {
      refetch();
      setAgencyToDelete(null);
    },
  });

  const handleDeleteAgency = useCallback(
    async (id: string | undefined) => {
      if (!id) return;
      try {
        await deleteAgency.mutateAsync({
          where: { id: id.toString() },
        });
      } catch (error) {
        console.error('Error deleting agency:', error);
        toast({
          title: 'Error',
          description: 'An error occurred while deleting the agency',
          variant: 'destructive',
        });
        // Keep the modal open if there's an error
        return;
      }
      setAgencyToDelete(null);
    },
    [deleteAgency],
  );

  const handleEditAgency = useCallback(
    (id: string | undefined) => {
      if (!id) return;
      router.push(`${pathname}/agency/${id}`);
    },
    [pathname, router],
  );

  const rowRenderer = useCallback(
    (agency: YupSchemas.Agency) => {
      return (
        <>
          <TableCell>{agency.agency_name}</TableCell>
          <TableCell>
            {agency.address_city}, {agency.address_state}
          </TableCell>
          <TableCell>
            {agency.contact_first_name} {agency.contact_last_name}
          </TableCell>
          <TableCell className='flex gap-2'>
            <Button variant='outline' onClick={() => handleEditAgency(agency.id)}>
              View
            </Button>

            <GenericDeleteConfirmationModal
              title='Are you sure you want to delete this agency?'
              handleDelete={() => handleDeleteAgency(agency.id)}
              isDeleting={deleteAgency.isPending}
              open={agencyToDelete === agency.id}
              onOpenChange={(open: boolean) => setAgencyToDelete(open ? agency.id : null)}
            />
          </TableCell>
        </>
      );
    },
    [agencyToDelete, deleteAgency.isPending, handleEditAgency, handleDeleteAgency],
  );

  const mobileRenderer = useCallback(
    (agency: YupSchemas.Agency) => {
      return (
        <div onClick={() => handleEditAgency(agency.id)}>
          <p>{agency.agency_name}</p>
          <p>
            {agency.address_city}, {agency.address_state}
          </p>

          <p>
            {agency.contact_first_name} {agency.contact_last_name}
          </p>
        </div>
      );
    },
    [handleEditAgency],
  );

  return (
    <div>
      <div className='mb-4 flex items-center justify-between'>
        <div>
          <h2 className='text-xl font-semibold'>Agencies</h2>
          <p className='text-sm text-gray-500'>Agencies are associated with affiliates.</p>
        </div>
        <GenericFormModal
          modalTitle='Add an Agency'
          fields={createAgencyConfig}
          schema={createAgencySchema}
          onSubmit={handleCreateAgency}
          open={isCreateAgencyModalOpen}
          actionButtonText='Add Agency'
          onOpenChange={setIsCreateAgencyModalOpen}
          trigger={
            <Button variant='outline'>
              <PlusIcon className='mr-2 h-4 w-4' />
              Add Agency
            </Button>
          }
        />
      </div>
      <GenericTable<YupSchemas.Agency>
        data={agencies ?? []}
        rowRenderer={rowRenderer}
        mobileRenderer={mobileRenderer}
        isLoading={isLoading}
        loadingMessage={'Loading agencies...'}
        headers={['Agency Name', 'Location', 'Contact', 'Actions']}
      />
    </div>
  );
};

export default AgencyManagement;
