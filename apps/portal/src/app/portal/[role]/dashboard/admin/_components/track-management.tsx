'use client';

import GenericDeleteConfirmationModal from '@/components/custom/generic-delete-confirmation-modal';
import { Button } from '@/components/ui/button';
import { useDeleteTrack, useFindManyTrack } from '@/hooks/generated';
import { PlusIcon } from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';

import CreateTrackModal from './create-track-modal';

const TrackManagement = () => {
  const router = useRouter();
  const pathname = usePathname();

  const {
    data: tracks,
    isLoading,
    refetch,
  } = useFindManyTrack({
    select: {
      id: true,
      title: true,
      track_summary: true,
      language_type: true,
      description: true,
    },
  });

  const deleteTrack = useDeleteTrack({
    onSuccess: () => {
      refetch();
    },
  });
  const handleDeleteTrack = (id: string) => {
    deleteTrack.mutate({
      where: { id: id },
    });
  };

  return (
    <div>
      <div className='mb-4 flex items-center justify-between'>
        <div>
          <h2 className='text-xl font-semibold'>Available Tracks</h2>
          <p className='text-sm text-gray-500'>Manage educational tracks in the system</p>
        </div>
        <CreateTrackModal
          onCreateTrack={() => {
            refetch();
          }}
          trigger={
            <Button variant='outline'>
              <PlusIcon className='mr-2 h-4 w-4' />
              Add Track
            </Button>
          }
          saveButtonText='Add Track'
          modalTitle='Add Track'
        />
      </div>
      <div className='grid gap-4 xl:w-1/2'>
        {isLoading ? (
          <div>Loading...</div>
        ) : (
          tracks?.map((track) => (
            <div key={track.id} className='flex flex-row justify-between rounded-lg border bg-white p-4'>
              <div>
                <h2 className='text-xl font-semibold'>{track.title}</h2>
                <p className='text-gray-600'>{track.description}</p>
                {track.language_type ? (
                  <span className='mt-2 inline-block text-sm text-gray-500'>
                    Language: <span className='capitalize'>{track.language_type}</span>
                  </span>
                ) : null}
              </div>
              <div className='flex flex-row gap-2'>
                <Button variant='outline' onClick={() => router.push(`${pathname}/track/${track.id}`)}>
                  View
                </Button>
                <GenericDeleteConfirmationModal
                  title='Are you sure you want to delete this track?'
                  handleDelete={() => handleDeleteTrack(track.id)}
                  isDeleting={deleteTrack.isPending}
                />
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default TrackManagement;
