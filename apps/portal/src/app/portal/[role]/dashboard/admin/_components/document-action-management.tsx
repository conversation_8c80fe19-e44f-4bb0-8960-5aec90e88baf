import GenericDeleteConfirmationModal from '@/components/custom/generic-delete-confirmation-modal';
import { Button } from '@/components/ui/button';
import { TooltipTrigger } from '@/components/ui/tooltip';
import { TooltipContent } from '@/components/ui/tooltip';
import { Tooltip } from '@/components/ui/tooltip';
import { TooltipProvider } from '@/components/ui/tooltip';
import { YupSchemas } from '@suiteapi/models';
import { Star } from 'lucide-react';

import ManageTagsModal from './manage-document-tags-modal';

// Define interface for document tag type
interface DocumentTag {
  id?: string;
  name?: string;
  tag?: {
    id: string;
    name: string;
  };
}

// type guard function
function isDocumentTag(value: unknown): value is DocumentTag {
  if (!value || typeof value !== 'object') return false;

  const obj = value as Partial<DocumentTag>;
  if (!obj.tag || typeof obj.tag !== 'object') return false;

  return typeof obj.tag.id === 'string' && obj.tag.id !== '' && typeof obj.tag.name === 'string' && obj.tag.name !== '';
}

interface DocumentActionManagementProps {
  document: YupSchemas.DocumentSchema;
  handleDeleteDocument: (id: string) => void;
  handleTogglePrimaryResource: (id: string, isPrimary: boolean) => void;
  fetchDocuments: () => void;
  deleteDocument: {
    mutateAsync: (args: { where: { id: string } }) => Promise<unknown>;
    isPending: boolean;
  };
  editablePrimaryResource?: boolean;
}

const DocumentActionManagement = ({
  document,
  handleDeleteDocument,
  handleTogglePrimaryResource,
  fetchDocuments,
  deleteDocument,
  editablePrimaryResource = false,
}: DocumentActionManagementProps) => {
  const documentTags = (document.documentTags || []).filter(isDocumentTag).map((document) => {
    // Since isDocumentTag already validated document.tag exists and has id/name
    return {
      id: document.tag!.id,
      name: document.tag!.name,
    };
  });

  return (
    <>
      <ManageTagsModal documentId={document.id} currentTags={documentTags} onSuccess={fetchDocuments} />

      {/* Show the star in both contexts, but only make it clickable if editablePrimaryResource is true */}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {editablePrimaryResource ? (
              <Button
                variant='ghost'
                onClick={() => {
                  if (document.id && document.is_primary_lesson_resource !== undefined) {
                    handleTogglePrimaryResource(document.id, document.is_primary_lesson_resource);
                  }
                }}
              >
                <Star
                  className={`h-6 w-6 hover:cursor-pointer hover:fill-emaBrandTertiary hover:text-emaBrandTertiary ${
                    document.is_primary_lesson_resource
                      ? 'fill-emaBrandTertiary text-emaBrandTertiary hover:fill-transparent hover:text-emaTextQuaternary'
                      : 'text-emaTextQuaternary hover:fill-emaBrandTertiary hover:text-emaBrandTertiary'
                  }`}
                />
              </Button>
            ) : (
              <div className='px-3 py-2'>
                <Star
                  className={`h-6 w-6 ${
                    document.is_primary_lesson_resource
                      ? 'fill-emaBrandTertiary text-emaBrandTertiary'
                      : 'text-emaTextQuaternary'
                  }`}
                />
              </div>
            )}
          </TooltipTrigger>
          <TooltipContent>
            {document.is_primary_lesson_resource
              ? editablePrimaryResource
                ? 'Remove as primary lesson resource'
                : 'Primary lesson resource'
              : editablePrimaryResource
                ? 'Set as primary lesson resource'
                : 'Not a primary lesson resource'}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <GenericDeleteConfirmationModal
        title='Delete Document'
        description='Are you sure you want to delete this document?'
        handleDelete={() => document.id && handleDeleteDocument(document.id)}
        isDeleting={deleteDocument.isPending}
      />
    </>
  );
};

export default DocumentActionManagement;
