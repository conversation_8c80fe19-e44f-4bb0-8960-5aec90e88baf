import FormFactory from '@/components/custom/form-factory';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useCreateTrack } from '@/hooks/generated';
import { YupSchemas } from '@suiteapi/models';
import { ReactNode, useState } from 'react';
import { FieldValues, SubmitHandler } from 'react-hook-form';

import { createTrackConfig, createTrackSchema } from '../_lib/track-config';

interface CreateTrackModalProps {
  trigger: ReactNode;
  defaultValues?: YupSchemas.TrackSchema;
  saveButtonText?: string;
  modalTitle?: string;
  onCreateTrack?: () => void;
}

const CreateTrackModal = ({
  trigger,
  defaultValues,
  saveButtonText = 'Save',
  modalTitle = 'Edit Track',
  onCreateTrack,
}: CreateTrackModalProps) => {
  const createTrack = useCreateTrack();
  const [open, setOpen] = useState(false);
  const handleSubmit: SubmitHandler<FieldValues> = async (data) => {
    try {
      await createTrack.mutateAsync({
        data: {
          title: data.title,
          description: data.description,
          language_type: data.language_type,
        },
      });
      if (onCreateTrack) {
        onCreateTrack();
      }
    } catch (error) {
      console.error(error);
    } finally {
      setOpen(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>

      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>{modalTitle}</DialogTitle>
        </DialogHeader>

        <FormFactory
          fields={createTrackConfig}
          schema={createTrackSchema}
          onSubmit={handleSubmit}
          actionButtonsComponent={<Button type='submit'>{saveButtonText}</Button>}
          defaultValues={defaultValues}
        />
      </DialogContent>
    </Dialog>
  );
};

export default CreateTrackModal;
