import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const addLinkToDocumentTableConfig: FieldConfig[] = [
  {
    name: 'document_name',
    label: 'Link Name',
    type: 'text',
    placeholder: 'Enter link name',
  },
  {
    name: 'external_url_c',
    label: 'Link',
    type: 'text',
    placeholder: 'Enter link',
  },
];

export const documentLinkSchema = yup.object().shape({
  document_name: yup.string().required('Link name is required').min(2, 'Link name must be at least 2 characters'),
  external_url_c: yup.string().required('Link URL is required').url('Must be a valid URL, e.g. https://www.google.com'),
});

export type DocumentLink = yup.InferType<typeof documentLinkSchema>;
