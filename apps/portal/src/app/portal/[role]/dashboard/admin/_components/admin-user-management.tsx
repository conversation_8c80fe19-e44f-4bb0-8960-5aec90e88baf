'use client';

import GenericDeleteConfirmationModal from '@/components/custom/generic-delete-confirmation-modal';
import GenericTable from '@/components/custom/generic-table';
import GenericTag from '@/components/custom/generic-tag';
import { Button } from '@/components/ui/button';
import { TableCell } from '@/components/ui/table';
import { useDeleteUser, useFindManyUser } from '@/hooks/generated/user';
import { toast } from '@/hooks/use-toast';
import { PlusIcon } from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import React, { useCallback, useState } from 'react';

import CreateUserModal from './create-user-modal';

const AdminUserManagement = () => {
  const router = useRouter();
  const pathname = usePathname();
  const [userToDelete, setUserToDelete] = useState<string | null>(null);

  const {
    data: users = [],
    isLoading,
    refetch,
  } = useFindManyUser({
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      status: true,
      userRoles: {
        select: {
          role: true,
        },
      },
    },
  });

  const handleRefetchUsers = async () => {
    try {
      await refetch(); // Refetch the users data after successful creation
    } catch (error) {
      console.error('Error adding user:', error);
    }
  };

  const deleteUser = useDeleteUser({
    onSuccess: () => {
      refetch();
      setUserToDelete(null);
    },
  });

  const handleDeleteUser = useCallback(
    async (id: string | undefined) => {
      if (!id) return;
      try {
        await deleteUser.mutateAsync({
          where: { id: id.toString() },
        });
      } catch (error) {
        console.error('Error deleting user:', error);
        toast({
          title: 'Error',
          description: 'An error occurred while deleting the user',
        });
        return;
      }
      setUserToDelete(null);
    },
    [deleteUser],
  );

  // Define headers array
  const headers = ['Name', 'Email', 'Roles', 'Actions'];

  // Define row renderer function
  const rowRenderer = (user: (typeof users)[0]) => (
    <>
      <TableCell>{`${user.firstName} ${user.lastName}`}</TableCell>
      <TableCell>{user.email}</TableCell>
      <TableCell>
        {user.userRoles.map((role) => (
          <span
            key={role.role.id}
            className='m-2 inline-flex items-center rounded-full bg-blue-50 p-1 px-2 text-xs font-medium capitalize text-blue-700 ring-1 ring-inset ring-blue-700/10'
          >
            {role.role.name}
          </span>
        ))}
      </TableCell>
      <TableCell>
        <div className='flex flex-row gap-2'>
          <Button variant='outline' onClick={() => router.push(`${pathname}/user/${user.id}`)}>
            View
          </Button>
          <GenericDeleteConfirmationModal
            title='Are you sure you want to delete this user?'
            handleDelete={() => handleDeleteUser(user.id)}
            open={userToDelete === user.id}
            onOpenChange={(open: boolean) => setUserToDelete(open ? user.id : null)}
            isDeleting={deleteUser.isPending}
          />
        </div>
      </TableCell>
    </>
  );

  // Define mobile renderer function
  const mobileRenderer = (user: (typeof users)[0]) => (
    <div className='space-y-2'>
      <div className='font-medium'>{`${user.firstName} ${user.lastName}`}</div>
      <div>{user.email}</div>
      <div>
        {user.userRoles.map((role) => (
          <GenericTag tag={role.role.name} key={role.role.id} color='blue' />
        ))}
      </div>
      <div className='flex flex-row gap-2'>
        <Button variant='outline' onClick={() => router.push(`${pathname}/user/${user.id}`)}>
          View
        </Button>
        <GenericDeleteConfirmationModal
          title='Are you sure you want to delete this user?'
          handleDelete={() => handleDeleteUser(user.id)}
          open={userToDelete === user.id}
          onOpenChange={(open: boolean) => setUserToDelete(open ? user.id : null)}
          isDeleting={deleteUser.isPending}
        />
      </div>
    </div>
  );

  return (
    <div>
      <div className='mt-8 flex flex-row items-center justify-between'>
        <h2 id='users-heading' className='mb-4 text-xl font-semibold'>
          Users
        </h2>
        <CreateUserModal
          trigger={
            <Button variant='outline'>
              <PlusIcon className='mr-2 h-4 w-4' />
              Add User
            </Button>
          }
          saveButtonText='Add User'
          modalTitle='Add User'
          onCreateUser={handleRefetchUsers}
        />
      </div>
      <GenericTable
        data={users}
        isLoading={isLoading}
        headers={headers}
        rowRenderer={rowRenderer}
        mobileRenderer={mobileRenderer}
        columnWidths={[25, 25, 25, 25]}
      />
    </div>
  );
};

export default AdminUserManagement;
