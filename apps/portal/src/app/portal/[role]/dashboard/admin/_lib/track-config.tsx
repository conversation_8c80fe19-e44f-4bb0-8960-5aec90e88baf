import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const trackLanguageOptions = [
  {
    label: 'English',
    value: 'english',
  },
  {
    label: 'Spanish',
    value: 'spanish',
  },
];

export const createTrackConfig: FieldConfig[] = [
  {
    name: 'title',
    label: 'Title',
    type: 'text',
    placeholder: 'Enter title',
  },
  {
    name: 'description',
    label: 'Description',
    type: 'text',
    placeholder: 'Enter description',
  },
  {
    name: 'language_type',
    label: 'Language',
    type: 'select',
    options: trackLanguageOptions,
  },
];

export const editTrackConfig: FieldConfig[] = [
  {
    name: 'title',
    label: 'Title',
    type: 'text',
    placeholder: 'Enter title',
  },
  {
    name: 'description',
    label: 'Description',
    type: 'text',
    placeholder: 'Enter description',
  },
  {
    name: 'track_summary',
    label: 'Track Summary',
    type: 'text',
    placeholder: 'Enter track summary',
  },
  {
    name: 'mom_summary',
    label: 'Mom Summary',
    type: 'text',
    placeholder: 'Enter mom summary',
  },
  {
    name: 'connection_description',
    label: 'Connection Description',
    type: 'text',
    placeholder: 'Enter connection description',
  },
  {
    name: 'language_type',
    label: 'Language',
    type: 'select',
    options: trackLanguageOptions,
  },
  {
    name: 'affiliates',
    label: 'Affiliates',
    type: 'multi-select',
    dynamicOptionsKey: 'affiliates',
  },
];

export const createTrackSchema = yup.object().shape({
  title: yup.string().required('Title is required'),
  description: yup.string().required('Description is required'),
  language_type: yup
    .string()
    .oneOf(['english', 'spanish'], 'Please select a valid language')
    .required('Language is required'),
});

export const editTrackSchema = yup.object().shape({
  title: yup.string().required('Title is required'),
  description: yup.string().required('Description is required'),
  track_summary: yup.string().required('Track summary is required'),
  mom_summary: yup.string().required('Mom summary is required'),
  connection_description: yup.string().required('Connection description is required'),
  language_type: yup
    .string()
    .oneOf(['english', 'spanish'], 'Please select a valid language')
    .required('Language is required'),
  affiliates: yup.array().optional(),
});
