import WithRestrictedScrolling from '@/components/custom/with-restricted-scrolling';

import CommonDashboardLayout from '../../_components/dashboard/common-dashboard-layout';
import { DashboardProvider } from '../../context/dashboard-context';

const DashboardLayout = async ({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { role: string };
}): Promise<React.ReactNode> => (
  <WithRestrictedScrolling>
    <DashboardProvider>
      <CommonDashboardLayout roleParam={params.role}>{children}</CommonDashboardLayout>
    </DashboardProvider>
  </WithRestrictedScrolling>
);

export default DashboardLayout;
