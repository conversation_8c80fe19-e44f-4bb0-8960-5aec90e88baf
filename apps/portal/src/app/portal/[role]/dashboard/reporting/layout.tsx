'use client';

import { But<PERSON> } from '@/components/ui/button';
import { useUserInfo } from '@/context/user-info';
import { Loader2 } from 'lucide-react';
import { ReactNode } from 'react';

import { AdvocateFilter } from './_components/AdvocateFilter';
import { FilterButtons } from './_components/FilterButtons';
import { LocationFilter } from './_components/LocationFilter';
import { ReportSelect } from './_components/ReportSelect';
import { EXPORT_CONTENT_ID, useExportReport } from './_lib/report-export-store';

export default function ReportingLayout({ children }: { children: ReactNode }) {
  const { isAdmin, isAtLeastCoordinator } = useUserInfo();
  const [isExporting, exportReport] = useExportReport();

  return (
    <div className='flex w-full max-w-7xl flex-col gap-y-7 self-center'>
      <div className='mt-4 flex'>
        <div className='flex-1'>
          <h1 className='text-3xl font-semibold'>Reporting</h1>
          <p className='mt-2 text-muted-foreground'>Access all your reports from one place</p>
        </div>
        <div>
          <Button disabled={isExporting} onClick={exportReport}>
            {isExporting ? <Loader2 className='mx-3 h-5 w-5 animate-spin' /> : 'Export'}
          </Button>
        </div>
      </div>

      <ReportSelect />

      {isAtLeastCoordinator ? (
        <div className='rounded-md bg-[#F7F3F0] px-7 py-4'>
          <div className='flex flex-wrap items-center gap-4'>
            {isAdmin ? <LocationFilter /> : null}
            <AdvocateFilter />
          </div>

          <FilterButtons />
        </div>
      ) : null}

      <div id={EXPORT_CONTENT_ID}>{children}</div>
    </div>
  );
}
