'use client';

import { useToast } from '@/hooks/use-toast';
import { saveAs } from 'file-saver';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import JSZip from 'jszip';
import { useEffect, useState } from 'react';
import { create } from 'zustand';

export function useCsvExport(fileName: string, data: any) {
  const subscribeExporter = useReportExportStore((s) => s.registerExporter);
  useEffect(() => {
    const unsubscribe = subscribeExporter({
      id: fileName,
      handler: (zip) => zip.file(fileName + '.csv', generateCSV(data)),
    });
    return unsubscribe;
  }, [fileName, data]);
}

export function useExportReport(): [boolean, () => void] {
  const [isExporting, setIsExporting] = useState(false);
  const { toast } = useToast();
  return [
    isExporting,
    () => {
      setIsExporting(true);
      exportReport()
        .then(() => toast({ title: 'Export Completed', variant: 'success' }))
        .catch((e) =>
          toast({
            title: 'Error',
            variant: 'destructive',
            description: `Export failed: "${e.message}"`,
          }),
        )
        .finally(() => setIsExporting(false));
    },
  ];
}

type ReportExporter = {
  id: string;
  handler: (zip: JSZip) => void;
};

type ReportExportStore = {
  exporters: ReportExporter[];
  registerExporter: (reportExporter: ReportExporter) => () => void;
};

const useReportExportStore = create<ReportExportStore>((set) => ({
  exporters: [],

  registerExporter: (newItem) => {
    set((state) => ({
      exporters: [...state.exporters, newItem],
    }));

    return () => {
      set((state) => ({
        exporters: state.exporters.filter((item) => item.id !== newItem.id),
      }));
    };
  },
}));

async function exportReport() {
  const name = window.location.pathname.split('/').filter(Boolean).pop() + '-report';
  const { exporters } = useReportExportStore.getState();

  const zip = new JSZip();

  exporters.forEach(({ handler }) => {
    handler(zip);
  });

  const pdfBlob = await generatePDF();
  zip.file(name + '.pdf', pdfBlob);

  const html = getHtml();
  zip.file(name + '.html', html);

  const zipBlob = await zip.generateAsync({ type: 'blob' });
  saveAs(zipBlob, name + '.zip');
}

function generateCSV(data: any): string {
  if (!data.length) return '';

  const headers = Object.keys(data[0]);
  const rows = data.map((row: any) => headers.map((field) => JSON.stringify(row[field] ?? '')).join(','));

  return [headers.join(','), ...rows].join('\r\n');
}

export const EXPORT_CONTENT_ID = 'export-content';

async function generatePDF(): Promise<Blob> {
  const element = document.getElementById(EXPORT_CONTENT_ID);
  if (!element) throw new Error('Export content element not found');

  const canvas = await html2canvas(element, { scale: 2, scrollY: -window.scrollY });
  const imgData = canvas.toDataURL('image/png');

  const pdf = new jsPDF('p', 'mm', 'a4');
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();

  const margin = 10;
  const contentWidth = pageWidth - margin * 2;

  const imgProps = pdf.getImageProperties(imgData);
  const imgWidth = contentWidth;
  const imgHeight = (imgProps.height * imgWidth) / imgProps.width;

  let heightLeft = imgHeight;
  let position = margin;

  pdf.addImage(imgData, 'PNG', margin, position, imgWidth, imgHeight);
  heightLeft -= pageHeight - margin * 2;

  while (heightLeft > 0) {
    position = heightLeft - imgHeight + margin;
    pdf.addPage();
    pdf.addImage(imgData, 'PNG', margin, position, imgWidth, imgHeight);
    heightLeft -= pageHeight - margin * 2;
  }

  return pdf.output('blob');
}

function getHtml() {
  const element = document.getElementById(EXPORT_CONTENT_ID);
  if (!element) throw new Error('Export content element not found');
  return element.outerHTML;
}
