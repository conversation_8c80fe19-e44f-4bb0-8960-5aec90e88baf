'use client';

import { Option } from 'react-multi-select-component';
import { create } from 'zustand';

type ReportFilterStore = {
  advocateFilter: Option[];
  advocateFilterInput: Option[];
  locationFilter: string;
  locationFilterInput: string;
  applyFilters: () => void;
  clearFilters: () => void;
  setAdvocateFilter: (value: Option[]) => void;
  setAdvocateFilterInput: (value: Option[]) => void;
  setLocationFilterInput: (value: string) => void;
};

const useFilterStore = create<ReportFilterStore>((set) => ({
  advocateFilter: [],
  advocateFilterInput: [],
  locationFilter: '',
  locationFilterInput: '',
  applyFilters: () => {
    set((state) => ({
      advocateFilter: state.advocateFilterInput,
      locationFilter: state.locationFilterInput,
    }));
  },
  clearFilters: () => {
    set(() => ({
      advocateFilter: [],
      locationFilter: '',
      advocateFilterInput: [],
      locationFilterInput: '',
    }));
  },
  setAdvocateFilter: (value) => {
    set(() => ({
      advocateFilter: value,
    }));
  },
  setAdvocateFilterInput: (value) => {
    set(() => ({
      advocateFilterInput: value,
    }));
  },
  setLocationFilterInput: (value) => {
    set(() => ({
      locationFilterInput: value,
    }));
  },
}));

export const useAdvocateFilter = () => useFilterStore((state) => state.advocateFilter);
export const useAdvocateFilterInput = () => useFilterStore((state) => state.advocateFilterInput);
export const useLocationFilter = () => useFilterStore((state) => state.locationFilter);
export const useLocationFilterInput = () => useFilterStore((state) => state.locationFilterInput);
export const useApplyFilters = () => useFilterStore((state) => state.applyFilters);
export const useClearFilters = () => useFilterStore((state) => state.clearFilters);
export const useSetAdvocateFilter = () => useFilterStore((state) => state.setAdvocateFilter);
export const useSetAdvocateFilterInput = () => useFilterStore((state) => state.setAdvocateFilterInput);
export const useSetLocationFilterInput = () => useFilterStore((state) => state.setLocationFilterInput);
