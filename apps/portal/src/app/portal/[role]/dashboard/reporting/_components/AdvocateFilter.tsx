'use client';

import { useUserInfo } from '@/context/user-info';
import { useFindManyUser } from '@/hooks/generated';
import { useEffect, useMemo, useRef } from 'react';
import { MultiSelect, Option } from 'react-multi-select-component';

import {
  useAdvocateFilterInput,
  useLocationFilterInput,
  useSetAdvocateFilter,
  useSetAdvocateFilterInput,
} from '../_lib/report-filter-store';

export function AdvocateFilter() {
  const setAdvocateFilter = useSetAdvocateFilter();
  const setAdvocateFilterInput = useSetAdvocateFilterInput();
  const advocateFilterInput = useAdvocateFilterInput();
  const locationFilterInput = useLocationFilterInput();

  const { userData, isAdmin } = useUserInfo();

  let affiliateWhereClause = {};
  if (isAdmin && locationFilterInput !== 'any' && locationFilterInput !== '') {
    affiliateWhereClause = { affiliateId: { equals: locationFilterInput } };
  } else {
    affiliateWhereClause = { affiliateId: { equals: userData?.affiliateId } };
  }

  const { data: advocates = [], isLoading } = useFindManyUser({
    select: {
      id: true,
      firstName: true,
      lastName: true,
    },
    where: {
      userRoles: {
        some: {
          role: {
            key: 'advocate',
          },
        },
      },
      ...affiliateWhereClause,
    },
  });

  const options = useMemo(
    () =>
      advocates.map((advocate) => ({
        value: advocate.id,
        label: `${advocate.firstName} ${advocate.lastName}`,
      })),
    [advocates],
  );

  const isFirstLoad = useRef(true);
  useEffect(() => {
    if (isLoading) return;
    setAdvocateFilterInput(options);
    if (isFirstLoad.current) {
      setAdvocateFilter(options);
      isFirstLoad.current = false;
    }
  }, [setAdvocateFilter, setAdvocateFilterInput, options, isLoading, isFirstLoad]);

  return (
    <div>
      <label id='advocate-select-label'>Advocate</label>
      <MultiSelect
        hasSelectAll={true}
        className='mt-4 w-72 border-input'
        labelledBy='advocate-select-label'
        options={options}
        overrideStrings={{
          selectSomeItems: 'All',
          allItemsAreSelected: 'All',
        }}
        value={advocateFilterInput}
        onChange={(selectedOptions: Option[]) => {
          setAdvocateFilterInput(selectedOptions);
        }}
      />
    </div>
  );
}
