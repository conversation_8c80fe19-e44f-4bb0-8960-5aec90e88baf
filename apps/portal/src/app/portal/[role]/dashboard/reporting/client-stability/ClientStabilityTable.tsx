import { WellnessAssessment } from '@/hooks/generated/__types';
import { useMemo } from 'react';

import { useCsvExport } from '../_lib/report-export-store';
import { latestScoreAverages, metrics } from './_lib/client-stability';

export function ClientStabilityTable({ assessments }: { assessments: WellnessAssessment[] }) {
  const scores = useMemo(() => latestScoreAverages(assessments), [assessments]);

  const csvData = useMemo(
    () =>
      metrics.map((m) => ({
        'Summary Name': m.label,
        Range: m.range,
        Baseline: m.baseline,
        "Mom's Average Score": scores[m.key],
      })),
    [scores],
  );

  useCsvExport('Client Stability Table', csvData);

  return (
    <div className='overflow-x-auto'>
      <table className='min-w-full table-auto text-left text-sm'>
        <thead>
          <tr className='border-b border-emaBorderSecondary text-xs text-emaTextTertiary'>
            <th className='px-4 py-3 font-medium'>Summary Name</th>
            <th className='px-4 py-3 font-medium'>Range</th>
            <th className='px-4 py-3 font-medium'>Baseline</th>
            <th className='px-4 py-3 font-medium'>Mom’s Average Score</th>
          </tr>
        </thead>
        <tbody className='divide-y divide-emaBorderSecondary'>
          {metrics.map((item, index) => (
            <tr key={index}>
              <td className='px-4 py-5 font-medium text-emaTextPrimary'>{item.label}</td>
              <td className='px-4 py-5 text-emaTextTertiary'>{item.range}</td>
              <td className='px-4 py-5 text-emaTextTertiary'>{item.baseline}</td>
              <td className='px-4 py-5 text-emaTextTertiary'>{scores[item.key]}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
