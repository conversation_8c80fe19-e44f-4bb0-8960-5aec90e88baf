'use client';

import { Button } from '@/components/ui/button';
import { isEqual } from 'lodash';

import {
  useAdvocateFilter,
  useAdvocateFilterInput,
  useApplyFilters,
  useClearFilters,
  useLocationFilter,
  useLocationFilterInput,
} from '../_lib/report-filter-store';

export function FilterButtons() {
  const advocateFilterInput = useAdvocateFilterInput();
  const locationFilterInput = useLocationFilterInput();
  const advocateFilter = useAdvocateFilter();
  const locationFilter = useLocationFilter();
  const clearFilters = useClearFilters();
  const applyFilters = useApplyFilters();

  const isApplyDisabled = isEqual(advocateFilter, advocateFilterInput) && isEqual(locationFilter, locationFilterInput);

  const isClearDisabled =
    advocateFilterInput.length === 0 &&
    locationFilterInput === 'any' &&
    advocateFilter.length === 0 &&
    locationFilter === 'any';

  return (
    <div className='mt-7 flex items-center justify-end'>
      <Button variant='link' disabled={isClearDisabled} onClick={clearFilters}>
        Clear filters
      </Button>
      <Button className='ml-4' variant='outline' disabled={isApplyDisabled} onClick={applyFilters}>
        Apply filters
      </Button>
    </div>
  );
}
