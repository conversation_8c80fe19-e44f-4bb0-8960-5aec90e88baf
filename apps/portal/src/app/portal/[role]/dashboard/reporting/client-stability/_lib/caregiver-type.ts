import { Mom } from '@/hooks/generated/__types';

type CaregiverValues = 'biological_mom' | 'relative_guardian' | 'nonrelative_guardian' | 'adoptive_mom';

const metrics: { label: string; value: CaregiverValues; color: string }[] = [
  { label: 'Biological Mom', value: 'biological_mom', color: '#123333' },
  { label: 'Relative Guardian', value: 'relative_guardian', color: '#3C595A' },
  { label: 'Non-Relative Guardian', value: 'nonrelative_guardian', color: '#637A7B' },
  { label: 'Adoptive Mom', value: 'adoptive_mom', color: '#7C8B8C' },
];

const roundToTenths = (n: number) => Math.round(n * 10) / 10;

export function getCaregiverPercentages(moms: Pick<Mom, 'caregiver_type_c'>[]) {
  const filtered = moms.filter((m) => !!m.caregiver_type_c) as { caregiver_type_c: CaregiverValues }[];

  const groups = filtered.reduce<{ [key: string]: number }>(
    (acc, { caregiver_type_c }) => ({
      ...acc,
      [caregiver_type_c]: (acc[caregiver_type_c] ?? 0) + 1,
    }),
    {},
  );

  const total = filtered.length;

  if (total === 0) {
    return metrics.map((m) => ({ ...m, percentage: 0 }));
  }
  return metrics.map((m) => ({ ...m, percentage: roundToTenths(((groups[m.value] ?? 0) / total) * 100) }));
}
