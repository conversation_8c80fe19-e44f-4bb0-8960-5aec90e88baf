import { useMemo } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveC<PERSON><PERSON>, Toolt<PERSON> } from 'recharts';

import { useCsvExport } from '../_lib/report-export-store';
import { getCaregiverPercentages } from './_lib/caregiver-type';

export function CaregiverDonutChart({ moms }: { moms: { id: string; caregiver_type_c: string | null }[] }) {
  const percentages = useMemo(() => getCaregiverPercentages(moms), [moms]);

  const csvData = useMemo(
    () => percentages.map((p) => ({ 'Caregiver Type': p.label, Percentage: p.percentage })),
    [percentages],
  );

  useCsvExport('Caregiver Type', csvData);

  return (
    <div>
      <div className='flex items-center border-b border-emaBorderSecondary px-4'>
        <div className='text-left text-lg font-semibold text-emaTextPrimary'>Caregiver Type</div>
        <div className='flex flex-1 flex-wrap items-center justify-end'>
          {percentages.map((item, index) => (
            <div key={index} className='border-l border-emaBorderSecondary p-4 pb-3'>
              <div className='flex text-sm text-emaTextSecondary'>
                <div className='mt-[6px] h-2 w-2 rounded-full' style={{ backgroundColor: item.color }}></div>
                <div className='ml-2'>
                  <div className='max-w-20 md:max-w-64'>{item.label}</div>
                  <div className='text-2xl font-bold text-emaTextPrimary'>{(item.percentage || 0).toFixed(1)}%</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className='mt-4 h-80 pb-4'>
        <ResponsiveContainer width='100%' height='100%'>
          <PieChart>
            <Pie
              data={percentages}
              dataKey='percentage'
              nameKey='label'
              cx='50%'
              cy='50%'
              innerRadius='55%'
              outerRadius='100%'
              stroke='none'
            >
              {percentages.map((item) => (
                <Cell key={`cell-${item.value}`} fill={item.color} />
              ))}
            </Pie>
            <Tooltip
              formatter={(value: number, name: string) => [`${value.toFixed(1)}%`, name]}
              contentStyle={{ borderRadius: '8px', fontWeight: '500' }}
            />
          </PieChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
