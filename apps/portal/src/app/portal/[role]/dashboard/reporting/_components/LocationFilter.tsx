import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useFindManyAffiliate } from '@/hooks/generated';

import { useLocationFilterInput, useSetLocationFilterInput } from '../_lib/report-filter-store';

export function LocationFilter() {
  const locationFilterInput = useLocationFilterInput();
  const setLocationFilterInput = useSetLocationFilterInput();

  const { data: affiliates = [] } = useFindManyAffiliate({
    select: {
      id: true,
      name: true,
    },
  });

  const locationOptions = affiliates.map((affiliate) => (
    <SelectItem className='hover:cursor-pointer' key={affiliate.id} value={affiliate.id}>
      {affiliate.name}
    </SelectItem>
  ));

  return (
    <label className='hover:cursor-pointer'>
      <span className='text-emaTextSecondary'>ĒMA Location</span>
      <Select value={locationFilterInput} onValueChange={setLocationFilterInput}>
        <SelectTrigger className='mt-4 w-72 min-w-64'>
          <SelectValue placeholder='Any' />
        </SelectTrigger>
        <SelectContent>
          <SelectItem className='hover:cursor-pointer' value='any'>
            Any
          </SelectItem>
          {locationOptions}
        </SelectContent>
      </Select>
    </label>
  );
}
