'use client';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useRouter } from 'next/navigation';

export function ReportSelect() {
  const router = useRouter();
  return (
    <div className='flex items-center'>
      <label>Select a Report:</label>
      <Select
        defaultValue='client-stability'
        onValueChange={(value) => {
          router.push('reporting/' + value);
        }}
      >
        <SelectTrigger className='ml-2 w-48'>
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value='client-stability'>Client Stability</SelectItem>
          {/* <SelectItem value='track-engagement'>Track Engagement</SelectItem>
          <SelectItem value='advocate-engagement'>Advocate Engagement Summary</SelectItem>
          <SelectItem value='coordinator-load'>Location Coordinator Load Distribution</SelectItem> */}
        </SelectContent>
      </Select>
    </div>
  );
}
