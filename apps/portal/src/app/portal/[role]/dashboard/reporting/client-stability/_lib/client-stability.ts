'use client';

import { WellnessAssessment } from '@/hooks/generated/__types';
import { sortBy } from 'lodash';

type DbKeys =
  | 'soc_score'
  | 'cw_score'
  | 'naa_score'
  | 'cc_score'
  | 'emp_score'
  | 'home_score'
  | 'res_score'
  | 'trnprt_score'
  | 'well_score';
type MetricKeys =
  | 'socialSupport'
  | 'childWelfare'
  | 'nurturing'
  | 'careOfChildren'
  | 'employment'
  | 'housing'
  | 'resilience'
  | 'transportation'
  | 'wellness';

export const metrics: Array<{
  key: MetricKeys;
  label: string;
  color: string;
  dbKey: DbKeys;
  range: string;
  baseline: string;
}> = [
  {
    key: 'socialSupport',
    label: 'Social Support',
    color: '#3B82F6',
    dbKey: 'soc_score',
    range: '(0-249)',
    baseline: '(0-249)',
  },
  {
    key: 'childWelfare',
    label: 'Child Welfare',
    color: '#8B5CF6',
    dbKey: 'cw_score',
    range: '(0-249)',
    baseline: '(0-249)',
  },
  {
    key: 'nurturing',
    label: 'Nurturing and Attachment',
    color: '#EC4899',
    dbKey: 'naa_score',
    range: '(0-27)',
    baseline: '(0-27)',
  },
  {
    key: 'careOfChildren',
    label: 'Care of Children',
    color: '#10B981',
    dbKey: 'cc_score',
    range: '(0-6)',
    baseline: '(0-6)',
  },
  {
    key: 'employment',
    label: 'Employment and Financial',
    color: '#F59E0B',
    dbKey: 'emp_score',
    range: '(0-4)',
    baseline: '(0-4)',
  },
  { key: 'housing', label: 'Housing', color: '#6366F1', dbKey: 'home_score', range: '(0-4)', baseline: '(0-4)' },
  {
    key: 'resilience',
    label: 'Resilience',
    color: '#6366F1',
    dbKey: 'res_score',
    range: '(0-4)',
    baseline: '(0-4)',
  },
  {
    key: 'transportation',
    label: 'Transportation',
    color: '#6366F1',
    dbKey: 'trnprt_score',
    range: '(0-4)',
    baseline: '(0-4)',
  },
  { key: 'wellness', label: 'Wellness', color: '#6366F1', dbKey: 'well_score', range: '(0-4)', baseline: '(0-4)' },
];

export const zeros = metrics.reduce((o, { key }) => ({ ...o, [key]: 0 }), {}) as Omit<ScoreGrouping, 'month'>;

export type ScoreGrouping = {
  month: number;
  socialSupport: number;
  childWelfare: number;
  nurturing: number;
  careOfChildren: number;
  employment: number;
  housing: number;
  resilience: number;
  transportation: number;
  wellness: number;
};

export function getScoreDifferences(scores: ScoreGrouping[]) {
  if (!scores.length) return zeros;
  const sortedByMonth = sortBy(scores, 'month');
  const startingScores = sortedByMonth[0];
  const endingScores = sortedByMonth[sortedByMonth.length - 1];

  return metrics.reduce(
    (o, { key }) => ({
      ...o,
      [key]: roundToTenths(endingScores[key] - startingScores[key]),
    }),
    {},
  ) as Omit<ScoreGrouping, 'month'>;
}

const roundToTenths = (n: number) => Math.round(n * 10) / 10;

const filterOutNulls = (assessments: WellnessAssessment[]) =>
  assessments.filter(
    (a) => !!a.completed_date && metrics.every(({ dbKey }) => a[dbKey] != null),
  ) as FilteredAssessment[];

type FilteredAssessment = Omit<WellnessAssessment, DbKeys & 'completed_date'> & { completed_date: Date } & Record<
    DbKeys,
    number
  >;

export function latestScoreAverages(assessments: WellnessAssessment[]) {
  const filtered = filterOutNulls(assessments);
  if (!filtered.length) return zeros;
  const sortedByLatestFirst = sortBy(filtered, 'completed_date').reverse();
  const latestMap: Record<string, FilteredAssessment> = {};

  for (const a of sortedByLatestFirst) {
    if (!latestMap[a.mom_id]) {
      latestMap[a.mom_id] = a;
    }
  }

  const latestScores = Object.values(latestMap);

  return metrics.reduce(
    (acc, { key, dbKey }) => ({
      ...acc,
      [key]: roundToTenths(latestScores.reduce((sum, a) => sum + a[dbKey], 0) / filtered.length),
    }),
    {},
  ) as Omit<ScoreGrouping, 'month'>;
}

export function averageScoresByMonthInterval(assessments: WellnessAssessment[]) {
  const filtered = filterOutNulls(assessments);
  const sortedByCompletedDate = sortBy(filtered, 'completed_date');

  const momZeroDateMap: Record<string, Date> = {};
  const groupedByMonth: Record<number, FilteredAssessment[]> = {};

  for (const assessment of sortedByCompletedDate) {
    let month = 0;
    if (!momZeroDateMap[assessment.mom_id]) {
      momZeroDateMap[assessment.mom_id] = assessment.completed_date;
    } else {
      month = closestMonthInterval(momZeroDateMap[assessment.mom_id], assessment.completed_date);
    }

    if (!groupedByMonth[month]) {
      groupedByMonth[month] = [];
    }

    groupedByMonth[month].push(assessment);
  }

  return Object.entries(groupedByMonth).map(([month, scores]) => ({
    month: parseInt(month),
    ...metrics.reduce(
      (acc, { key, dbKey }) => ({
        ...acc,
        [key]: scores.reduce((sum, item) => sum + item[dbKey], 0) / scores.length,
      }),
      {},
    ),
  })) as ScoreGrouping[];
}

function closestMonthInterval(startDate: Date, inputDate: Date) {
  const start = new Date(startDate);
  const input = new Date(inputDate);

  const yearDiff = input.getFullYear() - start.getFullYear();
  const monthDiff = input.getMonth() - start.getMonth();
  const dayDiff = input.getDate() - start.getDate();

  // Approximate total months including day fraction
  const months = yearDiff * 12 + monthDiff + dayDiff / 30;

  // Round to nearest multiple of 3
  const closestInterval = Math.round(months / 3) * 3;

  return closestInterval;
}
