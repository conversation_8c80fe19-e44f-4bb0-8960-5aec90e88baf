'use client';

import { useFindManyMom, useFindManyPairing, useFindManyWellnessAssessment } from '@/hooks/generated';

import { useAdvocateFilter } from '../_lib/report-filter-store';
import { CaregiverDonutChart } from './CaregiverTypeDonutChart';
import { ClientStabilityLineChart } from './ClientStabilityLineChart';
import { ClientStabilityTable } from './ClientStabilityTable';

export default function ClientStabilityReportPage() {
  const advocateFilter = useAdvocateFilter();

  const advocateIds = advocateFilter.map((advocate) => advocate.value);

  const { data: pairings = [] } = useFindManyPairing(
    {
      select: {
        momId: true,
      },
      where: {
        advocateUserId: { in: advocateIds },
      },
    },
    {
      enabled: advocateIds.length > 0,
    },
  );

  const momIds = pairings.map((pairing) => pairing.momId).filter((momId) => momId !== null);

  const { data: assessments = [] } = useFindManyWellnessAssessment(
    {
      where: {
        mom_id: { in: momIds },
      },
    },
    { enabled: momIds.length > 0 },
  );

  const { data: moms = [] } = useFindManyMom(
    {
      select: {
        id: true,
        caregiver_type_c: true,
      },
      where: {
        id: { in: momIds },
      },
    },
    { enabled: momIds.length > 0 },
  );

  return (
    <div className='flex flex-col gap-4'>
      <div className='max-w-7xl rounded-xl bg-white shadow'>
        <div className='mt-4 px-4'>
          <h2 className='text-lg font-semibold text-emaTextPrimary'>Client Stability</h2>
          <p className='text-emaTextTertiary'>Here you can see Client Stability scores over time</p>
        </div>

        <div className='rounded-xl bg-white shadow'>
          <ClientStabilityLineChart assessments={assessments} />
        </div>
      </div>

      <div className='rounded-xl bg-white shadow'>
        <ClientStabilityTable assessments={assessments} />
      </div>

      <div className='rounded-xl bg-white shadow'>
        <CaregiverDonutChart moms={moms} />
      </div>
    </div>
  );
}
