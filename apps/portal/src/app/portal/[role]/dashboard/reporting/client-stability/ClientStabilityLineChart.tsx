'use client';

import { WellnessAssessment } from '@/hooks/generated/__types';
import { useMemo } from 'react';
import { CartesianGrid, Line, LineChart, ReferenceLine, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

import { useCsvExport } from '../_lib/report-export-store';
import { averageScoresByMonthInterval, getScoreDifferences, metrics, zeros } from './_lib/client-stability';

export function ClientStabilityLineChart({ assessments }: { assessments: WellnessAssessment[] }) {
  const [scores, differences, latestScores] = useMemo(() => {
    const scores = averageScoresByMonthInterval(assessments);
    const differences = getScoreDifferences(scores);
    const latestScores = scores[scores.length - 1] ?? zeros;
    return [scores, differences, latestScores];
  }, [assessments]);

  const summaryData = useMemo(
    () =>
      metrics.map((m) => ({
        'Summary Name': m.label,
        Score: latestScores[m.key],
        Change: differences[m.key],
      })),
    [latestScores, differences],
  );

  useCsvExport('Client Stability Summary', summaryData);

  useCsvExport('Client Stability Line Chart', scores);

  return (
    <div className='flex flex-col gap-4 p-4 pr-12'>
      <div className='overflow-x-auto pb-4'>
        <table>
          <tbody>
            <tr>
              {metrics.map((metric) => (
                <td key={metric.label} className='px-3 align-top'>
                  <div className='flex flex-row gap-2'>
                    <div className='mt-2 h-2 w-2 rounded-full' style={{ backgroundColor: metric.color }} />
                    <div className='w-24 text-emaTextTertiary'>{metric.label}</div>
                  </div>
                </td>
              ))}
            </tr>
            <tr>
              {metrics.map((metric) => (
                <td key={metric.label} className='px-3'>
                  <div className='ml-4 text-xl font-semibold text-emaTextPrimary'>
                    {latestScores[metric.key] as number}
                    <span
                      className={`ml-1 text-sm font-normal text-${differences[metric.key] < 0 ? 'red' : 'green'}-600`}
                    >
                      {differences[metric.key]}
                    </span>
                  </div>
                </td>
              ))}
            </tr>
          </tbody>
        </table>
      </div>

      <ResponsiveContainer height={300}>
        <LineChart data={scores}>
          <CartesianGrid stroke='#E5E7EB' vertical={false} />
          <ReferenceLine x={scores[0]?.month} stroke='#E5E7EB' strokeWidth={1} />
          <ReferenceLine x={scores[scores.length - 1]?.month} stroke='#E5E7EB' strokeWidth={1} />
          <XAxis dataKey='month' tickLine={false} axisLine={false} tick={{ fill: '#475366' }} />
          <YAxis tickLine={false} axisLine={false} tick={{ fill: '#475366' }} />
          {metrics.map((metric) => (
            <Line
              key={metric.key}
              type='monotone'
              dataKey={metric.key}
              stroke={metric.color}
              strokeWidth={2}
              dot={false}
            />
          ))}
          <Tooltip />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
