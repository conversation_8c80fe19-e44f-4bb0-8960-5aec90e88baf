'use client';

import LandingPageAdvocate from '../../_components/dashboard/landing-page-advocate';
import LandingPageCoordinatorSupervisor from '../../_components/dashboard/landing-page-coordinator-supervisor';
import LandingPageProspectiveAdvocate from '../../_components/dashboard/landing-page-prospective-advocate';
import { AppointmentsProvider } from '../../context/appointments-context';
import { MomFlaggedNeedsProvider } from '../../context/mom-flagged-needs-context';
import { MomProfileProvider } from '../../context/mom-profile-context';
import { PairedMomsListProvider } from '../../context/paired-moms-context';

export default function DashboardPage({ params }: { params: { role: string } }): React.ReactNode {
  const { role } = params;

  // TODO: Prospective advocate solution not designed yet from backend/SuiteCRM perspective - refactor if necessary
  if (role === 'prospective-advocate') {
    return <LandingPageProspectiveAdvocate />;
  }

  return (
    <MomProfileProvider>
      <MomFlaggedNeedsProvider>
        <AppointmentsProvider>
          <PairedMomsListProvider>
            {role === 'advocate' ? <LandingPageAdvocate /> : null}
            {role === 'coordinator' || role === 'supervisor' ? (
              <LandingPageCoordinatorSupervisor primaryRole={role} />
            ) : null}
          </PairedMomsListProvider>
        </AppointmentsProvider>
      </MomFlaggedNeedsProvider>
    </MomProfileProvider>
  );
}
