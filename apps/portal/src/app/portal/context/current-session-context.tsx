'use client';

import React from 'react';
import { createContext, useContext } from 'react';

interface CurrentSessionContextType {
  currentSessionId: string;
}

const CurrentSessionContext = createContext<CurrentSessionContextType | undefined>(undefined);

export const CurrentSessionProvider: React.FC<{ currentSessionId: string; children: React.ReactNode }> = ({
  currentSessionId,
  children,
}): JSX.Element => {
  return <CurrentSessionContext.Provider value={{ currentSessionId }}>{children}</CurrentSessionContext.Provider>;
};

export const useCurrentSessionContext = (): CurrentSessionContextType => {
  const context = useContext(CurrentSessionContext);
  if (!context) {
    throw new Error('useCurrentSessionContext must be used within a CurrentSessionProvider');
  }
  return context;
};
