'use client';

import React from 'react';
import { createContext, useContext, useState } from 'react';

interface AssessmentsTabsContextType {
  currentTab: string;
  setCurrentTab: (tab: string) => void;
}

export const AssessmentsTabsContext = createContext<AssessmentsTabsContextType | undefined>(undefined);

export const AssessmentsTabsProvider: React.FC<{
  children: React.ReactNode;
  initialTab: string;
}> = ({ children, initialTab }): JSX.Element => {
  const [currentTab, setCurrentTab] = useState<string>(initialTab);

  return (
    <AssessmentsTabsContext.Provider
      value={{
        currentTab,
        setCurrentTab,
      }}
    >
      {children}
    </AssessmentsTabsContext.Provider>
  );
};

export const useAssessmentsTabsContext = (): AssessmentsTabsContextType => {
  const context = useContext(AssessmentsTabsContext);
  if (!context) {
    throw new Error('useAssessmentsTabsContext must be used within a AssessmentsTabsProvider');
  }
  return context;
};
