'use client';

import { getActivePairings, getSessionNotesListWithActivePairingByMomId } from '@/lib/portal';
import type { PairingResponseSchema } from '@/types/schemas/pairing';
import type { SessionSchema } from '@/types/schemas/session';
import { createContext, useCallback, useContext, useState } from 'react';

type ExtendedPairingResponseSchema = PairingResponseSchema & {
  momId?: string;
};
type PairedMomsList = ExtendedPairingResponseSchema[];
type SessionsByMomId = Record<string, SessionSchema[]>;

interface PairedMomsListContextType {
  pairedMomsLoading: boolean;
  pairedMomsList: PairedMomsList | null;
  fetchPairedMomsList: () => Promise<void>;
  sessionsByMomId: SessionsByMomId;
  fetchSessionsForPairings: () => Promise<void>;
  updatePairingsListTrigger: number;
  signalUpdatePairingsList: () => void;
}

const PairedMomsListContext = createContext<PairedMomsListContextType | undefined>(undefined);

export const PairedMomsListProvider: React.FC<{ children: React.ReactNode }> = ({ children }): JSX.Element => {
  const [pairedMomsLoading, setPairedMomsLoading] = useState<boolean>(true);
  const [pairedMomsList, setPairedMomsList] = useState<PairedMomsList | null>(null);
  const [sessionsByMomId, setSessionsByMomId] = useState<SessionsByMomId>({});

  /**
   * `updatePairingsListTrigger` can be incremented in the schedule session
   * modal or elsewhere to trigger an update of the list.
   */
  const [updatePairingsListTrigger, setUpdatePairingsListTrigger] = useState<number>(0);

  const fetchPairedMomsList = useCallback(async () => {
    setPairedMomsLoading(true);
    try {
      let pairedMomsData = await getActivePairings();
      pairedMomsData = pairedMomsData.filter((pairing) => pairing.mom !== null);
      setPairedMomsList(pairedMomsData);
    } catch (error) {
      console.error('Error fetching paired moms list:', error);
      // TODO: handle errors
    } finally {
      setPairedMomsLoading(false);
    }
  }, []);

  const fetchSessionsForPairings = useCallback(async () => {
    if (pairedMomsList?.length) {
      const sessionsForEachId = pairedMomsList.map(async (pairing) => {
        const momId = pairing.mom?.id as string;
        const sessions = await getSessionNotesListWithActivePairingByMomId(momId);
        setSessionsByMomId((prev) => ({ ...prev, [momId]: sessions }));
      });

      try {
        await Promise.all(sessionsForEachId);
      } catch (error) {
        console.error('Error fetching sessions for pairings:', error);
        setSessionsByMomId(
          pairedMomsList.reduce((acc, pairing) => {
            acc[pairing.mom?.id as string] = [];
            return acc;
          }, {} as SessionsByMomId),
        );
      }
    }
  }, [pairedMomsList]);

  const signalUpdatePairingsList = useCallback((): void => {
    setUpdatePairingsListTrigger((prev) => prev + 1);
  }, []);

  return (
    <PairedMomsListContext.Provider
      value={{
        pairedMomsLoading,
        pairedMomsList,
        fetchPairedMomsList,
        sessionsByMomId,
        fetchSessionsForPairings,
        updatePairingsListTrigger,
        signalUpdatePairingsList,
      }}
    >
      {children}
    </PairedMomsListContext.Provider>
  );
};

export const usePairedMomsListContext = (): PairedMomsListContextType => {
  const context = useContext(PairedMomsListContext);
  if (!context) {
    throw new Error('usePairedMomsListContext must be used within a PairedMomsListProvider');
  }
  return context;
};
