'use client';

import { MomActionPlanGoal } from '@/app/portal/types';
import { getActionPlanByMomId, getActivePairingByMomId, getMomById } from '@/lib/portal';
import { LessonSchema } from '@/types/schemas/lesson';
import type { MomType } from '@/types/schemas/mom';
import type { PairingSchema } from '@/types/schemas/pairing';
import React from 'react';
import { createContext, useCallback, useContext, useState } from 'react';

interface MomProfileContextType {
  momProfileLoading: boolean;
  momProfile: MomType | null;
  pairingInfo: PairingSchema | null;
  lessons: LessonSchema[] | null;
  actionPlanGoals: MomActionPlanGoal[] | null;
  fetchMomProfile: (momId: string) => Promise<void>;
  setMomProfileImageSrc: (src: string | null) => void;
  momProfileImageSrc: string | null;
  updateMomProfile: (mom: MomType) => void;
  getMomPhoto: () => string | null;
}

const MomProfileContext = createContext<MomProfileContextType | undefined>(undefined);

export const MomProfileProvider: React.FC<{ children: React.ReactNode }> = ({ children }): JSX.Element => {
  const [momProfileLoading, setMomProfileLoading] = useState(false);
  const [momProfile, setMomProfile] = useState<MomType | null>(null);
  const [pairingInfo, setPairingInfo] = useState<PairingSchema | null>(null);
  const [lessons, setLessons] = useState<LessonSchema[] | null>(null);
  const [actionPlanGoals, setActionPlanGoals] = useState<MomActionPlanGoal[] | null>(null);
  const [momProfileImageSrc, setMomProfileImageSrc] = useState<string | null>(null);

  const getMomPhoto = useCallback(() => {
    return momProfileImageSrc;
  }, [momProfileImageSrc]);

  const fetchMomProfile = useCallback(async (momId: string) => {
    setMomProfileLoading(true);
    try {
      const momRecord = (await getMomById(momId)) as MomType;
      const pairing = await getActivePairingByMomId(momId);

      const actionPlanGoals = await getActionPlanByMomId(momId);
      // Transform API goals to match portal's expected type
      const transformedGoals = actionPlanGoals.map((goal) => ({
        id: goal.id || '',
        name: goal.name || '',
        dueDate: goal.due_date?.toString() || '',
        description: goal.description || '',
        status: goal.status || '',
        completed: goal.completed || false,
        actionItems: goal.actionItems.map((item) => ({
          id: item.id || '',
          name: item.name || '',
          dueDate: item.due_date?.toString() || '',
          doneDate: item.updated_at?.toString(),
          status: item.status || '',
        })),
        onSubmit: async () => {}, // Placeholder function
      }));
      setActionPlanGoals(transformedGoals);

      // Create a properly typed mom record with all properties
      const typedMomRecord: MomType = {
        ...momRecord,
        languages_c: momRecord.languages_c?.map((lang) => String(lang)) || null,
      };
      setMomProfile(typedMomRecord);
      setPairingInfo(pairing || null);
      setLessons(pairing?.lessons || null);

      // Set the photo URL from the mom record
      // For headers and profile displays, we prefer thumbnails for better performance
      const photoUrl = typedMomRecord.thumbnailUrl || typedMomRecord.photoUrl;
      if (photoUrl) {
        setMomProfileImageSrc(photoUrl);
      } else {
        setMomProfileImageSrc(null);
      }
    } catch (error) {
      console.error('[MomProfileContext] Error fetching mom profile:', error);
      // TODO: handle error (toast message)
      console.error('Error fetching mom profile:', error);
      setLessons(null);
      setActionPlanGoals(null);
      setMomProfile(null);
      setPairingInfo(null);
      setMomProfileImageSrc(null);
    } finally {
      setMomProfileLoading(false);
    }
  }, []);

  const updateMomProfile = (mom: MomType) => {
    // Make sure we have valid field types
    const updatedMom: MomType = {
      ...mom,
      // Convert languages to string array if needed
      languages_c: mom.languages_c?.map((lang) => String(lang)) || null,
    };

    // Update the context state with the new mom profile
    setMomProfile(updatedMom);

    // Prefer thumbnailUrl for UI display if available (used in headers)
    const bestPhotoUrl = updatedMom.thumbnailUrl || updatedMom.photoUrl;

    setMomProfileImageSrc(bestPhotoUrl ?? null);
  };

  return (
    <MomProfileContext.Provider
      value={{
        momProfileLoading,
        momProfile,
        pairingInfo,
        lessons,
        actionPlanGoals,
        momProfileImageSrc,
        fetchMomProfile,
        setMomProfileImageSrc,
        updateMomProfile,
        getMomPhoto,
      }}
    >
      {children}
    </MomProfileContext.Provider>
  );
};

export const useMomProfileContext = (): MomProfileContextType => {
  const context = useContext(MomProfileContext);
  if (!context) {
    throw new Error('useMomProfileContext must be used within a MomProfileProvider');
  }
  return context;
};
