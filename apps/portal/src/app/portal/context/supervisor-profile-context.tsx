'use client';

import { useFindSupervisor } from '@/hooks/useFindSupervisor';
import { User } from '@/types/schemas/user';
import { createContext, useContext, useState } from 'react';

interface SupervisorProfileContextType {
  supervisorProfile: User | null | undefined;
  isLoading: boolean;
  fetchSupervisor: (id: string) => void;
}

export const SupervisorProfileContext = createContext<SupervisorProfileContextType | undefined>(undefined);

export const SupervisorProfileProvider: React.FC<{
  children: React.ReactNode;
  supervisorId?: string | null;
}> = ({ children, supervisorId: initialSupervisorId }): JSX.Element => {
  const [supervisorId, setSupervisorId] = useState<string | null>(initialSupervisorId ?? null);
  const { supervisor, isLoading } = useFindSupervisor(supervisorId ?? '');

  const fetchSupervisor = (id: string) => {
    setSupervisorId(id);
  };

  return (
    <SupervisorProfileContext.Provider
      value={{
        supervisorProfile: supervisor,
        isLoading,
        fetchSupervisor,
      }}
    >
      {children}
    </SupervisorProfileContext.Provider>
  );
};

export const useSupervisorProfileContext = (): SupervisorProfileContextType => {
  const context = useContext(SupervisorProfileContext);
  if (!context) {
    throw new Error('useSupervisorProfileContext must be used within a SupervisorProfileProvider');
  }
  return context;
};
