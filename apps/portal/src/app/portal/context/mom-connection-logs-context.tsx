'use client';

import React from 'react';
import { createContext, useContext, useState } from 'react';

interface MomConnectionLogsContextType {
  updateListTrigger: number;
  signalUpdateList: () => void;
}

const MomConnectionLogsContext = createContext<MomConnectionLogsContextType | undefined>(undefined);

export const MomConnectionLogsProvider: React.FC<{ children: React.ReactNode }> = ({ children }): JSX.Element => {
  /**
   * `updateListTrigger` can be incremented in the create connection log
   * modal to trigger an update of the list.
   *
   * The issue is that the modal is in the mom's profile header, and is
   * not in the component tree for the connection log list, but it still
   * needs to trigger a list update if the user _happens_ to be on that
   * view when they use the modal
   */
  const [updateListTrigger, setUpdateListTrigger] = useState<number>(0);

  const signalUpdateList = (): void => {
    setUpdateListTrigger((prev) => prev + 1);
  };

  return (
    <MomConnectionLogsContext.Provider
      value={{
        updateListTrigger,
        signalUpdateList,
      }}
    >
      {children}
    </MomConnectionLogsContext.Provider>
  );
};

export const useMomConnectionLogsContext = (): MomConnectionLogsContextType => {
  const context = useContext(MomConnectionLogsContext);
  if (!context) {
    throw new Error('useMomConnectionLogsContext must be used within a MomConnectionLogsProvider');
  }
  return context;
};
