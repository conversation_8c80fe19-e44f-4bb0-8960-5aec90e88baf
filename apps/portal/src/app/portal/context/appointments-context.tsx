'use client';

import { ReactNode, createContext, useCallback, useContext, useState } from 'react';

import { mockAppointmentsList } from '../_mock-data/appointments-mock-data';

// TODO: get Appointment type from API schema
interface Appointment {
  id: string;
  title: string;
  start_at: string;
  end_at: string;
}

interface AppointmentsContextType {
  appointments: Appointment[];
  fetchAppointments: () => void;
  appointmentsLoading: boolean;
}

const AppointmentsContext = createContext<AppointmentsContextType | undefined>(undefined);

export const AppointmentsProvider = ({ children }: { children: ReactNode }) => {
  const [appointmentsLoading, setAppointmentsLoading] = useState(true);
  const [appointments, setAppointments] = useState<Appointment[]>([]);

  const fetchAppointments = useCallback(async () => {
    setAppointmentsLoading(true);
    // TODO: Fetch appointments from API
    // const appointmentsList = await fetch('/api/appointments');
    // setAppointments(appointmentsList);
    setTimeout(() => setAppointmentsLoading(false), 500);
    setAppointments(mockAppointmentsList);
  }, []);

  return (
    <AppointmentsContext.Provider value={{ appointments, fetchAppointments, appointmentsLoading }}>
      {children}
    </AppointmentsContext.Provider>
  );
};

export const useAppointments = () => {
  const context = useContext(AppointmentsContext);
  if (!context) {
    throw new Error('useAppointments must be used within an AppointmentsProvider');
  }
  return context;
};
