'use client';

import { useUserInfo } from '@/context/user-info';
import React from 'react';
import { createContext, useContext } from 'react';

import { type PortalProfile, PortalRole } from '../types';

interface DashboardContextType {
  profile: PortalProfile;
}

const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

export const DashboardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { roles, userData } = useUserInfo();
  const roleHierarchy: PortalRole[] = ['supervisor', 'coordinator', 'advocate'];
  let primaryRole = roleHierarchy.find((role) => roles.includes(role)) ?? '';
  // 'administrator' isn't generally considered a 'primary role' but, if that's the only role the user has, give them that role. :)
  if (!primaryRole && roles.includes('administrator')) {
    primaryRole = 'administrator';
  }
  const portalRole = primaryRole.toLowerCase(); // dashboard URL Param, used to construct links within the dashboard

  const profile: PortalProfile = {
    firstName: userData?.firstName || '',
    lastName: userData?.lastName || '',
    email: userData?.email || '',
    roles: roles as PortalRole[],
    id: userData?.sub || '',
    portalRole, // dashboard URL Param
    affiliateId: userData?.affiliateId || '',
  };

  return <DashboardContext.Provider value={profile ? { profile } : undefined}>{children}</DashboardContext.Provider>;
};

export const useDashboardContext = (): DashboardContextType => {
  const context = useContext(DashboardContext);
  if (context === undefined) {
    throw new Error('useDashboardContext must be used within a DashboardProvider');
  }
  return context;
};
