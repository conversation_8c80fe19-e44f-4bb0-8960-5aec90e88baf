'use client';

import { useFindAdvocate } from '@/hooks/useFindAdvocate';
import { PairingSchema } from '@/types/schemas/pairing';
import { User } from '@/types/schemas/user';
import React from 'react';
import { createContext, useContext, useState } from 'react';

interface AdvocateProfileContextType {
  advocateProfile: User | null | undefined;
  pairingInfo: PairingSchema | null;
  advocate: User | null | undefined;
  isLoading: boolean;
}

export const AdvocateProfileContext = createContext<AdvocateProfileContextType | undefined>(undefined);

export const AdvocateProfileProvider: React.FC<{
  children: React.ReactNode;
  advocateId?: string | null;
}> = ({ children, advocateId: initialAdvocateId }): JSX.Element => {
  const [pairingInfo] = useState<PairingSchema | null>(null);
  const [advocateId] = useState<string | null>(initialAdvocateId ?? null);
  const { advocate, isLoading } = useFindAdvocate(advocateId ?? '');

  return (
    <AdvocateProfileContext.Provider
      value={{
        advocateProfile: advocate,
        pairingInfo,
        advocate,
        isLoading,
      }}
    >
      {children}
    </AdvocateProfileContext.Provider>
  );
};

export const useAdvocateProfileContext = (): AdvocateProfileContextType => {
  const context = useContext(AdvocateProfileContext);
  if (!context) {
    throw new Error('useAdvocateProfileContext must be used within a AdvocateProfileProvider');
  }
  return context;
};
