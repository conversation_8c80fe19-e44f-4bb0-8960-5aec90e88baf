'use client';

import { useFindCoordinator } from '@/hooks/useFindCoordinator';
import { User } from '@/types/schemas/user';
import React from 'react';
import { createContext, useContext, useState } from 'react';

interface CoordinatorProfileContextType {
  coordinatorProfile: User | null | undefined;
  isLoading: boolean;
  fetchCoordinator: (id: string) => void;
}

export const CoordinatorProfileContext = createContext<CoordinatorProfileContextType | undefined>(undefined);

export const CoordinatorProfileProvider: React.FC<{
  children: React.ReactNode;
  coordinatorId?: string | null;
}> = ({ children, coordinatorId: initialCoordinatorId }): JSX.Element => {
  const [coordinatorId, setCoordinatorId] = useState<string | null>(initialCoordinatorId ?? null);
  const { coordinator, isLoading } = useFindCoordinator(coordinatorId ?? '');

  const fetchCoordinator = (id: string) => {
    setCoordinatorId(id);
  };

  return (
    <CoordinatorProfileContext.Provider
      value={{
        coordinatorProfile: coordinator,
        isLoading,
        fetchCoordinator,
      }}
    >
      {children}
    </CoordinatorProfileContext.Provider>
  );
};

export const useCoordinatorProfileContext = (): CoordinatorProfileContextType => {
  const context = useContext(CoordinatorProfileContext);
  if (!context) {
    throw new Error('useCoordinatorProfileContext must be used within a CoordinatorProfileProvider');
  }
  return context;
};
