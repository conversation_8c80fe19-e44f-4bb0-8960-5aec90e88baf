'use client';

import { getBenevolenceByMomId } from '@/lib/portal';
import { YupSchemas } from '@suiteapi/models';
import React from 'react';
import { createContext, useCallback, useContext, useState } from 'react';

export type MomFlaggedNeeds = YupSchemas.BenevolenceSchema[];

interface MomFlaggedNeedsContextType {
  momFlaggedNeeds: MomFlaggedNeeds | null;
  setMomFlaggedNeeds: (needs: MomFlaggedNeeds) => void;
  fetchMomFlaggedNeeds: (momId: string) => Promise<void>;
}

const MomFlaggedNeedsContext = createContext<MomFlaggedNeedsContextType | undefined>(undefined);

export const MomFlaggedNeedsProvider: React.FC<{ children: React.ReactNode }> = ({ children }): JSX.Element => {
  const [momFlaggedNeeds, setMomFlaggedNeeds] = useState<MomFlaggedNeeds | null>(null);

  const fetchMomFlaggedNeeds = useCallback(async (momId: string) => {
    try {
      const needsList = await getBenevolenceByMomId(momId);

      setMomFlaggedNeeds(needsList);
    } catch (error) {
      console.error('Error fetching mom flagged needs:', error);
      // TODO: handle error
    }
  }, []);

  return (
    <MomFlaggedNeedsContext.Provider
      value={{
        momFlaggedNeeds,
        setMomFlaggedNeeds,
        fetchMomFlaggedNeeds,
      }}
    >
      {children}
    </MomFlaggedNeedsContext.Provider>
  );
};

export const useMomFlaggedNeedsContext = (): MomFlaggedNeedsContextType => {
  const context = useContext(MomFlaggedNeedsContext);
  if (!context) {
    throw new Error('useMomFlaggedNeedsContext must be used within a MomFlaggedNeedsProvider');
  }
  return context;
};
