'use client';

import { fetchSessionReportData, getSessionNotesListByMomId } from '@/lib/portal';
import type { CombinedSessionSchema, SessionSchema } from '@/types/schemas/session';
import { createContext, useCallback, useContext, useState } from 'react';
import React from 'react';

interface MomSessionNotesContextType {
  sessionsList: SessionSchema[] | null;
  sessionsListLoading: boolean;
  fetchSessionsList: (momId: string) => Promise<void>;
  sessionReport:
    | {
        id: string;
        lesson_id: string;
        session_id: string;
        lesson_status: 'not_started';
        attendance_and_promptness: '' | 'On_Time' | 'Late' | 'No_Show';
        moms_engagement_c: '' | 'Full' | 'Partial' | 'None';
        new_attempt: boolean;
        new_attempt_example: string;
        note: string;
        created_by_name: string;
        client_id_c: string;
        contact_id_c: string;
        date_submitted_c: string;
        covered_lesson_id: string;
        status_c: 'Submitted' | 'Not_Submitted' | 'Rejected' | 'Approved';
      }
    | undefined;
  sessionReportLoading: boolean;
  fetchSessionReport: (sessionId: string) => Promise<void>;
  sessionMeetingData: CombinedSessionSchema | undefined;
}

const MomSessionNotesContext = createContext<MomSessionNotesContextType | undefined>(undefined);

export const MomSessionNotesProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }): JSX.Element => {
  // List of all available session reports
  const [sessionsList, setSessionsList] = useState<SessionSchema[] | null>(null);
  const [sessionsListLoading, setSessionsListLoading] = useState<boolean>(true);

  const [sessionReport, setSessionReport] = useState<
    | {
        id: string;
        lesson_id: string;
        session_id: string;
        lesson_status: 'not_started';
        attendance_and_promptness: '' | 'On_Time' | 'Late' | 'No_Show';
        moms_engagement_c: '' | 'Full' | 'Partial' | 'None';
        new_attempt: boolean;
        new_attempt_example: string;
        note: string;
        created_by_name: string;
        client_id_c: string;
        contact_id_c: string;
        date_submitted_c: string;
        covered_lesson_id: string;
        status_c: 'Submitted' | 'Not_Submitted' | 'Rejected' | 'Approved';
      }
    | undefined
  >();
  const [sessionReportLoading, setSessionReportLoading] = useState<boolean>(true);
  const [sessionMeetingData, setSessionMeetingData] = useState<CombinedSessionSchema | undefined>();

  const fetchSessionsList = useCallback(async (momId: string) => {
    try {
      setSessionsListLoading(true);
      const list = await getSessionNotesListByMomId(momId);
      setSessionsList(list);
    } catch (error) {
      console.error('Error fetching moms flagged needs list:', error);
      // TODO: handle error
    } finally {
      setSessionsListLoading(false);
    }
  }, []);

  // Individual session report

  const fetchSessionReport = useCallback(async (sessionId: string) => {
    setSessionReportLoading(true);
    try {
      const data = await fetchSessionReportData(sessionId);
      // Transform API report to match portal's expected type
      const transformedReport = {
        id: data.report.id || '',
        lesson_id: data.lesson?.id || '',
        session_id: data.session.id || '',
        lesson_status: 'not_started' as const,
        attendance_and_promptness: (data.report.attendance_and_promptness || '') as '' | 'On_Time' | 'Late' | 'No_Show',
        moms_engagement_c: (data.report.moms_engagement_c || '') as '' | 'Full' | 'Partial' | 'None',
        new_attempt: data.report.new_attempt || false,
        new_attempt_example: data.report.new_attempt_example || '',
        note: data.report.note || '',
        created_by_name: data.session.created_by_name || '',
        client_id_c: data.session.pairing?.mom?.first_name || '',
        contact_id_c: data.session.pairing?.mom?.first_name || '',
        date_submitted_c: data.report.date_submitted_c || '',
        covered_lesson_id: data.lesson?.id || '',
        status_c: (data.report.status === 'submitted'
          ? 'Submitted'
          : data.report.status === 'new'
            ? 'Not_Submitted'
            : data.report.status === 'rejected'
              ? 'Rejected'
              : data.report.status === 'approved'
                ? 'Approved'
                : 'Not_Submitted') as 'Submitted' | 'Not_Submitted' | 'Rejected' | 'Approved',
      };
      setSessionReport(transformedReport);
      setSessionMeetingData(data.session);
    } catch (error) {
      console.error('Error fetching session notes:', error);
      setSessionReport(undefined);
      setSessionMeetingData(undefined);
    } finally {
      setSessionReportLoading(false);
    }
  }, []);

  return (
    <MomSessionNotesContext.Provider
      value={{
        sessionsList,
        sessionsListLoading,
        fetchSessionsList,
        sessionReport,
        sessionReportLoading,
        fetchSessionReport,
        sessionMeetingData,
      }}
    >
      {children}
    </MomSessionNotesContext.Provider>
  );
};

export const useMomSessionNotes = (): MomSessionNotesContextType => {
  const context = useContext(MomSessionNotesContext);
  if (!context) {
    throw new Error('useMomSessionNotes must be used within a MomSessionNotesProvider');
  }
  return context;
};
