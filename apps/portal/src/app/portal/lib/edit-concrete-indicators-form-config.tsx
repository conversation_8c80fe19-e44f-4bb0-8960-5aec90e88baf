import { type FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const editConcreteIndicatorsFormFields: FieldConfig[] = [
  {
    name: 'employment_status',
    label: 'Employment Status',
    type: 'select',
    placeholder: 'Select',
    options: [
      { value: 'part_time', label: 'Part Time' },
      { value: 'full_time', label: 'Full Time' },
    ],
  },
  {
    name: 'school_status',
    label: 'School Status',
    type: 'select',
    placeholder: 'Select',
    options: [
      { value: 'not_in_school', label: 'Not In School' },
      { value: 'in_school', label: 'In School' },
    ],
  },
  {
    name: 'income_status',
    label: 'Income Status',
    type: 'number',
  },
  {
    name: 'housing_status',
    label: 'Housing Status',
    type: 'select',
    placeholder: 'Select',
    options: [{ value: 'unstable', label: 'Unstable' }],
  },
  {
    name: 'child_welfare_status',
    label: 'Child Welfare Status',
    type: 'select',
    placeholder: 'Select',
    options: [{ value: 'well_cared', label: 'Well Cared' }],
  },
];

export const editConcreteIndicatorsFormSchema = yup.object().shape({
  employment_status: yup.string().required(),
  school_status: yup.string().required(),
  income_status: yup.number().required(),
  housing_status: yup.string().required(),
  child_welfare_status: yup.string().required(),
});
