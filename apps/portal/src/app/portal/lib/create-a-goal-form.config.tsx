import { StandardFieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const createAGoalFormConfig: StandardFieldConfig[] = [
  {
    name: 'name',
    label: 'Goal Title',
    type: 'text',
    placeholder: 'Enter goal title',
    schema: yup.string().required('Goal title is required'),
  },
  {
    name: 'description',
    label: 'Goal Description',
    type: 'textarea',
    placeholder: 'Enter goal description',
    schema: yup.string().required('Goal description is required'),
  },
  {
    name: 'dueDate',
    label: 'Goal Due Date',
    type: 'date',
    placeholder: 'Enter goal due date',
    schema: yup.date().required('Goal due date is required'),
  },
  {
    name: 'actionItems',
    label: 'Goal Tasks',
    type: 'action-item-list',
    disabled: (values = {}) => values.goal_title === '', // Disable tasks until title is filled
    listItemPlaceholder: 'Enter a task',
    addButtonLabel: 'Add Task',
    minItems: 1,
    maxItems: 10,
    schema: yup
      .array()
      .of(
        yup.object().shape({
          name: yup.string().required('Task name is required'),
          dueDate: yup.date().optional().nullable().default(null),
        }),
      )
      .min(1, 'At least one task is required')
      .required('Goal tasks are required'),
  },
];
