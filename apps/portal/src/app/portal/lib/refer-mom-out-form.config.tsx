import { type FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const referOutMomFormFields = (agencyOptions: { name: string }[]): FieldConfig[] => [
  {
    name: 'agency',
    label: '',
    type: 'select',
    placeholder: 'Select an agency',
    options: agencyOptions.map((agency) => ({
      value: agency.name,
      label: agency.name,
    })),
  },
  {
    name: 'reason',
    label: '',
    type: 'textarea',
    placeholder: 'Reason for referral',
  },
];

export const referOutMomFormSchema = yup.object().shape({});
