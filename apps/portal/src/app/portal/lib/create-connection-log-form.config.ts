import { getFieldKeysFromFieldConfig } from '@/lib/utils';
import { type FieldConfig } from '@/types/form-field';
import { YupSchemas } from '@suiteapi/models';
import pick from 'lodash/pick';
import * as yup from 'yup';

const connectionLogFormFields: FieldConfig[] = [
  {
    name: 'date_created_c',
    label: 'Date',
    type: 'date',
    placeholder: 'Enter date',
    maxDate: new Date(),
  },
  {
    name: 'contact_method_c',
    label: 'Contact Method',
    type: 'button-radio-group',
    options: YupSchemas.connectionMethodOptions,
  },
  {
    name: 'summary_c',
    label: 'Summary',
    type: 'textarea',
    placeholder: 'Enter details about your contact',
  },
  {
    name: 'is_visible_to_advocates_c',
    label: 'Visible to Advocates',
    type: 'button-radio-group',
    options: [
      // SuiteCRM 'Checkbox' field type uses enum "1"/"0" instead of true/false
      { value: '1', label: 'Yes' },
      { value: '0', label: 'No' },
    ],
  },
];

export const createConnectionLogFormFields = (isAtLeastCoordinator: boolean): FieldConfig[] =>
  // `is_visible_to_advocates_c` is user-selectable only if user's role is at least 'Coordinator'
  // otherwise, the field is hidden and the value is set to Yes
  connectionLogFormFields.filter((field) => field.name !== 'is_visible_to_advocates_c' || isAtLeastCoordinator);

const fieldKeys = getFieldKeysFromFieldConfig(connectionLogFormFields);
const uiSchema = { ...YupSchemas.connectionLogApiFieldSchemas, ...YupSchemas.connectionLogUiFieldSchemas };
export const createConnectionLogFormSchema = yup.object().shape(pick(uiSchema, fieldKeys));
