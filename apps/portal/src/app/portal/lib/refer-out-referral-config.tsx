import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const referOutReferralFormFields = (
  agencyOptions?: { value: string; label: string }[] | undefined,
): FieldConfig[] => [
  {
    name: 'referred_to_agency_id',
    label: 'Agency Name',
    type: 'select',
    placeholder: 'Select an agency',
    options: agencyOptions?.map((agency) => ({
      value: agency.value ?? '',
      label: agency.label ?? '',
    })),
  },
  {
    name: 'referred_to_agency_reason',
    label: 'Reason for referring out',
    type: 'textarea',
    placeholder: 'Reason for referring out',
  },
];

export const referOutReferralSchema = yup.object().shape({
  referred_to_agency_id: yup.string().required('Agency Name is required'),
  referred_to_agency_reason: yup.string().required('Reason for referring out is required'),
});

export type ReferOutReferralSchema = yup.InferType<typeof referOutReferralSchema>;
