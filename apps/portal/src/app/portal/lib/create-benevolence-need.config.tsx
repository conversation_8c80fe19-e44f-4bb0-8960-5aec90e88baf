import { getFieldKeysFromFieldConfig } from '@/lib/utils';
import { type FieldConfig } from '@/types/form-field';
import { YupSchemas } from '@suiteapi/models';
import pick from 'lodash/pick';
import * as yup from 'yup';

export const createBenevolenceNeedFormFields: FieldConfig[] = [
  {
    name: 'type_c',
    label: 'Type',
    type: 'select',
    placeholder: 'Select',
    // apparently value and label have to be the same value or it errors
    // Bugfix: value and label have to match
    options: [
      {
        value: YupSchemas.BenevolenceNeedsType.Financial.value,
        label: YupSchemas.BenevolenceNeedsType.Financial.label,
      },
      {
        value: YupSchemas.BenevolenceNeedsType.PhysicalGoods.value,
        label: YupSchemas.BenevolenceNeedsType.PhysicalGoods.label,
      },
      {
        value: YupSchemas.BenevolenceNeedsType.OtherServices.value,
        label: YupSchemas.BenevolenceNeedsType.OtherServices.label,
      },
    ],
  },
  {
    name: 'is_urgent_c',
    label: 'Is this urgent?',
    type: 'button-radio-group',
    options: [
      { value: true, label: 'Yes' },
      { value: false, label: 'No' },
    ],
  },
  {
    name: 'description',
    label: 'Context for need',
    type: 'textarea',
    placeholder: 'Provide additional context about the situation and factors that led to this need.',
  },
];

const fieldKeys = getFieldKeysFromFieldConfig(createBenevolenceNeedFormFields);
export const createBenevolenceNeedFormSchema = yup
  .object()
  .shape(pick(YupSchemas.benevolenceApiFieldSchemas, fieldKeys));
