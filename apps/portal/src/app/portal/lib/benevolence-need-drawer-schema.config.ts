import { type FieldConfig, type FormValues } from '@/types/form-field';
import { YupSchemas } from '@suiteapi/models';
import { DollarSign } from 'lucide-react';

export const getBenevolenceNeedsFormFields = (
  needType: YupSchemas.BenevolenceNeedsValues,
  isComplete: boolean | undefined | string, // this is being pulled from resolved_date_c
): FieldConfig[] => [
  {
    name: 'financial_amount_requested_c', // amount requested
    label: 'Amount Requested',
    type: 'number',
    icon: DollarSign,
    hidden: () => needType !== YupSchemas.BenevolenceNeedsType.Financial.value,
    disabled: () => isComplete === true,
  },
  {
    name: 'financial_mom_contribution_c', //moms contribution
    label: "Mom's Contribution",
    type: 'number',
    icon: DollarSign,
    hidden: () => needType !== YupSchemas.BenevolenceNeedsType.Financial.value,
    disabled: () => isComplete === true,
  },
  {
    name: 'financial_amount_gifted_c', // amount gifted
    label: 'Amount Gifted',
    type: 'number',
    icon: DollarSign,
    hidden: () => needType !== YupSchemas.BenevolenceNeedsType.Financial.value,
    disabled: () => isComplete === true,
  },
  {
    name: 'financial_prevention_plan_c', // financial plan
    label: 'Mom’s plan to prevent this financial burden in the future',
    type: 'textarea',
    placeholder: 'What plan does this mom have to help alleviate this financial burden in the future?',
    hidden: () => needType !== YupSchemas.BenevolenceNeedsType.Financial.value,
    disabled: () => isComplete === true,
  },
  {
    name: 'pg_fulfillment_method_c', // fullfilment method
    label: 'Method of Fulfillment',
    type: 'select',
    placeholder: 'Select',
    options: [
      { value: 'closet', label: 'Closet' },
      { value: 'gift_card', label: 'Gift Card' },
      { value: 'item_purchased', label: 'Item Purchased' },
      { value: 'external_provider', label: 'External Provider' },
    ],
    hidden: () => needType !== YupSchemas.BenevolenceNeedsType.PhysicalGoods.value,
    disabled: () => isComplete === true,
  },
  {
    name: 'physical_good_monetary_value_c', // monetary value
    label: 'Monetary Value',
    type: 'number',
    icon: DollarSign,
    hidden: () => needType !== YupSchemas.BenevolenceNeedsType.PhysicalGoods.value,
    disabled: () => isComplete === true,
  },
  {
    name: 'other_is_referral_needed_c', // other_is_referral_needed_c
    label: 'Is a referral needed?',
    type: 'button-radio-group',
    options: [
      { value: true, label: 'Yes' },
      { value: false, label: 'No' },
    ],
    hidden: () => needType !== YupSchemas.BenevolenceNeedsType.OtherServices.value,
    disabled: () => isComplete === true,
  },
  {
    name: 'did_address_need_c', // need addressed
    label: 'Were we able to address this need?',
    type: 'button-radio-group',
    options: [
      { value: true, label: 'Yes' },
      { value: false, label: 'No' },
    ],
    disabled: () => isComplete === true,
  },
  {
    name: 'provided_date_c', // date provided, different than resolved_date_c, which is the date the need was completed (use clicked "marked need as complete")
    label: 'Date Provided',
    type: 'date',
    placeholder: 'Enter the date this was resolved',
    disabled: () => isComplete === true,
  },
  {
    name: 'non_addressal_comment_c', // why not resolved
    label: 'If no, could you comment on why?',
    type: 'textarea',
    placeholder: 'This will help with future requests for benevolence needs.',
    hidden: (values?: FormValues) => values?.did_address_need_c === true,
    disabled: () => isComplete === true,
  },
  {
    name: 'notes_c', // additional notes
    label: `${needType === 'Other' ? '' : 'Additional '}Notes (Optional)`,
    type: 'textarea',
    placeholder:
      'Enter details around how this benevolence need was addressed such as actions taken, services contacted, or anything else that would be helpful.',
    disabled: () => isComplete === true,
  },
];
