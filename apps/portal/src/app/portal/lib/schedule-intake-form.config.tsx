import { type FieldConfig, type FormValues } from '@/types/form-field';
import { Link, MapPin } from 'lucide-react';
import * as yup from 'yup';

export const intakeScheduleFormFields: FieldConfig[] = [
  {
    name: 'date_start',
    label: 'Date',
    type: 'date',
    placeholder: 'Select a date',
    // Only allow selecting future dates (including today)
    minDate: new Date(),
  },
  {
    name: 'time_group',
    type: 'row-group',
    label: '',
    subFields: [
      {
        name: 'start_time',
        label: 'Start Time',
        type: 'time',
        placeholder: 'Select a time',
      },
      {
        name: 'end_time',
        label: 'End Time',
        type: 'time',
        placeholder: 'Select a time',
      },
    ],
  },
  {
    name: 'meeting_type',
    label: 'Meeting Type',
    type: 'button-radio-group',
    options: [
      { value: 'Virtual', label: 'Virtual (online)' },
      { value: 'In_Person', label: 'In Person' },
    ],
  },
  {
    name: 'location',
    label: 'Location',
    type: 'text',
    placeholder: 'Enter a location',
    icon: MapPin,
    hidden: (values?: FormValues) => values?.meeting_type !== 'In_Person',
  },
  {
    name: 'join_url',
    label: 'Meeting Link',
    type: 'text',
    placeholder: 'Enter a meeting link',
    icon: Link,
    hidden: (values?: FormValues) => values?.meeting_type !== 'Virtual',
  },
];

const scheduleIntakeFieldSchemas = {
  date_start: yup.string().required('Date is required'),
  start_time: yup.string().required('Start time is required'),
  end_time: yup.string().required('End time is required'),
  meeting_type: yup.string().required('Meeting type is required'),
  location: yup.string().optional(),
  join_url: yup.string().optional(),
};

export const scheduleIntakeFormSchema = yup.object().shape(scheduleIntakeFieldSchemas);
export type ScheduleIntakeFormSchema = yup.InferType<typeof scheduleIntakeFormSchema>;
