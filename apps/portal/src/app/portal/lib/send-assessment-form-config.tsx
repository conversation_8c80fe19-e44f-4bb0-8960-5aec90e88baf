import { type FieldConfig, type FormValues } from '@/types/form-field';
import * as yup from 'yup';

export const sendAssessmentFormFields: FieldConfig[] = [
  {
    name: 'contact_method',
    label: 'Contact Method',
    type: 'select',
    placeholder: 'Select',
    options: [
      { value: 'email', label: 'Email' },
      { value: 'text', label: 'Text' },
    ],
  },

  {
    name: 'email',
    label: '<PERSON>’s email address',
    type: 'email',
    placeholder: '',
    hidden: (values?: FormValues) => values?.contact_method !== 'email',
  },
  {
    name: 'phone',
    label: 'Mom’s Phone number',
    type: 'tel',
    placeholder: '',
    hidden: (values?: FormValues) => values?.contact_method !== 'text',
  },
  {
    name: 'link',
    label: 'Pre or Post assessment Link',
    type: 'text',
  },
];

export const sendAssessmentFormSchema = yup.object().shape({
  contact_method: yup.string().required(),
  phone: yup
    .string()
    .transform((v, originalV) => (originalV === '' ? undefined : v))
    .optional(),
  email: yup
    .string()
    .transform((v, originalV) => (originalV === '' ? undefined : v))
    .optional(),
  link: yup.string().required(),
});
