import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const SERVICES_OPTIONS = [
  { value: 'benevolence', label: 'Benevolence' },
  { value: 'care_communities', label: 'Care Communities' },
  { value: 'childcare', label: 'Childcare' },
  { value: 'closet_visit', label: 'Closet Visit' },
  { value: 'crisis_resources', label: 'Crisis Resources' },
  { value: 'education', label: 'Education' },
  { value: 'group_parenting', label: 'Group Parenting' },
  { value: 'health', label: 'Health' },
  { value: 'housing', label: 'Housing' },
  { value: 'legal', label: 'Legal' },
  { value: 'mental_health', label: 'Mental Health' },
  { value: 'substance', label: 'Substance' },
  { value: 'therapy', label: 'Therapy' },
];

export const OUTCOMES_OPTIONS = [
  { value: 'successful', label: 'Successful' },
  { value: 'unsuccessful', label: 'Unsuccessful' },
  { value: 'unknown', label: 'Unknown' },
];

export const createBenevolenceReferralFormConfig: FieldConfig[] = [
  {
    name: 'service_c',
    label: 'Service',
    type: 'select',
    placeholder: 'Select',
    options: SERVICES_OPTIONS,
  },
  {
    name: 'outcome_c',
    label: 'Outcome',
    type: 'select',
    placeholder: 'Select',
    options: OUTCOMES_OPTIONS,
  },
  {
    name: 'start_date',
    label: 'Start Date',
    type: 'date',
    minDate: new Date(),
    placeholder: 'Start Date',
  },
  {
    name: 'provider',
    label: 'Provider',
    type: 'text',
    placeholder: 'Enter the name of the provider',
  },
];

export const createBenevolenceReferralSchema = yup.object().shape({
  service_c: yup.string().required('Service is required'),
  outcome_c: yup.string().required('Outcome is required'),
  start_date: yup.date().required('Start date is required'),
  provider: yup.string().required('Provider is required'),
});
