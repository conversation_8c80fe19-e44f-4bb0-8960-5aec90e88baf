import { type FieldConfig, FormValues } from '@/types/form-field';
import { YupSchemas } from '@suiteapi/models';
import * as yup from 'yup';

import { Advocate, Track } from '../[role]/dashboard/people/moms/[id]/info/ema-info/page';

// Create options from MomStatus enum
export const momStatusOptions = [
  { value: YupSchemas.MomStatus.ACTIVE, label: 'Active' },
  { value: YupSchemas.MomStatus.INACTIVE, label: 'Inactive' },
];

export const trackStatusOptions = [
  { value: 'in_program', label: 'In Program' },
  { value: 'discharged_incomplete', label: 'Incomplete' },
  { value: 'complete', label: 'Complete' },
];

export const inProgramStatusOptions = [
  { value: 'waiting_to_begin', label: 'Waiting to Begin' },
  { value: 'session_in_progress', label: 'Session in Progress' },
];

export const dischargedIncompleteStatusOptions = [
  { value: 'track_requirements_unmet', label: 'Track Requirements Unmet' },
  { value: 'client_choice', label: 'Client Choice' },
  { value: 'relocated', label: 'Relocated' },
  { value: 'other', label: 'Other' },
];

export const incompleteContributingFactorsOptions = [
  { value: 'no_advocate', label: 'No advocate available (Group or 1-on-1)' },
  { value: 'extended_wait', label: 'Extended wait times for accessing services' },
  { value: 'priorities_shifted', label: "Client's priorities or focus have shifted" },
  { value: 'achieved_outcomes', label: 'Client has achieved desired outcomes and no longer requires ongoing support' },
];

export const completeStatusOptions = [
  { value: 'completed_full_track', label: 'Completed Full Track' },
  { value: 'completed_without_support_sessions', label: 'Completed without support sessions' },
  { value: 'completed_without_post_assessment', label: 'Completed without post-assessment' },
];

export const momBasicInfoEmaInfoAffiliateLocationField: FieldConfig[] = [
  {
    name: 'affiliate',
    label: 'ĒMA Location',
    type: 'text',
    placeholder: 'Location',
    description:
      'Need to transfer to another location? Discuss with your affiliate support manager for transfer options.',
    disabled: true,
  },
];

export const momBasicInfoEmaInfoAffiliateLocationSchema = yup.object().shape({
  affiliate: yup.string().optional(),
});

export const pairingStatusOptions = [
  { value: YupSchemas.PairingStatusType.WAITING_TO_BE_PAIRED, label: 'Waiting to be paired' },
  { value: YupSchemas.PairingStatusType.PAIRED, label: 'Paired' },
  { value: YupSchemas.PairingStatusType.PAIRING_COMPLETE, label: 'Pairing complete' },
];

export const momBasicInfoEMAInfoPairingFields = (
  advocateOptions: Advocate[],
  trackOptions: Track[],
  isCoordinatorOrAdmin: boolean,
  isEditingTrackEnabled: boolean,
): FieldConfig[] => [
  {
    name: 'status',
    label: 'Pairing Status',
    type: 'select',
    placeholder: 'Select',
    options: pairingStatusOptions,
    disabled: !isCoordinatorOrAdmin,
  },
  {
    name: 'advocateUser',
    label: 'Paired Advocate',
    type: 'select',
    placeholder: 'Select',
    options: advocateOptions.map((advocate) => ({
      value: advocate.id,
      label: advocate.name,
    })),
    disabled: !isCoordinatorOrAdmin,
  },
  {
    name: 'track',
    label: 'Assigned Track',
    type: 'select',
    placeholder: 'Select',
    options: trackOptions.map((track) => ({
      value: track.id,
      label: track.name,
    })),
    disabled: !isEditingTrackEnabled,
  },
  {
    name: 'track_status',
    label: 'Track Status',
    type: 'select',
    placeholder: 'Select',
    options: trackStatusOptions,
    disabled: !isCoordinatorOrAdmin,
  },
  {
    name: 'in_program_track_sub_status',
    label: 'In Program Track Status',
    type: 'select',
    placeholder: 'Select',
    options: inProgramStatusOptions,
    disabled: !isCoordinatorOrAdmin,
    hidden: (values?: FormValues) => values?.track_status !== 'in_program',
  },
  {
    name: 'discharge_incomplete_sub_status',
    label: 'Discharge Incomplete Sub Status',
    type: 'select',
    placeholder: 'Select',
    options: dischargedIncompleteStatusOptions,
    disabled: !isCoordinatorOrAdmin,
    hidden: (values?: FormValues) => values?.track_status !== 'discharged_incomplete',
  },
  {
    name: 'incomplete_reason_sub_status_reason',
    label: 'Incomplete Contributing Factors',
    type: 'select',
    placeholder: 'Select',
    options: incompleteContributingFactorsOptions,
    disabled: !isCoordinatorOrAdmin,
    hidden: (values?: FormValues) => values?.track_status !== 'discharged_incomplete',
  },
  {
    name: 'complete_reason_sub_status',
    label: 'Complete Reason',
    type: 'select',
    placeholder: 'Select',
    options: completeStatusOptions,
    disabled: !isCoordinatorOrAdmin,
    hidden: (values?: FormValues) => values?.track_status !== 'complete',
  },
];

export const momBasicInfoEmaInfoPairingSchema = yup.object().shape({
  status: yup
    .string()
    .oneOf([...pairingStatusOptions.map((option) => option.value)])
    .required('Pairing Status is required'),
  advocateUser: yup.string().optional(),
  track: yup.string().optional(),
  track_status: yup
    .string()
    .oneOf([...trackStatusOptions.map((option) => option.value)])
    .optional(),
  in_program_track_sub_status: yup
    .string()
    .nullable()
    .oneOf([...inProgramStatusOptions.map((option) => option.value), null]),
  discharge_incomplete_sub_status: yup
    .string()
    .nullable()
    .oneOf([...dischargedIncompleteStatusOptions.map((option) => option.value), null]),
  incomplete_reason_sub_status_reason: yup
    .string()
    .nullable()
    .oneOf([...incompleteContributingFactorsOptions.map((option) => option.value), null]),
  complete_reason_sub_status: yup
    .string()
    .nullable()
    .oneOf([...completeStatusOptions.map((option) => option.value), null]),
});

export const momBasicInfoEmaInfoFormFields: FieldConfig[] = [
  {
    name: 'status',
    label: 'Status',
    type: 'select',
    placeholder: 'Select',
    options: momStatusOptions,
  },
];

export const momBasicInfoEmaInfoFormSchema = yup.object().shape({
  status: yup
    .string()
    .oneOf(momStatusOptions.map((option) => option.value))
    .required(),
});
