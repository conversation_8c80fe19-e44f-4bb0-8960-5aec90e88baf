import type { FieldConfig } from '@/types/form-field';

export const getSendMomTextMessageFormFields = (momName: string): FieldConfig[] => [
  {
    name: 'contact_name',
    label: 'Name',
    type: 'text',
    disabled: () => true,
  },
  {
    name: 'contact_phone',
    label: 'Phone Number',
    type: 'tel',
    disabled: () => true,
  },
  {
    name: 'message_text',
    label: 'Message',
    type: 'textarea',
    placeholder: `Write something to ${momName}`,
  },
];
