import { type FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

import { ReferralSubStatus } from '../../../hooks/generated/__types';

export const denyReferralOptions: { value: ReferralSubStatus; label: string }[] = [
  { value: 'unable_to_contact', label: 'Unable to Contact' },
  { value: 'client_declined_services', label: 'Client Declined Services' },
  { value: 'ineligible_for_program', label: 'Ineligible for Program' },
  { value: 'duplicate_referral', label: 'Duplicate Referral' },
  { value: 'referred_to_alternate_program', label: 'Referred to Alternate Program' },
];

export const denyReferralFormFields: FieldConfig[] = [
  {
    name: 'referral_sub_status',
    label: 'Reason',
    type: 'select',
    placeholder: 'Select',
    options: denyReferralOptions,
  },
];

export const denyReferralFormSchema = yup.object().shape({
  referral_sub_status: yup.string().required('Reason is required'),
  status: yup
    .string()
    .optional()
    .oneOf(['', ...denyReferralOptions.map((option) => option.value)]),
});
