import { languageOptions } from '@/lib/constants';
import { type FieldConfig } from '@/types/form-field';
import { YupSchemas } from '@suiteapi/models';
import { CloudDownload } from 'lucide-react';
import * as yup from 'yup';

import { communicationPreferenceOptions } from '../[role]/dashboard/admin/_lib/user-management-edit-user-config';

export enum CommunicationPreferenceType {
  email = 'email',
  text_message = 'text_message',
  both = 'both',
}

export const momBasicInfoFormFields: FieldConfig[] = [
  {
    name: 'nameGroup',
    type: 'row-group',
    label: '',
    subFields: [
      { name: 'first_name', label: 'First Name', type: 'text', placeholder: 'Name' },
      { name: 'last_name', label: 'Last Name', type: 'text' },
    ],
  },
  {
    name: 'contactGroup',
    type: 'row-group',
    label: '',
    subFields: [
      { name: 'phone_other', label: 'Phone', type: 'tel' },
      { name: 'email1', label: 'Email Address', type: 'email' },
    ],
  },
  {
    name: 'preferred_contact_method_c',
    label: 'Preferred Contact Method',
    type: 'button-radio-group',
    options: communicationPreferenceOptions,
  },
  {
    name: 'sms_message_opt_in',
    label: '',
    type: 'checkbox',
    checkboxLabel:
      'Opt in for SMS messages with updates about your account. Message & data rates apply. 1-5 messages per week.',
  },
  {
    name: 'languages_c',
    label: 'Languages',
    type: 'multi-select',
    placeholder: 'Select',
    options: languageOptions,
  },
  {
    name: 'caregiver_type_c',
    label: 'Caregiver Type',
    type: 'select',
    placeholder: 'Select',
    options: [
      { value: 'biological_mom', label: 'Biological Mom' },
      { value: 'adoptive_mom', label: 'Adoptive Mom' },
      { value: 'relative_guardian', label: 'Relative Guardian' },
      { value: 'nonrelative_guardian', label: 'Non Relative Guardian' },
    ],
  },
  {
    name: 'birthdate',
    label: 'Date Of Birth',
    type: 'date',
    captionLayout: 'dropdown',
  },
];

export const momBasicInfoFormSchema = yup.object().shape({
  nameGroup: yup.object().shape({
    first_name: yup.string().required(),
    last_name: yup.string().required(),
  }),
  contactGroup: yup.object().shape({
    phone_other: yup.string().required(),
    email1: yup.string().required(),
  }),
  preferred_contact_method_c: yup
    .string()
    .oneOf([
      CommunicationPreferenceType.email,
      CommunicationPreferenceType.text_message,
      CommunicationPreferenceType.both,
    ])
    .required(),
  sms_message_opt_in: yup.boolean(),
  languages_c: yup
    .array()
    .of(
      yup.object().shape({
        label: yup.string().required(),
        value: yup.string().required(),
      }),
    )
    .nullable(),
  caregiver_type_c: yup.string().required(),
  birthdate: yup.date().nullable(),
});

export type MomBasicInfoFormSchema = yup.InferType<typeof momBasicInfoFormSchema>;

export const momServicesFormFields: FieldConfig[] = [
  {
    name: 'connected_housing_c',
    label: '',
    checkboxLabel: 'Housing',
    type: 'checkbox',
  },
  {
    name: 'connected_education_c',
    label: '',
    checkboxLabel: 'Education Services',
    type: 'checkbox',
  },
  {
    name: 'connected_mental_health_c',
    label: '',
    checkboxLabel: 'Mental Health Services',
    type: 'checkbox',
  },
  {
    name: 'connected_health_c',
    label: '',
    checkboxLabel: 'Health Services',
    type: 'checkbox',
  },
  {
    name: 'connected_childcare_c',
    label: '',
    checkboxLabel: 'Childcare',
    type: 'checkbox',
  },
  {
    name: 'connected_substance_c',
    label: '',
    checkboxLabel: 'Substance Use Treatment',
    type: 'checkbox',
  },
  {
    name: 'connected_legal_c',
    label: '',
    checkboxLabel: 'Legal Services',
    type: 'checkbox',
  },

  {
    name: 'connected_closet_c',
    label: '',
    checkboxLabel: 'Closet Visit',
    type: 'checkbox',
  },
  {
    name: 'connected_benevolance_c',
    label: '',
    checkboxLabel: 'Benevolence',
    type: 'checkbox',
  },
];

export const momServicesFormSchema = yup.object().shape({
  connected_benevolance_c: yup.bool().optional().nullable(),
  connected_childcare_c: yup.bool().optional().nullable(),
  connected_closet_c: yup.bool().optional().nullable(),
  connected_education_c: yup.bool().optional().nullable(),
  connected_health_c: yup.bool().optional().nullable(),
  connected_housing_c: yup.bool().optional().nullable(),
  connected_legal_c: yup.bool().optional().nullable(),
  connected_mental_health_c: yup.bool().optional().nullable(),
  connected_substance_c: yup.bool().optional().nullable(),
});

export const stateOptions = [
  { label: 'Alaska (AK)', value: 'AK' },
  { label: 'Alabama (AL)', value: 'AL' },
  { label: 'Arkansas (AR)', value: 'AR' },
  { label: 'Arizona (AZ)', value: 'AZ' },
  { label: 'California (CA)', value: 'CA' },
  { label: 'Colorado (CO)', value: 'CO' },
  { label: 'Connecticut (CT)', value: 'CT' },
  { label: 'District of Columbia (DC)', value: 'DC' },
  { label: 'Delaware (DE)', value: 'DE' },
  { label: 'Florida (FL)', value: 'FL' },
  { label: 'Georgia (GA)', value: 'GA' },
  { label: 'Hawaii (HI)', value: 'HI' },
  { label: 'Iowa (IA)', value: 'IA' },
  { label: 'Idaho (ID)', value: 'ID' },
  { label: 'Illinois (IL)', value: 'IL' },
  { label: 'Indiana (IN)', value: 'IN' },
  { label: 'Kansas (KS)', value: 'KS' },
  { label: 'Kentucky (KY)', value: 'KY' },
  { label: 'Louisiana (LA)', value: 'LA' },
  { label: 'Massachusetts (MA)', value: 'MA' },
  { label: 'Maryland (MD)', value: 'MD' },
  { label: 'Maine (ME)', value: 'ME' },
  { label: 'Michigan (MI)', value: 'MI' },
  { label: 'Minnesota (MN)', value: 'MN' },
  { label: 'Missouri (MO)', value: 'MO' },
  { label: 'Mississippi (MS)', value: 'MS' },
  { label: 'Montana (MT)', value: 'MT' },
  { label: 'North Carolina (NC)', value: 'NC' },
  { label: 'North Dakota (ND)', value: 'ND' },
  { label: 'Nebraska (NE)', value: 'NE' },
  { label: 'New Hampshire (NH)', value: 'NH' },
  { label: 'New Jersey (NJ)', value: 'NJ' },
  { label: 'New Mexico (NM)', value: 'NM' },
  { label: 'Nevada (NV)', value: 'NV' },
  { label: 'New York (NY)', value: 'NY' },
  { label: 'Ohio (OH)', value: 'OH' },
  { label: 'Oklahoma (OK)', value: 'OK' },
  { label: 'Oregon (OR)', value: 'OR' },
  { label: 'Pennsylvania (PA)', value: 'PA' },
  { label: 'Rhode Island (RI)', value: 'RI' },
  { label: 'South Carolina (SC)', value: 'SC' },
  { label: 'South Dakota (SD)', value: 'SD' },
  { label: 'Tennessee (TN)', value: 'TN' },
  { label: 'Texas (TX)', value: 'TX' },
  { label: 'Utah (UT)', value: 'UT' },
  { label: 'Virginia (VA)', value: 'VA' },
  { label: 'Vermont (VT)', value: 'VT' },
  { label: 'Washington (WA)', value: 'WA' },
  { label: 'Wisconsin (WI)', value: 'WI' },
  { label: 'West Virginia (WV)', value: 'WV' },
  { label: 'Wyoming (WY)', value: 'WY' },
];

export const momAddressInfoFormFields: FieldConfig[] = [
  {
    name: 'primary_address_street',
    label: 'Address',
    type: 'text',
  },
  {
    name: 'primary_address_city',
    label: 'City',
    type: 'text',
  },
  {
    name: 'primary_address_state',
    label: 'State',
    type: 'select',
    placeholder: 'Select',
    options: stateOptions,
  },
  {
    name: 'primary_address_postalcode',
    label: 'Zip',
    type: 'text',
  },
  {
    name: 'primary_address_county_c',
    label: 'County',
    type: 'text',
  },
  {
    name: 'address_access_c',
    label: 'Address Access Instructions',
    type: 'text',
    placeholder: 'Details about how to access the location',
  },
];

export const momAddressInfoFormSchema = yup.object().shape({
  primary_address_street: yup.string(),
});

export const momFilesUploadFormFields: FieldConfig[] = [
  {
    name: 'documents',
    label: '',
    type: 'files',
    icon: CloudDownload,
    fileUploadOptions: {
      maxFiles: 4,
      maxSize: 1024 * 1024 * 5,
      accept: {
        'image/*': ['.jpeg', '.png'],
        'application/pdf': ['.pdf'],
        'application/msword': ['.doc', '.docx'],
        'text/plain': ['.txt'],
      },
    },
  },
];

export const momFilesUploadFormSchema = yup.object().shape({
  documents: yup.array().of(YupSchemas.documentSchema).required(),
});
