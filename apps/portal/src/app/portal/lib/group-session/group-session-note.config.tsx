import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const groupSessionNoteConfig: FieldConfig[] = [
  {
    name: 'note',
    label: 'Overall Notes',
    type: 'textarea',
    placeholder: 'Share anything that you feel is relevant to today’s session',
  },
];

export const groupSessionNoteSchema = yup.object().shape({
  note: yup.string().required('Note is required'),
});
