import { FieldConfig, FieldOptions, FormValues } from '@/types/form-field';
import { SessionType } from '@/types/schemas/session';

export const getGroupSessionNoteInfoConfig = (
  momOptions: FieldOptions,
  lessonOptions: FieldConfig['options'],
  currentLesson: string | undefined,
  onChangeMomCallback: (momId: string) => void,
  onChangeSessionTypeCallback: (isTrackSession: boolean) => void,
): FieldConfig[] => [
  {
    name: 'date_start',
    label: 'Date',
    type: 'date',
    placeholder: 'Select a date',
    minDate: new Date(),
  },
  {
    name: 'time_group',
    type: 'row-group',
    label: '',
    subFields: [
      {
        name: 'start_time',
        label: 'Start Time',
        type: 'time',
        placeholder: 'Select a time',
      },
      {
        name: 'end_time',
        label: 'End Time',
        type: 'time',
        placeholder: 'Select a time',
      },
    ],
  },
  {
    name: 'session_type',
    label: 'Session Type',
    type: 'button-radio-group',
    options: [
      { value: SessionType.Track_Session, label: 'Track Session' },
      { value: SessionType.Support_Session, label: 'Support Session' },
    ],
    onChange: (values?: FormValues) => {
      if (values && values.session_type) {
        onChangeSessionTypeCallback(values.session_type === SessionType.Track_Session);
      }
    },
  },
  {
    name: 'current_lesson',
    label: 'Lesson',
    type: 'select',
    dynamicOptionsKey: 'lessons',
    options: lessonOptions,
    defaultValue: currentLesson,
    hidden: (values?: FormValues) => values?.session_type !== SessionType.Track_Session,
  },
  {
    name: 'name', // This will probably need to be a different key since it will be a list of names
    label: 'Participants/Moms',
    type: 'multi-select',
    overrideStrings: {
      allItemsAreSelected: 'All moms are selected',
      selectAll: 'Select all moms',
      search: 'Search for a mom',
    },
    options: momOptions || [],
    onChange: (values?: FormValues) => {
      if (values && values.mom_id) {
        onChangeMomCallback(Array.isArray(values.mom_id) ? values.mom_id[0] : values.mom_id);
      }
    },
  },
  {
    name: 'next_lesson',
    label: 'Next Lesson',
    type: 'select',
    placeholder: 'Next Lesson',
    dynamicOptionsKey: 'lessons',
    options: lessonOptions,
    hidden: (values?: FormValues) => values?.session_type !== SessionType.Track_Session,
  },
];
