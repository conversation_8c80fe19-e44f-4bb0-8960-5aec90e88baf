import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const editAdvocateBackgroundCheckConfig: FieldConfig[] = [
  {
    name: 'backgroundCheck',
    label: 'Background Check Complete?',
    type: 'button-radio-group',
    options: [
      { label: 'Yes', value: 'yes' },
      { label: 'No', value: 'no' },
    ],
  },
];

export const editAdvocateBackgroundCheckFormSchema = yup.object().shape({
  backgroundCheck: yup.string().oneOf(['yes', 'no']).required('Background check is required'),
});
