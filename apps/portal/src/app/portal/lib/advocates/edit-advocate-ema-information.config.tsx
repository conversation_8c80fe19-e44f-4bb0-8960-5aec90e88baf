import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

import { advocateStatusOptions } from '../../[role]/dashboard/admin/_lib/user-management-edit-user-config';

export const editAdvocateEMAInformation = (
  listOfCoordinatorsOptions: { label: string; value: string }[],
): FieldConfig[] => [
  {
    name: 'advocate_status',
    label: 'Advocate Status',
    type: 'select',
    placeholder: 'Advocate Status',
    options: advocateStatusOptions,
  },
  {
    name: 'affiliate',
    label: 'Affiliate',
    type: 'text',
    description:
      'Need to transfer to another location? Discuss with your affiliate support manager for transfer options.',
    disabled: true,
  },
  {
    name: 'assignedCoordinators',
    label: 'Assigned Coordinators',
    type: 'multi-select',
    placeholder: 'Assigned Coordinators',
    options: listOfCoordinatorsOptions,
  },
];

export const editAdvocateEMAInfoFormSchema = yup.object().shape({
  advocate_status: yup.string().required('Advocate status is required'),
  affiliate: yup.string().nullable().optional(),
  assignedCoordinators: yup
    .array()
    .of(yup.mixed().transform((value) => (typeof value === 'string' ? value : value?.value || value?.id)))
    .nullable()
    .optional(),
});
