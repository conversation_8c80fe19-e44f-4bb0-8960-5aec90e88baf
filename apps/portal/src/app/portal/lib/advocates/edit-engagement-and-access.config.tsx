import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const editAdvocateEngagementAndAccessConfig: FieldConfig[] = [
  {
    name: 'accessToResources',
    label: 'Access to Resources',
    type: 'button-radio-group',
    options: [
      { label: 'Yes', value: 'yes' },
      { label: 'No', value: 'no' },
    ],
  },
  {
    name: 'eventAccess',
    label: 'Event Access',
    type: 'button-radio-group',
    options: [
      { label: 'Yes', value: 'yes' },
      { label: 'No', value: 'no' },
    ],
  },
  {
    name: 'outreachAccess',
    label: 'Outreach Access',
    type: 'button-radio-group',
    options: [
      { label: 'Yes', value: 'yes' },
      { label: 'No', value: 'no' },
    ],
  },
  {
    name: 'adminAccess',
    label: 'Admin Access',
    type: 'button-radio-group',
    options: [
      { label: 'Yes', value: 'yes' },
      { label: 'No', value: 'no' },
    ],
  },
  {
    name: 'specialProjectsAccess',
    label: 'Special Projects Access',
    type: 'button-radio-group',
    options: [
      { label: 'Yes', value: 'yes' },
      { label: 'No', value: 'no' },
    ],
  },
];

export const editAdvocateEngagementAndAccessFormSchema = yup.object().shape({
  accessToResources: yup.boolean().required('Access to resources is required'),
  eventAccess: yup.boolean().required('Event access is required'),
  outreachAccess: yup.boolean().required('Outreach access is required'),
  adminAccess: yup.boolean().required('Admin access is required'),
  specialProjectsAccess: yup.boolean().required('Special projects access is required'),
});
