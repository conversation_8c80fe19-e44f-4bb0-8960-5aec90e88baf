import { languageOptions } from '@/lib/constants';
import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const editAdvocateBasicInformation: FieldConfig[] = [
  {
    name: 'nameGroup',
    label: '',
    type: 'row-group',
    subFields: [
      {
        label: 'First Name',
        name: 'firstName',
        type: 'text',
        placeholder: 'First Name',
      },
      {
        name: 'lastName',
        label: 'Last Name',
        type: 'text',
        placeholder: 'Last Name',
      },
    ],
  },
  {
    name: 'contactGroup',
    label: '',
    type: 'row-group',
    subFields: [
      {
        label: 'Email Address',
        name: 'email',
        type: 'email',
        placeholder: 'Email Address',
      },
      {
        name: 'phone',
        label: 'Phone',
        type: 'tel',
        placeholder: 'Phone',
      },
    ],
  },
  {
    name: 'language_preference_c',
    label: 'Language Preference',
    type: 'select',
    placeholder: 'Language Preference',
    options: languageOptions,
  },
  {
    name: 'languages_c',
    label: 'Languages',
    type: 'multi-select',
    placeholder: 'Languages',
    options: languageOptions,
  },
  {
    name: 'date_of_birth',
    label: 'Date of Birth',
    type: 'date',
    placeholder: 'Date of Birth',
  },
  {
    name: 'availability',
    label: 'Availability',
    type: 'text',
    placeholder: 'Availability',
  },
  {
    name: 'address_street',
    label: 'Address',
    type: 'text',
    placeholder: 'Address',
  },
  {
    name: 'address_city',
    label: 'City',
    type: 'text',
    placeholder: 'City',
  },
  {
    name: 'address_state',
    label: 'State',
    type: 'text',
    placeholder: 'State',
  },
  {
    name: 'address_postalcode',
    label: 'Postal Code',
    type: 'text',
    placeholder: 'Postal Code',
  },
];

export const editAdvocateBasicInfoFormSchema = yup.object().shape({
  firstName: yup.string().required('First name is required'),
  lastName: yup.string().required('Last name is required'),
  email: yup.string().email('Invalid email format').required('Email is required'),
  phone: yup.string().required('Phone is required'),
  language_preference_c: yup.string().nullable(),
  languages_c: yup
    .array()
    .of(
      yup.object().shape({
        label: yup.string().required(),
        value: yup.string().required(),
      }),
    )
    .nullable(),
  date_of_birth: yup.date().nullable().optional(),
  address_street: yup.string().nullable().optional(),
  address_city: yup.string().nullable().optional(),
  address_state: yup.string().nullable().optional(),
  address_postalcode: yup.string().nullable().optional(),
  availability: yup.string().nullable().optional(),
});
