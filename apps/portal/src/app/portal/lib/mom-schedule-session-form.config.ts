import { type FieldConfig, type FieldOptions, type FormValues } from '@/types/form-field';
import { SessionType } from '@/types/schemas/session';
import { Link, MapPin } from 'lucide-react';

export const getMomScheduleSessionFormFields = (
  isCoordinator: boolean,
  pairingOrMomOptions: FieldOptions,
  lessonOptions: FieldConfig['options'],
  trackOptions: FieldConfig['options'],
  selectedTrackId: string | undefined,
  onChangeSelectedPairingIdsCallback: (pairingIds: string[]) => void,
  onChangeSessionTypeCallback: (isTrackSession: boolean) => void,
  onChangeSelectedTrackCallback: (trackId: string) => void,
): FieldConfig[] => [
  {
    name: 'pairing_or_mom_ids',
    label: 'Participants/Moms',
    type: 'multi-select',
    placeholder: 'Select',
    overrideStrings: {
      allItemsAreSelected: 'All moms are selected',
      selectAll: 'Select all moms',
      search: 'Search for a mom',
    },
    options: pairingOrMomOptions || [],
    onChange: (values?: FormValues) => {
      if (values && values.pairing_or_mom_ids) {
        onChangeSelectedPairingIdsCallback([
          ...(values.pairing_or_mom_ids as { value: string; label: string }[]).map(
            (pairingOrMom) => pairingOrMom.value,
          ),
        ]);
      }
    },
  },
  {
    name: 'session_type',
    label: 'Session Type',
    type: 'button-radio-group',
    options: [
      ...(!isCoordinator ? [{ value: SessionType.Track_Session, label: 'Track Session' }] : []),
      { value: SessionType.Support_Session, label: 'Support Session' },
    ],
    onChange: (values?: FormValues) => {
      if (values && values.session_type) {
        onChangeSessionTypeCallback(values.session_type === SessionType.Track_Session);
      }
    },
  },
  {
    name: 'current_track',
    label: 'Track',
    type: 'select',
    placeholder: 'Track',
    dynamicOptionsKey: 'tracks',
    options: trackOptions,
    defaultValue: selectedTrackId,
    hidden: (values?: FormValues) =>
      values?.session_type !== SessionType.Track_Session || !trackOptions || trackOptions.length <= 1,
    onChange: (values?: FormValues) => {
      if (values && values.current_track) {
        onChangeSelectedTrackCallback(values?.current_track as string);
      }
    },
  },
  {
    name: 'current_lesson',
    label: 'Lesson',
    type: 'select',
    placeholder: 'Lesson',
    dynamicOptionsKey: 'lessons',
    options: lessonOptions,
    hidden: (values?: FormValues) => values?.session_type !== SessionType.Track_Session,
  },
  {
    name: 'date_start',
    label: 'Date',
    type: 'date',
    placeholder: 'Select a date',
    // Only allow selecting future dates (including today)
    minDate: new Date(),
  },
  {
    name: 'time_group',
    type: 'row-group',
    label: '',
    subFields: [
      {
        name: 'start_time',
        label: 'Start Time',
        type: 'time',
        placeholder: 'Select a time',
      },
      {
        name: 'end_time',
        label: 'End Time',
        type: 'time',
        placeholder: 'Select a time',
      },
    ],
  },
  {
    name: 'meeting_type',
    label: 'Meeting Type',
    type: 'button-radio-group',
    options: [
      { value: 'Virtual', label: 'Virtual (online)' },
      { value: 'In_Person', label: 'In Person' },
    ],
  },
  {
    name: 'location',
    label: 'Location',
    type: 'text',
    placeholder: 'Enter a location',
    icon: MapPin,
    hidden: (values?: FormValues) => values?.meeting_type !== 'In_Person',
  },
  {
    name: 'join_url',
    label: 'Meeting Link',
    type: 'text',
    placeholder: 'Enter a meeting link',
    icon: Link,
    hidden: (values?: FormValues) => values?.meeting_type !== 'Virtual',
  },
];
