import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

// We only need these specific fields for the coordinator assignment form.
// By creating a minimal type that only includes id, firstName, and lastName,
// we avoid type conflicts with other fields (like languages_c) that we don't use in this form.
// The fields are nullable because the database schema allows these fields to be null.
type CoordinatorOption = {
  id: string | null;
  firstName: string | null;
  lastName: string | null;
};

// Update the function to accept the minimal type
export const assignCoordinatorFormFields = (coordinatorOptions: CoordinatorOption[]): FieldConfig[] => [
  {
    name: 'coordinator',
    label: '',
    type: 'button-radio-group',
    placeholder: 'Select',
    options: coordinatorOptions.map((coordinator) => ({
      value: coordinator.id ?? '',
      label: `${coordinator.firstName ?? ''} ${coordinator.lastName ?? ''}`,
    })),
  },
];

export const assignCoordinatorFormSchema = yup.object().shape({
  coordinator: yup.string().required('Coordinator is required'),
});
