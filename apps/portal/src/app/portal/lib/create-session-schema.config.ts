import { FieldConfig, FormValues } from '@/types/form-field';
import { SessionType } from '@/types/schemas/session';

export const getCreateSessionFormFields = ({
  isCoordinator,
  lessonOptions,
}: {
  isCoordinator: boolean;
  lessonOptions: FieldConfig['options'];
}): FieldConfig[] => [
  {
    name: 'date_start',
    type: 'date',
    label: 'Session Date',
    description: 'Enter the date that the session took place.',
    // This form is for creating session reports for sessions that have already taken place
    maxDate: new Date(),
  },
  {
    name: 'session_type',
    label: 'Session Type',
    type: 'button-radio-group',
    options: [
      ...(!isCoordinator ? [{ value: SessionType.Track_Session, label: 'Track Session' }] : []),
      { value: SessionType.Support_Session, label: 'Support Session' },
    ],
  },
  {
    name: 'current_lesson',
    label: 'Current Lesson',
    type: 'select',
    placeholder: 'Current Lesson',
    options: lessonOptions,
    hidden: (values?: FormValues) => values?.session_type !== SessionType.Track_Session,
  },
];
