import { getFieldKeysFromFieldConfig } from '@/lib/utils';
import { type FieldConfig } from '@/types/form-field';
import { YupSchemas } from '@suiteapi/models';
import pick from 'lodash/pick';
import * as yup from 'yup';

// Coordinator Note for/on a Mom, Court Update is viewable by coordinators and advocates, Safety or Concern Update is viewable by coordinators only
export const createCoordinatorNoteForMomFormFields: FieldConfig[] = [
  {
    name: 'type_c',
    label: 'Note Type',
    type: 'select',
    placeholder: 'Select',
    options: [
      { value: 'court_update', label: 'Court Update' },
      { value: 'safety_or_concern_update', label: 'Safety or Concern Update' },
    ],
  },

  {
    name: 'description',
    label: 'Note Details',
    type: 'textarea',
  },
];

// Coordinator Note for/on an Advocate
export const createCoordinatorNoteForAdvocateFormFields: FieldConfig[] = [
  {
    name: 'type_c',
    label: 'Note Type',
    type: 'select',
    placeholder: 'Select',
    options: [
      { value: 'interview_advocate', label: 'Interview Advocate' },
      { value: 'safety_or_concern_update', label: 'Safety or Concern Update' },
    ],
  },

  {
    name: 'description',
    label: 'Note Details',
    type: 'textarea',
  },
];

const fieldKeys = getFieldKeysFromFieldConfig(createCoordinatorNoteForMomFormFields);
export const createCoordinatorNoteForMomFormSchema = yup
  .object()
  .shape(pick(YupSchemas.coordinatorNotesApiFieldSchemas, fieldKeys));
