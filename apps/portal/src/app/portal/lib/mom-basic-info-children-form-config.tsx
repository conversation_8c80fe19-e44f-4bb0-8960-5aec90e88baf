import {
  FatherInvolved,
  Gender,
  LegalCustodyStatus,
  LivesWith,
  formatFatherInvolved,
  formatGender,
  formatLegalCustodyStatus,
  formatLivesWith,
} from '@/app/portal/[role]/dashboard/people/_utils/people-utils';
import { type FieldConfig, type FormValues } from '@/types/form-field';
import * as yup from 'yup';

export const childFormFields: FieldConfig[] = [
  {
    name: 'first_name',
    label: 'Child Name',
    type: 'text',
    placeholder: 'First Name',
    description: "Enter the child's first name",
    required: true,
  },
  {
    name: 'gender',
    label: 'Gender',
    type: 'select',
    placeholder: 'Select',
    description: "The child's gender",
    options: Object.values(Gender)
      .filter((value) => value !== Gender.Not_Specified)
      .map((value) => ({
        value,
        label: formatGender(value),
      })),
    required: true,
  },
  {
    name: 'lives_with',
    label: 'Lives with',
    type: 'select',
    placeholder: 'Select',
    description: 'Indicate who the child lives with',
    options: Object.values(LivesWith)
      .filter((value) => value !== LivesWith.NotSpecified)
      .map((value) => ({
        value,
        label: formatLivesWith(value),
      })),
    required: true,
  },
  {
    name: 'legal_custody_status',
    label: 'Legal custody status with client',
    type: 'select',
    placeholder: 'Select',
    description: "Specify the child's legal custody status",
    options: Object.values(LegalCustodyStatus)
      .filter((value) => value !== LegalCustodyStatus.NotSpecified)
      .map((value) => ({
        value,
        label: formatLegalCustodyStatus(value),
      })),
    required: true,
  },
  {
    name: 'birthdate',
    label: 'Date Of Birth',
    type: 'date',
    captionLayout: 'dropdown',
    required: true,
  },
  {
    name: 'father_involved',
    label: 'Father involved',
    type: 'multi-select',
    placeholder: 'Select All Applicable',
    description: "Indicate the level of involvement of the child's father",
    options: Object.values(FatherInvolved)
      .filter((value) => value !== FatherInvolved.NotSpecified)
      .map((value) => ({
        value,
        label: formatFatherInvolved([value]).replace(/,\s*$/, ''),
      })),
    required: true,
  },
  {
    name: 'father_involvement',
    label: 'Father involvement',
    type: 'textarea',
    placeholder: 'Share Your Comments',
    description: "Details about the nature and extent of father's involvement",
  },
];

export interface ChildFormData extends FormValues {
  first_name: string;
  birthdate: Date | null;
  gender: string;
  lives_with: string;
  legal_custody_status: string;
  father_involved: string[] | { value: string; label: string }[];
  father_involvement?: string;
  additional_info?: string | null;
  [key: string]: string | string[] | Date | null | { value: string; label: string }[] | undefined;
}

export const childFormSchema = yup.object().shape({
  first_name: yup.string().required('First name is required'),
  birthdate: yup
    .date()
    .nullable()
    .required('Date of birth is required')
    .max(new Date(), 'Date of birth cannot be in the future'),
  gender: yup.string().required('Gender is required'),
  lives_with: yup.string().required('Lives with information is required'),
  legal_custody_status: yup.string().required('Legal custody status is required'),
  father_involved: yup
    .array()
    .of(
      yup.mixed().test('is-valid-option', 'Invalid option', (value) => {
        if (typeof value === 'string') return true;
        if (typeof value === 'object' && value !== null) {
          return 'value' in value && 'label' in value;
        }
        return false;
      }),
    )
    .required('Father involvement information is required')
    .min(1, 'Please select at least one option'),
  father_involvement: yup.string().nullable(),
  additional_info: yup.string().nullable(),
});

// Default values for creating a new child
export const defaultChildFormValues: ChildFormData = {
  first_name: '',
  birthdate: null,
  gender: '',
  lives_with: '',
  legal_custody_status: '',
  father_involved: [],
  father_involvement: '',
  additional_info: '',
};

export type ChildFormSchema = ChildFormData;

// Children totals form configuration
export const childrenTotalsFormFields: FieldConfig[] = [
  {
    name: 'total_children',
    label: 'Total number of children in and out of home',
    description:
      'Include all children under 18 who live with you, those you care for, or those you have parental rights for.',
    type: 'number',
    min: 0,
    defaultValue: 0,
    required: true,
  },
  {
    name: 'children_in_home',
    label: 'Total number of children in home',
    description: 'Number of children that live with mother. Include children temporarily placed out of home of CPS.',
    subtext: 'Note: This field is for information purposes only and is not saved to the database.',
    type: 'number',
    min: 0,
    defaultValue: 0,
    required: false,
  },
];

export const childrenTotalsFormSchema = yup.object({
  total_children: yup.number().min(0, 'Cannot be negative').required('Required'),
  children_in_home: yup.number().min(0, 'Cannot be negative').optional(),
});
