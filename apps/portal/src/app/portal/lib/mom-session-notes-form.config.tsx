import { getFieldKeysFromFieldConfig } from '@/lib/utils';
import { type FieldConfig, type FormValues } from '@/types/form-field';
import { LessonSchema } from '@/types/schemas/lesson';
import { SessionType, SessionTypes } from '@/types/schemas/session';
import pick from 'lodash/pick';
import { Link, MapPin } from 'lucide-react';
import * as yup from 'yup';

import SessionNotesLessonDetails from '../[role]/dashboard/people/moms/[id]/session-notes/[note]/_components/session-notes-lesson-details';

// Define types directly for types that are needed for other exports
// These type definitions don't create runtime variables
type SessionReportApiSchemaType = {
  id: yup.StringSchema<string | undefined>;
  attendance_and_promptness: yup.StringSchema<string | undefined>;
  moms_engagement_c: yup.StringSchema<string | undefined>;
  new_attempt: yup.BooleanSchema<boolean | undefined>;
  new_attempt_example: yup.StringSchema<string | undefined>;
  note: yup.StringSchema<string | undefined>;
  created_by_name: yup.StringSchema<string | undefined>;
  client_id_c: yup.StringSchema<string | undefined>;
  contact_id_c: yup.StringSchema<string | undefined>;
  date_submitted_c: yup.StringSchema<string | undefined>;
  covered_lesson_id: yup.StringSchema<string | undefined>;
  status_c: yup.StringSchema<string | undefined>;
};

export const sessionReportFormSchemas = {
  id: yup.string().optional(),
  attendance_and_promptness: yup.string().required().oneOf(['On_Time', 'Late', 'No_Show', '']),
  moms_engagement_c: yup.string().required().oneOf(['Full', 'Partial', 'None', '']),
  new_attempt: yup.boolean().required(),
  new_attempt_example: yup.string().required(),
  note: yup.string().optional(),
  created_by_name: yup.string().optional(), // name of the advocate
  client_id_c: yup.string().optional(), // name of the mom
  contact_id_c: yup.string().optional(), // mom id
  date_submitted_c: yup.string().optional(),
  status_c: yup.string().optional().oneOf(['Submitted', 'Not_Submitted', 'Rejected', 'Approved']),
};

// Define types directly without creating unused runtime variables
export type SessionReportSchema = yup.InferType<yup.ObjectSchema<SessionReportApiSchemaType>>;

// Define types directly without creating unused runtime variables
export type CreateSessionReportSchema = yup.InferType<
  yup.ObjectSchema<
    {
      lesson_id: yup.StringSchema<string>;
      lesson_status: yup.StringSchema<string>;
      session_id: yup.StringSchema<string>;
    } & SessionReportApiSchemaType
  >
>;

const scheduleNextSessionFieldSchemas = {
  mom_id: yup.string().required(),
  session_type: yup.string().oneOf(SessionTypes).required(),
  current_lesson: yup.string().optional(),
  date: yup.string().required(),
  start_time: yup.string().required(),
  end_time: yup.string().required(),
  meeting_type: yup.string().oneOf(['in_person', 'virtual']).required(),
  location: yup.string().optional(),
  meeting_link: yup.string().optional(),
  note: yup.string().optional(),
};

export const getThisWeeksSessionFields = (
  setPublishedCurrentLesson: (lesson: LessonSchema | undefined) => void,
  lessonStatus: string | undefined,
): FieldConfig[] => {
  return [
    {
      name: 'session_type',
      label: 'Session',
      type: 'button-radio-group',
      options: [
        { value: SessionType.Track_Session, label: 'Track Session' },
        { value: SessionType.Support_Session, label: 'Support Session' },
      ],
    },
    {
      name: 'note',
      label: 'Overall Notes',
      placeholder: "Share anything that you feel is relevant to mom's case",
      type: 'textarea',
      bottomContent: <SessionNotesLessonDetails setPublishedCurrentLesson={setPublishedCurrentLesson} />,
      bottomContentHidden: (values?: FormValues) => values?.session_type === SessionType.Support_Session,
    },
    {
      name: 'lesson_status',
      label: 'Lesson is',
      type: 'button-radio-group',
      options: [
        { value: 'not_started', label: 'Not Yet Started' },
        { value: 'in_progress', label: 'In Progress' },
        { value: 'completed', label: 'Completed' },
      ],
      defaultValue: lessonStatus,
      hidden: (values?: FormValues) => values?.session_type === SessionType.Support_Session,
    },
    {
      name: 'moms_engagement_c',
      label: "Mom's engagement with lesson",
      type: 'button-radio-group',
      options: [
        { value: 'Full', label: 'Full' },
        { value: 'Partial', label: 'Partial' },
        { value: 'None', label: 'None' },
      ],
    },
    {
      name: 'attendance_and_promptness',
      label: 'Attendance & Promptness',
      type: 'button-radio-group',
      options: [
        { value: 'On_Time', label: 'On Time' },
        { value: 'Late', label: 'Late' },
        { value: 'No_Show', label: 'No Show' },
      ],
    },
    {
      name: 'new_attempt',
      label: 'Did mom try something new to implement the lesson or to improve herself since last week?',
      type: 'button-radio-group',
      options: [
        { value: true, label: 'Yes' },
        { value: false, label: 'No' },
      ],
    },
    {
      name: 'new_attempt_example',
      label: 'If yes, provide an example. If no, provide context.',
      type: 'textarea',
      placeholder: 'Additional context',
    },
  ];
};

const thisWeeksSessionFieldKeys = getFieldKeysFromFieldConfig(getThisWeeksSessionFields(() => {}, undefined));

export const thisWeeksSessionNotesSchema = yup
  .object()
  .shape(pick(sessionReportFormSchemas, thisWeeksSessionFieldKeys));

export const scheduleNextSessionFormFields: FieldConfig[] = [
  { name: 'date', label: 'Date', type: 'date', placeholder: 'Select a date' },
  {
    name: 'time_group',
    type: 'row-group',
    label: 'Time',
    subFields: [
      {
        name: 'start_time',
        label: 'Start Time',
        type: 'time',
        placeholder: 'Select a time',
      },
      {
        name: 'end_time',
        label: 'End Time',
        type: 'time',
        placeholder: 'Select a time',
      },
    ],
  },
  {
    name: 'meeting_type',
    label: 'Meeting Type',
    type: 'button-radio-group',
    options: [
      { value: 'in_person', label: 'In Person' },
      { value: 'virtual', label: 'Virtual (online)' },
    ],
  },
  {
    name: 'location',
    label: 'Location',
    type: 'text',
    icon: MapPin,
    placeholder: 'Enter a location',
    hidden: (values?: FormValues) => values?.meeting_type !== 'in_person',
  },
  {
    name: 'meeting_link',
    label: 'Meeting Link',
    type: 'text',
    icon: Link,
    placeholder: 'Enter a meeting link',
    hidden: (values?: FormValues) => values?.meeting_type !== 'virtual',
  },
  {
    name: 'current_lesson',
    label: 'Next Lesson',
    type: 'select',
    options: [
      { value: 'lesson-1', label: 'Lesson 1' },
      { value: 'lesson-2', label: 'Lesson 2' },
      { value: 'lesson-3', label: 'Lesson 3' },
      { value: 'lesson-4', label: 'Lesson 4' },
      { value: 'lesson-5', label: 'Lesson 5' },
    ], // TODO: fetch lessons from API
    placeholder: 'Current Lesson',
  },
];

const scheduleNextSessionFieldKeys = getFieldKeysFromFieldConfig(scheduleNextSessionFormFields);

export const scheduleNextSessionSchema = yup
  .object()
  .shape(pick(scheduleNextSessionFieldSchemas, scheduleNextSessionFieldKeys));
