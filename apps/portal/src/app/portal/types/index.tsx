import { FormValues } from '@/types/form-field';
import { SessionSchema } from '@/types/schemas/session';
import { YupSchemas } from '@suiteapi/models';
import { type LucideIcon } from 'lucide-react';
import { RefObject } from 'react';
import { FieldValues, UseFormReturn } from 'react-hook-form';
import * as yup from 'yup';

// Type for transformed response from API to support dashboard UX (not raw API response)
export type PortalRole = 'administrator' | 'supervisor' | 'coordinator' | 'advocate'; // TODO: get this type from a higher source of truth

export interface PortalProfile {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  roles: PortalRole[];
  portalRole: string; // primary role for loading portal functionality (1 role at a time to be used for portal dashboard even if user has multiple roles)
  profilePictureURL?: string;
  affiliateId: string;
}

export interface PortalNavLink {
  icon: React.ReactNode;
  label: string;
  href: string;
  isActive: boolean;
  isHidden?: boolean;
}

export interface DashboardActionsCard {
  icon: LucideIcon;
  label: string;
  description: string;
}

export interface ContactLogEntry {
  id: string;
  type: string;
  date: string;
  description: string;
  visibility: string;
  contacted_by: string;
  created_at?: string;
}
export interface FlaggedNeed {
  mom_name: string;
  expressed_need: string;
  is_urgent: boolean;
  notes: string;
  created_at: string;
}
export interface SessionNotesNeedsTypes {
  id: string;
  needs_categories?: YupSchemas.BenevolenceNeedsValues[];
  is_need_urgent?: 'yes' | 'no';
  need_description?: string;
}

export interface MomActionPlanTask {
  id: string;
  name: string;
  dueDate?: string;
  doneDate?: string;
  status?: string;
}

export interface MomActionPlanGoal {
  id: string;
  name: string;
  dueDate: string;
  description: string;
  status: string;
  completed: boolean;
  actionItems: MomActionPlanTask[];
  onSubmit: (data: FieldValues) => Promise<void>;
}

export interface SessionNotesThisWeekTypes {
  start_time?: string;
  end_time?: string;
  lesson_status?: 'in_progress' | 'complete';
  mom_engagement_level?: 'full' | 'partial' | 'none';
  observations?: string;
  attendance_status?: 'on_time' | 'late' | 'no_show';
  additional_notes?: string;
}

export interface SessionNotesScheduleNextTypes {
  date?: string;
  start_time?: string;
  end_time?: string;
  location?: string;
}

export type SessionNotesType = 'support-session' | 'track-session';

export interface SessionLesson {
  id: string;
  title: string;
  description?: string;
  dueDate?: string;
  completed?: boolean;
  link?: string;
}

export interface SessionNotesRecord {
  id: string;
  type: SessionNotesType;
  mom_name: string;
  session_date: string;
  description: string;
  created_by: string;
  created_at: string;
  current_lesson: SessionLesson;
  learning_track?: string;
  assigned_advocate?: string;
  prev_lesson?: SessionLesson;
  flagged_needs?: SessionNotesNeedsTypes[];
  this_weeks_connection?: SessionNotesThisWeekTypes;
  schedule_next_connection?: SessionNotesScheduleNextTypes;
}

export interface MostRecentAndUpcomingSessions {
  mostRecentPast: SessionSchema | undefined;
  nextUpcoming: SessionSchema | undefined;
}

export interface MomProfileTabItem {
  value: string;
  label: string;
}

export type pg_fulfillment_method_cType =
  | 'Closet'
  | 'Gift Card'
  | 'Item Purchased'
  | 'External Provider'
  | null
  | undefined;

export interface FormFactoryRef {
  form: UseFormReturn<FormValues>;
}

export interface WellBeingAssessmentFormData {
  basicForm: {
    values: FormValues;
    schema: yup.ObjectSchema<yup.AnyObject>;
    ref: RefObject<FormFactoryRef> | null;
  };
  clientForm: {
    values: FormValues;
    schema: yup.ObjectSchema<yup.AnyObject>;
    ref: RefObject<FormFactoryRef> | null;
  };
  childForms: {
    values: FormValues;
    schema: yup.ObjectSchema<yup.AnyObject>;
    ref: RefObject<FormFactoryRef> | null;
  }[];
  childrenForm: {
    values: FormValues;
    schema: yup.ObjectSchema<yup.AnyObject>;
    ref: RefObject<FormFactoryRef> | null;
  };
  wellBeingForm: {
    values: FormValues;
    schema: yup.ObjectSchema<yup.AnyObject>;
    ref: RefObject<FormFactoryRef> | null;
  };
}
