'use client';

import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { useFindManyConnectionLog } from '@/hooks/generated';
import { Mom } from '@/hooks/generated/__types';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

import ConnectionLogsExportPage from './connection-logs-export-page';

const ConnectionLogsExportView = () => {
  const searchParams = useSearchParams();
  const momId = searchParams.get('momId');

  const { fetchMomProfile, momProfileLoading, momProfile } = useMomProfileContext();
  const { isLoading, data: connectionLogs } = useFindManyConnectionLog({
    where: {
      mom_id: momId,
    },
  });

  useEffect(() => {
    if (!momId) return;

    fetchMomProfile(momId);
  }, [momId, fetchMomProfile]);

  if (!momId) {
    return <div className='mt-20'>Unable to load connection logs</div>;
  }

  return (
    <>
      {momProfileLoading || !momProfile || isLoading || !connectionLogs ? (
        <div className='mt-20'>Loading...</div>
      ) : (
        <ConnectionLogsExportPage mom={momProfile as unknown as Mom} connectionLogs={connectionLogs} />
      )}
    </>
  );
};

export default ConnectionLogsExportView;
