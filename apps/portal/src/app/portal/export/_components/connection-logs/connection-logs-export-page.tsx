import ReadOnlyModuleData from '@/components/custom/read-only-module-data';
import { ConnectionLog, Mom } from '@/hooks/generated/__types';
import { formatDate } from 'date-fns';

interface ConnectionLogsExportPageProps {
  mom: Mom;
  connectionLogs: ConnectionLog[] | [];
}

const ConnectionLogsExportPage: React.FC<ConnectionLogsExportPageProps> = ({ mom, connectionLogs }) => {
  const placeholder = '---';
  const clogs =
    connectionLogs?.map((log) => {
      const { date_created_c, contact_method_c, summary_c, name } = log;
      const formattedDate = formatDate(date_created_c, 'MM/dd/yyyy') || placeholder;
      const connectionMethod = contact_method_c || placeholder;

      return {
        label: formattedDate,
        value: [`Method: ${connectionMethod}`, `Name: ${name}`, `Summary: ${summary_c}`],
      };
    }) || [];

  const connectionLogsData = {
    headerText: `${mom?.first_name} ${mom?.last_name}`,
    title: 'Connection Logs',
    sections: [
      {
        sectionTitle: '', // Empty title for the "header"
        data: [
          {
            label: 'Date', // Header for the date column
            value: 'Details', // Header for the details column
          },
        ],
      },
      {
        sectionTitle: '',
        data: clogs,
      },
    ],
  };

  return <ReadOnlyModuleData {...connectionLogsData} />;
};

export default ConnectionLogsExportPage;
