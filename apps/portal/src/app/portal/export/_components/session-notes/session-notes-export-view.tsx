'use client';

import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { useMomSessionNotes } from '@/app/portal/context/mom-session-notes-context';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

import SessionNotesReadOnlyDataPage from './session-notes-page';

const SessionNotesExportView = () => {
  const searchParams = useSearchParams();
  const momId = searchParams.get('momId');
  const sessionId = searchParams.get('sessionId');

  const { fetchMomProfile, momProfileLoading } = useMomProfileContext();
  const { sessionsList, sessionsListLoading, fetchSessionsList } = useMomSessionNotes();

  useEffect(() => {
    if (!momId) return;

    fetchMomProfile(momId);

    if (!sessionId) {
      // If there is no sessionId query param, fetch all sessions and display whole list
      void fetchSessionsList(momId);
    }
  }, [momId, sessionId, fetchMomProfile, fetchSessionsList]);

  if (!momId) {
    return <div className='mt-20'>Unable to load session notes</div>;
  }

  if (momProfileLoading) {
    return <div className='mt-20'>Loading...</div>;
  }

  return sessionId ? (
    <SessionNotesReadOnlyDataPage sessionId={sessionId} />
  ) : (
    <>
      {sessionsListLoading || !sessionsList ? (
        <div className='mt-20'>Loading...</div>
      ) : (
        <>
          {sessionsList.map((session, index) => (
            // Make sure each session is on a new page for printing or saving as PDF
            <div key={session.id} className={index === sessionsList.length - 1 ? '' : 'mb-4 break-after-page'}>
              <SessionNotesReadOnlyDataPage sessionId={String(session.id)} />
            </div>
          ))}
        </>
      )}
    </>
  );
};

export default SessionNotesExportView;
