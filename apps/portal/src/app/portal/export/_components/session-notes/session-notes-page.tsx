import { buildReadOnlySessionNotesData } from '@/app/portal/[role]/dashboard/people/_utils/session-utils';
import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import ReadOnlyModuleData from '@/components/custom/read-only-module-data';
import { fetchSessionReportData } from '@/lib/portal';
import type { SessionSchema } from '@/types/schemas/session';
import { useEffect, useState } from 'react';

interface SessionNotesReadOnlyDataPageProps {
  sessionId: string;
}

const SessionNotesReadOnlyDataPage: React.FC<SessionNotesReadOnlyDataPageProps> = ({ sessionId }) => {
  const { actionPlanGoals } = useMomProfileContext();

  /**
   * Not using global session notes context here, because in case of exporting all session notes,
   * each page must maintain its own internal context. Using the global context would cause
   * the context to be overwritten with each new request in the loop
   */
  const [sessionReport, setSessionReport] = useState<
    | {
        id: string;
        lesson_id: string;
        session_id: string;
        lesson_status: 'not_started';
        attendance_and_promptness: '' | 'On_Time' | 'Late' | 'No_Show';
        moms_engagement_c: '' | 'Full' | 'Partial' | 'None';
        new_attempt: boolean;
        new_attempt_example: string;
        note: string;
        created_by_name: string;
        client_id_c: string;
        contact_id_c: string;
        date_submitted_c: string;
        covered_lesson_id: string;
        status_c: 'Submitted' | 'Not_Submitted' | 'Rejected' | 'Approved';
      }
    | undefined
  >();
  const [sessionReportLoading, setSessionReportLoading] = useState<boolean>(true);
  const [sessionReportError, setSessionReportError] = useState<string | undefined>();
  const [sessionMeetingData, setSessionMeetingData] = useState<SessionSchema | undefined>();

  const fetchSessionReport = async () => {
    setSessionReportLoading(true);
    try {
      const data = await fetchSessionReportData(sessionId);
      // Transform API report to match portal's expected type
      const transformedReport = {
        id: data.report.id || '',
        lesson_id: data.lesson.id || '',
        session_id: data.session.id || '',
        lesson_status: 'not_started' as const,
        attendance_and_promptness: (data.report.attendance_and_promptness || '') as '' | 'On_Time' | 'Late' | 'No_Show',
        moms_engagement_c: (data.report.moms_engagement_c || '') as '' | 'Full' | 'Partial' | 'None',
        new_attempt: data.report.new_attempt || false,
        new_attempt_example: data.report.new_attempt_example || '',
        note: data.report.note || '',
        created_by_name: data.session.created_by_name || '',
        client_id_c: data.session.pairing?.mom?.first_name || '',
        contact_id_c: data.session.pairing?.mom?.first_name || '',
        date_submitted_c: data.report.date_submitted_c || '',
        covered_lesson_id: data.lesson.id || '',
        status_c: (data.report.status === 'submitted'
          ? 'Submitted'
          : data.report.status === 'new'
            ? 'Not_Submitted'
            : data.report.status === 'rejected'
              ? 'Rejected'
              : data.report.status === 'approved'
                ? 'Approved'
                : 'Not_Submitted') as 'Submitted' | 'Not_Submitted' | 'Rejected' | 'Approved',
      };
      setSessionReport(transformedReport);
      setSessionMeetingData(data.session);
    } catch (error) {
      console.error('Error fetching session notes:', error);
      setSessionReport(undefined);
      setSessionMeetingData(undefined);
      setSessionReportError('Sorry, an error occurred. The session notes could not be loaded.');
    } finally {
      setSessionReportLoading(false);
    }
  };

  useEffect(() => {
    fetchSessionReport();
  }, [sessionId]);

  if (sessionReportLoading) {
    return <div className='mt-20'>Loading...</div>;
  }

  if (sessionReportError) {
    return (
      <div className='mt-20 flex flex-col items-center justify-center gap-4 text-destructive'>
        <p>{sessionReportError}</p>
        <p>{`Reference: Session ID ${sessionId}`}</p>
      </div>
    );
  }

  const sessionNotes = buildReadOnlySessionNotesData(actionPlanGoals, sessionReport, sessionMeetingData);

  return <ReadOnlyModuleData {...sessionNotes} />;
};

export default SessionNotesReadOnlyDataPage;
