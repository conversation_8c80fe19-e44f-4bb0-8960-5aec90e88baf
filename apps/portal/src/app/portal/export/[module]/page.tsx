import ConnectionLogsExportView from '../_components/connection-logs/connection-logs-export-view';
import SessionNotesExportView from '../_components/session-notes/session-notes-export-view';

/**
 * Generic page for "exporting" data such as session notes, etc.
 * Use `ReadOnlyModuleData` component to present data in a clean, simple, and printer-friendly view.
 * Makes it easy to use print dialog to save as PDF or print to paper.
 * Query params (read in each individual component) can be used to aid in fetching data to display.
 */

interface ExportPageProps {
  params: {
    module: string;
  };
}

const ExportPage: React.FC<ExportPageProps> = ({ params }): React.ReactNode => {
  const { module } = params;

  switch (module) {
    case 'session-notes':
      return <SessionNotesExportView />;
    case 'connection-logs':
      return <ConnectionLogsExportView />;
    default:
      return <div>Module not found</div>;
  }
};

export default ExportPage;
