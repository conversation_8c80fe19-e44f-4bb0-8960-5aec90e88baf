import { MomProfileProvider } from '@/app/portal/context/mom-profile-context';
import React from 'react';

import { MomSessionNotesProvider } from '../context/mom-session-notes-context';

const ExportLayout = ({ children }: { children: React.ReactNode }): React.ReactNode => {
  return (
    <div className='flex min-h-screen w-full flex-col items-center max-md:max-w-full'>
      <MomProfileProvider>
        <MomSessionNotesProvider>{children}</MomSessionNotesProvider>
      </MomProfileProvider>
    </div>
  );
};

export default ExportLayout;
