import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Play } from 'lucide-react';

const WatchTrainingVideosCard: React.FC = () => {
  return (
    <Card className='mt-6 flex items-center justify-between p-6'>
      <div className='flex w-full flex-col max-md:max-w-full'>
        <h2 className='text-lg font-semibold leading-7 text-gray-900 max-md:max-w-full'>
          Watch Advocate Training Videos
        </h2>
        <p className='mt-1 text-ellipsis text-sm leading-5 text-slate-600 max-md:max-w-full'>
          Learn about the EMA Advocate approach
        </p>
      </div>
      <Button variant='outline' className='flex w-max items-center'>
        <Play className='mr-2 h-5 w-5' />
        <span>Watch Training</span>
      </Button>
    </Card>
  );
};

export default WatchTrainingVideosCard;
