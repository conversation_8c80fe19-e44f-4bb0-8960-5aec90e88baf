import ImageSkeleton from '@/app/portal/_components/ui/image-skeleton';
import { useFindUniqueMom } from '@/hooks/generated/mom';
import { sfetch } from '@/lib/sfetch';
import Image from 'next/image';
import { useEffect, useState } from 'react';

// Default avatar path
const DEFAULT_AVATAR = '/assets/mom_generic_avatar.png';

interface MomPhotoProps {
  momId: string;
  size?: number;
  useIcon?: boolean; // New prop to specifically request icon
}

export function MomPhoto({ momId, size = 40, useIcon = true }: MomPhotoProps) {
  const { data: mom, isLoading: isMomLoading } = useFindUniqueMom(
    { where: { id: momId } },
    {
      enabled: !!momId,
      staleTime: 30000, // Cache for 30 seconds to reduce API calls
    },
  );

  // State for blob URL and loading state
  const [blobUrl, setBlobUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);

  // Get name for alt text
  const name = mom ? `${mom.first_name || ''} ${mom.last_name || ''}`.trim() : '';

  // Load the image blob when mom data changes
  useEffect(() => {
    // Skip if no mom data
    if (!mom) return;

    // Clean up function for blob URLs
    let currentBlobUrl: string | null = null;
    const cleanupBlobUrl = () => {
      if (currentBlobUrl) {
        URL.revokeObjectURL(currentBlobUrl);
      }
    };

    // Choose the appropriate photo URL based on context
    // For tables (small images), prefer the icon, then thumbnail, then full photo
    // For other uses, prefer thumbnail or full photo
    let photoUrlToUse: string | null | undefined = null;

    if (useIcon) {
      // For tables - prefer icon, then thumbnail, then full photo
      photoUrlToUse = mom.iconUrl || mom.thumbnailUrl || mom.photoUrl;
    } else {
      // For other contexts - prefer thumbnail or full photo
      photoUrlToUse = mom.thumbnailUrl || mom.photoUrl;
    }

    // If no photo URL, just show default
    if (!photoUrlToUse) {
      setIsLoading(false);
      setError(true);
      return;
    }

    // Load the image blob
    const loadImage = async () => {
      try {
        setIsLoading(true);
        setError(false);

        const photoUrl = photoUrlToUse as string;
        const filename = photoUrl.split('/').pop();
        if (!filename) throw new Error('Invalid filename');

        // Add timestamp for cache-busting
        const timestamp = Date.now();
        const proxyUrl = `/v1/s3/proxy/${filename}?t=${timestamp}`;

        // Fetch the image
        const response = await sfetch(proxyUrl);
        if (!response.ok) throw new Error('Failed to load image');

        // Create blob URL
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);

        // Store the current blob URL for cleanup
        currentBlobUrl = url;

        // Set the blob URL
        setBlobUrl(url);
        setIsLoading(false);
      } catch (_error) {
        setError(true);
        setIsLoading(false);
      }
    };

    loadImage();

    // Clean up blob URL on unmount or when mom changes
    return () => {
      // Clean up any previous blob URL
      if (blobUrl) {
        URL.revokeObjectURL(blobUrl);
      }
      cleanupBlobUrl();
    };
  }, [mom, useIcon]); // Add useIcon to dependencies

  // Get the appropriate photoUrl property to check loading state
  const photoToCheck =
    mom && (useIcon ? mom.iconUrl || mom.thumbnailUrl || mom.photoUrl : mom.thumbnailUrl || mom.photoUrl);

  // Show loading state while fetching mom data or image blob
  if (isMomLoading || (isLoading && !error && photoToCheck)) {
    return (
      <div
        className='relative flex items-center justify-center overflow-hidden rounded-full bg-gray-100'
        style={{ width: size, height: size }}
      >
        <ImageSkeleton size={size} />
      </div>
    );
  }

  // Show the image or default avatar
  return (
    <div
      className='relative flex items-center justify-center overflow-hidden rounded-full bg-gray-100'
      style={{ width: size, height: size }}
    >
      <Image
        src={blobUrl || DEFAULT_AVATAR}
        alt={name || 'Mom avatar'}
        width={size}
        height={size}
        className='h-full w-full rounded-full object-cover'
        priority
        unoptimized={!!blobUrl}
      />
    </div>
  );
}
