import React from 'react';

interface ImageSkeletonProps {
  size?: number | string;
  className?: string;
}

/**
 * A reusable skeleton loader for profile images with shimmer animation
 */
export const ImageSkeleton = ({ size = '100%', className = '' }: ImageSkeletonProps) => {
  // Convert size to style object - can be a number (px) or string (CSS value)
  const sizeStyle =
    typeof size === 'number' ? { width: `${size}px`, height: `${size}px` } : { width: size, height: size };

  return (
    <div className={`relative overflow-hidden rounded-full ${className}`} style={sizeStyle}>
      <div className='absolute inset-0 rounded-full bg-gray-200'></div>
      <div
        className='shimmer-animation absolute inset-0 rounded-full'
        style={{
          background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent)',
        }}
      ></div>
      <div className='absolute left-1/2 top-1/4 h-[40%] w-[40%] -translate-x-1/2 transform rounded-full bg-gray-300'></div>
      <div className='absolute left-1/2 top-[55%] h-[25%] w-[50%] -translate-x-1/2 transform rounded-full bg-gray-300'></div>
      <style jsx global>{`
        @keyframes shimmer {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(100%);
          }
        }

        .shimmer-animation {
          animation: shimmer 1.5s infinite;
        }

        /* Add transition classes for image loading */
        .fade-in {
          opacity: 0;
          animation: fadeIn 0.3s ease-in-out forwards;
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }

        .image-container {
          position: relative;
          transition: opacity 0.3s ease-in-out;
        }

        .image-container img {
          transition: opacity 0.3s ease-in-out;
        }
      `}</style>
    </div>
  );
};

export default ImageSkeleton;
