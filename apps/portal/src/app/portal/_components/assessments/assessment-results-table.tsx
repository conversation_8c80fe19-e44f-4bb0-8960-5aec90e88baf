'use client';

import GenericTable from '@/components/custom/generic-table';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { TableCell, TableRow } from '@/components/ui/table';
import { MoreVertical } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { usePathname } from 'next/navigation';
import React, { useState } from 'react';

import SendAssessmentFormModal from '../../[role]/dashboard/people/moms/[id]/_components/send-assessment-form-modal';
import MomViewAssessmentResultDrawer from '../../[role]/dashboard/people/moms/[id]/_components/view-assessment-result-drawer';

interface MomAssessmentsTableProps {
  isLoading?: boolean;
}

interface CardActionsDropdownProps {
  handleOpenModal: () => void;
}

interface AssessmentData {
  name: string;
  prescore: string;
  postscore: string;
}

const MomAssessmentResultsTable = (_: MomAssessmentsTableProps) => {
  const router = useRouter();
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [data] = useState<AssessmentData[]>([
    { name: 'Stress', prescore: '60', postscore: '-' },
    { name: 'Trauma', prescore: '10', postscore: '-' },
    { name: 'Social Support', prescore: '3.20', postscore: '-' },
    { name: 'Financial Security', prescore: '2.00', postscore: '-' },
    { name: 'Depression', prescore: '10', postscore: '-' },
  ]);

  const headers = [
    'Assessment',
    'Pre-Assessment Score',
    'Post-Assessment Score',
    'Results & Recommendation',
    'Actions',
  ];

  const renderRow = (assessment: AssessmentData) => (
    <TableRow className='cursor-pointer' onClick={() => {}}>
      <TableCell className='text-xs text-emaTextTertiary'>{assessment.name}</TableCell>
      <TableCell className='min-w-[180px] text-xs text-emaTextTertiary'>{assessment.prescore}</TableCell>
      <TableCell className='min-w-[200px] text-xs text-emaTextTertiary'>{assessment.postscore}</TableCell>
      <TableCell className='min-w-[220px] text-center text-xs text-emaTextTertiary'>
        <button
          onClick={() => {
            setIsDrawerOpen(true);
          }}
        >
          View
        </button>
      </TableCell>
      <TableCell>
        <CardActionsDropdown handleOpenModal={handleOpenModal} />
      </TableCell>
    </TableRow>
  );

  const renderMobileItem = (assessment: AssessmentData) => (
    <Link href={'#'}>
      <div className='space-y-2'>
        <div>
          <span className='font-medium'> {assessment.name}</span>
        </div>
        <div>
          <span className='font-medium'>Pre Assessment Score: {assessment.prescore}</span>
        </div>
        <div>
          <span className='font-medium'>Post Assessment Score: {assessment.postscore}</span>
        </div>
        <div className='text-sm text-gray-500'>
          <button
            onClick={() => {
              setIsDrawerOpen(true);
            }}
          >
            View
          </button>
        </div>
      </div>
    </Link>
  );

  const handleCloseModal = () => {
    setIsOpen(false);
  };

  const handleOpenModal = () => {
    setIsOpen(true);
  };

  return (
    <div>
      <GenericTable
        data={data}
        isLoading={false}
        headers={headers}
        headerSection={
          <div className='-ml-4 items-center justify-between space-y-2 md:flex'>
            <h2 className='text-xl font-semibold'>Assessment Results - Nurture Parenting</h2>
            <div className='flex flex-col space-y-2'>
              <Button
                onClick={() => {
                  router.push(`${pathname}/pre-assessment`);
                }}
                type='button'
                variant='default'
                className='text-sm'
              >
                Take Pre-Assessment
              </Button>
              <Button
                onClick={() => {
                  router.push(`${pathname}/post-assessment`);
                }}
                type='button'
                variant='default'
                className='text-sm'
              >
                Take Post-Assessment
              </Button>
              {/* TODO: https://servant-io.atlassian.net/browse/EMA-815 */}
              <Button type='button' variant='outline' onClick={() => setIsOpen(true)} className='text-sm'>
                Send Pre or Post Assessment
              </Button>
            </div>
          </div>
        }
        rowRenderer={renderRow}
        mobileRenderer={renderMobileItem}
        emptyMessage='No assessments found.'
        shouldUseCustomRowComponent={true}
        tableWrapperClassName='mt-0 px-6'
        tableHeaderClassName='text-xs'
        shouldShowPagination={false}
      />

      <SendAssessmentFormModal open={isOpen} onClose={handleCloseModal} />
      <MomViewAssessmentResultDrawer
        isOpen={isDrawerOpen}
        onClose={() => {
          setIsDrawerOpen(false);
        }}
      />
    </div>
  );
};

export default MomAssessmentResultsTable;

const CardActionsDropdown: React.FC<CardActionsDropdownProps> = ({ handleOpenModal }) => {
  const router = useRouter();
  const pathname = usePathname();

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant='ghost' size='icon' className='rounded-full'>
            <MoreVertical className='h-6 w-6 text-gray-500' />
            <span className='sr-only'>Toggle paired mom actions menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end'>
          <DropdownMenuItem
            onClick={() => {
              router.push(`${pathname}/pre-assessment`);
            }}
          >
            View Pre-Assessment
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => {
              router.push(`${pathname}/post-assessment`);
            }}
          >
            View Post-Assessment
          </DropdownMenuItem>
          {/* TODO: https://servant-io.atlassian.net/browse/EMA-815 */}
          <DropdownMenuItem onClick={handleOpenModal}>Resend Assessments</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
};
