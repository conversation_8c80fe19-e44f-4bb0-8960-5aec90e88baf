'use client';

import GenericTable from '@/components/custom/generic-table';
import { Button } from '@/components/ui/button';
import { TableCell, TableRow } from '@/components/ui/table';
import Link from 'next/link';
import React from 'react';
import { useState } from 'react';

import EditConcreteIndicatorsDrawer from '../../[role]/dashboard/people/moms/[id]/_components/edit-concrete-indicators-drawer';

interface Assessment {
  name: string;
  pre: string;
  post: string;
}

const MomAssessmentIndicatorsTable = () => {
  const [isOpen, SetIsOpen] = useState(false);

  const data = [
    { name: 'Employment Status', pre: 'Part Time', post: '-' },
    { name: 'School Status', pre: 'Not In School', post: '-' },
    { name: 'Income Status', pre: '$0.00', post: '-' },
    { name: 'Housing Status', pre: 'Unstable', post: '-' },
    { name: 'Child Placement Status', pre: '-', post: '-' },
  ];

  const headers = ['Status', 'Pre-Assessment', 'Post-Assessment'];

  const renderRow = (assessment: Assessment) => (
    <TableRow className='cursor-pointer' onClick={() => {}}>
      <TableCell className='text-xs text-emaTextTertiary'>{assessment.name}</TableCell>
      <TableCell className='min-w-[180px] text-xs text-emaTextTertiary'>{assessment.pre}</TableCell>
      <TableCell className='min-w-[200px] text-xs text-emaTextTertiary'>{assessment.post}</TableCell>
    </TableRow>
  );

  const handleClose = () => {
    SetIsOpen(false);
  };

  const renderMobileItem = (assessment: Assessment) => (
    <Link href={'#'}>
      <div className='space-y-2'>
        <div className='flex justify-between'>
          <span className='font-medium'> {assessment.name}</span>
        </div>
        <div className='flex justify-between text-sm text-gray-500'>
          <span>Pre Assessment: {assessment.pre}</span>
        </div>
        <div className='flex justify-between text-sm text-gray-500'>
          <span>Post Assessment: {assessment.post}</span>
        </div>
      </div>
    </Link>
  );

  return (
    <div>
      <GenericTable
        data={data}
        isLoading={false}
        headers={headers}
        headerSection={
          <div className='-ml-4 flex items-center justify-between'>
            <h2 className='text-xl font-semibold'>Concrete Indicators - Nurture Parenting</h2>
            <Button type='button' onClick={() => SetIsOpen(true)} variant='outline' className='text-sm'>
              Edit
            </Button>
          </div>
        }
        rowRenderer={renderRow}
        mobileRenderer={renderMobileItem}
        emptyMessage='No indicators found.'
        shouldUseCustomRowComponent={true}
        tableWrapperClassName='mt-0 px-6'
        tableHeaderClassName='text-xs'
        shouldShowPagination={false}
      />
      <EditConcreteIndicatorsDrawer isOpen={isOpen} onClose={handleClose} />
    </div>
  );
};

export default MomAssessmentIndicatorsTable;
