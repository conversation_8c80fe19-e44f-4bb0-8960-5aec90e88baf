import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { useEffect, useState } from 'react';

import { assessmentScoreFields, assessmentScoreSchema } from './_lib/assessments-score.config';

interface AssessmentData {
  [key: string]: string;
}

const AssessmentsScore = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const onSave = (_data: AssessmentData) => {
    setIsEditing(false);
    setIsSaving(true);
    try {
      // save data to database
      toast({
        title: 'Success',
        description: 'The assessment score has been successfully saved',
        variant: 'success',
      });
    } catch (error) {
      console.error('Error saving assessments score:', error);
      toast({
        title: 'Error',
        description: 'There was an error saving the assessment score. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const defaultValues = [
    {
      pre_as_construct_a: '7.2',
      post_as_construct_a: '8.5',
    },
    {
      pre_as_construct_b: '6.8',
      post_as_construct_b: '8.1',
    },
    {
      pre_as_construct_c: '5.9',
      post_as_construct_c: '7.4',
    },
    {
      pre_as_construct_d: '6.5',
      post_as_construct_d: '7.8',
    },
    {
      pre_as_construct_e: '7.1',
      post_as_construct_e: '8.3',
    },
  ];

  useEffect(() => {
    // fetch assessment score data
  }, []);

  return (
    <div className='max-w-[700px] space-y-4 rounded-lg bg-white p-6 shadow'>
      <div className='flex items-center justify-between'>
        <h2 className='text-lg font-semibold'>AAPI Assessment Results - Nurturing Parenting</h2>
      </div>

      <div className='mb-4 flex items-center justify-between text-sm leading-5 text-emaTextSecondary [@media(min-width:429px)]:hidden'>
        <span>Pre-Assessment Score</span>
        <span>Post-Assessment Score</span>
      </div>
      <div className='mt-6 hidden w-full flex-wrap items-start gap-4 max-md:max-w-full [@media(min-width:429px)]:flex'>
        <span className='flex w-2/6 w-60 min-w-[200px] min-w-[50px] max-w-[280px] flex-col items-start text-sm font-semibold leading-5 text-emaTextSecondary max-md:min-w-[100px] max-md:max-w-[100px]'>
          Construct
        </span>
        <div className='xs:flex-row xs:gap-0 flex min-w-[240px] flex-1 shrink basis-0 flex-row gap-4 lg:flex-row lg:gap-0 lg:space-x-4'>
          <div className='flex flex-col lg:flex-1'>Pre-Assessment Score</div>
          <div className='ml-4 flex flex-col lg:flex-1'>Post-Assessment Score</div>
        </div>
      </div>
      <FormFactory
        fields={assessmentScoreFields}
        onSubmit={onSave}
        schema={assessmentScoreSchema}
        defaultValues={defaultValues.reduce((acc, item) => ({ ...acc, ...item }), {})}
        readOnly={!isEditing}
        labelWrapperClassName='w-2/6 max-md:max-w-[100px] min-w-[50px] max-md:min-w-[100px]'
        formFieldSubFieldsWrapperClass='flex flex-row xs:flex-row xs:gap-0'
        formFieldElClass='w-full'
        actionButtonsComponent={
          <>
            {isEditing ? (
              <Button
                type='submit'
                onClick={() => {
                  setIsEditing(false);
                  onSave(defaultValues.reduce((acc, item) => ({ ...acc, ...item }), {}) as Record<string, string>);
                }}
              >
                {isSaving ? 'Saving...' : 'Save Changes'}
              </Button>
            ) : null}{' '}
          </>
        }
      />

      <Button onClick={() => setIsEditing(!isEditing)} variant={isEditing ? 'secondary' : 'default'}>
        {isEditing ? 'Cancel' : 'Edit'}
      </Button>
    </div>
  );
};

export default AssessmentsScore;
