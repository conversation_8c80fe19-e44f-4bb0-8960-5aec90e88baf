import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const assessmentScoreFields: FieldConfig[] = [
  {
    name: 'construct_a_group',
    label: 'Construct A',
    type: 'row-group',
    subFields: [
      {
        name: 'pre_as_construct_a',
        label: '',
        type: 'text',
      },
      {
        name: 'post_as_construct_a',
        label: '',
        type: 'text',
      },
    ],
  },
  {
    name: 'construct_b_group',
    label: 'Construct B',
    type: 'row-group',
    subFields: [
      {
        name: 'pre_as_construct_b',
        label: '',
        type: 'text',
      },
      {
        name: 'post_as_construct_b',
        label: '',
        type: 'text',
      },
    ],
  },
  {
    name: 'construct_c_group',
    label: 'Construct C',
    type: 'row-group',
    subFields: [
      {
        name: 'pre_as_construct_c',
        label: '',
        type: 'text',
      },
      {
        name: 'post_as_construct_c',
        label: '',
        type: 'text',
      },
    ],
  },
  {
    name: 'construct_d_group',
    label: 'Construct D',
    type: 'row-group',
    subFields: [
      {
        name: 'pre_as_construct_d',
        label: '',
        type: 'text',
      },
      {
        name: 'post_as_construct_d',
        label: '',
        type: 'text',
      },
    ],
  },
  {
    name: 'construct_e_group',
    label: 'Construct E',
    type: 'row-group',
    subFields: [
      {
        name: 'pre_as_construct_e',
        label: '',
        type: 'text',
      },
      {
        name: 'post_as_construct_e',
        label: '',
        type: 'text',
      },
    ],
  },
];

export const assessmentScoreSchema = yup.object().shape({
  construct_a_group: yup.object().shape({
    pre_as_construct_a: yup.string(),
    post_as_construct_a: yup.string(),
  }),
  construct_b_group: yup.object().shape({
    pre_as_construct_b: yup.string(),
    post_as_construct_b: yup.string(),
  }),
  construct_c_group: yup.object().shape({
    pre_as_construct_c: yup.string(),
    post_as_construct_c: yup.string(),
  }),
  construct_d_group: yup.object().shape({
    pre_as_construct_d: yup.string(),
    post_as_construct_d: yup.string(),
  }),
  construct_e_group: yup.object().shape({
    pre_as_construct_e: yup.string(),
    post_as_construct_e: yup.string(),
  }),
});
