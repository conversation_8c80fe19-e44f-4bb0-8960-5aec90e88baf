import { FormFactoryRef, WellBeingAssessmentFormData } from '@/app/portal/types';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { assignSummaryFieldsAndCalculateScores } from '@/services/wellbeing-assessment-scoring';
import { generateFieldsSchema } from '@/types/form-field';
import { Dispatch, SetStateAction, useEffect, useMemo, useRef, useState } from 'react';

import IntakeFormDescriptiveHeading from '../../[role]/dashboard/people/referrals/_components/intake-form-descriptive-heading';
import { getWellBeingAssessmentFields } from '../../[role]/dashboard/people/referrals/_components/intake-referral-form.config';
import { validateWellbeingAssessmentForm } from '../../[role]/dashboard/people/referrals/_components/utils';

const WellBeingAssessment = ({
  person,
  formData,
  setFormData,
  onBackStep,
  onNextStep,
  nextStepLabel,
  isNextStepLoading,
  readOnly,
}: {
  person: 'mom' | 'coordinator';
  formData: WellBeingAssessmentFormData;
  setFormData: Dispatch<SetStateAction<WellBeingAssessmentFormData>>;
  onBackStep?: () => void;
  onNextStep?: () => void;
  nextStepLabel?: string;
  isNextStepLoading?: boolean;
  readOnly?: boolean;
}) => {
  const formRef = useRef<FormFactoryRef>(null);
  const [formError, setFormError] = useState<string>();

  const wellBeingAssessmentFields = useMemo(() => getWellBeingAssessmentFields(person), [person]);
  const wellBeingAssessmentSchema = useMemo(
    () => generateFieldsSchema(wellBeingAssessmentFields, formData.wellBeingForm.values),
    [wellBeingAssessmentFields, formData.wellBeingForm.values],
  );

  useEffect(() => {
    const { newWellBeingFormValues, updateRequired } = assignSummaryFieldsAndCalculateScores(
      formData,
      wellBeingAssessmentFields,
    );

    if (updateRequired) {
      formRef.current?.form.reset(newWellBeingFormValues);
      setFormData((formData) => ({
        ...formData,
        wellBeingForm: { ...formData.wellBeingForm, values: newWellBeingFormValues },
      }));
    }
  }, [formData, setFormData, wellBeingAssessmentFields]);

  useEffect(() => {
    setFormData((formData) => ({
      ...formData,
      wellBeingForm: { ...formData.wellBeingForm, schema: wellBeingAssessmentSchema, ref: formRef },
    }));
  }, [setFormData, wellBeingAssessmentSchema]);

  return (
    <div className='mb-14 flex flex-col gap-4'>
      <IntakeFormDescriptiveHeading
        title='Introduction to Family Well-Being Assessment'
        description='The rest of this assessment will take around 20 minutes. We would love to learn about different areas of your life, such as family, housing, and daily needs, to see where we can support you directly or connect you with helpful resources.'
      />
      <div className='border-emaBorder rounded-lg border bg-white p-4'>
        <FormFactory
          ref={formRef}
          fields={wellBeingAssessmentFields}
          onSubmit={() => {}}
          schema={wellBeingAssessmentSchema}
          defaultValues={formData.wellBeingForm.values}
          actionButtonsComponent={null}
          onChange={(values) =>
            setFormData((formData) => ({ ...formData, wellBeingForm: { ...formData.wellBeingForm, values } }))
          }
          readOnly={readOnly}
        />
      </div>
      {formError && <p className='self-end text-red-500'>{formError}</p>}
      <div className='flex'>
        {onBackStep && (
          <Button onClick={onBackStep} variant='outline' className='mr-auto bg-gray-200 text-gray-700'>
            Back
          </Button>
        )}
        {onNextStep && nextStepLabel && (
          <Button
            className='ml-auto'
            onClick={async () => {
              setFormError(undefined);
              const formError = await validateWellbeingAssessmentForm(formData);
              if (formError) {
                setFormError(formError);
              } else {
                onNextStep();
              }
            }}
            disabled={isNextStepLoading}
          >
            {nextStepLabel}
          </Button>
        )}
      </div>
    </div>
  );
};

export default WellBeingAssessment;
