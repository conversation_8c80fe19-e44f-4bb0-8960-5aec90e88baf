'use client';

import { Button } from '@/components/ui/button';
import { useUserInfo } from '@/context/user-info';
import { removeAccessToken } from '@/lib/accessTokenStorageService';
import { sfetch } from '@/lib/sfetch';
import { useQuery } from '@tanstack/react-query';
import { CircleUserRound, LogOut } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { useDashboardContext } from '../../context/dashboard-context';

// Create a custom event name specifically for user photos
const USER_PHOTO_UPDATED_EVENT = 'user-photo-updated';

const NavSidebarUserInfo: React.FC = () => {
  const router = useRouter();
  const { profile } = useDashboardContext();
  const { userData } = useUserInfo();
  const userId = userData?.sub;
  const [avatarError, setAvatarError] = useState(false);
  const [timestamp, setTimestamp] = useState(Date.now());
  const [isLoading, setIsLoading] = useState(true);
  const [hasAttemptedLoad, setHasAttemptedLoad] = useState(false);

  // Fetch the user data to get the photo URL
  const { data: user } = useQuery({
    queryKey: ['user-data', userId],
    queryFn: async () => {
      if (!userId) return null;

      const response = await sfetch(`/v1/user/${userId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch user data');
      }
      return response.json();
    },
    enabled: !!userId,
  });

  // Get the photo URL from the user data
  const userIcon = user?.iconUrl;

  // Fetch the actual photo from S3 proxy
  const { data: iconImageUrl } = useQuery({
    queryKey: ['user-photo', userId, userIcon, timestamp],
    queryFn: async () => {
      if (!userIcon) return null;

      const filename = userIcon.split('/').pop();
      if (!filename) return null;

      try {
        const response = await sfetch(`/v1/s3/proxy/${filename}?_t=${timestamp}`);
        if (!response.ok) {
          throw new Error('Failed to fetch photo');
        }
        const blob = await response.blob();
        return URL.createObjectURL(blob);
      } catch (error) {
        console.error('Error downloading photo:', error);
        setAvatarError(true);
        return null;
      }
    },
    enabled: !!userIcon && !avatarError,
  });

  // Clean up object URLs when component unmounts or when iconImageUrl changes
  useEffect(() => {
    return () => {
      if (iconImageUrl) {
        URL.revokeObjectURL(iconImageUrl);
      }
    };
  }, [iconImageUrl]);

  // Listen for ONLY user photo updates
  useEffect(() => {
    const handleUserPhotoUpdate = () => {
      setTimestamp(Date.now());
      setAvatarError(false);
      setIsLoading(true);
      setHasAttemptedLoad(false);
    };

    // Listen to the custom user-specific event
    window.addEventListener(USER_PHOTO_UPDATED_EVENT, handleUserPhotoUpdate);

    // For backward compatibility with older events, check the entity type
    const handleLegacyPhotoUpdate = (event: Event) => {
      const customEvent = event as CustomEvent<{ entityType: string; entityId: string }>;

      // Only proceed if this is specifically a user photo update
      if (customEvent.detail && customEvent.detail.entityType === 'user') {
        handleUserPhotoUpdate();
      }
    };

    // Also listen to the general photo-updated event, but filter for user updates
    window.addEventListener('photo-updated', handleLegacyPhotoUpdate);

    return () => {
      window.removeEventListener(USER_PHOTO_UPDATED_EVENT, handleUserPhotoUpdate);
      window.removeEventListener('photo-updated', handleLegacyPhotoUpdate);
    };
  }, []);

  // Handle loading state
  useEffect(() => {
    if (!profile || !user) return;

    // If we have an icon URL, wait for it to load
    if (userIcon) {
      if (iconImageUrl) {
        setIsLoading(false);
        setHasAttemptedLoad(true);
      }
    } else {
      // If we don't have an icon URL, we can show the default icon
      setIsLoading(false);
      setHasAttemptedLoad(true);
    }
  }, [profile, user, userIcon, iconImageUrl]);

  const handleAvatarError = () => {
    setAvatarError(true);
    setIsLoading(false);
    setHasAttemptedLoad(true);
  };

  const logout = async (): Promise<void> => {
    const logoutResponse = await sfetch('/v1/auth/logout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}),
    });

    removeAccessToken();

    if (logoutResponse.ok) {
      router.push('/login');
    } else {
      // TODO: handle error
      // window.alert('There was an error logging out. Please try again.');
    }
  };

  // Show loading state
  if (isLoading || !hasAttemptedLoad) {
    return (
      <div className='mt-auto flex items-center justify-between bg-gray-50 p-4 font-light text-gray-700'>
        <div className='flex items-center gap-2'>
          <div className='relative flex h-10 w-10 animate-pulse items-center justify-center overflow-hidden rounded-full bg-gray-200' />
          <div className='flex flex-col gap-1'>
            <div className='h-8 w-32 animate-pulse rounded bg-gray-200' />
            <div className='h-3 w-32 animate-pulse rounded bg-gray-200' />
          </div>
        </div>
        <Button variant='ghost' size='icon' className='text-gray-500 hover:text-gray-700' onClick={logout}>
          <LogOut className='h-5 w-5' />
        </Button>
      </div>
    );
  }

  return (
    <div className='mt-auto flex items-center justify-between bg-gray-50 p-4 font-light text-gray-700'>
      <div className='flex items-center gap-2'>
        <div className='relative flex h-10 w-10 items-center justify-center overflow-hidden rounded-full bg-gray-200'>
          {iconImageUrl && !avatarError ? (
            <Image
              src={iconImageUrl}
              alt={profile.firstName}
              width={40}
              height={40}
              className='h-full w-full object-cover'
              onError={handleAvatarError}
            />
          ) : (
            <CircleUserRound className='h-7 w-7 text-gray-400' />
          )}
        </div>
        <div>
          <div className='text-sm font-medium'>{`${profile.firstName} ${profile.lastName}`}</div>
          <div className='text-xs text-gray-500'>{profile.email}</div>
        </div>
      </div>
      <Button variant='ghost' size='icon' className='text-gray-500 hover:text-gray-700' onClick={logout}>
        <LogOut className='h-5 w-5' />
      </Button>
    </div>
  );
};

export default NavSidebarUserInfo;
