import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Menu } from 'lucide-react';
import Image from 'next/image';

import NavSidebar from './nav-sidebar';

interface NavHeaderProps {
  portalRole: string;
}

const NavHeader = ({ portalRole }: NavHeaderProps): React.ReactNode => (
  <header className='fixed left-0 top-0 z-10 flex h-14 w-full items-center gap-4 bg-emaBgPage px-4 py-2 shadow-sm lg:hidden'>
    <div className='w-full flex-1'>
      <Image src='/assets/ema-logo.svg' alt='EMA Logo' width={78} height={24} />
    </div>
    <Sheet>
      <SheetTrigger asChild>
        <Button variant='outline' size='icon' className='shrink-0 lg:hidden'>
          <Menu className='h-5 w-5' />
          <span className='sr-only'>Toggle navigation menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side='left' className='flex flex-col max-[400px]:w-full'>
        <NavSidebar portalRole={portalRole} mode='mobile' />
      </SheetContent>
    </Sheet>
  </header>
);

export default NavHeader;
