'use client';

import { cn } from '@/lib/utils';
import { ChevronRight, House } from 'lucide-react';
import { usePathname } from 'next/navigation';
import { Fragment } from 'react';

interface BreadcrumbItem {
  label: string;
  href: string;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
  className?: string;
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ items, className }) => {
  const currentUrl = usePathname();
  const breadcrumbsBase = `${currentUrl.split('dashboard')[0]}dashboard`;

  return (
    <nav aria-label='Breadcrumb' className={cn('flex items-center', className)}>
      <ol className='my-auto flex min-w-[240px] items-center gap-2 self-stretch'>
        <li>
          <a
            href={breadcrumbsBase}
            className='my-auto flex w-7 items-start self-stretch rounded-md p-1 hover:bg-gray-100'
          >
            <House className='h-5 w-5 text-slate-600' />
            <span className='sr-only'>Home</span>
          </a>
        </li>
        {items.map((item, index) => (
          <Fragment key={item.label}>
            <ChevronRight className='h-4 w-4 text-slate-400' aria-hidden='true' />
            <li>
              <a
                href={breadcrumbsBase + item.href}
                className={cn(
                  'my-auto self-stretch rounded-md px-2 py-1 text-sm font-medium leading-5',
                  index === items.length - 1
                    ? 'bg-gray-50 font-semibold text-slate-700'
                    : 'text-slate-600 hover:bg-gray-100',
                )}
                aria-current={index === items.length - 1 ? 'page' : undefined}
              >
                {item.label}
              </a>
            </li>
          </Fragment>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumbs;
