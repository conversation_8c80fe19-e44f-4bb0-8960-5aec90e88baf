'use client';

import { type PortalNavLink } from '@/app/portal/types';
import { useUserInfo } from '@/context/user-info';
import { cn } from '@/lib/utils';
import {
  CalendarDays,
  ChartColumnIncreasing,
  ChartNoAxesColumn,
  FileCog,
  Files,
  LayoutDashboard,
  SettingsIcon,
  Users,
} from 'lucide-react';
import Image from 'next/image';
import { usePathname } from 'next/navigation';

import NavSidebarLink from './nav-sidebar-link';
import NavSidebarSearch from './nav-sidebar-search';
import NavSidebarUserInfo from './nav-sidebar-user-info';

interface NavSidebarProps {
  portalRole: string;
  mode: 'desktop' | 'mobile';
}

const NavSidebar: React.FC<NavSidebarProps> = ({ portalRole, mode }) => {
  const { roles } = useUserInfo();
  const isAdmin = roles.includes('administrator');
  const currentUrl = usePathname();

  const mainNavItems: PortalNavLink[] = [
    {
      // possible to add unique icons per nav item in the future
      icon: <LayoutDashboard />,
      label: 'Dashboard',
      href: `/portal/${portalRole}/dashboard`,
      isActive: currentUrl.endsWith('/dashboard'),
    },
    {
      icon: <Users />,
      label: 'My People',
      href: `/portal/${portalRole}/dashboard/people`,
      isActive: currentUrl.includes('/people'),
      // TODO: Prospective advocate solution not designed yet from backend/SuiteCRM perspective - refactor if necessary
      isHidden: portalRole === 'prospective-advocate',
    },
    {
      icon: <Files />,
      label: 'Resources',
      href: `/portal/${portalRole}/dashboard/resources`,
      isActive: currentUrl.includes('/resources'),
    },
    {
      icon: <ChartColumnIncreasing />,
      label: 'Reporting',
      href: `/portal/${portalRole}/dashboard/reporting/client-stability`,
      isActive: currentUrl.includes('/reporting'),
      isHidden: portalRole === 'prospective-advocate',
    },
    {
      icon: <CalendarDays />,
      label: 'Events',
      href: `/portal/${portalRole}/dashboard/events`,
      isActive: currentUrl.includes('/events'),
      isHidden: portalRole === 'prospective-advocate',
    },
    {
      icon: <FileCog />,
      label: 'Admin',
      href: `/portal/${portalRole}/dashboard/admin`,
      isActive: currentUrl.includes('/admin'),
      isHidden: !isAdmin,
    },
    {
      icon: <FileCog />,
      label: 'Users',
      href: `/portal/${portalRole}/dashboard/users`,
      isActive: currentUrl.includes('/users'),
      isHidden: portalRole !== 'supervisor',
    },
  ];

  const bottomNavItems: PortalNavLink[] = [
    {
      icon: <SettingsIcon />,
      label: 'Settings',
      href: `/portal/${portalRole}/dashboard/settings`,
      isActive: currentUrl.includes('/settings'),
    },
  ];

  return (
    <nav
      className={cn(
        mode === 'desktop'
          ? 'hidden border-r border-solid border-r-gray-200 lg:flex lg:w-[270px] lg:min-w-[270px] lg:max-w-[270px]'
          : 'flex',
        'sticky top-0 h-screen flex-col overflow-y-auto bg-white',
      )}
    >
      <div className='flex min-w-[240px] flex-grow flex-col'>
        <div className='flex-shrink-0 pt-8'>
          <div className={cn('flex flex-col items-start', mode === 'desktop' ? 'pl-6 pr-5' : 'px-0', 'w-full')}>
            <Image src='/assets/ema-logo.svg' alt='EMA Logo' width={78} height={24} />
          </div>
          <div className={mode === 'desktop' ? 'px-4' : 'px-0'}>
            <NavSidebarSearch />
          </div>
          <div
            className={cn(
              'mt-6 flex w-full flex-col text-base font-semibold text-slate-700',
              mode === 'desktop' ? 'px-4' : 'px-0',
            )}
          >
            {mainNavItems.map((item) => (item.isHidden ? null : <NavSidebarLink key={item.label} {...item} />))}
          </div>
        </div>
        <div className='flex-grow' />
        <div className={cn('w-full flex-shrink-0 pb-8', mode === 'desktop' ? 'px-4' : 'px-0')}>
          <div className='flex w-full flex-col text-base font-semibold'>
            {bottomNavItems.map((item) => (
              <NavSidebarLink key={item.label} {...item} />
            ))}
          </div>
          <NavSidebarUserInfo />
        </div>
      </div>
    </nav>
  );
};

export default NavSidebar;
