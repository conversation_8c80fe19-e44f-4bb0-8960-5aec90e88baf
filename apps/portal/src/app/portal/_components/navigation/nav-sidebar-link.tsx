import Link from 'next/link';
import { FC, ReactNode } from 'react';

interface NavSidebarLinkProps {
  icon: ReactNode;
  label: string;
  href: string;
  isActive?: boolean;
}

const NavSidebarLink: FC<NavSidebarLinkProps> = ({ icon, label, href, isActive = false }) => {
  return (
    <Link href={href}>
      <div
        className={`flex w-full items-center gap-2 overflow-hidden whitespace-nowrap px-3 py-2 ${
          isActive ? 'bg-stone-100 text-gray-800' : 'bg-white text-slate-700'
        } rounded-md`}
      >
        <div className='my-auto flex w-full flex-1 shrink basis-0 items-center gap-3 self-stretch'>
          {icon}
          <div className='my-auto self-stretch'>{label}</div>
        </div>
      </div>
    </Link>
  );
};

export default NavSidebarLink;
