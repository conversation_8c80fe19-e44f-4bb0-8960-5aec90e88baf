'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { SearchIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { useDashboardContext } from '../../context/dashboard-context';

const NavSidebarSearch: React.FC = () => {
  const [query, setQuery] = useState('');
  const router = useRouter();
  const { profile } = useDashboardContext();

  const handleSearch = () => {
    if (query.trim()) {
      router.push(`/portal/${profile?.portalRole}/dashboard/people/search?q=${encodeURIComponent(query)}`);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className='mt-6 flex w-full flex-row text-base'>
      <Input
        type='search'
        placeholder={`Search...`}
        aria-label='Search'
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        onKeyDown={handleKeyDown}
        className='rounded-r-none ring-0 focus:ring-0 focus-visible:ring-0'
      />
      <Button onClick={handleSearch} className='h-10 w-10 rounded-l-none p-0'>
        <SearchIcon />
      </Button>
    </div>
  );
};

export default NavSidebarSearch;
