'use client';

import { useDashboardContext } from '@/app/portal/context/dashboard-context';

interface DashboardHeaderProps {
  headerText: string;
  actionsComponent: React.ReactNode;
}

const DashboardHeader = ({ headerText, actionsComponent }: DashboardHeaderProps): JSX.Element => {
  const { profile } = useDashboardContext();
  const { firstName } = profile;

  return (
    <header className='mb-8 flex flex-wrap items-start justify-between gap-4 lg:items-center'>
      <div>
        <h1 className='text-3xl font-bold'>Welcome back, {firstName}</h1>
        <p className='text-muted-foreground'>{headerText}</p>
      </div>
      <div className='mt-4 flex flex-wrap items-center gap-4 lg:mt-0'>{actionsComponent}</div>
    </header>
  );
};

export default DashboardHeader;
