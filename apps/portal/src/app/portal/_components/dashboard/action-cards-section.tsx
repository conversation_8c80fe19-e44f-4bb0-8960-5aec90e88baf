import type { DashboardActionsCard } from '@/app/portal/types';

import ActionCard from './action-card';

interface ActionCardsSectionProps {
  actionCards: DashboardActionsCard[];
}

const ActionCardsSection = ({ actionCards }: ActionCardsSectionProps): JSX.Element => {
  return (
    <section className='mb-8 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3'>
      {actionCards.map((item: DashboardActionsCard, i) => (
        <ActionCard key={`${item.label}-${i}`} icon={item.icon} label={item.label} description={item.description} />
      ))}
    </section>
  );
};

export default ActionCardsSection;
