import React from 'react';

import WatchTrainingVideosCard from '../prospective-advocate/watch-training-videos-card';
import DashboardHeader from './dashboard-header';

const LandingPageProspectiveAdvocate: React.FC = () => {
  return (
    <>
      <DashboardHeader
        headerText='Stay up to date with your pairings, upcoming sessions, and action plans.'
        actionsComponent={null}
      />
      <WatchTrainingVideosCard />
    </>
  );
};

export default LandingPageProspectiveAdvocate;
