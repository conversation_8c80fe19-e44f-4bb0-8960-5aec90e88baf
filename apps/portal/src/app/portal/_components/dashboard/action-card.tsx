import type { DashboardActionsCard } from '@/app/portal/types';

const ActionCard: React.FC<DashboardActionsCard> = ({ icon, label, description }) => {
  // lucide-react icon component from configuration object
  const Icon = icon;

  return (
    <div className='rounded-lg border bg-white shadow-sm transition-shadow hover:shadow-md'>
      <button
        type='button'
        className='h-full w-full p-4 text-left focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500'
      >
        <div className='flex items-center space-x-4'>
          <div className='flex-shrink-0 rounded-lg bg-darkGreen p-3'>
            <Icon className='h-6 w-6 text-white' />
          </div>
          <div className='flex-grow'>
            <h2 className='text-lg font-semibold text-gray-900'>{label}</h2>
            <p className='text-sm text-gray-500'>{description}</p>
          </div>
        </div>
      </button>
    </div>
  );
};

export default ActionCard;
