import TablePaginationFooter from '@/components/custom/table-pagination-footer';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { formatDateFromString } from '@suiteapi/models';
import { YupSchemas } from '@suiteapi/models';
import { ChevronRight } from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import { useState } from 'react';

import { MomPhoto } from '../ui/mom-photo';

const SessionNotesTable = ({
  data,
  isLoading,
}: {
  data?: YupSchemas.SessionReportWithPairings[];
  isLoading: boolean;
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const sessionList = data?.filter(
    (session) =>
      session.status === YupSchemas.SessionNoteStatus.SUBMITTED ||
      session.status === YupSchemas.SessionNoteStatus.REJECTED,
  );
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 7;
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = sessionList ? sessionList.slice(indexOfFirstItem, indexOfLastItem) : [];
  const totalPages = sessionList ? Math.ceil(sessionList.length / itemsPerPage) : 0;

  const handleEditButtonClick = (id: string | undefined, contactId: string | undefined) => {
    router.push(`${pathname}/people/moms/${contactId}/session-notes/${id}`);
  };

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Session name is either 'Group Session' or the name of the Mom if a single session.
  const getSessionNoteName = (sessionReport: YupSchemas.SessionReportWithPairings) => {
    if (!sessionReport.session.pairing) {
      return 'Error Loading Session Note';
    }

    const mom = sessionReport.session.pairing.mom;
    if (!mom) {
      return sessionReport.session.name || 'Unnamed Session';
    }

    return `${mom.first_name || ''} ${mom.last_name || ''}`.trim();
  };

  const getAdvocateNameFromSessionNote = (sessionReport: YupSchemas.SessionReportWithPairings) => {
    if (!sessionReport.session.pairing?.advocateUser) {
      return undefined;
    }
    const advocate = sessionReport.session.pairing.advocateUser;
    return `${advocate.firstName || ''} ${advocate.lastName || ''}`.trim() || undefined;
  };

  return (
    <>
      {isLoading ? (
        <p className='py-4 text-center'>Loading sessions...</p>
      ) : (
        <>
          {/* Mobile View */}
          <div className='mb-4 flex flex-col gap-3 md:hidden'>
            {currentItems.map((sessionReport, i) => (
              <SessionCard
                key={`${sessionReport.id}-${i}`}
                handleEditButtonClick={() =>
                  handleEditButtonClick(sessionReport.session.id, sessionReport.session.pairing?.mom?.id)
                }
                name={getSessionNoteName(sessionReport)}
                date={
                  sessionReport.date_submitted_c
                    ? formatDateFromString((sessionReport.date_submitted_c as Date).toISOString())
                    : 'Not Submitted'
                }
                advocate={getAdvocateNameFromSessionNote(sessionReport)}
              />
            ))}
          </div>

          {/* Desktop View */}
          <div className='mt-6 w-full overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm'>
            <div className='mx-auto max-md:hidden'>
              <div className='overflow-x-auto'>
                <Table className='w-full'>
                  <TableHeader>
                    <TableRow>
                      <TableHead className='md:col-span-3'>Task</TableHead>
                      <TableHead className='md:col-span-3'>Name</TableHead>
                      <TableHead className='md:col-span-3'>Paired Advocate</TableHead>
                      <TableHead className='md:col-span-2'>Date Submitted</TableHead>
                      <TableHead className='text-center md:col-span-1'>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {currentItems.map((sessionReport, i) => {
                      return (
                        <TableRow key={`${sessionReport.id}-${i}`}>
                          <TableCell>Session Notes Review</TableCell>
                          <TableCell className='font-medium'>
                            <div className='flex items-center gap-2'>
                              <MomPhoto momId={sessionReport.session.pairing?.mom?.id || ''} />
                              <div>
                                <p>{getSessionNoteName(sessionReport)}</p>
                                <p className='text-sm font-light text-emaTextTertiary'>Mom</p>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{getAdvocateNameFromSessionNote(sessionReport)}</TableCell>
                          <TableCell>
                            {sessionReport.date_submitted_c
                              ? formatDateFromString((sessionReport.date_submitted_c as Date).toISOString())
                              : 'Not Submitted'}
                          </TableCell>
                          <TableCell className='text-center'>
                            <Button
                              variant='ghost'
                              size='icon'
                              onClick={() =>
                                handleEditButtonClick(sessionReport.session.id, sessionReport.session.pairing?.mom?.id)
                              }
                            >
                              Review
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            </div>
            <div className='rounded bg-background md:p-4'>
              {currentItems.length > 0 ? (
                <TablePaginationFooter
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                />
              ) : (
                <p className='py-4 text-center'>There are currently no sessions to display.</p>
              )}
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default SessionNotesTable;

interface SessionCardProps {
  handleEditButtonClick: () => void;
  name: string | undefined;
  date: string;
  advocate: string | undefined;
}

const SessionCard: React.FC<SessionCardProps> = ({ handleEditButtonClick, name, date, advocate }) => {
  return (
    <div onClick={handleEditButtonClick} className='cursor-pointer rounded-2xl bg-emaBgQuaternary p-4'>
      <div className='mb-2 flex justify-between'>
        <div className='flex items-center gap-2'>
          <span>Mom:</span>
          <span className='font-semibold'>{name}</span>
        </div>
        <div className='flex items-center'>
          <ChevronRight className='text-emaTextQuaternary' />
        </div>
      </div>
      <p className='text-sm font-light text-emaTextTertiary'>Advocate: {advocate}</p>
      <p className='text-sm font-light text-emaTextTertiary'>Date Submitted: {date}</p>
    </div>
  );
};
