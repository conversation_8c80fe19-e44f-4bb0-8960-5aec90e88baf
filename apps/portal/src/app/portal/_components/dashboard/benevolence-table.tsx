import TablePaginationFooter from '@/components/custom/table-pagination-footer';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import renderIconForType from '@/components/utils/renderIconForType';
import { trueFalseValueToYesNoLabel } from '@/lib/utils';
import { formatDateFromString } from '@suiteapi/models';
import { YupSchemas } from '@suiteapi/models';
import { format } from 'date-fns';
import { ChevronRight, CircleUser, CloudDownload } from 'lucide-react';
import Image from 'next/image';
import { useEffect, useState } from 'react';

import BenevolenceNeedDrawer from '../../[role]/dashboard/people/_components/benevolence-need-drawer';
import { MomFlaggedNeeds } from '../../context/mom-flagged-needs-context';
import { useMomProfileContext } from '../../context/mom-profile-context';
import { MomPhoto } from '../ui/mom-photo';

interface BenevolenceTableProps {
  momData: YupSchemas.BenevolenceSchema[] | MomFlaggedNeeds | null;
  toggleColumn: 'status' | 'mom';
  showFilterButtons?: boolean;
  title: string;
  showExportBtn?: boolean;
  subtitle?: string;
  onCloseDrawer?: () => void;
  filterOutMetNeeds?: boolean;
}

const getDateEntered = (dateEntered: string | Date | undefined) => {
  if (!dateEntered) return 'date not available';
  // If dateEntered is a Date object, format it directly
  if (dateEntered instanceof Date) {
    return format(dateEntered, 'MM/dd/yy');
  }
  // Otherwise use the existing formatDateFromString function
  return formatDateFromString(dateEntered);
};

const BenevolenceTable = ({
  momData,
  toggleColumn,
  showFilterButtons = true,
  title,
  showExportBtn = false,
  subtitle,
  onCloseDrawer,
  filterOutMetNeeds = true,
}: BenevolenceTableProps) => {
  const benevolenceData: YupSchemas.BenevolenceSchema[] | MomFlaggedNeeds | null = momData;
  const [currentPage, setCurrentPage] = useState(1);
  const [filter, setFilter] = useState<'all' | 'urgent' | 'oldest'>('all');
  const itemsPerPage = 6;
  const [selectedEntry, setSelectedEntry] = useState<YupSchemas.BenevolenceSchema | null>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (momData) {
      setIsLoading(false);
    }
  }, [momData]);

  // getting indexes
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;

  const getFilteredAndSortedData = (): YupSchemas.BenevolenceSchema[] => {
    // Ensure we have data to work with; if null, return an empty array
    let data: YupSchemas.BenevolenceSchema[] = benevolenceData ? [...benevolenceData] : [];

    // Only filter out completed entries if filterOutMetNeeds is true
    if (filterOutMetNeeds) {
      data = data.filter((entry) => !entry.resolved_date_c);
    }

    // Filter by 'urgent' if selected
    if (filter === 'urgent') {
      data = data.filter((entry) => entry.is_urgent_c === true);
    }

    // Sort by 'oldest' if selected
    if (filter === 'oldest') {
      data = data.sort((a, b) => {
        const dateA = a.created_at ? new Date(a.created_at).getTime() : Infinity;
        const dateB = b.created_at ? new Date(b.created_at).getTime() : Infinity;
        return dateA - dateB;
      });
    }

    // Always sort resolved needs to the bottom
    data.sort((a, b) => {
      if (a.resolved_date_c && !b.resolved_date_c) return 1;
      if (!a.resolved_date_c && b.resolved_date_c) return -1;
      return 0;
    });

    return data;
  };

  const filteredData = getFilteredAndSortedData();

  // Setting what items to display
  const currentItems = filteredData.slice(indexOfFirstItem, indexOfLastItem);

  // Total pages for pagination
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);

  // Handling the page change
  const handlePageChange = (pageNumber: number): void => {
    setCurrentPage(pageNumber);
  };

  // Setting the filter setting
  const handleFilterChange = (type: 'all' | 'urgent' | 'oldest') => {
    setFilter(type);
    setCurrentPage(1);
  };

  const handleManage = (entry: YupSchemas.BenevolenceSchema) => {
    setSelectedEntry(entry);
    setIsDrawerOpen(true);
  };

  const handleCloseDrawer = (): void => {
    setIsDrawerOpen(false);

    if (onCloseDrawer) {
      onCloseDrawer();
    }
  };

  const handleExportData = () => {
    if (!benevolenceData || benevolenceData.length === 0) {
      alert('No data to export');
      return;
    }

    const csvHeader = 'ID,Name,Type,Urgent,Date Requested,Context\n';
    const csvRows = benevolenceData.map((entry) => {
      // Check if ema_benevolence_contacts_name exists on the entry... this is an issue with the first set of data and should be able to be removed

      const name = 'ema_benevolence_contacts_name' in entry ? entry.ema_benevolence_contacts_name : '';
      // Ensure it's a string or provide a fallback... this is an issue with the first set of data and should be able to be removed when real data is in
      const created_at = getDateEntered(entry.created_at);

      return `${entry.id},${name},${entry.type_c},${trueFalseValueToYesNoLabel(entry.is_urgent_c ?? undefined)},${created_at},${entry.description}`;
    });

    const csvContent = csvHeader + csvRows.join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'benevolence_data.csv';
    link.click();
  };

  return (
    <section className='hi mt-6 w-full overflow-hidden border-gray-200 md:rounded-xl md:border md:bg-white md:shadow-sm'>
      <div className='flex flex-row items-center justify-between p-6'>
        <div>
          <h2 className='text-lg font-semibold tracking-tight text-zinc-950'>{title}</h2>
          {subtitle ? <p className='font-light'>{subtitle}</p> : null}
        </div>
        {currentItems.length <= 0 ? null : (
          <>
            {showExportBtn ? (
              <Button onClick={handleExportData} variant={'outline'}>
                <CloudDownload className='mr-2' />
                Export All
              </Button>
            ) : null}
            {showFilterButtons ? (
              <div>
                <Button
                  variant={'outline'}
                  className={`rounded-br-none rounded-tr-none border-r-0 ${filter === 'all' ? 'bg-accent' : ''}`}
                  onClick={() => {
                    handleFilterChange('all');
                  }}
                >
                  Show All
                </Button>
                <Button
                  variant={'outline'}
                  className={`rounded-none border-r-0 ${filter === 'urgent' ? 'bg-accent' : ''}`}
                  onClick={() => {
                    handleFilterChange('urgent');
                  }}
                >
                  Urgent
                </Button>
                <Button
                  variant={'outline'}
                  className={`rounded-bl-none rounded-tl-none ${filter === 'oldest' ? 'bg-accent' : ''}`}
                  onClick={() => {
                    handleFilterChange('oldest');
                  }}
                >
                  Oldest
                </Button>
              </div>
            ) : null}
          </>
        )}
      </div>
      {isLoading ? (
        <div className='w-full px-4 pb-8 text-center'>Loading...</div>
      ) : currentItems.length <= 0 ? (
        <div className='w-full px-4 pb-8 text-center'>There are currently no flagged needs.</div>
      ) : (
        <>
          <div className='mb-4 flex flex-col gap-3 md:hidden'>
            {currentItems.map((entry, i) => (
              <BenevolenceEntryCard
                key={`benevolence-need-card-${i}`}
                entry={entry}
                handleManageClick={() => handleManage(entry)}
                toggleColumn={toggleColumn}
              />
            ))}
          </div>
          <Table className='hidden md:block'>
            <TableHeader>
              <TableRow
                className={`grid grid-cols-12 py-3 pl-6 ${toggleColumn === 'mom' ? 'grid-cols-[2.5fr_2.5fr_2fr_1fr_3fr_1fr]' : 'grid-cols-[2.5fr_1fr_2fr_1fr_3.5fr_1fr]'}`}
              >
                {toggleColumn === 'mom' ? <TableHead className='flex h-full items-center'>Mom</TableHead> : null}
                <TableHead className='flex h-full items-center'>Need Type</TableHead>
                <TableHead className='flex h-full items-center'>Requested</TableHead>
                {toggleColumn === 'status' ? <TableHead className='flex h-full items-center'>Status</TableHead> : null}
                <TableHead className='flex h-full items-center'>Urgent</TableHead>
                <TableHead className='flex h-full items-center'>Context for Need</TableHead>
                <TableHead className='flex h-full items-center'>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentItems.map((entry) => (
                <TableRow
                  key={entry.id}
                  className={`grid cursor-pointer grid-cols-12 py-3 pl-6 hover:bg-gray-50 ${toggleColumn === 'mom' ? 'grid-cols-[2.5fr_2.5fr_2fr_1fr_3fr_1fr]' : 'grid-cols-[2.5fr_1fr_2fr_1fr_3.5fr_1fr]'}`}
                >
                  {toggleColumn === 'mom' ? (
                    <TableCell>
                      <div className='flex items-center'>
                        <MomPhoto momId={entry.id || ''} />
                        <div className='pl-2'>{entry.ema_benevolence_contacts_name}</div>
                      </div>
                    </TableCell>
                  ) : null}
                  <TableCell className='font-medium'>
                    <div className='flex items-center gap-2'>
                      {renderIconForType(entry?.type_c ?? '')} {entry?.type_c}
                    </div>
                  </TableCell>

                  <TableCell>{getDateEntered(entry.created_at)}</TableCell>
                  {toggleColumn === 'status' ? (
                    <TableCell>{entry.resolved_date_c ? <ResolvedTag /> : <FlaggedTag />}</TableCell>
                  ) : null}
                  <TableCell>{trueFalseValueToYesNoLabel(entry.is_urgent_c ?? undefined)}</TableCell>
                  <TableCell>{entry.description}</TableCell>

                  <TableCell
                    onClick={() => {
                      handleManage(entry);
                    }}
                    className='capitalize hover:cursor-pointer'
                  >
                    Manage
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          {currentItems.length > 0 ? (
            <div className='rounded bg-background md:p-4'>
              <TablePaginationFooter
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          ) : null}
          <BenevolenceNeedDrawer isOpen={isDrawerOpen} onClose={handleCloseDrawer} entry={selectedEntry} />
        </>
      )}
    </section>
  );
};

const ResolvedTag = () => <div className='w-fit rounded-xl border bg-gray-50 px-2 text-sm'>Resolved</div>;

const FlaggedTag = () => (
  <div className='w-fit rounded-xl border border-red-200 bg-red-50 px-2 text-sm text-red-700'>Flagged Need</div>
);

interface BenevolenceEntryProps {
  entry: YupSchemas.BenevolenceSchema;
  handleManageClick: () => void;
  toggleColumn: 'mom' | 'status';
  profileImage?: string;
}

const BenevolenceEntryCard: React.FC<BenevolenceEntryProps> = ({
  entry,
  handleManageClick,
  toggleColumn,
  profileImage = '', // not implemented in API yet
}) => {
  const { type_c, is_urgent_c, created_at, resolved_date_c, description } = entry;
  const { momProfile } = useMomProfileContext();
  return (
    <div
      onClick={() => {
        handleManageClick();
      }}
      className='cursor-pointer rounded-2xl bg-emaBgQuaternary p-4'
    >
      <div className='mb-2 flex w-full flex-row justify-between'>
        <div className='flex flex-col'>
          {toggleColumn === 'mom' ? (
            <div className='mb-2 flex h-full items-center pr-1'>
              {profileImage ? (
                <Image
                  src={profileImage}
                  alt={`${momProfile?.first_name} ${momProfile?.last_name}`}
                  width={32}
                  height={32}
                  className='mr-2 h-[32px] w-[32px] rounded-full px-1'
                />
              ) : (
                <CircleUser className='mr-2' />
              )}
              <p className='text-lg font-semibold'>{`${momProfile?.first_name} ${momProfile?.last_name}`}</p>
            </div>
          ) : null}
          {toggleColumn === 'status' ? (
            <div className='mb-3'>{resolved_date_c ? <ResolvedTag /> : <FlaggedTag />}</div>
          ) : null}
          <div className='mb-2 flex items-center'>
            <span>{renderIconForType(type_c ?? '')}</span>
            <span className='text-md ml-2'>{type_c}</span>
          </div>
          <p>Urgent: {trueFalseValueToYesNoLabel(is_urgent_c ?? undefined)}</p>
        </div>
        <div className='flex flex-row'>
          <p className='text-emaTextQuaternary'>{getDateEntered(created_at)}</p>
          <ChevronRight className='text-emaTextQuaternary' />
        </div>
      </div>
      <div className='mb-2 mt-2 line-clamp-2'>{description}</div>
    </div>
  );
};

export default BenevolenceTable;
