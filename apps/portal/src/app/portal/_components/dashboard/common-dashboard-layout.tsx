'use client';

import { useUserInfo } from '@/context/user-info';
import { PORTAL_ROLE_PARAMS_MAP } from '@/lib/constants';
import { notFound } from 'next/navigation';

import type { PortalRole } from '../../types';
import NavHeader from '../navigation/nav-header';
import NavSidebar from '../navigation/nav-sidebar';

interface CommonDashboardLayoutProps {
  roleParam: string;
  children: React.ReactNode;
}

export default function CommonDashboardLayout({ roleParam, children }: CommonDashboardLayoutProps): React.ReactNode {
  const { isInitialized, roles } = useUserInfo();
  const role = PORTAL_ROLE_PARAMS_MAP[roleParam];

  if (isInitialized && (!role || !roles.includes(role as PortalRole))) {
    // Redirect to 404 page if the role param is not a valid role, or the user does not have the role assigned
    return notFound();
  }

  return (
    <div className='mx-auto flex min-h-screen w-full flex-col lg:flex-row'>
      <NavSidebar portalRole={roleParam} mode='desktop' />
      <div className='flex h-screen min-w-0 flex-grow flex-col overflow-y-auto'>
        <NavHeader portalRole={roleParam} />
        <main className='mt-14 flex flex-1 flex-col gap-4 p-4 max-sm:p-2 lg:mt-0 lg:gap-6 lg:p-6'>{children}</main>
      </div>
    </div>
  );
}
