import { Button } from '@/components/ui/button';
import { useUserInfo } from '@/context/user-info';
import { useFindManyBenevolenceNeed } from '@/hooks/generated/benevolence-need';
import { usePathname, useRouter } from 'next/navigation';

import MomScheduleSessionModal from '../../[role]/dashboard/people/_components/mom-schedule-session-modal';
import { useDashboardContext } from '../../context/dashboard-context';
import MyPairings from '../advocate/my-pairings';
import ActionItemsSection from './action-items-section';
import BenevolenceTable from './benevolence-table';
import DashboardHeader from './dashboard-header';

interface LandingPageCoordinatorSupervisorProps {
  primaryRole: 'coordinator' | 'supervisor'; // from URL path params
}

const LandingPageCoordinatorSupervisor: React.FC<LandingPageCoordinatorSupervisorProps> = ({ primaryRole }) => {
  const { profile } = useDashboardContext();
  const isSupervisor = primaryRole === 'supervisor';
  const isSuperAdvocate = primaryRole === 'coordinator' && profile?.roles?.includes('advocate');
  const { roles } = useUserInfo();
  const isAdmin = roles.includes('administrator');
  const router = useRouter();
  const pathname = usePathname();
  // Fetch all benevolence needs
  const { data: benevolenceNeeds, refetch: refetchBenevolenceNeeds } = useFindManyBenevolenceNeed({
    include: {
      mom: true,
    },
  });

  const handleDrawerClose = () => {
    refetchBenevolenceNeeds();
  };

  return (
    <>
      <DashboardHeader
        headerText='Get the latest updates on your moms, session reports, and upcoming tasks.'
        actionsComponent={
          <>
            <MomScheduleSessionModal buttonComponent={<Button variant='outline'>Schedule Session</Button>} />
            {isAdmin ? (
              <Button variant='secondary' onClick={() => router.push(`${pathname}/admin`)}>
                Manage Users
              </Button>
            ) : null}
          </>
        }
      />

      <ActionItemsSection />

      {/* "Vital Support" section (not shown to supervisors) */}
      {isSupervisor ? null : (
        <BenevolenceTable
          subtitle={'Here are the latest needs. Click on the column header to sort by date.'}
          momData={benevolenceNeeds?.map((bn) => ({ ...bn, created_at: bn.created_at?.toISOString() })) ?? []}
          toggleColumn={'mom'}
          title={'Current Needs for Your Moms'}
          onCloseDrawer={handleDrawerClose}
        />
      )}

      {/* "My Pairings" section (only shown to super-advocates) */}
      {isSuperAdvocate ? <MyPairings /> : null}
    </>
  );
};

export default LandingPageCoordinatorSupervisor;
