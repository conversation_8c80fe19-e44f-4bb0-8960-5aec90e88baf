import GenericTable from '@/components/custom/generic-table';
import { Button } from '@/components/ui/button';
import { TableCell } from '@/components/ui/table';
import { User as UserType } from '@/types/schemas/user';
import { format } from 'date-fns';
import { ChevronRight, User } from 'lucide-react';
import { useState } from 'react';

import ActionItemsNewAdvocatesDrawer from './action-items-new-advocates-drawer';

const ActionItemsNewAdvocatesTable = ({
  data,
  isLoading,
  onDataRefresh,
}: {
  data: UserType[];
  isLoading: boolean;
  onDataRefresh: () => void;
}) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedEntry, setSelectedEntry] = useState<UserType | null>(null);

  const handleOpenDrawer = (entry: UserType) => {
    setSelectedEntry(entry);
    setIsDrawerOpen(true);
  };

  const handleCloseDrawer = () => {
    setIsDrawerOpen(false);
  };

  return (
    <>
      <GenericTable
        data={data}
        isLoading={isLoading}
        headers={['Task', 'Name', 'Affiliate', 'Paired Advocate', 'Date Submitted', 'Actions']}
        rowRenderer={(item) => (
          <>
            <TableCell>Advocate Interest Form</TableCell>
            <TableCell className='font-medium'>
              <div className='flex items-center gap-2'>
                <div className='rounded-full border border-emaBorderSecondary bg-offGrey p-2'>
                  <User className='h-7 w-7 text-emaTextTertiary' />
                </div>
                <div>
                  <p>{`${item.firstName || ''} ${item.lastName || ''}`}</p>
                  <p className='text-sm font-light text-emaTextTertiary'>Advocate</p>
                </div>
              </div>
            </TableCell>
            <TableCell>{item.affiliate?.name || 'N/A'}</TableCell>
            <TableCell>N/A</TableCell>
            <TableCell>{item.created_at ? format(new Date(item.created_at), 'MM/dd/yy') : ''}</TableCell>
            <TableCell>
              <Button onClick={() => handleOpenDrawer(item)} variant='outline' size='sm' className='border-none'>
                Review
              </Button>
            </TableCell>
          </>
        )}
        mobileRenderer={(item) => (
          <div onClick={() => handleOpenDrawer(item)} className='cursor-pointer rounded-2xl bg-emaBgQuaternary p-4'>
            <div className='mb-2 flex justify-between'>
              <div className=''>
                <span className='block'>Advocate:</span>
                <span className='block font-semibold'>{`${item.firstName || ''} ${item.lastName || ''}`}</span>
              </div>
              <div className='flex items-center'>
                <p>Review</p>
                <ChevronRight className='text-emaTextQuaternary' />
              </div>
            </div>
            <p className='text-sm font-light text-emaTextTertiary'>Task: Advocate Interest Form</p>
            <p className='text-sm font-light text-emaTextTertiary'>Affiliate: {item.affiliate?.name || 'N/A'}</p>
            <p className='text-sm font-light text-emaTextTertiary'>
              Date Submitted: {item.created_at ? format(new Date(item.created_at), 'MM/dd/yy') : ''}
            </p>
          </div>
        )}
      />
      <ActionItemsNewAdvocatesDrawer
        onDataRefresh={onDataRefresh}
        isOpen={isDrawerOpen}
        onClose={handleCloseDrawer}
        entry={selectedEntry}
      />
    </>
  );
};

export default ActionItemsNewAdvocatesTable;