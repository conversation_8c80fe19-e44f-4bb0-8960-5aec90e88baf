import { Button } from '@/components/ui/button';
import { TableCell } from '@/components/ui/table';
import { formatDateFromString } from '@suiteapi/models';
import { ChevronRight } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';
import { useEffect, useState } from 'react';

import GenericTable from '../../../../../components/custom/generic-table';
import { MomPhoto } from '../../ui/mom-photo';
import ActionItemsNewMomReferralDrawer from './action-items-new-mom-referral-drawer';

// Type to handle both MomType and enhanced properties
export type EnhancedMomType = {
  id?: string;
  name?: string | null;
  first_name?: string | null;
  last_name?: string | null;
  date_entered?: string | null;
  table_label: string;
  table_actions_label: string;
  table_actions_path?: string;
  [key: string]: unknown;
};

const ActionItemsNewMomsTable = ({
  data,
  isLoading,
  onDataRefresh,
}: {
  data: EnhancedMomType[];
  isLoading: boolean;
  onDataRefresh: () => void;
}) => {
  const router = useRouter();
  const [isReferralDrawerOpen, setIsReferralDrawerOpen] = useState(false);
  const [selectedEntry, setSelectedEntry] = useState<EnhancedMomType | null>(null);

  const handleReviewReferral = (item: EnhancedMomType) => {
    setSelectedEntry(item);
    setIsReferralDrawerOpen(true);
  };

  const handleActionButtonClick = (item: EnhancedMomType) => {
    if (item.table_actions_label === 'Review') {
      handleReviewReferral(item);
    } else if (
      item.table_actions_label === 'Assign' ||
      item.table_actions_label === 'Start Intake' ||
      item.table_actions_label === 'View Intake'
    ) {
      router.push(item.table_actions_path || '');
    }
  };

  const onDenyReferralSuccess = () => {
    onDataRefresh();
    setIsReferralDrawerOpen(false);
  };

  // this is called in referral drawer > assign coordinator modal > assign coordinator
  const handleRefreshTable = async () => {
    await onDataRefresh();
  };

  // ensures the selected entry stays updated to prevent stale data in the drawer.
  useEffect(() => {
    if (isReferralDrawerOpen && selectedEntry) {
      const updatedEntry = data.find((item) => item.id === selectedEntry?.id);
      if (updatedEntry) {
        setSelectedEntry(updatedEntry);
      }
    }
  }, [data, isReferralDrawerOpen, selectedEntry]);

  return (
    <>
      <GenericTable
        data={data}
        isLoading={isLoading}
        headers={['Task', 'Name', 'Paired Advocate', 'Date Submitted', 'Actions']}
        rowRenderer={(item) => (
          <>
            <TableCell>{item.table_label}</TableCell>
            <TableCell className='font-medium'>
              <div className='flex items-center gap-2'>
                <MomPhoto momId={item.id || ''} />
                <div>
                  <p>{item.name ?? `${item.first_name} ${item.last_name}`}</p>
                  <p className='text-sm font-light text-emaTextTertiary'>Mom</p>
                </div>
              </div>
            </TableCell>
            <TableCell>N/A</TableCell>
            <TableCell>{item.date_entered ? formatDateFromString(item.date_entered) : ''}</TableCell>
            <TableCell className='flex flex-col gap-2'>
              <Button onClick={() => handleActionButtonClick(item)} variant='outline' size='sm'>
                {item.table_actions_label}
              </Button>
              {/* TODO:https://servant-io.atlassian.net/browse/EMA-698 */}
              {item.table_actions_label === 'Start Intake' || item.table_actions_label === 'View Intake' ? (
                <Button
                  variant='outline'
                  onClick={() => {
                    handleReviewReferral(item);
                  }}
                >
                  Review
                </Button>
              ) : null}
            </TableCell>
          </>
        )}
        mobileRenderer={(item) => (
          <div
            onClick={() => router.push(item.table_actions_path || '')}
            className='cursor-pointer rounded-2xl bg-emaBgQuaternary p-4'
          >
            <div className='mb-2 flex justify-between'>
              <div className='flex items-center gap-2'>
                <span>Mom:</span>
                <span className='font-semibold'>{item.name ?? `${item.first_name} ${item.last_name}`}</span>
              </div>
              <div
                onClick={(e) => {
                  e.stopPropagation(); // This prevents the click event from bubbling up to the parent div, which would trigger the router navigation
                  handleActionButtonClick(item);
                }}
                className='flex items-center'
              >
                <p>{item.table_actions_label}</p>
                <ChevronRight className='text-emaTextQuaternary' />
              </div>
            </div>
            <p className='text-sm font-light text-emaTextTertiary'>Task: {item.table_label}</p>
            <p className='text-sm font-light text-emaTextTertiary'>
              Date Submitted: {item.date_entered ? formatDateFromString(item.date_entered) : ''}
            </p>
          </div>
        )}
        emptyMessage='There are currently no moms to display.'
        loadingMessage='Loading moms...'
      />
      <ActionItemsNewMomReferralDrawer
        onScheduleIntakeSuccess={onDataRefresh}
        onDenyReferral={onDenyReferralSuccess}
        isOpen={isReferralDrawerOpen}
        onClose={() => setIsReferralDrawerOpen(false)}
        entry={selectedEntry}
        refreshTable={handleRefreshTable}
      />
    </>
  );
};

export default ActionItemsNewMomsTable;
