'use client';

import { REFERRALS_MODULE_NAME } from '@/app/api/_lib/mappedModuleNameConstants';
import { handleDownloadCalendarFile } from '@/app/portal/[role]/dashboard/people/_utils/session-utils';
import { intakeScheduleFormFields, scheduleIntakeFormSchema } from '@/app/portal/lib/schedule-intake-form.config';
import { updateReferralStatusSchema } from '@/app/portal/lib/updateReferralStatus.config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { useGenericModules } from '@/hooks/useGenericModules';
import { createReferralMeeting } from '@/lib/portal';
import type { FormValues } from '@/types/form-field';
import { SessionType } from '@/types/schemas/session';
import { YupSchemas } from '@suiteapi/models';
import { CalendarPlus } from 'lucide-react';
import React, { useState } from 'react';

import { EnhancedMomType } from './action-items-new-moms-table';
import ScheduleIntakeSuccess from './schedule-intake-success';

interface ScheduleIntakeModalProps {
  open?: boolean;
  onClose?: () => void;
  successCallback?: () => void;
  entry?: EnhancedMomType | null;
}

const ScheduleIntakeModal: React.FC<ScheduleIntakeModalProps> = ({ open, onClose, successCallback, entry }) => {
  const externallyControlledOpenState = typeof open === 'boolean' && typeof onClose === 'function';
  const externalOpenProps = externallyControlledOpenState ? { open } : {};
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [scheduledSessionData, setScheduledSessionData] = useState<YupSchemas.ReferralSessionDataType | null>(null);
  const [scheduledWithMomId, setScheduledWithMomId] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const { patch } = useGenericModules(REFERRALS_MODULE_NAME, updateReferralStatusSchema);

  const handleCloseAndClearForm = (): void => {
    if (isSuccess && successCallback) {
      // used to trigger refresh of content that relates to session data
      successCallback();
    }

    if (externallyControlledOpenState) {
      onClose?.();
    }
  };

  const onScheduleSessionSuccess = (
    sessionResponse: { data: YupSchemas.ReferralSessionDataType },
    momId: string | undefined,
  ): void => {
    setScheduledWithMomId(momId!);
    setScheduledSessionData(sessionResponse.data);
    setIsSuccess(true);
  };

  const handleSubmit = async (data: FormValues): Promise<void> => {
    if (!entry?.id || !entry?.full_name) {
      console.error('Entry is undefined or missing required fields.');
      setErrorMessage('Entry information is missing.');
      return;
    }

    setIsSubmitting(true);

    // Calculate date end
    const dateEnd =
      typeof data.date_start === 'string' && data.end_time
        ? `${data.date_start.split('T')[0]}T${data.end_time}`
        : undefined;

    //  data for session API
    const dataToSendToSessionAPI = {
      referral_id: entry.id,
      full_name: `${entry.first_name || ''} ${entry.last_name || ''}`.trim() || 'Unknown Name',
      session_type: SessionType.Referral_Session,
      date_end: dateEnd || '',
      date_start: (data.date_start || '') as string,
      start_time: (data.start_time || '') as string,
      end_time: (data.end_time || '') as string,
      meeting_type: String(data.meeting_type || ''),
      location: (data.location || '') as string,
      join_url: (data.join_url || '') as string,
    };

    setScheduledSessionData(dataToSendToSessionAPI as YupSchemas.ReferralSessionDataType);

    try {
      // Call referral meeting creation API
      const response = await createReferralMeeting(dataToSendToSessionAPI);
      onScheduleSessionSuccess(response, entry.id);

      // Patch the status change
      await patch(entry.id as string, { status: 'prospect_intake_scheduled' });
      toast({
        title: 'Success',
        description: 'The intake meeting has been successfully scheduled',
        variant: 'success',
      });
    } catch (error) {
      console.error('Error creating referral meeting:', error);
      toast({
        title: 'Error',
        description: 'There was an error scheduling the intake meeting. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog onOpenChange={handleCloseAndClearForm} {...externalOpenProps}>
      <DialogTrigger asChild>
        <Button>
          <CalendarPlus className='mr-2' size={16} />
          Schedule Intake
        </Button>
      </DialogTrigger>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle>{isSuccess ? 'Intake Scheduled!' : 'Schedule Intake'}</DialogTitle>
        </DialogHeader>
        {!isSuccess && (
          <div className='space-y-4'>
            <FormFactory
              fields={intakeScheduleFormFields}
              schema={scheduleIntakeFormSchema}
              onSubmit={handleSubmit}
              actionButtonsComponent={
                <DialogFooter className='mt-4 sm:justify-end'>
                  <DialogClose asChild>
                    <Button type='button' variant='secondary'>
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button type='submit' variant='default' disabled={isSubmitting}>
                    {isSubmitting ? 'Saving...' : 'Save Changes'}
                  </Button>
                </DialogFooter>
              }
            />
            {errorMessage ? <div className='w-full text-center text-destructive'>{errorMessage}</div> : null}
          </div>
        )}
        {isSuccess && scheduledWithMomId && scheduledSessionData ? (
          <>
            <ScheduleIntakeSuccess sessionData={scheduledSessionData} />

            <DialogFooter className='mt-4 sm:justify-end'>
              <DialogClose asChild>
                <Button type='button' variant='secondary'>
                  Done
                </Button>
              </DialogClose>
              <Button
                type='button'
                onClick={() =>
                  handleDownloadCalendarFile(
                    scheduledSessionData,
                    `${entry?.first_name || ''} ${entry?.last_name || ''}`.trim() || 'Unknown Name',
                    'Intake Session with',
                  )
                }
              >
                Add to Calendar
              </Button>
            </DialogFooter>
          </>
        ) : null}
      </DialogContent>
    </Dialog>
  );
};

export default ScheduleIntakeModal;
