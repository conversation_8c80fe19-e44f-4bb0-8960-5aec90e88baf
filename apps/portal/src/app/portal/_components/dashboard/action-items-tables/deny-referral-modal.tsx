import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { denyReferral<PERSON>ormFields, denyReferralFormSchema } from '@/app/portal/lib/deny-referral.config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useUpdateMom } from '@/hooks/generated';
import { ReferralSubStatus } from '@/hooks/generated/__types';
import { useToast } from '@/hooks/use-toast';
import { FormValues } from '@/types/form-field';
import { useState } from 'react';

import { EnhancedMomType } from './action-items-new-moms-table';

const DenyReferralModal = ({
  entry,
  successCallback,
}: {
  entry: EnhancedMomType | null;
  successCallback: () => void;
}) => {
  const { toast } = useToast();
  const { mutateAsync: updateMom } = useUpdateMom();
  const { fetchMomProfile } = useMomProfileContext();
  const [isOpen, setIsOpen] = useState<boolean>(false);

  if (!entry) return null;

  const handleSubmit = async (formData: FormValues) => {
    try {
      await updateMom({
        data: {
          prospect_status: 'did_not_engage_in_program',
          referral_sub_status: formData.referral_sub_status as ReferralSubStatus,
        },
        where: { id: entry.id as string },
      });
      successCallback();
      fetchMomProfile(entry.id as string);
      toast({
        title: 'Success',
        description: 'The referral has been successfully denied',
        variant: 'success',
      });
      setIsOpen(false);
    } catch (error) {
      console.error('Error denying referral:', error);
      toast({
        title: 'Error',
        description: 'There was an error denying the referral. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant='outline'>Deny</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Deny Referral</DialogTitle>
          <DialogDescription>Please choose the reason for denying the referral.</DialogDescription>
        </DialogHeader>
        <FormFactory
          fields={denyReferralFormFields}
          schema={denyReferralFormSchema}
          onSubmit={handleSubmit}
          actionButtonsComponent={
            <DialogFooter>
              <DialogClose asChild>
                <Button variant='outline'>Cancel</Button>
              </DialogClose>
              <Button type='submit' className='bg-darkGreen text-white'>
                Submit
              </Button>
            </DialogFooter>
          }
        />
      </DialogContent>
    </Dialog>
  );
};

export default DenyReferralModal;
