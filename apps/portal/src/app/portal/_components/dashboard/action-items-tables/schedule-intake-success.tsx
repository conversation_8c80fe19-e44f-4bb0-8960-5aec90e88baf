import { formatDateFromDateStartAndEndTime } from '@suiteapi/models';
import { YupSchemas } from '@suiteapi/models';
import { Calendar, MapPin, MessageCircleMore, User, Video } from 'lucide-react';
import React from 'react';

const ScheduleIntakeSuccess = ({ sessionData }: { sessionData: YupSchemas.ReferralSessionDataType }) => {
  const { full_name, date_start, start_time, end_time, meeting_type, location, join_url } = sessionData;

  const formattedTime = formatDateFromDateStartAndEndTime({
    date_start: String(date_start),
    start_time: String(start_time),
    end_time: String(end_time),
  });
  return (
    <div className='flex flex-col gap-4'>
      {/* Mom Info */}
      <div className='flex gap-2'>
        <User className='h-6 w-6 text-emaTextQuaternary' />
        <div>
          <div className='text-emaTextQuaternary'>Mom</div>
          <div>{full_name}</div>
        </div>
      </div>
      {/* Meeting Info */}
      <div className='flex gap-2'>
        <MessageCircleMore className='h-6 w-6 text-emaTextQuaternary' />
        <div>
          <div className='text-emaTextQuaternary'>Meeting Type</div>
          <div>{meeting_type === 'in_person' ? 'In Person' : 'Virtual (Online)'}</div>
        </div>
      </div>
      {/* Date */}
      <div className='flex gap-2'>
        <Calendar className='h-6 w-6 text-emaTextQuaternary' />
        <div>
          <div className='text-emaTextQuaternary'>Date</div>
          <div>{formattedTime}</div>
        </div>
      </div>
      {/* Link or Location */}
      <div className='flex gap-2'>
        {location ? (
          <>
            <MapPin className='h-6 w-6 text-emaTextQuaternary' />
            <div>
              <div className='text-emaTextQuaternary'>Location</div>
              <div>{location}</div>
            </div>
          </>
        ) : null}
        {join_url ? (
          <>
            <Video className='h-6 w-6 text-emaTextQuaternary' />
            <div>
              <div className='text-emaTextQuaternary'>Link</div>
              <div>{join_url as string}</div>
            </div>
          </>
        ) : null}
      </div>
    </div>
  );
};

export default ScheduleIntakeSuccess;
