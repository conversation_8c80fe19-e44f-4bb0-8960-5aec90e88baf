import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useUpdateMom } from '@/hooks/generated';
import { toast } from '@/hooks/use-toast';
import { YupSchemas } from '@suiteapi/models';
import { useState } from 'react';

import { EnhancedMomType } from './action-items-new-moms-table';

const ApproveMom = ({ referral }: { referral: EnhancedMomType | null }) => {
  const [isOpen, setIsOpen] = useState(false);
  const updateMom = useUpdateMom();

  const handleApproveMom = async () => {
    if (!referral) return;

    await updateMom.mutateAsync(
      {
        where: { id: referral.id },
        data: {
          status: YupSchemas.MomStatus.ACTIVE,
          prospect_status: YupSchemas.ProspectStatus.ENGAGED_IN_PROGRAM,
        },
      },
      {
        onSuccess: () => {
          toast({
            title: 'Success',
            description: 'The mom has been successfully approved for the program',
            variant: 'success',
          });
          setIsOpen(false);
        },
        onError: () => {
          toast({
            title: 'Error',
            description: 'There was an error approving the mom for the program. Please try again.',
            variant: 'destructive',
          });
        },
      },
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button>Approve Mom for Program</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Approve Mom for Program</DialogTitle>
          <DialogDescription>Are you sure you want to approve this mom for the program?</DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant='secondary' onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleApproveMom}>Approve</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ApproveMom;
