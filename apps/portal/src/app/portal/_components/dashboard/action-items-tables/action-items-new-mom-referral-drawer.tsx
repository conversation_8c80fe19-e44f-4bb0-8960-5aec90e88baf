'use client';

import ResponsiveDrawer from '@/components/custom/responsive-drawer';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { useCoordinators } from '@/hooks/useCoordinators';
import { downloadDocument, getReferralDocuments } from '@/lib/portal';
import { YupSchemas } from '@suiteapi/models';
import { DownloadCloud } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import React from 'react';

import { EnhancedMomType } from './action-items-new-moms-table';
import ApproveMom from './approve-mom';
import AssignCoordinatorModal from './assign-coordinator-modal';
import DenyReferralModal from './deny-referral-modal';
import ReferOutMomModal from './refer-out-mom-modal';
import ScheduleIntakeModal from './schedule-intake-modal';

const ActionItemsNewMomReferralDrawer = ({
  isOpen,
  onClose,
  entry,
  onDenyReferral,
  onScheduleIntakeSuccess,
  refreshTable,
}: {
  isOpen: boolean;
  onClose: () => void;
  entry: EnhancedMomType | null;
  onDenyReferral: () => void;
  onScheduleIntakeSuccess: () => void;
  refreshTable: () => void;
}) => {
  const [documents, setDocuments] = useState<YupSchemas.DocumentSchema[]>([]);
  const { coordinators } = useCoordinators();

  const onDenyReferralSuccess = () => {
    onDenyReferral();
  };
  const router = useRouter();

  useEffect(() => {
    if (isOpen && entry?.id) {
      (async () => {
        try {
          const docResponse = await getReferralDocuments(entry.id!);
          setDocuments(docResponse as YupSchemas.DocumentSchema[]);
        } catch (err) {
          console.error('Document fetch error:', err);
        }
      })();
    }
  }, [isOpen, entry?.id]);

  const handleDownload = async (documentId: string) => {
    try {
      const filename = documents.find((doc) => doc.id === documentId)?.filename || 'document';
      await downloadDocument(documentId, filename);
    } catch (error) {
      console.error('Error downloading file:', error);
      toast({
        title: 'Error downloading file',
        description: `${error}`,
        variant: 'destructive',
      });
    }
  };

  const fields = [
    { label: 'Submitted', value: entry?.date_entered ? new Date(entry.date_entered).toLocaleDateString() : 'N/A' },
    {
      label: 'Assigned Coordinator',
      value: entry?.assigned_user_id ? (
        coordinators?.find((c) => c.id === entry.assigned_user_id)?.firstName +
        ' ' +
        coordinators?.find((c) => c.id === entry.assigned_user_id)?.lastName
      ) : (
        <AssignCoordinatorModal referral={entry} onUpdate={refreshTable} />
      ),
    },
    { label: 'Referral Type', value: entry?.referral_type_c === 'self' ? 'Self' : 'Community' },
    { label: 'Agency Details', value: entry?.account_name || 'N/A' },
    { label: 'Submitted By', value: entry?.created_by_name || 'N/A' },
    { label: 'Mom', value: `${entry?.first_name || ''} ${entry?.last_name || ''}` },
    { label: 'Email', value: entry?.email1 || 'N/A' },
    { label: 'Phone Number', value: entry?.phone_other || 'N/A' },
    { label: 'Preferred Contact Method', value: entry?.preferred_contact_method_c || 'N/A' },
    { label: 'Pregnancy Status', value: entry?.currently_pregnant_c || 'N/A' },
    { label: 'Number of Children', value: entry?.number_of_children_c || 'N/A' },
    {
      label: 'Is this referral to support a court-ordered requirement?',
      value: entry?.supports_court_order_c === true ? 'Yes' : 'No',
    },
    { label: 'Service Selected', value: entry?.service_selected_c || 'N/A' },
    { label: 'Other Needs', value: entry?.need_details_c || 'None specified' },
    {
      label: 'Files',
      value:
        documents && documents.length > 0 ? (
          <ul className='space-y-2'>
            {documents.map((doc: YupSchemas.DocumentSchema, index: number) => (
              <li key={doc.id!} className='ml-4 flex flex-col items-end'>
                <button
                  className='flex items-center hover:underline'
                  onClick={async () => {
                    await handleDownload(doc.id!);
                  }}
                >
                  <span className='mx-2 text-right'>{doc.document_name || `Document ${index + 1}`}</span>
                  <DownloadCloud onClick={() => handleDownload(doc.id!)} />
                </button>
              </li>
            ))}
          </ul>
        ) : (
          'N/A'
        ),
    },
  ];

  return (
    <ResponsiveDrawer isOpen={isOpen} onClose={onClose} title='Review Referral' className='pl-4'>
      <div className='mt-6 space-y-6 px-3'>
        <div className='space-y-4'>
          {fields.map(({ label, value }, index) => (
            <div key={`${label}-${index}`} className='flex items-center justify-between pb-2'>
              <div className='font-semibold text-gray-700'>{label}</div>
              <div className='text-right text-gray-900'>
                {React.isValidElement(value) ? value : String(value || 'N/A')}
              </div>
            </div>
          ))}
        </div>

        <div className='flex flex-wrap justify-end gap-4 border-t pt-4'>
          <DenyReferralModal successCallback={onDenyReferralSuccess} entry={entry} />
          <ReferOutMomModal referral={entry} onSuccess={onDenyReferralSuccess} />
          <ApproveMom referral={entry} />
          {/* TODO: When we have an intake flow, put the path in the button here https://servant-io.atlassian.net/browse/EMA-698 */}
          {entry?.prospect_status === 'prospect' ? (
            <ScheduleIntakeModal entry={entry || {}} successCallback={onScheduleIntakeSuccess} />
          ) : (
            <Button onClick={() => router.push(`/portal/coordinator/dashboard/people/referrals/${entry?.id}`)}>
              Start Intake
            </Button>
          )}
        </div>
      </div>
    </ResponsiveDrawer>
  );
};

export default ActionItemsNewMomReferralDrawer;
