import {
  assignCoordinator<PERSON><PERSON><PERSON>ields,
  assignCoordinatorFormSchema,
} from '@/app/portal/lib/assign-coordinator-form.config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useUpdateMom } from '@/hooks/generated/mom';
import { useCoordinators } from '@/hooks/useCoordinators';
import { Pencil } from 'lucide-react';
import { useState } from 'react';
import { FieldValues } from 'react-hook-form';

import { EnhancedMomType } from './action-items-new-moms-table';

const AssignCoordinatorModal = ({
  referral,
  onUpdate,
}: {
  referral: EnhancedMomType | null;
  onUpdate?: () => void;
}) => {
  const { coordinators } = useCoordinators();
  const [isOpen, setIsOpen] = useState(false);
  const { mutateAsync: updateMomAsync } = useUpdateMom();

  const handleAssignCoordinator = async (data: FieldValues) => {
    if (!referral) return;
    await updateMomAsync({
      where: { id: referral.id },
      data: {
        assigned_user_id: data.coordinator,
      },
    });

    onUpdate?.();
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <div className='flex cursor-pointer items-center space-x-1'>
          <span className='mr-2 underline'>Assign</span> <Pencil size={16} />
        </div>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Assign to Coordinator</DialogTitle>{' '}
          <DialogDescription>Choose from your assigned Coordinators or assign to yourself.</DialogDescription>
        </DialogHeader>
        {coordinators && coordinators.length > 0 ? (
          <FormFactory
            fields={assignCoordinatorFormFields(
              coordinators.map((coord) => ({
                id: coord.id,
                firstName: coord.firstName,
                lastName: coord.lastName,
              })),
            )}
            schema={assignCoordinatorFormSchema}
            onSubmit={(data) => handleAssignCoordinator(data)}
            actionButtonsComponent={
              <DialogFooter>
                <Button variant='default'>Assign</Button>
              </DialogFooter>
            }
          />
        ) : (
          <div>No coordinators found</div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default AssignCoordinatorModal;
