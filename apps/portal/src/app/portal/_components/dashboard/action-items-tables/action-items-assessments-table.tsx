import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import { usePairedMomsListContext } from '@/app/portal/context/paired-moms-context';
import GenericTable from '@/components/custom/generic-table';
import { Button } from '@/components/ui/button';
import { TableCell } from '@/components/ui/table';
import { useFindManyAAPIScore } from '@/hooks/generated/aapi-score';
import { formatDateFromString } from '@suiteapi/models';
import { ChevronRight } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo } from 'react';

import { MomPhoto } from '../../ui/mom-photo';

// TODO: https://servant-io.atlassian.net/browse/EMA-599

interface AssessmentItem {
  id: string;
  name: string;
  task: string;
  date_entered?: string | null;
  link?: string;
  paired_advocate?: string;
  momId: string;
}

interface AAPIScore {
  id: string;
  mom_id: string;
  constructAPreAssessment?: number | null;
  constructBPreAssessment?: number | null;
  constructAPostAssessment?: number | null;
  constructBPostAssessment?: number | null;
  mom?: {
    id?: string;
    first_name: string | null;
    last_name: string | null;
    date_entered?: string | null;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

interface ActionItemsAssessmentsTableProps {
  data?: AssessmentItem[];
  isLoading?: boolean;
}

// Define task types for different assessment scenarios
const getAssessmentTask = (score: AAPIScore): string => {
  if (!score.constructAPreAssessment && !score.constructBPreAssessment) {
    return 'Needs Pre-Assessment';
  }
  if (score.constructAPreAssessment && !score.constructAPostAssessment) {
    return 'Needs Post-Assessment';
  }
  return 'Review Assessment Results';
};

const ActionItemsAssessmentsTable = ({
  data: propData,
  isLoading: propIsLoading,
}: ActionItemsAssessmentsTableProps) => {
  const router = useRouter();
  const { profile } = useDashboardContext();
  const { pairedMomsList, fetchPairedMomsList } = usePairedMomsListContext();

  // Fetch all paired moms if not using provided data
  useEffect(() => {
    if (!propData) {
      fetchPairedMomsList();
    }
  }, [fetchPairedMomsList, propData]);

  // Get mom IDs from pairings
  const momIds = useMemo(() => {
    if (propData) return [];
    if (!pairedMomsList) return [];
    return pairedMomsList
      .filter((pairing) => pairing.mom)
      .map((pairing) => pairing.mom?.id)
      .filter((id) => id) as string[];
  }, [pairedMomsList, propData]);

  // Fetch AAPI scores for the paired moms
  const { data: aapiScores, isLoading } = useFindManyAAPIScore(
    {
      where: {
        mom_id: {
          in: momIds,
        },
      },
      include: {
        mom: true,
      },
    },
    {
      enabled: momIds.length > 0 && !propData,
    },
  );

  // Transform AAPI scores into assessment action items
  const assessmentItems = useMemo(() => {
    if (propData) return propData;
    if (!aapiScores) return [] as AssessmentItem[];

    return aapiScores.map((score) => {
      const mom = score.mom;
      const pairedAdvocate = 'N/A'; // We'll just use N/A since we don't have direct access to the paired advocate

      return {
        id: score.id,
        momId: score.mom_id,
        name: mom ? `${mom.first_name || ''} ${mom.last_name || ''}`.trim() : 'Unknown',
        task: getAssessmentTask(score),
        date_entered: mom?.date_entered,
        paired_advocate: pairedAdvocate,
        link: `/portal/${profile.portalRole}/dashboard/people/moms/${score.mom_id}/assessments`,
      } as AssessmentItem;
    });
  }, [aapiScores, propData, profile.portalRole]);

  return (
    <GenericTable
      data={assessmentItems}
      isLoading={propIsLoading || isLoading}
      headers={['Task', 'Name', 'Paired Advocate', 'Date Submitted', 'Actions']}
      rowRenderer={(item) => (
        <>
          <TableCell>{item.task}</TableCell>
          <TableCell className='font-medium'>
            <div className='flex items-center gap-2'>
              <MomPhoto momId={item.momId || ''} />
              <div>
                <p>{item.name}</p>
                <p className='text-sm font-light text-emaTextTertiary'>Mom</p>
              </div>
            </div>
          </TableCell>
          <TableCell>{item.paired_advocate || 'N/A'}</TableCell>
          <TableCell>{item.date_entered ? formatDateFromString(item.date_entered) : 'N/A'}</TableCell>
          <TableCell>
            <Button onClick={() => router.push(item.link || '')} variant='outline' size='sm' className='border-none'>
              Review
            </Button>
          </TableCell>
        </>
      )}
      mobileRenderer={(item) => (
        <div onClick={() => router.push(item.link || '')} className='cursor-pointer rounded-2xl bg-emaBgQuaternary p-4'>
          <div className='mb-2 flex justify-between'>
            <div className='flex items-center gap-2'>
              <span>Mom:</span>
              <span className='font-semibold'>{item.name}</span>
            </div>
            <div className='flex items-center'>
              <p>Review</p>
              <ChevronRight className='text-emaTextQuaternary' />
            </div>
          </div>
          <p className='text-sm font-light text-emaTextTertiary'>Task: {item.task}</p>
          <p className='text-sm font-light text-emaTextTertiary'>
            Date Submitted: {item.date_entered ? formatDateFromString(item.date_entered) : 'N/A'}
          </p>
        </div>
      )}
      emptyMessage='There are currently no assessments to display.'
      loadingMessage='Loading assessments...'
    />
  );
};

export default ActionItemsAssessmentsTable;
