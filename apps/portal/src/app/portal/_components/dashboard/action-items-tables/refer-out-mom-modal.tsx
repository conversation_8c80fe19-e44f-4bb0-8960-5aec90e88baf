import { useMomProfileContext } from '@/app/portal/context/mom-profile-context';
import { referOutReferralFormFields, referOutReferralSchema } from '@/app/portal/lib/refer-out-referral-config';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  Di<PERSON>Content,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useFindManyAgency, useUpdateMom } from '@/hooks/generated';
import { useToast } from '@/hooks/use-toast';
import { decodeHtmlEntities } from '@/lib/utils';
import { useState } from 'react';
import { FieldValues } from 'react-hook-form';
import { useCreateServiceReferral } from '@/hooks/generated/service-referral';

import { EnhancedMomType } from './action-items-new-moms-table';

const ReferOutMomModal = ({ referral, onSuccess }: { referral: EnhancedMomType | null; onSuccess: () => void }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { toast } = useToast();
  const { data: agencies } = useFindManyAgency();
  const { mutateAsync: updateMom } = useUpdateMom();
  const { mutateAsync: createServiceReferral } = useCreateServiceReferral();
  const { fetchMomProfile } = useMomProfileContext();

  let agencyOptions: { value: string; label: string }[] | null | undefined = null;
  if (!referral) return;
  agencyOptions =
    agencies?.map((agency) => ({
      value: String(agency.id),
      label: decodeHtmlEntities(agency.name ?? ''),
    })) || [];

  const handleSubmit = async (data: FieldValues) => {
    try {
      await updateMom({
        data: {
          referred_to_agency_id: data.referred_to_agency_id,
          referred_to_agency_reason: data.referred_to_agency_reason,
          prospect_status: 'did_not_engage_in_program',
          referral_sub_status: 'referred_to_alternate_program',
        },
        where: { id: referral.id as string },
      });

      // Create service referral
      await createServiceReferral({
        data: {
          service: data.service_c,
          outcome: data.outcome_c,
          start_date: data.start_date,
          provider: data.provider,
          mom: {
            connect: {
              id: data.mom_id,
            },
          },
        },
      });

      toast({
        title: 'Success',
        description: 'The mom has been successfully referred to another agency',
        variant: 'success',
      });
      fetchMomProfile(referral.id!);
      setIsOpen(false);
      onSuccess();
    } catch (error) {
      console.error(error);
      toast({
        title: 'Error',
        description: 'There was an error referring the mom to another agency. Please try again.',
        variant: 'destructive',
      });
    }
  };
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant='outline'>Refer Out</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Refer Out a Mom</DialogTitle>
        </DialogHeader>

        <FormFactory
          fields={referOutReferralFormFields(agencyOptions)}
          schema={referOutReferralSchema}
          onSubmit={handleSubmit}
          actionButtonsComponent={
            <DialogFooter>
              <DialogClose asChild>
                <Button type='button' variant='secondary'>
                  Cancel
                </Button>
              </DialogClose>
              <Button variant='default'>Refer Out</Button>
            </DialogFooter>
          }
        />
      </DialogContent>
    </Dialog>
  );
};

export default ReferOutMomModal;
