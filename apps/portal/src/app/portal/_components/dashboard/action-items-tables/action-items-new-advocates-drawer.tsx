import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import ResponsiveDrawer from '@/components/custom/responsive-drawer';
import { Button } from '@/components/ui/button';
import { useCreateNotification } from '@/hooks/generated';
import { useUpdateAdvocateOnboarding } from '@/hooks/generated/advocate-onboarding';
import { useUpdateUser } from '@/hooks/generated/user';
import { useMoodlePassword } from '@/hooks/use-moodle-password';
import { useToast } from '@/hooks/use-toast';
import { formatPhoneNumber } from '@/lib/utils';
import { User } from '@/types/schemas/user';
import React from 'react';

const ActionItemsNewAdvocatesDrawer = ({
  isOpen,
  onClose,
  entry,
  onDataRefresh,
}: {
  isOpen: boolean;
  onClose: () => void;
  entry: User | null;
  onDataRefresh: () => void;
}) => {
  const { toast } = useToast();
  const { profile } = useDashboardContext();
  const createNotification = useCreateNotification();
  const updateAdvocateOnboarding = useUpdateAdvocateOnboarding();
  const getMoodlePassword = useMoodlePassword();

  // Helper function to get a valid name for notifications
  const getRecipientName = () => {
    if (entry?.firstName) return entry.firstName;
    if (entry?.email) {
      const emailParts = entry.email.split('@');
      return emailParts[0] || 'Advocate';
    }
    return 'Advocate';
  };

  const fields = [
    { label: 'Submitted', value: entry?.created_at ? new Date(entry.created_at).toLocaleDateString() : 'N/A' },
    { label: 'Name', value: `${entry?.firstName || ''} ${entry?.lastName || ''}` },
    { label: 'Username', value: entry?.username || 'N/A' },
    {
      label: 'Email',
      value: entry?.email || 'N/A',
    },
    {
      label: 'Phone number',
      value:
        formatPhoneNumber(
          entry?.phone || entry?.phone_work || entry?.phone_mobile || entry?.phone_home || entry?.phone_other,
        ) || 'N/A',
    },
    { label: 'City', value: entry?.address_city || 'N/A' },
    { label: 'State', value: entry?.address_state || 'N/A' },
    { label: 'Zip', value: entry?.address_postalcode || 'N/A' },
    { label: 'Affiliate', value: entry?.affiliate?.name || 'N/A' },
    {
      label:
        'How did you hear about us? If it was through a church, community group, or organization, please specify which one.',
      value: entry?.description || 'No details provided.',
    },
    {
      label: 'Are you willing to complete a background check?',
      value: entry?.advocate_status === 'Active' ? 'Yes' : 'No',
    },
  ];

  const updateUser = useUpdateUser();

  const handleRejectAdvocate = async () => {
    if (!entry?.id) {
      toast({
        title: 'Error',
        description: 'Unable to find user record. Please try again.',
        variant: 'destructive',
      });
      return;
    }

    try {
      // If there's an advocate onboarding record, update it with review information
      if (entry.advocateOnboarding && 'id' in entry.advocateOnboarding && entry.advocateOnboarding.id) {
        await updateAdvocateOnboarding.mutateAsync({
          where: { id: entry.advocateOnboarding.id as string },
          data: {
            reviewNote: 'Rejected by coordinator',
            reviewedBy: `${profile.firstName} ${profile.lastName}`,
            reviewedAt: new Date(),
          },
        });
      }

      // Update the user's advocate status
      await updateUser.mutateAsync({
        where: { id: entry.id },
        data: { advocate_status: 'Rejected' },
      });

      toast({
        title: 'Success',
        description: 'The advocate status has been successfully updated',
        variant: 'success',
      });

      onClose();
      onDataRefresh();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'There was an error updating the advocate status. Please try again.',
        variant: 'destructive',
      });
      console.error('Error updating advocate status:', error);
    }
  };

  const handleSendInviteToTraining = async () => {
    if (!entry?.id) {
      toast({
        title: 'Error',
        description: 'Unable to find user record. Please try again.',
        variant: 'destructive',
      });
      return;
    }

    try {
      // If there's an advocate onboarding record, update it with review information
      if (entry.advocateOnboarding && 'id' in entry.advocateOnboarding && entry.advocateOnboarding.id) {
        await updateAdvocateOnboarding.mutateAsync({
          where: { id: entry.advocateOnboarding.id as string },
          data: {
            reviewNote: 'Approved for training by coordinator',
            reviewedBy: `${profile.firstName} ${profile.lastName}`,
            reviewedAt: new Date(),
          },
        });
      }

      // Update the user status
      await updateUser.mutateAsync({
        where: { id: entry.id },
        data: { advocate_status: 'In_Training' },
      });

      // Get the Moodle password and then create notification
      try {
        // First, get the Moodle password
        const moodlePassword = await getMoodlePassword.mutateAsync(entry.id);
        
        const notificationData = {
          template: 'advocate_invite_training' as const,
          template_params: {
            recipientFirstName: getRecipientName(),
            moodleUrlBase: process.env.NEXT_PUBLIC_MOODLE_URL_BASE,
            username: entry.username,
            password: moodlePassword.password,
          },
          recipient_user: {
            connect: {
              id: entry.id,
            },
          },
          status: 'pending' as const,
        };

        await createNotification.mutateAsync({ data: notificationData });

        toast({
          title: 'Success',
          description: 'Training invitation has been sent successfully',
          variant: 'success',
        });
      } catch (notificationError) {
        console.error('Error creating notification:', notificationError);
        toast({
          title: 'Partial Success',
          description: 'Advocate status was updated, but there was an error sending the notification.',
          variant: 'default',
        });
      }

      onClose();
      onDataRefresh();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'There was an error updating the advocate status. Please try again.',
        variant: 'destructive',
      });
      console.error('Error updating advocate status:', error);
    }
  };

  return (
    <ResponsiveDrawer isOpen={isOpen} onClose={onClose} title='Review Interest Form' className='pl-4'>
      {entry ? (
        <div className='mt-6 space-y-20 px-3'>
          <div className='grid grid-cols-2 gap-4'>
            {fields.map(({ label, value }, index) => (
              // This needs to be a react fragment so it keeps the two divs as two columns
              <React.Fragment key={`${label}-${index}`}>
                <div>{label}</div>
                <div className='text-right'>{value}</div>
              </React.Fragment>
            ))}
          </div>
          <div className='flex justify-end space-x-4 border-t pt-4'>
            <Button
              onClick={() => {
                handleRejectAdvocate();
              }}
              variant='outline'
            >
              Reject
            </Button>
            <Button className='bg-darkGreen' onClick={handleSendInviteToTraining}>
              Send Invite to Training Videos
            </Button>
          </div>
        </div>
      ) : (
        <div>No entry selected</div>
      )}
    </ResponsiveDrawer>
  );
};

export default ActionItemsNewAdvocatesDrawer;