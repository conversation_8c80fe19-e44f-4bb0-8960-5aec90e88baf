import { usePairedMomsListContext } from '@/app/portal/context/paired-moms-context';

import MomAddModal from '../../[role]/dashboard/people/_components/mom-add-modal';
import MomScheduleSessionModal from '../../[role]/dashboard/people/_components/mom-schedule-session-modal';
import MyPairings from '../advocate/my-pairings';
import DashboardHeader from './dashboard-header';
import AppointmentsAndEvents from './upcoming-appointments-events';

const LandingPageAdvocate: React.FC = () => {
  const { signalUpdatePairingsList } = usePairedMomsListContext();

  return (
    <>
      <DashboardHeader
        headerText='Stay up to date with your pairings, upcoming sessions, and action plans.'
        actionsComponent={
          <>
            <MomAddModal />
            <MomScheduleSessionModal successCallback={signalUpdatePairingsList} />
          </>
        }
      />
      <MyPairings />
      <AppointmentsAndEvents />
    </>
  );
};

export default LandingPageAdvocate;
