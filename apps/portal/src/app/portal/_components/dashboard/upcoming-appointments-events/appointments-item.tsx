import { Calendar } from 'lucide-react';
import React from 'react';

interface AppointmentItemProps {
  title: string;
  start_at: string;
}

const AppointmentItem: React.FC<AppointmentItemProps> = ({ title, start_at }) => {
  const startAtDate = new Date(start_at);

  const date = startAtDate.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
  });

  const time = startAtDate.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  });

  return (
    <div className='flex w-full flex-wrap items-start border-b border-gray-200 text-slate-600 max-md:max-w-full'>
      <div className='flex min-w-[240px] flex-1 font-medium text-gray-900 max-md:max-w-full'>
        <div className='flex w-full items-center gap-3 px-6 py-5 max-md:max-w-full max-md:px-5'>
          <div>
            <Calendar className='h-5 w-5 text-gray-500' />
          </div>
          <div className='my-auto self-stretch truncate'>
            <span className='underline'>{title}</span>
          </div>
        </div>
      </div>
      <div className='flex flex-wrap items-start'>
        <div className='w-[110px] self-stretch py-5 pl-6 max-md:px-5'>{date}</div>
        <div className='w-[110px] self-stretch px-1 py-5 max-md:px-5'>{time}</div>
      </div>
    </div>
  );
};

export default AppointmentItem;
