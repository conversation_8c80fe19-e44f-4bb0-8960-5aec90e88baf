'use client';

import { usePairedMomsListContext } from '@/app/portal/context/paired-moms-context';
import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreVertical } from 'lucide-react';
import { useState } from 'react';

import MomScheduleSessionModal from '../../../[role]/dashboard/people/_components/mom-schedule-session-modal';

const AppointmentsDropdown: React.FC = () => {
  const [scheduleSessionModalOpen, setScheduleSessionModalOpen] = useState(false);
  const { signalUpdatePairingsList } = usePairedMomsListContext();

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant='ghost' size='icon' className='rounded-full'>
            <MoreVertical className='h-6 w-6 text-gray-500' />
            <span className='sr-only'>Toggle appointments and events menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end'>
          <DropdownMenuItem onClick={() => void setScheduleSessionModalOpen(true)}>Schedule Session</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <MomScheduleSessionModal
        open={scheduleSessionModalOpen}
        onClose={() => void setScheduleSessionModalOpen(false)}
        successCallback={signalUpdatePairingsList}
      />
    </>
  );
};

export default AppointmentsDropdown;
