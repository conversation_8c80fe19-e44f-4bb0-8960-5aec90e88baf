'use client';

import { useAppointments } from '@/app/portal/context/appointments-context';
import EventsCalendar from '@/components/custom/events-calendar';
import { Button } from '@/components/ui/button';
import React from 'react';
import { useEffect, useState } from 'react';

import AppointmentsDropdown from './appointments-dropdown';
import AppointmentsItem from './appointments-item';

const AppointmentsAndEvents: React.FC = () => {
  const { appointments, fetchAppointments, appointmentsLoading } = useAppointments();
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);

  const appointmentDates = appointments.map((appointment) => new Date(appointment.start_at));

  const filteredAppointments = selectedDate
    ? appointments.filter(
        (appointment) => new Date(appointment.start_at).toDateString() === selectedDate.toDateString(),
      )
    : appointments;

  useEffect(() => {
    fetchAppointments();
  }, [fetchAppointments]);

  return (
    <section className='flex w-full min-w-[320px] flex-col'>
      <div className='flex w-full max-w-full flex-col'>
        <header className='flex w-full flex-wrap gap-4 max-md:max-w-full'>
          <h2 className='min-w-[240px] flex-1 shrink text-lg font-semibold leading-7 text-gray-900 max-md:max-w-full'>
            Upcoming Appointments and Events
          </h2>
          <div className='flex w-10 flex-col self-start'>
            <AppointmentsDropdown />
          </div>
        </header>
        <div className='mt-5 flex min-h-[1px] w-full bg-gray-200 max-md:max-w-full' />
      </div>
      {appointmentsLoading ? (
        <div className='m-6 flex h-52 w-full flex-col items-center justify-center rounded-md bg-gray-50 opacity-70'>
          <p className='text-gray-600'>Loading appointments...</p>
        </div>
      ) : (
        <div className='flex w-full max-w-full flex-wrap items-start gap-4 max-md:flex-wrap-reverse'>
          <div className='mt-2 flex flex-1 flex-col overflow-hidden text-sm leading-5 max-md:max-w-full'>
            {filteredAppointments.length ? (
              <>
                {filteredAppointments.map((appointment) => (
                  <AppointmentsItem key={appointment.id} title={appointment.title} start_at={appointment.start_at} />
                ))}
                <div className='flex w-full items-center gap-5 pt-2'>
                  <Button variant='ghost' onClick={() => setSelectedDate(undefined)}>
                    View all
                  </Button>
                </div>
              </>
            ) : (
              // Rectangular container with faint background and about p-6 for empty state, with messaging in center
              <div className='m-6 flex h-52 w-full flex-col items-center justify-center rounded-md bg-gray-50 opacity-70'>
                {appointments.length && selectedDate ? (
                  <>
                    <p className='text-gray-600'>No appointments or events scheduled for this day.</p>
                    <Button variant='ghost' onClick={() => setSelectedDate(undefined)}>
                      View all
                    </Button>
                  </>
                ) : (
                  <p className='text-gray-600'>No upcoming appointments or events scheduled.</p>
                )}
              </div>
            )}
          </div>

          <div className='w-[480px] max-xl:w-96 max-md:w-full'>
            <EventsCalendar date={selectedDate} setDate={setSelectedDate} eventDates={appointmentDates} />
          </div>
        </div>
      )}
    </section>
  );
};

export default AppointmentsAndEvents;
