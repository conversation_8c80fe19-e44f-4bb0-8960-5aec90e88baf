import { Select, SelectContent, SelectItem, SelectTrigger } from '@/components/ui/select';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useFindManyMom, useFindManySessionNote, useFindManyUser } from '@/hooks/generated';
import React, { useMemo, useState } from 'react';

import { MomProfileProvider } from '../../context/mom-profile-context';
import ActionItemsSessionNotes from './action-items-session-notes-table';
import ActionItemsAssessmentsTable from './action-items-tables/action-items-assessments-table';
import ActionItemsNewAdvocatesTable from './action-items-tables/action-items-new-advocates-table';
import ActionItemsNewMomsTable from './action-items-tables/action-items-new-moms-table';

const tabItems = [
  { label: 'Session Notes', value: 'Session Notes' },
  { label: 'New Moms', value: 'New Moms' },
  { label: 'Assessments', value: 'Assessments' },
  { label: 'New Advocates', value: 'New Advocates' },
];

const ActionItems: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState('Session Notes');

  const { data: advocates, isLoading: isLoadingAdvocates } = useFindManyUser({
    where: {
      userRoles: {
        some: {
          role: {
            key: {
              in: ['advocate', 'onboarding_advocate'],
            },
          },
        },
      },
      advocate_status: {
        notIn: ['Rejected', 'In_Training'],
      },
    },
    include: {
      userRoles: {
        include: {
          role: true,
        },
      },
      affiliate: true,
      advocateOnboarding: true,
    },
  });

  const safeAdvocates = Array.isArray(advocates) ? advocates : [];

  const {
    data: sessionNotes,
    isLoading: isLoadingSessionNotes,
    refetch: refetchSessionNotes,
  } = useFindManySessionNote({
    include: {
      session: {
        include: {
          pairing: {
            include: {
              advocateUser: true,
              mom: true,
            },
          },
        },
      },
    },
  });

  const {
    data: referrals,
    isLoading: isLoadingReferrals,
    refetch: refetchReferrals,
  } = useFindManyMom({
    include: {
      _count: {
        select: { wellnessAssessments: true },
      },
    },
    where: {
      prospect_status: 'prospect',
    },
  });

  const processedNewMomsData = useMemo(() => {
    // moms who are not assigned to a coordinator/advocate, and have not been converted need to go over referral review... only visible to supervisors
    const referralData =
      referrals
        ?.filter((item) => !item.assigned_user_id && item.prospect_status === 'prospect')
        .map((item) => ({
          ...item,
          table_label: 'Referral Review',
          table_actions_label: 'Review',
          table_actions_path: '',
        })) ?? [];

    // go through intake with moms who are assigned to someone and not converted
    const intakeData =
      referrals
        ?.filter((item) => item.assigned_user_id || item.prospect_status === 'prospect_intake_scheduled')
        .map((item) => ({
          ...item,
          table_label: 'Intake Form',
          table_actions_label: item._count.wellnessAssessments > 0 ? 'View Intake' : 'Start Intake',
          table_actions_path: `/portal/coordinator/dashboard/people/referrals/${item.id}`,
        })) ?? [];

    return [...referralData, ...intakeData];
  }, [referrals]);

  const refetchData = async () => {
    try {
      await refetchReferrals();
      await refetchSessionNotes();
    } catch (error) {
      console.error('Error refetching data:', error);
    }
  };

  // TODO: https://servant-io.atlassian.net/browse/EMA-599
  const isLoadingAssessments = false;
  const assessments = [
    {
      id: '1',
      name: 'Mom Name',
      task: 'Task Name',
      date_entered: '2024-10-28T18:14:00+00:00',
      link: '',
      paired_advocate: 'Advocate Name',
      momId: '1',
    },
  ];

  // Transform advocates data to match the expected format for the table
  const transformedAdvocates = safeAdvocates
    .filter((advocate) => Array.isArray(advocate.languages_c))
    .map((advocate) => {
      // Omit userRoles from the transformed object and transform affiliate
      const { userRoles, affiliate, advocateOnboarding, ...rest } = advocate;
      return {
        ...rest,
        languages_c: (advocate.languages_c ?? []).map((lang) =>
          typeof lang === 'string' ? { value: lang, label: lang } : lang,
        ),
        userRolesDisplay: (userRoles ?? [])
          .filter((ur) => ur && ur.role)
          .map((ur) => ({
            value: ur.role.key ?? '',
            label: ur.role.name ?? ur.role.key ?? '',
          })),
        // Transform affiliate to match expected User type structure
        affiliate: affiliate
          ? {
              id: affiliate.id,
              name: affiliate.name,
              email1: affiliate.email1 ?? undefined,
              description: affiliate.description ?? undefined,
              billing_address_street: affiliate.billing_address_street ?? undefined,
              billing_address_street_2: affiliate.billing_address_street_2 ?? undefined,
              billing_address_street_3: affiliate.billing_address_street_3 ?? undefined,
              billing_address_street_4: affiliate.billing_address_street_4 ?? undefined,
              billing_address_city: affiliate.billing_address_city ?? undefined,
              billing_address_state: affiliate.billing_address_state ?? undefined,
              billing_address_postalcode: affiliate.billing_address_postalcode ?? undefined,
              billing_address_country: affiliate.billing_address_country ?? undefined,
              phone_office: affiliate.phone_office ?? undefined,
              website: affiliate.website ?? undefined,
              contact_name: affiliate.contact_name ?? undefined,
            }
          : null,
        // Include advocateOnboarding data for workflow management
        advocateOnboarding: advocateOnboarding || null,
      };
    });

  return (
    <MomProfileProvider>
      <div className='rounded-lg bg-white p-6 shadow-md'>
        <h2 className='mb-4 text-xl font-semibold'>Action Items</h2>

        <Tabs defaultValue='View All' value={selectedTab} onValueChange={setSelectedTab} className='w-full py-4'>
          {/* Tabs for Desktop */}
          <TabsList className='hidden h-14 w-full justify-start rounded-lg border border-emaBgQuaternary bg-emaBgPage p-2 py-4 text-left md:flex'>
            {tabItems.map((tab) => (
              <TabsTrigger
                key={tab.value}
                value={tab.value}
                className='rounded-md px-4 py-2 font-medium text-emaTextQuaternary hover:text-emaTextPrimary data-[state=active]:bg-white data-[state=active]:text-emaTextSecondary data-[state=active]:shadow-md'
              >
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>

          {/* Select Menu for Mobile */}
          <div className='block md:hidden'>
            <Select value={selectedTab} onValueChange={setSelectedTab}>
              <SelectTrigger className='w-full rounded-lg border border-emaBgQuaternary px-4 py-2'>
                {selectedTab}
              </SelectTrigger>
              <SelectContent>
                {tabItems.map((tab) => (
                  <SelectItem key={tab.value} value={tab.value}>
                    {tab.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className='mt-6'>
            <TabsContent value='Session Notes'>
              <ActionItemsSessionNotes data={sessionNotes || []} isLoading={isLoadingSessionNotes} />
            </TabsContent>
            <TabsContent value='New Moms'>
              <ActionItemsNewMomsTable
                data={processedNewMomsData}
                isLoading={isLoadingReferrals}
                onDataRefresh={refetchData}
              />
            </TabsContent>
            <TabsContent value='Assessments'>
              <ActionItemsAssessmentsTable data={assessments} isLoading={isLoadingAssessments} />
            </TabsContent>
            <TabsContent value='New Advocates'>
              <ActionItemsNewAdvocatesTable
                data={transformedAdvocates}
                isLoading={isLoadingAdvocates}
                onDataRefresh={refetchData}
              />
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </MomProfileProvider>
  );
};

export default ActionItems;