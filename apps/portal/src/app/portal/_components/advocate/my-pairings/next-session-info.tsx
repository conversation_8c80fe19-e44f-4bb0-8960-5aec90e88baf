import { formatDateFromString } from '@suiteapi/models';
import { Calendar, ChevronRight, Users, Video } from 'lucide-react';
import React from 'react';

import { NextSessionProps } from './types';

const NextSessionInfo: React.FC<NextSessionProps> = ({ isVirtual, date }) => {
  const formattedDate = formatDateFromString(date, "E, MMM d 'at' h:mm a");
  const meetingType = isVirtual ? 'Virtual' : 'In Person';

  return (
    <div className='flex w-[307px] max-w-full flex-col rounded-none text-slate-600'>
      <div className='flex w-full flex-col rounded border border-solid border-gray-300 bg-emaBgPage py-4 pl-4 pr-2'>
        <h3 className='self-start text-sm font-semibold leading-none'>Next Session</h3>
        <div className='mt-2 flex justify-between gap-5 text-sm leading-5'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2 self-start'>
              {isVirtual ? <Video className='h-6 w-6 text-gray-500' /> : <Users className='h-6 w-6 text-gray-500' />}
              <span className='my-auto self-stretch'>{meetingType}</span>
            </div>
            <div className='mt-2 flex w-full items-center gap-2'>
              <Calendar className='h-6 w-6 text-gray-500' />
              <span className='my-auto self-stretch'>{formattedDate}</span>
            </div>
          </div>
          <ChevronRight className='mt-1 h-5 w-5 text-gray-500' />
        </div>
      </div>
    </div>
  );
};

export default NextSessionInfo;
