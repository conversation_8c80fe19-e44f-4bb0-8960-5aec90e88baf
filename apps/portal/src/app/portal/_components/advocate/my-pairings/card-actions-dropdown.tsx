'use client';

import MomCreateConnectionLogModal from '@/app/portal/[role]/dashboard/people/_components/mom-create-connection-log-modal';
import MomScheduleSessionModal from '@/app/portal/[role]/dashboard/people/_components/mom-schedule-session-modal';
import SendMomTextMessageModal from '@/app/portal/[role]/dashboard/people/_components/send-mom-text-message-modal';
import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import { usePairedMomsListContext } from '@/app/portal/context/paired-moms-context';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreVertical } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface CardActionsDropdownProps {
  id: string;
  pairingId: string;
  firstName: string;
  fullName: string;
  phoneNumber: string;
  upcomingSessionNotesLink: string;
}

const CardActionsDropdown: React.FC<CardActionsDropdownProps> = ({
  id,
  pairingId,
  firstName,
  fullName,
  phoneNumber,
  upcomingSessionNotesLink,
}) => {
  const router = useRouter();
  const { profile } = useDashboardContext();
  const { portalRole } = profile; // user may be super-advocate with 'coordinator' as primary role
  const { signalUpdatePairingsList } = usePairedMomsListContext();
  const [scheduleSessionModalOpen, setScheduleSessionModalOpen] = useState(false);
  const [logConnectionModalOpen, setLogConnectionModalOpen] = useState(false);
  const [sendTextMessageModalOpen, setSendTextMessageModalOpen] = useState(false);

  const goToMomProfile = () => {
    router.push(`/portal/${portalRole}/dashboard/people/moms/${id}`);
  };

  const goToSessionNotesList = () => {
    router.push(`/portal/${portalRole}/dashboard/people/moms/${id}/session-notes`);
  };

  const goToVitalSupport = () => {
    router.push(`/portal/${portalRole}/dashboard/people/moms/${id}/vital-support`);
  };

  const goToUpcomingSessionNotes = () => {
    router.push(upcomingSessionNotesLink);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant='ghost' size='icon' className='rounded-full'>
            <MoreVertical className='h-6 w-6 text-gray-500' />
            <span className='sr-only'>Toggle paired mom actions menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end'>
          <DropdownMenuItem onClick={goToMomProfile}>Go to {firstName}&lsquo;s profile</DropdownMenuItem>
          <DropdownMenuItem onClick={() => void setLogConnectionModalOpen(true)}>Log Connection</DropdownMenuItem>
          <DropdownMenuItem onClick={() => void setScheduleSessionModalOpen(true)}>Schedule Session</DropdownMenuItem>
          <DropdownMenuItem onClick={() => void setSendTextMessageModalOpen(true)}>
            Text Message {firstName}
          </DropdownMenuItem>
          <DropdownMenuItem onClick={goToSessionNotesList}>View All Session Notes</DropdownMenuItem>
          <DropdownMenuItem onClick={goToVitalSupport}>View Vital Support Need</DropdownMenuItem>
          <DropdownMenuItem onClick={goToUpcomingSessionNotes} disabled={!upcomingSessionNotesLink}>
            Upcoming Session Notes
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <MomScheduleSessionModal
        pairingId={pairingId}
        open={scheduleSessionModalOpen}
        onClose={() => void setScheduleSessionModalOpen(false)}
        successCallback={signalUpdatePairingsList}
      />
      <MomCreateConnectionLogModal
        momId={id}
        open={logConnectionModalOpen}
        onClose={() => void setLogConnectionModalOpen(false)}
      />
      <SendMomTextMessageModal
        momId={id}
        momFullName={fullName}
        momFirstName={firstName}
        momPhoneNumber={phoneNumber}
        open={sendTextMessageModalOpen}
        onClose={() => void setSendTextMessageModalOpen(false)}
      />
    </>
  );
};

export default CardActionsDropdown;
