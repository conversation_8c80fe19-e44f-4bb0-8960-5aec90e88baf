import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import { usePairedMomsListContext } from '@/app/portal/context/paired-moms-context';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { ChevronRight } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React from 'react';

import MomCreateConnectionLogModal from '../../../[role]/dashboard/people/_components/mom-create-connection-log-modal';
import MomScheduleSessionModal from '../../../[role]/dashboard/people/_components/mom-schedule-session-modal';
import { findMostRecentAndUpcomingSessions } from '../../../[role]/dashboard/people/_utils/session-utils';
import CardActionsDropdown from './card-actions-dropdown';
import GetReadySection from './get-ready-section';
import NextSessionInfo from './next-session-info';
import { type PairingData } from './types';

const PairingCard: React.FC<PairingData> = ({ id, pairingId, firstName, fullName, phoneNumber }) => {
  const router = useRouter();
  const { profile } = useDashboardContext();
  const { portalRole } = profile;
  const { sessionsByMomId, signalUpdatePairingsList } = usePairedMomsListContext();

  const sessions = sessionsByMomId[id] || [];
  const { mostRecentPast, nextUpcoming } = findMostRecentAndUpcomingSessions(sessions);
  const hasScheduledSession = !!nextUpcoming;
  const isUpcomingSessionVirtual = !!nextUpcoming?.join_url;
  const upcomingSessionDate = String(nextUpcoming?.date_start);
  const upcomingSessionNotesLink = nextUpcoming?.id
    ? `/portal/${portalRole}/dashboard/people/moms/${id}/session-notes/${String(nextUpcoming.id)}`
    : '';

  const goToUpcomingSessionNotes = () => {
    router.push(upcomingSessionNotesLink);
  };

  return (
    <Card className='my-auto flex w-[347px] min-w-[240px] flex-col self-stretch rounded-xl border border-solid border-zinc-200 bg-white pb-5 shadow-sm'>
      <CardHeader className='flex min-h-[75px] flex-col items-start justify-center overflow-hidden border-b border-gray-300 py-6 pl-5 text-xl font-semibold leading-8 text-gray-900'>
        <div className='flex w-[304px] max-w-full flex-col'>
          <div className='flex w-full max-w-[304px] justify-between gap-5'>
            <Link href={`/portal/${profile.portalRole}/dashboard/people/moms/${id}`}>
              <div className='flex items-center gap-2'>
                <h2 className='my-auto self-stretch'>{fullName}</h2>
                <ChevronRight className='h-5 w-5 text-gray-500' />
              </div>
            </Link>
            <CardActionsDropdown
              id={id}
              pairingId={pairingId}
              firstName={firstName}
              fullName={fullName}
              phoneNumber={phoneNumber}
              upcomingSessionNotesLink={upcomingSessionNotesLink}
            />
          </div>
        </div>
      </CardHeader>
      <CardContent className='mt-4 flex flex-col max-md:mx-2.5'>
        {!sessionsByMomId[id] ? (
          <div className='flex h-40 w-full items-center justify-center'>Loading...</div>
        ) : (
          <>
            {hasScheduledSession ? (
              <>
                <Link href={upcomingSessionNotesLink}>
                  <NextSessionInfo isVirtual={isUpcomingSessionVirtual} date={upcomingSessionDate} />
                </Link>
                <GetReadySection
                  momId={id}
                  previousSessionNotesId={mostRecentPast?.id || ''}
                  nextSessionNotesId={nextUpcoming?.id || ''}
                />
              </>
            ) : (
              <div className='flex h-[166px] flex-col'>
                <h3 className='min-h-[20px] w-full gap-4 self-stretch font-semibold leading-none text-slate-600'>
                  Next Session
                </h3>
                <div className='flex h-full w-full flex-col items-center justify-center gap-2'>
                  <p className='mt-4 w-full text-center leading-3 text-black'>No sessions currently scheduled.</p>
                  <MomScheduleSessionModal
                    pairingId={pairingId}
                    buttonComponent={
                      <Button variant='ghost'>
                        <span className='underline'>Schedule session</span>
                      </Button>
                    }
                    successCallback={signalUpdatePairingsList}
                  />
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
      <CardFooter>
        <div className='flex w-full flex-col gap-2'>
          <MomCreateConnectionLogModal
            momId={id}
            buttonComponent={
              <Button variant='default' className='w-full'>
                Log Connection
              </Button>
            }
          />
          <Button variant='outline' className='w-full' onClick={goToUpcomingSessionNotes} disabled={!nextUpcoming?.id}>
            Upcoming Session Notes
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default PairingCard;
