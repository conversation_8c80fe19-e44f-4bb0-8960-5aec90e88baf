import { useDashboardContext } from '@/app/portal/context/dashboard-context';
import { ChevronRight, List, NotepadText, Play } from 'lucide-react';
import Link from 'next/link';
import { Fragment } from 'react';

interface GetReadySectionProps {
  momId: string;
  previousSessionNotesId?: string;
  nextSessionNotesId: string;
}

const GetReadySection: React.FC<GetReadySectionProps> = ({ momId, previousSessionNotesId, nextSessionNotesId }) => {
  const { profile } = useDashboardContext();
  const { portalRole } = profile;

  const readyItems = [
    {
      icon: <List className='h-6 w-6 text-gray-500' />,
      title: 'Review Action Plan',
      description: 'Help remind yourself of goals and tasks',
      url: `/portal/${portalRole}/dashboard/people/moms/${momId}/action-plan`,
    },
    {
      icon: <NotepadText className='h-6 w-6 text-gray-500' />,
      title: 'Review Last Session Notes',
      description: 'Find ideas or tasks to follow up on',
      url: previousSessionNotesId
        ? `/portal/${portalRole}/dashboard/people/moms/${momId}/session-notes/${previousSessionNotesId}`
        : null,
    },
    {
      icon: <Play className='h-6 w-6 text-gray-500' />,
      title: 'Review Content',
      description: 'Get prepared to lead with confidence',
      url: nextSessionNotesId
        ? `/portal/${portalRole}/dashboard/people/moms/${momId}/session-notes/${nextSessionNotesId}`
        : null,
    },
  ];

  return (
    <div className='mt-4 flex w-full flex-col'>
      <h3 className='text-sm font-semibold leading-none text-slate-600'>Get Ready</h3>
      <ul className='mt-2 flex w-full flex-col text-sm leading-5'>
        {readyItems.map((item) => (
          <Fragment key={item.title}>
            {item.url ? (
              <Link href={item.url || '#'} className='w-full'>
                <li className='flex min-h-[62px] w-full items-center gap-2 py-px'>
                  <div className='my-auto flex min-w-[240px] flex-1 shrink basis-0 items-start gap-2 self-stretch'>
                    {item.icon}
                    <div className='flex min-w-[240px] flex-1 shrink basis-0 flex-col'>
                      <h4 className='font-semibold text-gray-900'>{item.title}</h4>
                      <p className='text-slate-600'>{item.description}</p>
                    </div>
                  </div>
                  <ChevronRight className='h-5 w-5 text-gray-500' />
                </li>
              </Link>
            ) : null}
          </Fragment>
        ))}
      </ul>
    </div>
  );
};

export default GetReadySection;
