'use client';

import { usePairedMomsListContext } from '@/app/portal/context/paired-moms-context';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel';
import React from 'react';
import { useEffect } from 'react';

import PairingCard from './pairing-card';
import type { PairingData } from './types';

const MyPairings: React.FC = () => {
  const {
    pairedMomsLoading,
    pairedMomsList,
    fetchPairedMomsList,
    fetchSessionsForPairings,
    updatePairingsListTrigger,
  } = usePairedMomsListContext();

  const pairingsData: PairingData[] =
    pairedMomsList
      ?.filter((pairing) => pairing.mom)
      .map((pairing) => {
        const mom = pairing.mom!;
        const phoneNumber = mom.phone_other || '';
        return {
          id: mom.id as string,
          pairingId: pairing.id as string,
          name: mom.name as string,
          firstName: mom.first_name as string,
          fullName: `${mom.first_name} ${mom.last_name}` as string,
          phoneNumber: phoneNumber,
        };
      }) || [];

  useEffect(() => {
    void fetchPairedMomsList();
  }, [updatePairingsListTrigger, fetchPairedMomsList]); // successfully scheduling a new session triggers a refresh of the pairings list

  useEffect(() => {
    // after paired moms list has loaded, load the session list for each pairing
    fetchSessionsForPairings();
  }, [pairedMomsList, fetchSessionsForPairings]);

  if (pairedMomsLoading) {
    return <div>Loading...</div>;
  }

  return !pairingsData.length ? (
    <div>No pairings found</div>
  ) : (
    <Carousel>
      <header className='flex justify-between pb-2'>
        <h3 className='text-2xl font-semibold leading-8 text-gray-900'>Your Pairings</h3>
        <div className='flex items-start gap-4'>
          <CarouselPrevious />
          <CarouselNext />
        </div>
      </header>
      <CarouselContent>
        {pairingsData.map((pairing) => (
          <CarouselItem key={pairing.id} className='pl-4'>
            <PairingCard {...pairing} />
          </CarouselItem>
        ))}
      </CarouselContent>
    </Carousel>
  );
};

export default MyPairings;
