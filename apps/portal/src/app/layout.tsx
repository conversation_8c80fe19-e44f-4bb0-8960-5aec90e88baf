import { Toaster } from '@/components/ui/toaster';
import { UserInfoProvider } from '@/context/user-info';
import ZenstackProviders from '@/providers/zenstackProviders';
import { Metadata } from 'next';

import './styles/global.css';

export const metadata: Metadata = {
  title: 'EMA Advocate Portal',
  description: '',
};

const RootLayout = ({ children }: { children: React.ReactNode }): JSX.Element => {
  return (
    <html lang='en'>
      <body>
        <ZenstackProviders>
          <UserInfoProvider>
            {children}
            <Toaster />
          </UserInfoProvider>
        </ZenstackProviders>
      </body>
    </html>
  );
};
export default RootLayout;
