import Image from 'next/image';
import Link from 'next/link';
import { Fragment } from 'react';

// TODO: this page is intended to improve Vercel preview viewing experience for dev team and stakeholders
// this is not intended to be a production page - change or show based on an environment variable prior to production

const previewLinks = [
  {
    label: 'Dashboard Login',
    href: '/login',
  },
  {
    label: 'Self-Referral Form',
    href: '/referral/self',
  },
  {
    label: 'Community Referral Form',
    href: '/referral/community',
  },
  {
    label: 'Advocate Interest Form',
    href: '/advocate-interest',
  },
  {
    label: 'National Resources (no affiliates page)',
    href: '/referral/no-affiliates',
  },
];

const Page = (): JSX.Element => {
  return (
    <main className='flex h-screen flex-col items-center justify-center space-y-4 bg-emaBgPage'>
      <Image src='/assets/ema-logo.svg' alt='EMA Logo' width={78} height={24} />
      <section className='flex flex-col items-center space-y-4'>
        <h1 className='text-base font-normal text-emaTextSecondary'>Welcome to the preview page for the ĒMA portal.</h1>
        <div className='flex flex-row gap-3'>
          {previewLinks.map((link, index) => (
            <Fragment key={link.href}>
              <Link href={link.href}>
                <span className='text-base font-semibold text-emaTextPrimary hover:underline'>{link.label}</span>
              </Link>
              {index < previewLinks.length - 1 && <div>|</div>}
            </Fragment>
          ))}
        </div>
      </section>
    </main>
  );
};
export default Page;
