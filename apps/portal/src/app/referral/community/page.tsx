'use client';

import { useFindManyAgency } from '@/hooks/generated';
import { YupSchemas } from '@suiteapi/models';
import { useEffect } from 'react';

import MultiStepReferralForm from '../_components/multi-step-referral-form';
import { type Agencies, useReferralFormContext } from '../_components/referral-form-context';
import ReferralPageWrapper from '../_components/referral-page-wrapper';
import communityReferralFormsConfig from '../_lib/community-referral-form.config';

export default function Page(): JSX.Element {
  const { title, description } = communityReferralFormsConfig.header;
  const { data: agencies, error: agenciesError } = useFindManyAgency();
  const { setAgencies } = useReferralFormContext();

  useEffect(() => {
    const validatedAgencies = agencies?.map((agency) => YupSchemas.agencySchema.validateSync(agency));
    setAgencies((agenciesError ? [] : validatedAgencies || []) as Agencies);
  }, [agencies, agenciesError]);

  return (
    <ReferralPageWrapper headerTitle={title} headerDescription={description} variant='wide'>
      <MultiStepReferralForm referralType='community' />
    </ReferralPageWrapper>
  );
}
