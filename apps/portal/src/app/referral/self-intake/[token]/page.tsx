'use client';

import IntakeStepper from '@/app/portal/[role]/dashboard/people/referrals/_components/intake-stepper';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';

import { sfetch } from '../../../../lib/sfetch';
import { assignSummaryFieldsAndCalculateScores } from '../../../../services/wellbeing-assessment-scoring';
import { getWellBeingAssessmentFields } from '../../../portal/[role]/dashboard/people/referrals/_components/intake-referral-form.config';
import {
  StepId,
  getInitialWellBeingAssessmentFormData,
} from '../../../portal/[role]/dashboard/people/referrals/_components/utils';
import { WellBeingAssessmentFormData } from '../../../portal/types';
import ReferralPageWrapper from '../../_components/referral-page-wrapper';

const SelfReferralIntake = ({ token }: { token: string }) => {
  const router = useRouter();
  const defaultFormData = useMemo(() => getInitialWellBeingAssessmentFormData({ person: 'mom' }), []);
  const [formData, setFormData] = useState(defaultFormData);
  const [currentStepId, setCurrentStepId] = useState<StepId>('basic-information');

  const steps: { id: StepId; name: string }[] = [
    { id: 'basic-information', name: 'Basic Information' },
    { id: 'wellbeing-assessment', name: 'Family Well-Being' },
  ];

  const sendWellnessAssessment = useMutation({
    mutationFn: async () => {
      // Assigning summary fields and calculating scores before submitting the form
      const wellBeingAssessmentFields = getWellBeingAssessmentFields('coordinator');
      const { newWellBeingFormValues } = assignSummaryFieldsAndCalculateScores(formData, wellBeingAssessmentFields);
      const newFormData: WellBeingAssessmentFormData = {
        ...formData,
        wellBeingForm: { ...formData.wellBeingForm, values: newWellBeingFormValues },
      };

      const response = await sfetch(`/v1/wellness-assessment/token/${token}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          basicForm: { values: newFormData.basicForm.values },
          clientForm: { values: newFormData.clientForm.values },
          childForms: newFormData.childForms.map((childForm) => ({ values: childForm.values })),
          childrenForm: { values: newFormData.childrenForm.values },
          wellBeingForm: { values: newFormData.wellBeingForm.values },
        }),
      });
      if (!response.ok) {
        throw new Error('Failed to send the wellness assessment');
      }
    },
  });

  const handleBackStep = () => {
    const currentStepIndex = steps.findIndex((step) => step.id === currentStepId);
    if (currentStepIndex > 0) {
      setCurrentStepId(steps[currentStepIndex - 1].id);
    }
  };

  const handleNextStep = async () => {
    const currentStepIndex = steps.findIndex((step) => step.id === currentStepId);
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepId(steps[currentStepIndex + 1].id);
    } else {
      await sendWellnessAssessment.mutateAsync();
      router.replace('/referral/self-intake/thank-you');
    }
  };

  useEffect(() => {
    (async () => {
      const response = await sfetch(`/v1/wellness-assessment/token/${token}`, {
        method: 'GET',
      });
      if (!response.ok) {
        router.replace('/referral/self-intake/thank-you');
      }
    })();
  }, []);

  return (
    <IntakeStepper
      person='mom'
      steps={steps}
      currentStepId={currentStepId}
      setCurrentStepId={setCurrentStepId}
      onBackStep={handleBackStep}
      onNextStep={handleNextStep}
      wellBeingAssessmentStepLabel='Submit'
      isWellBeingAssessmentStepLoading={sendWellnessAssessment.isPending}
      formData={formData}
      setFormData={setFormData}
    />
  );
};

const SelfReferralIntakePage = ({ params }: { params: { token: string } }) => {
  return (
    <ReferralPageWrapper headerTitle='Family Well Being assessment' variant='wide'>
      <SelfReferralIntake token={params.token} />
    </ReferralPageWrapper>
  );
};

export default SelfReferralIntakePage;
