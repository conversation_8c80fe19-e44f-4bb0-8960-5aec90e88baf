import Image from 'next/image';
import React from 'react';

import ImageTitleDescriptionHeader from '../../../../components/custom/image-title-description-header';

const ThankYou = () => {
  return (
    <div className='flex min-h-screen bg-emaBgPage'>
      <div className='flex flex-col justify-center p-8 md:w-1/2'>
        <div className='flex flex-col items-center'>
          <div className='flex flex-col text-left'>
            <ImageTitleDescriptionHeader
              title='Thank you.'
              description={[
                'Your Family Well-Being Assessment has been completed.',
                'Someone from the ĒMA team will follow up with you in the next 48 hours.',
              ]}
            />
          </div>
        </div>
      </div>
      <div className='relative hidden h-screen w-1/2 md:block'>
        <Image src='/assets/ema-moms.jpg' alt='Thank you' className='object-cover' fill priority />
      </div>
    </div>
  );
};

export default ThankYou;
