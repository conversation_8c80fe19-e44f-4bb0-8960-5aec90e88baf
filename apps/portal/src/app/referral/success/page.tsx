import Image from 'next/image';
import React from 'react';

const SuccessPage: React.FC = () => {
  return (
    <div className='flex h-screen flex-col bg-emaBgPage lg:flex-row'>
      {/* mobile image view */}
      <div className='mobile-header-image-16-9 relative w-full lg:hidden'>
        <Image
          src='/assets/ema-moms-banner-wide.jpg'
          alt='Thank you'
          fill
          sizes='(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
          style={{
            objectFit: 'cover',
          }}
          priority
        />
      </div>

      <main className='flex min-w-[320px] flex-1 shrink basis-0 flex-col'>
        <section className='flex w-full flex-1 flex-col items-center px-8 pt-12 text-base leading-6 max-md:max-w-full max-md:px-5 lg:pt-60'>
          <div className='flex w-[360px] max-w-[360px] flex-col max-sm:px-5'>
            <Image src='/assets/ema-logo.svg' alt='EMA Logo' width={105} height={32} />
            <div className='mt-10 flex w-full flex-col'>
              <h1 className='text-4xl font-semibold leading-10 tracking-tighter text-gray-900'>Thank you.</h1>
              <p className='mt-3'>Your referral has been received.</p>
              <p className='mt-3'>Someone from the ĒMA team will follow up on your request within the next 48 hours.</p>
            </div>
          </div>
        </section>
        <footer className='min-h-[96px] w-full px-8 py-10 text-sm leading-5 max-md:max-w-full max-md:px-5'>
          © ĒMA {new Date().getFullYear()}
        </footer>
      </main>

      {/* desktop image view */}
      <div className='relative hidden md:block md:w-1/2'>
        <Image
          src='/assets/ema-moms-banner-wavy-border.svg'
          alt='Thank you'
          className='h-full'
          fill
          sizes='(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
          style={{
            objectFit: 'cover',
          }}
          priority
        />
      </div>
    </div>
  );
};

export default SuccessPage;
