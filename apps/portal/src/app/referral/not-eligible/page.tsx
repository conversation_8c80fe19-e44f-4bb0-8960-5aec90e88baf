import Link from 'next/link';

import ReferralCardSection from '../_components/referral-card-section';
import ReferralPageWrapper from '../_components/referral-page-wrapper';
import { ineligibleResourcesPageConfig } from '../_lib/ineligible-page.config';

export default function Page(): JSX.Element {
  const { title, description, cardTitle, resourcesList, contactText, contactEmail } = ineligibleResourcesPageConfig;

  return (
    <ReferralPageWrapper headerTitle={title} headerDescription={description}>
      <ReferralCardSection title={cardTitle}>
        <section className='z-10 w-full max-w-full pb-2 text-sm leading-5 text-gray-900'>
          <ul className='list-disc pl-4'>
            {resourcesList.map((item, index) => (
              <li key={index} className='mt-2'>
                <Link
                  href={item.link}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='underline hover:text-emaTextTertiary'
                >
                  <span className='font-semibold'>{item.title}</span>
                </Link>
                {': '}
                {item.description}
              </li>
            ))}
          </ul>
        </section>
      </ReferralCardSection>
      <p className='mt-4 text-base leading-6 text-emaTextTertiary'>
        {contactText}{' '}
        <Link href={`mailto:${contactEmail}`} className='underline hover:text-emaTextTertiary'>
          <span className='font-semibold'>{contactEmail}</span>
        </Link>
        .
      </p>
    </ReferralPageWrapper>
  );
}
