import Link from 'next/link';

import ReferralCardSection from '../_components/referral-card-section';
import ReferralPageWrapper from '../_components/referral-page-wrapper';
import { noAffiliatesResourcesPageConfig } from '../_lib/no-affiliates-page.config';

export default function Page(): JSX.Element {
  const { title, description, cardTitle, resourcesHeader, resourcesList, contactText, contactEmail } =
    noAffiliatesResourcesPageConfig;

  return (
    <ReferralPageWrapper headerTitle={title} headerDescription={description}>
      <ReferralCardSection title={cardTitle}>
        <section className='z-10 w-full max-w-full pb-2 text-sm leading-5 text-gray-900'>
          <p className='mb-4'>{resourcesHeader}</p>
          <ul className='list-disc pl-4'>
            {resourcesList.map((item, index) => (
              <li key={index} className='mt-2'>
                <Link
                  href={item.link}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='underline hover:text-emaTextTertiary'
                >
                  <span className='font-semibold'>{item.title}</span>
                </Link>
                {': '}
                {item.description}
              </li>
            ))}
          </ul>
          <p className='mt-4'>
            {contactText}{' '}
            <Link href={`mailto:${contactEmail}`} className='underline hover:text-emaTextTertiary'>
              <span className='font-semibold'>{contactEmail}</span>
            </Link>
            .
          </p>
        </section>
      </ReferralCardSection>
    </ReferralPageWrapper>
  );
}
