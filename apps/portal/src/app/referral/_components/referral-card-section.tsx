import { Card, CardContent } from '@/components/ui/card';

interface ReferralCardSectionProps {
  title?: string;
  children: React.ReactNode;
}

const ReferralCardSection = ({ title, children }: ReferralCardSectionProps): JSX.Element => {
  return (
    <div className='flex min-w-[240px] flex-1 shrink basis-0 flex-col max-md:max-w-full'>
      {title ? <h2 className='text-sm font-semibold text-emaTextSecondary'>{title}</h2> : null}
      <Card className='mt-4'>
        <CardContent className='p-6'>{children}</CardContent>
      </Card>
    </div>
  );
};

export default ReferralCardSection;
