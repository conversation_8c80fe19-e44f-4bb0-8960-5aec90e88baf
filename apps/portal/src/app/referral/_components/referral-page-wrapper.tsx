import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';
import Image from 'next/image';

interface ReferralPageWrapperProps {
  headerTitle: string;
  headerDescription?: string | string[];
  variant?: 'wide' | 'default';
  children: React.ReactNode;
}

const referralPageWrapperVariants = cva('w-full mx-auto', {
  variants: {
    variant: {
      default: 'max-w-[768px]',
      wide: 'max-w-[964px]',
    },
  },
  defaultVariants: {
    variant: 'default',
  },
});

const ReferralPageWrapper = ({
  headerTitle,
  headerDescription,
  variant,
  children,
}: ReferralPageWrapperProps): JSX.Element => (
  <div className='flex min-h-screen flex-col bg-emaBgPage p-4 md:p-8'>
    <div className={cn(referralPageWrapperVariants({ variant }))}>
      <header className='items-left mb-1 flex flex-col gap-1.5'>
        <Image src='/assets/ema-logo.svg' alt='EMA Logo' className='mb-4' width='130' height='40' />
        <div>
          <h1 className='text-3xl font-semibold text-emaTextPrimary'>{headerTitle}</h1>
          {Array.isArray(headerDescription) ? (
            <div className='flex flex-col gap-1.5 pt-3'>
              {headerDescription.map((item) => (
                <p className='mt-1 text-base leading-6 text-emaTextTertiary' key={item}>
                  {item}
                </p>
              ))}
            </div>
          ) : (
            <p className='mt-1 text-base leading-6 text-emaTextTertiary'>{headerDescription}</p>
          )}
        </div>
      </header>
      {children}
    </div>
  </div>
);

export default ReferralPageWrapper;
