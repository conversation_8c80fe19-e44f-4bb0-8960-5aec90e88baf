'use client';

import { getAffiliatesCommunityReferral, getAffiliatesSelfReferral } from '@/lib/referral';
import { decodeHtmlEntities } from '@/lib/utils';
import type { FieldOptions, FormValues } from '@/types/form-field';
import { YupSchemas } from '@suiteapi/models';
import { type ReactNode, createContext, useContext, useState } from 'react';
import { InferType } from 'yup';

export type Agencies = InferType<typeof YupSchemas.agencySchema>[];

interface ReferralFormContextProps {
  formData: FormValues;
  updateFormData: (data: FormValues) => void;
  resetFormData: () => void;
  setAgencies: (agencyOptions: Agencies | null) => void;
  fetchAffiliateOptions: (referralType: 'self' | 'community', zip: string) => Promise<unknown[]>;
  getDynamicOptions: (optionKey: string) => FieldOptions | null;
}

const ReferralFormContext = createContext<ReferralFormContextProps | undefined>(undefined);

export const useReferralFormContext = (): ReferralFormContextProps => {
  const context = useContext(ReferralFormContext);
  if (!context) {
    throw new Error('useReferralFormContext must be used within a ReferralFormProvider');
  }
  return context;
};

interface ReferralFormProviderProps {
  children: ReactNode;
}

export const ReferralFormProvider = ({ children }: ReferralFormProviderProps): JSX.Element => {
  const [formData, setFormData] = useState<FormValues>({});
  const [dynamicOptions, setDynamicOptions] = useState<{
    /*
     * If the key being queried is undefined, this should indicate a loading state
     * A null value is used to indicate that fetching the options was attempted, but could not be fetched (e.g. due to an API error)
     * A successful fetch with no options returned should result in an empty array
     */
    [key: string]: FieldOptions | null;
  }>({});

  const updateFormData = (data: FormValues): void => {
    setFormData((prevData) => ({ ...prevData, ...data }));
  };

  const resetFormData = (): void => {
    setFormData({});
    setDynamicOptions({});
  };

  const setAgencies = (agencyOptions: Agencies | null): void => {
    if (agencyOptions === null) {
      // API error - enable showing error message
      setDynamicOptions((prev) => ({ ...prev, agencies: null }));
      return;
    }

    const agencies = agencyOptions.map((agency) => ({
      value: String(agency.id),
      label: decodeHtmlEntities(agency.name),
    }));

    setDynamicOptions((prev) => ({ ...prev, agencies }));
  };

  const fetchAffiliateOptions = async (referralType: 'self' | 'community', zip: string): Promise<unknown[]> => {
    // fetch affiliate options based on zip
    const getAffiliates = referralType === 'self' ? getAffiliatesSelfReferral : getAffiliatesCommunityReferral;
    const affiliates = await getAffiliates(zip, 50);

    const affiliateOptions = affiliates.affiliate_locations.map((affiliate) => ({
      value: String(affiliate.id),
      label: decodeHtmlEntities(affiliate.name),
    }));

    setDynamicOptions((prev) => ({ ...prev, [`${referralType}ReferralAffiliateLocations`]: affiliateOptions }));
    return affiliateOptions;
  };

  const getDynamicOptions = (optionKey: string): FieldOptions | null => {
    const options = dynamicOptions[optionKey];
    if (options === null) {
      // API error - return null to indicate error state
      return null;
    }
    return options;
  };

  return (
    <ReferralFormContext.Provider
      value={{ formData, updateFormData, resetFormData, setAgencies, fetchAffiliateOptions, getDynamicOptions }}
    >
      {children}
    </ReferralFormContext.Provider>
  );
};
