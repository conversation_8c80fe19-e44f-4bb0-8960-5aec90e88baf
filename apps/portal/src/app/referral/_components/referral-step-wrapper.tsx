import FormFactory from '@/components/custom/form-factory';
import { toast } from '@/hooks/use-toast';
import { postReferral } from '@/lib/referral';
import { flattenFormFields, getDefaultFormValues } from '@/lib/utils';
import type { FieldConfig, FormActionButtonsConfig, FormValues } from '@/types/form-field';
import { YupSchemas } from '@suiteapi/models';
import omit from 'lodash/omit';
import { useRouter } from 'next/navigation';
import React from 'react';
import { useState } from 'react';
import { AnyObject, AnySchema, ObjectSchema } from 'yup';

import ReferralCardSection from './referral-card-section';
import { useReferralFormContext } from './referral-form-context';

interface FormConfig {
  title: string;
  sideNavLabel?: string;
  fields: FieldConfig[];
  schema: ObjectSchema<Record<string, unknown>> | AnySchema;
}

interface ReferralStepWrapperProps {
  referralType: 'self' | 'community';
  formConfig: FormConfig;
  onPreviousStep: () => void;
  onNextStep: () => void;
  isFirstStep?: boolean;
  isFinalStep?: boolean;
}

const ReferralStepWrapper = ({
  referralType,
  formConfig,
  onPreviousStep,
  onNextStep,
  isFirstStep,
  isFinalStep,
}: ReferralStepWrapperProps): JSX.Element => {
  const [loading, setLoading] = useState(false);

  const isCloudflareTurnstileEnabled = process.env.NEXT_PUBLIC_SHOULD_BYPASS_TURNSTILE_AUTH !== 'true';
  // Even if cloudflareTurnstileEnabled is false, API will not accept an empty string, so a mock default must be used
  const [cloudflareTurnstileToken, setCloudflareTurnstileToken] = useState(isCloudflareTurnstileEnabled ? '' : '12345');
  const { formData, updateFormData, fetchAffiliateOptions, getDynamicOptions } = useReferralFormContext();
  const router = useRouter();

  const actionButtonsConfig: FormActionButtonsConfig = {
    continueLabel: isFinalStep ? 'Submit' : 'Continue',
    cancelLabel: 'Back',
  };

  const handleSaveAndGoToPrevious = (data: FormValues): void => {
    updateFormData(data);
    setCloudflareTurnstileToken('');
    onPreviousStep();
  };

  const handleContinue = async (data: FormValues): Promise<void> => {
    if (data.gender_c === 'male' || data.number_of_children_c === 0) {
      // If referral prospect is male or has no children, redirect to national resources page
      router.push('/referral/not-eligible');
    } else if (data.primary_address_postalcode) {
      setLoading(true);
      try {
        // fetch affiliate options and set field options in context
        const affiliates = await fetchAffiliateOptions(referralType, data.primary_address_postalcode as string);

        if (!affiliates.length) {
          // If no affiliates found within 50 miles, redirect to national resources page
          router.push('/referral/no-affiliates');
        } else {
          updateFormData(data);
          onNextStep();
        }
      } catch (error) {
        toast({
          title: 'Error',
          description: (error as Error).message,
          variant: 'destructive',
        });
        setLoading(false);
      }
    } else {
      updateFormData(data);
      onNextStep();
    }
  };

  // build self referral API request body
  const buildSelfReferralRequest = (referralFields: FormValues): YupSchemas.CreateSelfReferralRequest => ({
    cloudflare_turnstile_token: cloudflareTurnstileToken,
    referral: referralFields as YupSchemas.CommunityReferralSchema,
  });

  // build community referral API request body
  const buildCommunityReferralRequest = (referralFields: FormValues): YupSchemas.CreateCommunityReferralRequest => {
    const documents = referralFields.documents as YupSchemas.DocumentSchema[] | undefined;
    return {
      cloudflare_turnstile_token: cloudflareTurnstileToken,
      referral: omit(referralFields, ['documents']) as YupSchemas.CommunityReferralSchema,
      ...(documents?.length ? { documents: documents } : {}),
    };
  };

  const handleSubmit = async (data: FormValues): Promise<void> => {
    updateFormData(data); // preserve final step input data in case API call fails and user navigates back
    setLoading(true);

    const referralFields = { ...formData, ...data };

    const referralData: YupSchemas.CreateSelfReferralRequest | YupSchemas.CreateCommunityReferralRequest =
      referralType === 'self'
        ? buildSelfReferralRequest(referralFields)
        : buildCommunityReferralRequest(referralFields);

    try {
      await postReferral(referralData, referralType);
      router.push('/referral/success');
    } catch (error) {
      console.error('Error submitting referral form', error);
      setLoading(false);
    }
  };

  const formFieldsWithDynamicOptions = formConfig.fields.map((field: FieldConfig) => {
    if (field.dynamicOptionsKey) {
      return {
        ...field,
        options: getDynamicOptions(field.dynamicOptionsKey),
      };
    }

    return field;
  });

  const handleCloudflareTurnstileToken = (token: string): void => {
    setCloudflareTurnstileToken(token);
  };

  return loading ? (
    <div>Loading...</div>
  ) : (
    <ReferralCardSection title={formConfig.title}>
      <FormFactory
        fields={formFieldsWithDynamicOptions}
        schema={formConfig.schema as ObjectSchema<AnyObject>}
        onSubmit={isFinalStep ? handleSubmit : handleContinue}
        onBack={isFirstStep ? undefined : handleSaveAndGoToPrevious}
        defaultValues={getDefaultFormValues(flattenFormFields(formConfig.fields), formData)}
        actionButtonsConfig={actionButtonsConfig}
        cloudflareTurnstileCallback={isFinalStep ? handleCloudflareTurnstileToken : undefined}
        isCloudflareTurnstileEnabled={isFinalStep && isCloudflareTurnstileEnabled}
        submitDisabled={isFinalStep && !cloudflareTurnstileToken && isCloudflareTurnstileEnabled}
      />
    </ReferralCardSection>
  );
};

export default ReferralStepWrapper;
