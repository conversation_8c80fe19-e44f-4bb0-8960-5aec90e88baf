'use client';

import { Progress } from '@/components/ui/progress';
import { calculateStepProgress } from '@/lib/utils';
import { Fragment, useState } from 'react';

import communityReferralFormsConfig from '../_lib/community-referral-form.config';
import selfReferralFormsConfig from '../_lib/self-referral-form.config';
import ReferralStepWrapper from './referral-step-wrapper';
import StepsNav from './steps-nav';

interface MultiStepReferralFormProps {
  referralType: 'self' | 'community';
}

const MultiStepReferralForm = ({ referralType }: MultiStepReferralFormProps): JSX.Element => {
  const formConfig = referralType === 'self' ? selfReferralFormsConfig : communityReferralFormsConfig;
  const stepKeys = Object.keys(formConfig.steps);
  const navLabels = stepKeys.map((key) => ({
    title: formConfig.steps[key].title,
  }));
  const numSteps = stepKeys.length;
  const [step, setStep] = useState(1);

  return (
    <div className='mt-7'>
      {referralType === 'community' ? (
        <div className='mb-8 hidden md:block'>
          <Progress value={calculateStepProgress(step, numSteps)} className='w-full' />
        </div>
      ) : null}
      <div className='flex flex-col md:flex-row md:space-x-8'>
        {referralType === 'community' ? <StepsNav currentStep={step} steps={navLabels} /> : null}
        <div className='w-full md:w-[768px]'>
          {stepKeys.map((key, index) => (
            <Fragment key={key}>
              {step === index + 1 && (
                <ReferralStepWrapper
                  referralType={referralType}
                  formConfig={formConfig.steps[key]}
                  onNextStep={() => {
                    setStep((prev) => prev + 1);
                  }}
                  onPreviousStep={() => {
                    setStep((prev) => prev - 1);
                  }}
                  isFirstStep={step === 1}
                  isFinalStep={step === numSteps}
                />
              )}
            </Fragment>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MultiStepReferralForm;
