import { YupSchemas } from '@suiteapi/models';
import React from 'react';

interface ReferralServicesInfoProps {
  servicesConfig: YupSchemas.ReferralServicesConfig;
}

const ReferralServicesInfo: React.FC<ReferralServicesInfoProps> = ({ servicesConfig }) => {
  const { overviewText, servicesList, conclusionText } = servicesConfig;

  return (
    <section className='z-10 w-full max-w-full pb-2 text-sm leading-5 text-gray-900'>
      <h3 className='sr-only'>EMA Support Program</h3>
      <p className='mb-4'>{overviewText}</p>
      <ul className='list-disc pl-4'>
        {servicesList.map((item, index) => (
          <li key={index} className='mt-2'>
            {item.title && <span className='font-semibold'>{item.title}: </span>}
            {item.description}
          </li>
        ))}
      </ul>
      {conclusionText ? <p className='mt-4'>{conclusionText}</p> : null}
    </section>
  );
};

export default ReferralServicesInfo;
