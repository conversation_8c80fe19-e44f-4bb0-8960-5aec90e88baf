interface StepsNavProps {
  currentStep: number;
  steps: { title: string; navLabel?: string }[];
}

const StepsNav = ({ currentStep, steps }: StepsNavProps): JSX.Element => {
  return (
    <nav className='mb-4 hidden w-full flex-col text-sm font-semibold leading-5 text-emaTextPrimary md:mb-0 md:flex md:w-[280px]'>
      {steps.map((step, index) => (
        <div
          key={step.title}
          className={`gap-2 self-stretch px-3 py-2 ${index === 0 ? '' : 'mt-1'} w-full ${
            currentStep === index + 1 ? 'border-l-2 border-l-emaBrandSecondary' : 'text-emaTextQuaternary'
          } min-h-[36px]`}
        >
          {step.navLabel || step.title}
        </div>
      ))}
    </nav>
  );
};

export default StepsNav;
