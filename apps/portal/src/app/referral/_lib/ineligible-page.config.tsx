import { nationalResourcesList } from '@/app/referral/_lib/national-resources.config';
import type { NationalResourcesPageConfig } from '@/app/referral/types';

export const ineligibleResourcesPageConfig: NationalResourcesPageConfig = {
  title: 'Not eligible for ĒMA? Here are some other resources!',
  description: [
    'Thank you for submitting a referral to Every Mother’s Advocate. We appreciate your trust in our services and your effort to connect individuals with our organization.',
    'Unfortunately, we are unable to accept this referral, as our services are specifically for biological mothers or primary maternal caregivers.',
    'We understand the importance of receiving support. Below are a few national resources that may be able to assist you:',
  ],
  resourcesList: nationalResourcesList,
  contactText: 'If you have any questions or require further assistance, please do not hesitate to contact us at',
  contactEmail: '<EMAIL>',
};
