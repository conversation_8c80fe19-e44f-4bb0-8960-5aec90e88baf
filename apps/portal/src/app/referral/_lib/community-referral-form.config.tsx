import { communicationPreferenceOptions } from '@/app/portal/[role]/dashboard/admin/_lib/user-management-edit-user-config';
import { getFieldKeysFromFieldConfig } from '@/lib/utils';
import type { FieldConfig } from '@/types/form-field';
import { type ReferralFormConfig } from '@/types/schemas/referral-form-config';
import { YupSchemas } from '@suiteapi/models';
import pick from 'lodash/pick';
import * as yup from 'yup';

import ReferralServicesInfo from '../_components/referral-services-info';

const communityReferralServicesInfo: YupSchemas.ReferralServicesConfig = {
  overviewText:
    'At ĒMA, we’re here to support mothers in crisis by providing them with the tools, resources, and community connections they need to stabilize. Our program pairs each mother with a dedicated advocate and case manager, who offer personalized guidance and access to critical services concurrently, which include:',
  servicesList: [
    {
      title: 'Case Management',
      description: 'Individualized support to navigate crises, set goals, and access resources.',
    },
    {
      title: 'Learning Tracks',
      description: '4-6 month trauma-informed courses including Empowered Parenting or Resilience Roadmap.',
    },
    {
      title: 'Vital Supports',
      description: 'Assistance with navigating housing, childcare, tangible needs, and other essential services.',
    },
    {
      title: 'Community Connection',
      description:
        'Opportunities to participate in workshops, local events, and volunteer activities, fostering a sense of belonging and continued growth.',
    },
  ],
};

const step1Fields: FieldConfig[] = [
  {
    name: 'agency_id',
    label: 'Agency Name',
    type: 'select',
    placeholder: 'Select an agency',
    dynamicOptionsKey: 'agencies',
    // options not defined in field config, will be mapped in dynamically
    bottomDivider: true,
  },
  {
    name: 'name_group',
    type: 'row-group',
    label: 'Your Name',
    subFields: [
      {
        name: 'referring_contact_first_name_c',
        label: '',
        type: 'text',
        placeholder: 'First',
      },
      {
        name: 'referring_contact_last_name_c',
        label: '',
        type: 'text',
        placeholder: 'Last',
      },
    ],
  },
  {
    name: 'referring_contact_email_c',
    label: 'Your Email',
    type: 'email',
    placeholder: 'Enter your email address',
  },
  {
    name: 'referring_contact_phone_c',
    label: 'Your Phone Number',
    type: 'tel',
    placeholder: 'Enter your phone number',
  },
];

const step2Fields: FieldConfig[] = [
  {
    name: 'consent_obtained_c',
    label: 'Consent to share information',
    type: 'checkbox',
    defaultValue: false,
    checkboxLabel:
      'By checking this box, I agree that I received written or verbal consent from my client to disclose the information provided in this referral for the purpose of Every Mother’s Advocate (ĒMA) services to be considered. My client further consents to be contacted via phone by the ĒMA Case Management team upon referral approval.',
  },
  {
    name: 'primary_address_postalcode',
    label: 'Mom’s Zip Code',
    type: 'text',
    placeholder: 'Enter Mom’s Zip Code',
  },
  {
    name: 'name_group',
    type: 'row-group',
    label: 'Mom’s Name',
    subFields: [
      {
        name: 'first_name',
        label: '',
        type: 'text',
        placeholder: 'First',
      },
      {
        name: 'last_name',
        label: '',
        type: 'text',
        placeholder: 'Last',
      },
    ],
  },
  {
    name: 'email1',
    label: 'Email',
    type: 'email',
    placeholder: 'Enter mom’s email address',
  },
  {
    name: 'phone_other',
    label: 'Phone Number',
    type: 'tel',
    placeholder: 'Enter mom’s phone number',
  },
  {
    name: 'preferred_contact_method_c',
    label: 'Preferred Method of Contact',
    type: 'button-radio-group',
    options: communicationPreferenceOptions,
  },
  {
    name: 'gender_c',
    label: 'Gender',
    type: 'button-radio-group',
    options: [
      {
        value: 'male',
        label: 'Male',
      },
      {
        value: 'female',
        label: 'Female',
      },
      {
        value: 'no_response',
        label: 'Prefer not to answer',
      },
    ],
  },
  {
    name: 'caregiver_type_c',
    label: 'Caregiver Type',
    type: 'select',
    options: YupSchemas.caregiverOptions,
    placeholder: 'Select an option',
  },
  {
    name: 'currently_pregnant_c',
    label: 'Is the mom currently pregnant?',
    type: 'button-radio-group',
    options: [
      {
        value: 'yes',
        label: 'Pregnant',
      },
      {
        value: 'no',
        label: 'Not Pregnant',
      },
      {
        value: 'unknown',
        label: 'Unknown',
      },
    ],
  },
  {
    name: 'number_of_children_c',
    label: 'Number of children',
    type: 'number',
    min: 0,
    max: 20,
    placeholder: 'Number of children',
  },
  {
    name: 'supports_court_order_c',
    label: 'Is this referral to support a court-ordered requirement?',
    type: 'button-radio-group',
    options: [
      {
        value: true,
        label: 'Yes',
      },
      {
        value: false,
        label: 'No',
      },
    ],
  },
  {
    name: 'documents',
    label: 'Upload Files',
    type: 'files',
    fileUploadOptions: {
      maxFiles: 4,
      maxSize: 1024 * 1024 * 5,
      accept: {
        'image/*': ['.jpeg', '.png'],
        'application/pdf': ['.pdf'],
        'application/msword': ['.doc', '.docx'],
        'text/plain': ['.txt'],
      },
    },
  },
];

const step3Fields: FieldConfig[] = [
  {
    name: 'affiliate_id',
    label: 'Available Locations',
    type: 'select',
    placeholder: 'Select a location',
    options: [],
    dynamicOptionsKey: 'communityReferralAffiliateLocations',
    bottomContent: <ReferralServicesInfo servicesConfig={communityReferralServicesInfo} />,
  },
  {
    name: 'need_details_c',
    label: 'Reason for Referral',
    type: 'text',
    placeholder: 'Enter a reason for the referral...',
  },
  {
    name: 'what_else_c',
    label: 'Is there anything else you would like us to know?',
    type: 'textarea',
  },
];

const step1FieldKeys = getFieldKeysFromFieldConfig(step1Fields);

const step1FieldSchema = yup.object().shape(pick(YupSchemas.communityReferralApiFieldSchemas, step1FieldKeys));

const step2FieldKeys = getFieldKeysFromFieldConfig(step2Fields);

const step2FieldSchema = yup.object().shape(pick(YupSchemas.communityReferralApiFieldSchemas, step2FieldKeys));

const step3FieldKeys = getFieldKeysFromFieldConfig(step3Fields);

const step3FieldSchema = yup.object().shape(pick(YupSchemas.communityReferralApiFieldSchemas, step3FieldKeys));

const communityReferralFormsConfig: ReferralFormConfig = {
  header: {
    title: 'Community Referral Form',
    description:
      'For all agencies, nonprofits, or churches seeking to connect a mother in crisis to the ĒMA program, please submit a referral here and our team will be in touch with your client as soon as possible.',
  },
  steps: {
    step1: {
      title: 'Agency Details',
      fields: step1Fields,
      schema: step1FieldSchema,
    },
    step2: {
      title: 'Mom Information',
      fields: step2Fields,
      schema: step2FieldSchema,
    },
    step3: {
      title: 'Available Services',
      sideNavLabel: 'Services',
      fields: step3Fields,
      schema: step3FieldSchema,
    },
  },
};

export default communityReferralFormsConfig;
