import { nationalResourcesList } from '@/app/referral/_lib/national-resources.config';
import type { NationalResourcesPageConfig } from '@/app/referral/types';

export const noAffiliatesResourcesPageConfig: NationalResourcesPageConfig = {
  title: 'No Affiliates Found in Your Area',
  description:
    'Thank you for your interest in our services. Unfortunately, we currently do not have available resources within 50 miles of your location. We understand the importance of receiving support, and we’re continuously working to expand our reach.',
  resourcesHeader: 'Here are some national resources that may be able to assist you:',
  resourcesList: nationalResourcesList,
  contactText:
    'We encourage you to check back with us in the future. If you have any questions, please don’t hesitate to reach out to us at',
  contactEmail: '<EMAIL>',
};
