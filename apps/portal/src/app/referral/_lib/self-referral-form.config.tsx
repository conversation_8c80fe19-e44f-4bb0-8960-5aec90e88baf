import { communicationPreferenceOptions } from '@/app/portal/[role]/dashboard/admin/_lib/user-management-edit-user-config';
import { getFieldKeysFromFieldConfig } from '@/lib/utils';
import type { FieldConfig } from '@/types/form-field';
import { type ReferralFormConfig } from '@/types/schemas/referral-form-config';
import { YupSchemas } from '@suiteapi/models';
import pick from 'lodash/pick';
import * as yup from 'yup';

import ReferralServicesInfo from '../_components/referral-services-info';

const selfReferralServicesInfo: YupSchemas.ReferralServicesConfig = {
  overviewText:
    'At ĒMA, we’re here to support you as you navigate the challenges you’re facing. Our program is designed to empower you with tools, resources, and community connections to help you stabilize and grow. You’ll be paired with both a dedicated advocate and a case manager, who will work together to guide you every step of the way, offering personalized support. Here’s what we offer:',
  servicesList: [
    {
      title: 'Case Management',
      description:
        'Your case manager will provide one-on-one support to help you navigate your situation, set meaningful goals, and connect you to helpful resources.',
    },
    {
      title: 'Learning Tracks',
      description:
        'Join a 4-6 month course, like Empowered Parenting or Resilience Roadmap, to gain new skills and insights in a supportive, trauma-informed environment.',
    },
    {
      title: 'Vital Supports',
      description:
        "We'll assist you in navigating options for housing, childcare, and other essential services that may be helpful for your family.",
    },
    {
      title: 'Community Connection',
      description:
        'Be part of workshops, local events, and volunteer activities that offer a sense of belonging and personal growth within a caring community.',
    },
  ],
  conclusionText: 'We’re here to walk alongside you and believe in your ability to overcome challenges and thrive.',
};

const step1Fields: FieldConfig[] = [
  {
    name: 'primary_address_postalcode',
    label: 'Your Zip Code',
    type: 'text',
    placeholder: 'Enter Your Zip Code',
  },
  {
    name: 'name_group',
    type: 'row-group',
    label: 'Your Name',
    subFields: [
      {
        name: 'first_name',
        label: '',
        type: 'text',
        placeholder: 'First',
      },
      {
        name: 'last_name',
        label: '',
        type: 'text',
        placeholder: 'Last',
      },
    ],
  },
  {
    name: 'email1',
    label: 'Email',
    type: 'email',
    placeholder: 'Enter your email address',
  },
  {
    name: 'phone_other',
    label: 'Phone Number',
    type: 'tel',
    placeholder: 'Enter your phone number',
  },
  {
    name: 'preferred_contact_method_c',
    label: 'Preferred Method of Contact',
    type: 'button-radio-group',
    options: communicationPreferenceOptions,
  },
  {
    name: 'gender_c',
    label: 'Gender',
    type: 'button-radio-group',
    options: [
      {
        value: 'male',
        label: 'Male',
      },
      {
        value: 'female',
        label: 'Female',
      },
      {
        value: 'no_response',
        label: 'Prefer not to answer',
      },
    ],
  },
  {
    name: 'caregiver_type_c',
    label: 'Caregiver Type',
    type: 'select',
    options: YupSchemas.caregiverOptions,
    placeholder: 'Select an option',
  },
  {
    name: 'currently_pregnant_c',
    label: 'Are you currently pregnant?',
    type: 'button-radio-group',
    options: [
      {
        value: 'yes',
        label: 'Pregnant',
      },
      {
        value: 'no',
        label: 'Not Pregnant',
      },
      {
        value: 'unknown',
        label: 'Unknown',
      },
    ],
  },
  {
    name: 'number_of_children_c',
    label: 'How many children do you have?',
    type: 'number',
    min: 0,
    max: 20,
    placeholder: 'Number of children',
  },
];

const step2Fields: FieldConfig[] = [
  {
    name: 'affiliate_id',
    label: 'Available Locations',
    type: 'select',
    placeholder: 'Select a location',
    options: [],
    dynamicOptionsKey: 'selfReferralAffiliateLocations',
    bottomContent: <ReferralServicesInfo servicesConfig={selfReferralServicesInfo} />,
  },
  {
    name: 'need_details_c',
    label: 'Reason for Referral',
    type: 'text',
    placeholder: 'Enter a reason for the referral...',
  },
  {
    name: 'what_else_c',
    label: 'Is there anything else you would like us to know?',
    type: 'textarea',
  },
];

const step1FieldKeys = getFieldKeysFromFieldConfig(step1Fields);

const step1FieldSchema = yup.object().shape(pick(YupSchemas.selfReferralApiFieldSchemas, step1FieldKeys));

const step2FieldKeys = getFieldKeysFromFieldConfig(step2Fields);

const step2FieldSchema = yup.object().shape(pick(YupSchemas.selfReferralApiFieldSchemas, step2FieldKeys));

const selfReferralFormsConfig: ReferralFormConfig = {
  header: {
    title: 'Self Referral Form',
    description:
      'Are you a mom in need of support? Please submit this form and someone from the ĒMA Team will contact you within 24-48 hours.',
  },
  steps: {
    step1: {
      title: 'About You',
      fields: step1Fields,
      schema: step1FieldSchema,
    },
    step2: {
      title: 'Available Services',
      fields: step2Fields,
      schema: step2FieldSchema,
    },
  },
};

export default selfReferralFormsConfig;
