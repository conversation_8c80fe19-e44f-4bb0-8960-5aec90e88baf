import ReactSwagger from '@/components/api-doc/react-swagger';
import { getApiDocs } from '@/lib/swagger';

export default async function IndexPage(): Promise<JSX.Element> {
  try {
    const spec = await getApiDocs();
    return (
      <section className='container'>
        <ReactSwagger spec={spec} />
      </section>
    );
  } catch (error) {
    console.error('Failed to load API docs:', error);
    return (
      <section className='container'>
        <p>Error loading API documentation.</p>
      </section>
    );
  }
}
