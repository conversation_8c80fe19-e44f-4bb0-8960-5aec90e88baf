import { Language } from '@/lib/constants';
import { type AvailabilityMatrixFieldConfig, type StandardFieldConfig } from '@/types/form-field';
import { type FormValues } from '@/types/form-field';
import * as yup from 'yup';

// =============================================
// Application Status and Interest Enums
// =============================================
export enum OnboardingStatus {
  Pending = 'Pending',
  Approved = 'Approved',
  Rejected = 'Rejected',
}

export enum Interest {
  Advocate = 'Advocate',
  Volunteer = 'Volunteer',
}

export enum MeetingType {
  Virtual = 'Virtual',
  InPerson = 'InPerson',
}

// =============================================
// Application Input Types
// =============================================
export interface AdvocateApplicationCreateInput {
  // User fields
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth?: Date;
  addressStreet: string;
  addressCity: string;
  addressState: string;
  addressPostalcode: string;

  // Onboarding fields
  referredBy?: string;
  canCompleteBackground?: boolean;
  interests: Interest[];
  hasMultiLang?: boolean;
  languages: { label: string; value: Language }[];
  hasExpCrisis?: boolean;
  hasExpFoster?: boolean;
  hasExpVictims?: boolean;
  hasExpWelfare?: boolean;
  hasExpChildren?: string;
  personalNote?: string;
  groupPref?: string;
  parentingNote?: string;
  availability?: string;
  interviewDate?: Date;
  interviewStartTime?: string;
  interviewEndTime?: string;
  interviewMeetingType?: MeetingType;
  interviewLocation?: string;
  cloudflare_turnstile_token: string;
}

// =============================================
// Availability Types and Utilities
// =============================================
export interface DayAvailability {
  sunday?: TimeSlot[];
  monday?: TimeSlot[];
  tuesday?: TimeSlot[];
  wednesday?: TimeSlot[];
  thursday?: TimeSlot[];
  friday?: TimeSlot[];
  saturday?: TimeSlot[];
}

export const timeSlots = ['morning', 'afternoon', 'evening'] as const;
export type TimeSlot = (typeof timeSlots)[number];

// Map time slots to letters
const timeSlotToLetter: Record<TimeSlot, string> = {
  morning: 'a',
  afternoon: 'b',
  evening: 'c',
};

// Map letters back to time slots
const letterToTimeSlot: Record<string, TimeSlot> = {
  a: 'morning',
  b: 'afternoon',
  c: 'evening',
};

/**
 * Converts availability from form format to database format
 * Format: "0:a,c;1:b;..." where 0-6 are days (Sun-Sat) and a-c are time slots
 */
export function serializeAvailability(availability: DayAvailability): string {
  const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
  const parts = [];

  for (let i = 0; i < days.length; i++) {
    const day = days[i];
    const times = availability[day as keyof DayAvailability];
    if (times && times.length > 0) {
      const timeLetters = times.map((t) => timeSlotToLetter[t]).join(',');
      parts.push(`${i}:${timeLetters}`);
    }
  }

  return parts.join(';');
}

/**
 * Converts availability from database format to form format
 * Expects format: "0:a,c;1:b;..." where 0-6 are days (Sun-Sat) and a-c are time slots
 */
export function deserializeAvailability(availabilityString: string | undefined | null): DayAvailability {
  if (!availabilityString) {
    return {};
  }

  const result: DayAvailability = {};
  const dayParts = availabilityString.split(';');
  const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];

  for (const dayPart of dayParts) {
    if (!dayPart.includes(':')) continue;

    const [dayNum, timeStr] = dayPart.split(':');
    const dayIndex = parseInt(dayNum, 10);

    if (!isNaN(dayIndex) && dayIndex >= 0 && dayIndex < 7 && timeStr) {
      const times = timeStr
        .split(',')
        .map((letter) => letterToTimeSlot[letter])
        .filter(Boolean) as TimeSlot[];
      result[days[dayIndex] as keyof DayAvailability] = times;
    }
  }

  return result;
}

// =============================================
// Form Step Configuration
// =============================================
export type StepKey =
  | 'generalInfo'
  | 'interest'
  | 'personal'
  | 'experience'
  | 'parenting'
  | 'availability'
  | 'agreements';

export type AdvocateFieldConfig = (StandardFieldConfig | AvailabilityMatrixFieldConfig) & {
  step: StepKey;
  layout?: 'horizontal' | 'vertical';
};

// Form step titles and descriptions
export const formConfig = {
  steps: {
    generalInfo: {
      title: 'General Information',
      description: 'Basic contact information and details',
    },
    interest: {
      title: 'Interest',
      description: 'In which ways are you interested in volunteering?\n(Check all that apply)',
    },
    personal: {
      title: 'Address Information',
      description: 'Your address details',
    },
    experience: {
      title: 'Experience',
      description: 'Your relevant experience and background',
    },
    parenting: {
      title: 'Parenting Group Preferences',
      description: 'Your preferences for working with different groups',
    },
    availability: {
      title: 'Availability',
      description: 'Your schedule and time commitment',
    },
    agreements: {
      title: 'Advocate Agreements',
      description: 'Review and accept each agreement and enter your electronic signature below.',
    },
  },
};

// =============================================
// Form Field Configuration
// =============================================
// Fields for each step
export const formStepFields: Record<StepKey, string[]> = {
  generalInfo: ['firstName', 'lastName', 'email', 'phone', 'dateOfBirth'],
  interest: ['interests'],
  personal: ['addressStreet', 'addressCity', 'addressState', 'addressPostalcode'],
  experience: [
    'hasMultiLang',
    'languages',
    'hasExpCrisis',
    'hasExpFoster',
    'hasExpVictims',
    'hasExpWelfare',
    'hasExpChildren',
    'personalNote',
  ],
  parenting: ['groupPref', 'parentingNote'],
  availability: ['availability'],
  agreements: [
    'agreeTerms1',
    'disagreeTerms1',
    'agreeTerms2',
    'disagreeTerms2',
    'agreeTerms3',
    'disagreeTerms3',
    'agreeTerms4',
    'disagreeTerms4',
    'agreeTerms5',
    'disagreeTerms5',
    'signature',
  ],
};

// Field configurations for the entire form
export const advocateApplicationFields: AdvocateFieldConfig[] = [
  // General Information Step
  {
    name: 'firstName',
    label: 'First Name',
    type: 'text',
    required: true,
    step: 'generalInfo',
  },
  {
    name: 'lastName',
    label: 'Last Name',
    type: 'text',
    required: true,
    step: 'generalInfo',
  },
  {
    name: 'email',
    label: 'Email',
    type: 'email',
    required: true,
    step: 'generalInfo',
  },
  {
    name: 'phone',
    label: 'Phone Number',
    type: 'tel',
    required: true,
    step: 'generalInfo',
  },
  {
    name: 'dateOfBirth',
    label: 'Date of Birth',
    type: 'date-dropdown',
    required: true,
    step: 'generalInfo',
    minYears: 18, // Must be at least 18 years old
    maxYears: 100, // Maximum age of 100
    displayFormat: 'MM/dd/yyyy',
    placeholder: 'Select your date of birth',
  },

  // Interest Step
  {
    name: 'interests',
    label: 'Interests',
    type: 'checkbox-group',
    options: Object.values(Interest).map((value) => ({ label: value, value })),
    required: true,
    step: 'interest',
  },

  // Personal Information Step
  {
    name: 'addressStreet',
    label: 'Street Address',
    type: 'text',
    required: true,
    step: 'personal',
  },
  {
    name: 'addressCity',
    label: 'City',
    type: 'text',
    required: true,
    step: 'personal',
  },
  {
    name: 'addressState',
    label: 'State',
    type: 'text',
    required: true,
    step: 'personal',
  },
  {
    name: 'addressPostalcode',
    label: 'Postal Code',
    type: 'text',
    required: true,
    step: 'personal',
  },

  // Experience Step
  {
    name: 'hasMultiLang',
    label: 'Are you multilingual?',
    type: 'checkbox',
    required: false,
    step: 'experience',
  },
  {
    name: 'languages',
    label: 'Languages Spoken',
    type: 'multi-select',
    options: [
      { label: 'English', value: 'english' },
      { label: 'Spanish', value: 'spanish' },
      { label: 'Chinese (Mandarin)', value: 'chinese_mandarin' },
      { label: 'Vietnamese', value: 'vietnamese' },
      { label: 'Tagalog', value: 'tagalog' },
      { label: 'Arabic', value: 'arabic' },
      { label: 'Korean', value: 'korean' },
      { label: 'Russian', value: 'russian' },
      { label: 'French', value: 'french' },
      { label: 'Hindi', value: 'hindi' },
      { label: 'Portuguese', value: 'portuguese' },
      { label: 'Bengali', value: 'bengali' },
      { label: 'Urdu', value: 'urdu' },
      { label: 'German', value: 'german' },
      { label: 'Haitian Creole', value: 'haitian_creole' },
      { label: 'Polish', value: 'polish' },
      { label: 'Italian', value: 'italian' },
      { label: 'Japanese', value: 'japanese' },
      { label: 'Persian/Farsi', value: 'persian_farsi' },
      { label: 'Gujarati', value: 'gujarati' },
      { label: 'Other', value: 'other' },
    ],
    required: false,
    step: 'experience',
    onChange: (values?: FormValues) => {
      if (values && 'languages' in values && Array.isArray(values.languages)) {
        values.languages = values.languages.map((v) => ({
          label: typeof v === 'string' ? v.charAt(0).toUpperCase() + v.slice(1) : v.label,
          value: typeof v === 'string' ? v : v.value,
        }));
      }
    },
  },
  {
    name: 'hasExpCrisis',
    label: 'Do you have crisis support experience?',
    type: 'checkbox',
    required: false,
    step: 'experience',
  },
  {
    name: 'hasExpFoster',
    label: 'Do you have foster care experience?',
    type: 'checkbox',
    required: false,
    step: 'experience',
  },
  {
    name: 'hasExpVictims',
    label: 'Do you have victim support experience?',
    type: 'checkbox',
    required: false,
    step: 'experience',
  },
  {
    name: 'hasExpWelfare',
    label: 'Do you have welfare services experience?',
    type: 'checkbox',
    required: false,
    step: 'experience',
  },
  {
    name: 'hasExpChildren',
    label: 'Describe your experience with children',
    type: 'textarea',
    required: false,
    step: 'experience',
  },
  {
    name: 'personalNote',
    label: 'Additional Notes',
    type: 'textarea',
    required: false,
    step: 'experience',
  },

  // Parenting Step
  {
    name: 'groupPref',
    label: 'Preferred Age Group',
    type: 'select',
    options: [
      { label: 'None', value: 'None' },
      { label: '0-3 years', value: '0-3' },
      { label: '4-9 years', value: '4-9' },
      { label: '10-18 years', value: '10-18' },
    ],
    required: false,
    step: 'parenting',
  },
  {
    name: 'parentingNote',
    label: 'Parenting Notes',
    type: 'textarea',
    required: false,
    step: 'parenting',
  },

  // Availability Step
  {
    name: 'availability',
    label: '',
    type: 'availability-matrix',
    required: true,
    step: 'availability',
    options: {
      days: ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'] as const,
      timeSlots: ['morning', 'afternoon', 'evening'] as const,
    },
    layout: 'horizontal',
  },

  // Agreements Step
  {
    name: 'agreeTerms1',
    label: 'Terms of Service',
    type: 'checkbox',
    required: true,
    step: 'agreements',
    checkboxLabel: 'I agree to the Terms of Service',
  },
  {
    name: 'disagreeTerms1',
    label: '',
    type: 'checkbox',
    required: true,
    step: 'agreements',
    checkboxLabel: 'I do not agree to the Terms of Service',
  },
  {
    name: 'agreeTerms2',
    label: 'Privacy Policy',
    type: 'checkbox',
    required: true,
    step: 'agreements',
    checkboxLabel: 'I agree to the Privacy Policy',
  },
  {
    name: 'disagreeTerms2',
    label: '',
    type: 'checkbox',
    required: true,
    step: 'agreements',
    checkboxLabel: 'I do not agree to the Privacy Policy',
  },
  {
    name: 'agreeTerms3',
    label: 'Code of Conduct',
    type: 'checkbox',
    required: true,
    step: 'agreements',
    checkboxLabel: 'I agree to the Code of Conduct',
  },
  {
    name: 'disagreeTerms3',
    label: '',
    type: 'checkbox',
    required: true,
    step: 'agreements',
    checkboxLabel: 'I do not agree to the Code of Conduct',
  },
  {
    name: 'agreeTerms4',
    label: 'Confidentiality Agreement',
    type: 'checkbox',
    required: true,
    step: 'agreements',
    checkboxLabel: 'I agree to the Confidentiality Agreement',
  },
  {
    name: 'disagreeTerms4',
    label: '',
    type: 'checkbox',
    required: true,
    step: 'agreements',
    checkboxLabel: 'I do not agree to the Confidentiality Agreement',
  },
  {
    name: 'agreeTerms5',
    label: 'Volunteer Agreement',
    type: 'checkbox',
    required: true,
    step: 'agreements',
    checkboxLabel: 'I agree to the Volunteer Agreement',
  },
  {
    name: 'disagreeTerms5',
    label: '',
    type: 'checkbox',
    required: true,
    step: 'agreements',
    checkboxLabel: 'I do not agree to the Volunteer Agreement',
  },
  {
    name: 'signature',
    label: 'Electronic Signature',
    type: 'text',
    required: true,
    step: 'agreements',
    placeholder: 'Type your full name as your electronic signature',
  },
];

// =============================================
// Agreement Configuration
// =============================================
export interface Agreement {
  title: string;
  content: string;
  agreeField: string;
  disagreeField: string;
  agreeText: string;
  disagreeText: string;
}

export const advocateAgreements: Agreement[] = [
  {
    title: 'Termination for Advocates',
    content: `At ĒMA, we value your choice to serve and understand that circumstances may change.

As an advocate, your role is considered at-will, which means you are free to end your volunteer service with ĒMA at any time, with and without reason.

Similarly, ĒMA reserves the right to end your volunteer role at its discretion, with or without cause.`,
    agreeField: 'agreeTerms1',
    disagreeField: 'disagreeTerms1',
    agreeText: 'I have read and agree with the policy as outlined above.',
    disagreeText: 'I have read and DO NOT agree with the policy as outlined above.',
  },
  {
    title: 'Confidentiality',
    content: `At ĒMA, respecting a mother's dignity is at the heart of our mission. Protecting her information is a crucial part of this respect.

As an ĒMA Advocate, you are entrusted with sensitive details about each mother you work with, and it is your responsibility to maintain strict confidentiality.

**What does this mean?**

1. **Safeguarding Information:** All personal details and information obtained during assessments and services are confidential unless disclosure is legally required.

2. **Release of Information:** ĒMA requires a signed release before sharing a mother's information with anyone, except in circumstances mandated by law.

3. **Legal Exceptions to Confidentiality:** Disclosure may be required by law in cases such as:
   * Suspected child or elder abuse/neglect
   * Threats of harm to self or others
   * Court proceedings involving the client or cases related to the parent-child relationship
   * Legal proceedings involving minors or criminal cases under specific conditions

ĒMA's approach to confidentiality aligns with our commitment to each mother's dignity and to providing a safe, trusting environment.`,
    agreeField: 'agreeTerms2',
    disagreeField: 'disagreeTerms2',
    agreeText: 'As an advocate, I will honor and uphold this trust in every interaction.',
    disagreeText: 'I have read and DO NOT agree with the policy as outlined above.',
  },
  {
    title: 'Criminal Background Checks',
    content: `To uphold our commitment to creating a safe and trusting environment for mothers in crisis, ĒMA requires all advocates to complete a criminal background check.

**What does this mean?**

- **Comprehensive Screening:** All new advocates must undergo a criminal background check, which includes fingerprinting, before beginning service.
- **Fit for Service:** ĒMA will review the results with careful consideration, and, at its discretion, determine each advocate's eligibility to serve based on findings from the background check.`,
    agreeField: 'agreeTerms3',
    disagreeField: 'disagreeTerms3',
    agreeText:
      "I understand ĒMA's goal is to ensure that every advocate aligns with ĒMA's standards of trust and integrity, respecting the mothers' dignity and safeguarding their information.",
    disagreeText: 'I have read and DO NOT agree with the policy as outlined above.',
  },
  {
    title: 'Communications',
    content: `At ĒMA, open and honest communication is essential to building trust and ensuring the best support for the mothers in our program. As an advocate, clear communication ensures that each mother receives the most reliable and supportive services possible.

**What does this mean?**

- **Staying Informed**: Please stay up-to-date on all trainings offered by ĒMA. Your awareness and continued learning helps maintain consistent and quality service for our families.
- **Raising Concerns**: If you encounter challenges or issues that may impact your support, promptly communicate these to the designated ĒMA staff member. This proactive approach allows us to address potential disruptions to our services effectively.
- **Timely Documentation**: Any paperwork or documentation required of you must be completed accurately and on time. Detailed records help us coordinate with external agencies and partners, ensuring that each mother receives the support she needs.`,
    agreeField: 'agreeTerms4',
    disagreeField: 'disagreeTerms4',
    agreeText:
      "By following these communication practices, I commit to contribute to ĒMA's efforts of creating respectful, highly relational and dignified environments, ensuring a seamless, compassionate experience for the mothers we support.",
    disagreeText: 'I have read and DO NOT agree with the policy as outlined above.',
  },
  {
    title: 'What You Can Expect From ĒMA',
    content: `ĒMA believes in creating a harmonious working relationship for all its volunteers. In pursuit of this goal, the ĒMA team is committed to the following:

- **Ongoing Support**: Offering ongoing support for volunteers as they support mothers in the program.
- **Engaging Environment**: Providing an exciting, challenging, and rewarding environment and experience.
- **Fair Selection**: Selecting people based on ability, attitude, and character without discrimination.
- **Growth Opportunities**: Providing regular opportunities to increase learning and build relationships with other volunteers.
- **Respect and Dignity**: Respecting each individual's rights and treating all volunteers with dignity, consideration, and respect.
- **Mission Alignment**: Promoting an atmosphere in keeping with ĒMA's vision, mission, and goals.`,
    agreeField: 'agreeTerms5',
    disagreeField: 'disagreeTerms5',
    agreeText: 'I have read and agree with the policy as outlined above.',
    disagreeText: 'I have read and DO NOT agree with the policy as outlined above.',
  },
];

// =============================================
// Form Validation Schemas
// =============================================
// Main form validation schema
export const advocateApplicationSchema = yup
  .object()
  .shape({
    // General Information
    firstName: yup.string().required('First name is required'),
    lastName: yup.string().required('Last name is required'),
    email: yup.string().email('Please enter a valid email').required('Email is required'),
    phone: yup.string().required('Phone number is required'),
    dateOfBirth: yup
      .date()
      .required('Date of birth is required')
      .max(new Date(new Date().setFullYear(new Date().getFullYear() - 18)), 'You must be at least 18 years old'),

    // Interest
    interests: yup
      .array()
      .of(yup.string().oneOf(Object.values(Interest)))
      .nullable(),

    // Personal Information
    addressStreet: yup.string().required('Street address is required'),
    addressCity: yup.string().required('City is required'),
    addressState: yup.string().required('State is required'),
    addressPostalcode: yup.string().required('Postal code is required'),

    // Experience
    hasMultiLang: yup.boolean().default(false),
    languages: yup
      .array()
      .of(
        yup.object().shape({
          label: yup.string().required(),
          value: yup.string().oneOf(Object.values(Language), 'Please select a valid language').required(),
        }),
      )
      .required('Please select at least one language')
      .min(1, 'Please select at least one language')
      .test('multi-lang-check', 'Please select at least two languages if you are multilingual', function (value) {
        const hasMultiLang = this.parent.hasMultiLang;
        if (hasMultiLang && (!value || value.length < 2)) {
          return false;
        }
        return true;
      }),
    hasExpCrisis: yup.boolean().default(false),
    hasExpFoster: yup.boolean().default(false),
    hasExpVictims: yup.boolean().default(false),
    hasExpWelfare: yup.boolean().default(false),
    hasExpChildren: yup.string().optional(),
    personalNote: yup.string().optional(),

    // Parenting
    groupPref: yup.string().optional(),
    parentingNote: yup.string().optional(),

    // Availability
    availability: yup
      .string()
      .required('Please select at least one day and one time slot')
      .test('has-availability', 'Please select at least one day and one time slot', function (value) {
        if (!value) return false;
        const availabilityData = deserializeAvailability(value);
        return Object.values(availabilityData).some((timeSlots) => timeSlots && timeSlots.length > 0);
      }),

    // Agreements
    agreeTerms1: yup.boolean().oneOf([true], 'You must agree to the Terms of Service'),
    disagreeTerms1: yup.boolean().oneOf([false], 'You cannot disagree with the Terms of Service'),
    agreeTerms2: yup.boolean().oneOf([true], 'You must agree to the Privacy Policy'),
    disagreeTerms2: yup.boolean().oneOf([false], 'You cannot disagree with the Privacy Policy'),
    agreeTerms3: yup.boolean().oneOf([true], 'You must agree to the Code of Conduct'),
    disagreeTerms3: yup.boolean().oneOf([false], 'You cannot disagree with the Code of Conduct'),
    agreeTerms4: yup.boolean().oneOf([true], 'You must agree to the Confidentiality Agreement'),
    disagreeTerms4: yup.boolean().oneOf([false], 'You cannot disagree with the Confidentiality Agreement'),
    agreeTerms5: yup.boolean().oneOf([true], 'You must agree to the Volunteer Agreement'),
    disagreeTerms5: yup.boolean().oneOf([false], 'You cannot disagree with the Volunteer Agreement'),
    signature: yup.string().required('Signature is required'),
  })
  .test('has-availability', 'Please select at least one day and one time slot', function (value) {
    if (!value.availability) return false;
    const availabilityData = deserializeAvailability(value.availability);
    return Object.values(availabilityData).some((timeSlots) => timeSlots && timeSlots.length > 0);
  });

// Step-specific validation schemas
type StepSchemaShape = yup.AnyObject;

export const stepSchemas: Record<StepKey, yup.ObjectSchema<StepSchemaShape>> = {
  generalInfo: yup.object().shape({
    firstName: yup.string().required('First name is required'),
    lastName: yup.string().required('Last name is required'),
    email: yup.string().email('Please enter a valid email').required('Email is required'),
    phone: yup.string().required('Phone number is required'),
    dateOfBirth: yup
      .date()
      .required('Date of birth is required')
      .max(new Date(new Date().setFullYear(new Date().getFullYear() - 18)), 'You must be at least 18 years old'),
  }),

  interest: yup.object().shape({
    interests: yup
      .array()
      .of(yup.string().oneOf(Object.values(Interest)))
      .nullable(),
  }),

  personal: yup.object().shape({
    addressStreet: yup.string().required('Street address is required'),
    addressCity: yup.string().required('City is required'),
    addressState: yup.string().required('State is required'),
    addressPostalcode: yup.string().required('Postal code is required'),
  }),

  experience: yup.object().shape({
    hasMultiLang: yup.boolean().default(false),
    languages: yup
      .array()
      .of(
        yup.object().shape({
          label: yup.string().required(),
          value: yup.string().oneOf(Object.values(Language), 'Please select a valid language').required(),
        }),
      )
      .required('Please select at least one language')
      .min(1, 'Please select at least one language')
      .test('multi-lang-check', 'Please select at least two languages if you are multilingual', function (value) {
        const hasMultiLang = this.parent.hasMultiLang;
        if (hasMultiLang && (!value || value.length < 2)) {
          return false;
        }
        return true;
      }),
    hasExpCrisis: yup.boolean().default(false),
    hasExpFoster: yup.boolean().default(false),
    hasExpVictims: yup.boolean().default(false),
    hasExpWelfare: yup.boolean().default(false),
    hasExpChildren: yup.string().optional(),
    personalNote: yup.string().optional(),
  }),

  parenting: yup.object().shape({
    groupPref: yup.string().optional(),
    parentingNote: yup.string().optional(),
  }),

  availability: yup.object().shape({
    availability: yup
      .string()
      .required('Please select at least one day and one time slot')
      .test('has-availability', 'Please select at least one day and one time slot', function (value) {
        if (!value) return false;
        const availabilityData = deserializeAvailability(value);
        return Object.values(availabilityData).some((timeSlots) => timeSlots && timeSlots.length > 0);
      }),
  }),

  agreements: yup.object().shape({
    agreeTerms1: yup.boolean().oneOf([true], 'You must agree to the Terms of Service'),
    disagreeTerms1: yup.boolean().oneOf([false], 'You cannot disagree with the Terms of Service'),
    agreeTerms2: yup.boolean().oneOf([true], 'You must agree to the Privacy Policy'),
    disagreeTerms2: yup.boolean().oneOf([false], 'You cannot disagree with the Privacy Policy'),
    agreeTerms3: yup.boolean().oneOf([true], 'You must agree to the Code of Conduct'),
    disagreeTerms3: yup.boolean().oneOf([false], 'You cannot disagree with the Code of Conduct'),
    agreeTerms4: yup.boolean().oneOf([true], 'You must agree to the Confidentiality Agreement'),
    disagreeTerms4: yup.boolean().oneOf([false], 'You cannot disagree with the Confidentiality Agreement'),
    agreeTerms5: yup.boolean().oneOf([true], 'You must agree to the Volunteer Agreement'),
    disagreeTerms5: yup.boolean().oneOf([false], 'You cannot disagree with the Volunteer Agreement'),
    signature: yup
      .string()
      .required('Signature is required')
      .test(
        'matches-name',
        'Your electronic signature must match your full name (First Name Last Name)',
        function (value) {
          const firstName = this.parent.firstName;
          const lastName = this.parent.lastName;
          if (!firstName || !lastName) return false;
          return value?.trim().toLowerCase() === `${firstName} ${lastName}`.trim().toLowerCase();
        },
      ),
  }),
};

// =============================================
// Field Type Definitions
// =============================================
export interface FieldOptionObj {
  label: string;
  value: string;
}
