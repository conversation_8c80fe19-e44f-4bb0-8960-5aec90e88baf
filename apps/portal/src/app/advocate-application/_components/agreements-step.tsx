import { Card } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { type FormValues } from '@/types/form-field';
import { useState } from 'react';
import ReactMarkdown from 'react-markdown';

import { type Agreement, advocateAgreements } from './advocate-application.config';

interface AgreementsStepProps {
  formData: FormValues & {
    signature?: string;
  };
  onChange: (name: string, value: boolean | string) => void;
  agreements?: Agreement[];
}

export default function AgreementsStep({ formData, onChange, agreements: customAgreements }: AgreementsStepProps) {
  const agreements = customAgreements || advocateAgreements;
  const [signatureTouched, setSignatureTouched] = useState(false);

  const handleAgreementChange = (agreeField: string, disagreeField: string, value: boolean) => {
    onChange(agreeField, value);
    onChange(disagreeField, !value);
  };

  return (
    <div className='space-y-8'>
      {agreements.map((agreement) => (
        <div key={agreement.title} className='space-y-4'>
          <Label className='text-lg font-medium'>{agreement.title}</Label>
          <Card className='w-full bg-white p-4'>
            <div className='markdown-content mb-4 text-sm text-gray-600'>
              <ReactMarkdown>{agreement.content}</ReactMarkdown>
            </div>
            <div className='space-y-2'>
              <div className='flex items-center space-x-2'>
                <Checkbox
                  id={agreement.agreeField}
                  checked={formData[agreement.agreeField] as boolean}
                  onCheckedChange={(checked) =>
                    handleAgreementChange(agreement.agreeField, agreement.disagreeField, checked as boolean)
                  }
                />
                <Label htmlFor={agreement.agreeField}>{agreement.agreeText}</Label>
              </div>
              <div className='flex items-center space-x-2'>
                <Checkbox
                  id={agreement.disagreeField}
                  checked={formData[agreement.disagreeField] as boolean}
                  onCheckedChange={(checked) =>
                    handleAgreementChange(agreement.disagreeField, agreement.agreeField, checked as boolean)
                  }
                />
                <Label htmlFor={agreement.disagreeField}>{agreement.disagreeText}</Label>
              </div>
            </div>
          </Card>
        </div>
      ))}

      <div className='space-y-4'>
        <Label className='text-lg font-medium'>Signature To Acknowledge All Sections</Label>
        <Card className='w-full bg-white p-4'>
          <div>
            <Label htmlFor='signature'>Electronic Signature</Label>
            <Input
              id='signature'
              type='text'
              placeholder='Type your full name as your electronic signature'
              value={formData.signature || ''}
              onChange={(e) => {
                setSignatureTouched(true);
                onChange('signature', e.target.value);
              }}
              className='mt-1'
            />
            {signatureTouched && !formData.signature && (
              <p className='mt-1 text-sm text-red-500'>Please enter your full name as your electronic signature</p>
            )}
            {signatureTouched && formData.signature && formData.signature.trim().split(/\s+/).length < 2 && (
              <p className='mt-1 text-sm text-red-500'>Please enter both your first and last name</p>
            )}
          </div>
        </Card>
      </div>

      {!formData.agreeTerms1 ||
        !formData.agreeTerms2 ||
        !formData.agreeTerms3 ||
        !formData.agreeTerms4 ||
        (!formData.agreeTerms5 && (
          <p className='mt-4 text-sm text-red-500'>Please review and accept all agreements before proceeding.</p>
        ))}
    </div>
  );
}
