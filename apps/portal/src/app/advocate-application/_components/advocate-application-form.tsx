'use client';

import AdvocateStepsNav from '@/app/advocate-application/_components/advocate-steps-nav';
import { AdvocateOnboardingProcess } from '@/components/custom/advocate-onboarding-process';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { Language } from '@/lib/constants';
import { calculateStepProgress, getCalendarLink } from '@/lib/utils';
import { type FieldConfig, type FormFieldValue, type FormValues } from '@/types/form-field';
import { faker } from '@faker-js/faker';
import { formatTime } from '@suiteapi/models';
import { Calendar, Link as LinkIcon, MapPin, User, Video } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';

import {
  Interest,
  MeetingType,
  type <PERSON><PERSON><PERSON>,
  advocateApplication<PERSON>ields,
  formConfig,
  formStepFields,
  stepSchemas,
} from './advocate-application.config';
import AgreementsStep from './agreements-step';
import ScheduleInterviewModal from './schedule-interview-modal';

export interface AdvocateFormValues extends FormValues {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth?: Date | null;
  addressStreet: string;
  addressCity: string;
  addressState: string;
  addressPostalcode: string;
  referredBy?: string;
  canCompleteBackground?: boolean;
  interests: Interest[];
  languages: { label: string; value: Language }[];
  hasExpCrisis?: boolean;
  hasExpFoster?: boolean;
  hasExpVictims?: boolean;
  hasExpWelfare?: boolean;
  hasExpChildren?: string;
  personalNote?: string;
  groupPref?: string;
  parentingNote?: string;
  availability: string;
  agreeTerms1?: boolean;
  disagreeTerms1?: boolean;
  agreeTerms2?: boolean;
  disagreeTerms2?: boolean;
  agreeTerms3?: boolean;
  disagreeTerms3?: boolean;
  agreeTerms4?: boolean;
  disagreeTerms4?: boolean;
  agreeTerms5?: boolean;
  disagreeTerms5?: boolean;
  signature?: string;
  [key: string]: FormFieldValue;
}

interface AdvocateApplicationFormProps {
  turnstileSiteKey?: string;
  shouldBypassTurnstile?: boolean;
  initialData?: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    city: string;
    state: string;
    zip: string;
    referredBy?: string;
    willingBackgroundCheck: 'yes' | 'no';
  };
}

export default function AdvocateApplicationForm({
  turnstileSiteKey,
  shouldBypassTurnstile,
  initialData,
}: AdvocateApplicationFormProps) {
  const [currentStep, setCurrentStep] = useState<StepKey>('generalInfo');
  const [highestStep, setHighestStep] = useState<number>(1);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [isApplicationSubmitted, setIsApplicationSubmitted] = useState(false);
  const [interviewDetails, setInterviewDetails] = useState<{
    date: string;
    startTime: string;
    endTime: string;
    meetingType: string;
    location: string;
  } | null>(null);

  const [formData, setFormData] = useState<AdvocateFormValues>({
    firstName: initialData?.firstName || '',
    lastName: initialData?.lastName || '',
    email: initialData?.email || '',
    phone: initialData?.phone || '',
    addressStreet: '',
    addressCity: initialData?.city || '',
    addressState: initialData?.state || '',
    addressPostalcode: initialData?.zip || '',
    interests: [],
    languages: [],
    availability: '',
    agreeTerms1: false,
    disagreeTerms1: false,
    agreeTerms2: false,
    disagreeTerms2: false,
    agreeTerms3: false,
    disagreeTerms3: false,
    agreeTerms4: false,
    disagreeTerms4: false,
    agreeTerms5: false,
    disagreeTerms5: false,
  });
  const { toast } = useToast();
  const steps = formConfig.steps as Record<StepKey, { title: string; description: string }>;
  const isDev = process.env.NODE_ENV === 'development';

  // Fill form and jump directly to final step
  const fillAndGoToAgreements = () => {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    const sampleData: Partial<AdvocateFormValues> = {
      firstName,
      lastName,
      email: faker.internet.email({ firstName, lastName }),
      phone: faker.phone.number({ style: 'international' }),
      dateOfBirth: faker.date.birthdate({ min: 18, max: 65, mode: 'age' }),
      addressStreet: faker.location.streetAddress(),
      addressCity: faker.location.city(),
      addressState: faker.location.state({ abbreviated: true }),
      addressPostalcode: faker.location.zipCode(),
      referredBy: faker.helpers.arrayElement(['Friend', 'Family', 'Social Media', 'Website', 'Other']),
      canCompleteBackground: faker.datatype.boolean(),
      interests: [Interest.Advocate],
      languages: [{ label: 'English', value: Language.english }],
      hasExpCrisis: faker.datatype.boolean(),
      hasExpFoster: faker.datatype.boolean(),
      hasExpVictims: faker.datatype.boolean(),
      hasExpWelfare: faker.datatype.boolean(),
      hasExpChildren: faker.helpers.arrayElement(['None', 'Some', 'Extensive']),
      personalNote: faker.lorem.paragraph(),
      groupPref: faker.helpers.arrayElement(['None', '0-3', '4-9', '10-18']),
      parentingNote: faker.lorem.sentence(),
      availability: '1:a,b;3:b,c;5:a,c', // Monday morning/afternoon, Wednesday afternoon/evening, Friday morning/evening
      agreeTerms1: true,
      disagreeTerms1: false,
      agreeTerms2: true,
      disagreeTerms2: false,
      agreeTerms3: true,
      disagreeTerms3: false,
      agreeTerms4: true,
      disagreeTerms4: false,
      agreeTerms5: true,
      disagreeTerms5: false,
      signature: `${firstName} ${lastName}`,
    };

    setFormData((prev) => ({
      ...prev,
      ...sampleData,
    }));
    setCurrentStep('agreements');
    setHighestStep(Object.keys(steps).length);
  };

  // Add beforeunload event listener for form exit confirmation
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (!isApplicationSubmitted) {
        e.preventDefault();
        e.returnValue = '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [isApplicationSubmitted]);

  const areButtonsDisabled = () => {
    if (!formData) return true;
    return (
      !formData.agreeTerms1 ||
      formData.disagreeTerms1 ||
      !formData.agreeTerms2 ||
      formData.disagreeTerms2 ||
      !formData.agreeTerms3 ||
      formData.disagreeTerms3 ||
      !formData.agreeTerms4 ||
      formData.disagreeTerms4 ||
      !formData.agreeTerms5 ||
      formData.disagreeTerms5 ||
      !formData.signature
    );
  };

  const handleNext = async (data: FormValues) => {
    // Update form data with the new values
    setFormData((prev) => ({ ...prev, ...data }));

    // Move to the next step
    const stepKeys = Object.keys(steps) as StepKey[];
    const currentIndex = stepKeys.indexOf(currentStep);
    if (currentIndex < stepKeys.length - 1) {
      const nextStep = stepKeys[currentIndex + 1];
      setCurrentStep(nextStep);
      setHighestStep(Math.max(highestStep, currentIndex + 2));
    }
  };

  const handleBack = () => {
    const stepKeys = Object.keys(steps) as StepKey[];
    const currentIndex = stepKeys.indexOf(currentStep);
    if (currentIndex > 0) {
      const prevStep = stepKeys[currentIndex - 1];
      setCurrentStep(prevStep);
    }
  };

  const handleStepClick = (stepIndex: number) => {
    const stepKeys = Object.keys(steps) as StepKey[];
    // Convert from 1-based to 0-based index
    const zeroBasedIndex = stepIndex - 1;
    if (zeroBasedIndex >= 0 && zeroBasedIndex < stepKeys.length) {
      const newStep = stepKeys[zeroBasedIndex];
      setCurrentStep(newStep);
    }
  };

  const handleSchedule = async (interviewData: FormValues) => {
    try {
      // Parse the date string and create a proper Date object
      const interviewDate = new Date(interviewData.date as string);
      if (isNaN(interviewDate.getTime())) {
        throw new Error('Invalid date format');
      }

      // When building submission data for interview scheduling
      const submissionData: FormValues = {
        ...formData,
        interviewDate: interviewDate.toISOString(), // Convert to ISO string
        interviewStartTime: interviewData.startTime,
        interviewEndTime: interviewData.endTime,
        interviewMeetingType: interviewData.meetingType,
        interviewLocation:
          interviewData.meetingType === MeetingType.Virtual
            ? (interviewData.virtualLocation as string)
            : (interviewData.inPersonLocation as string),
      };

      // Only include the token if Turnstile is not bypassed
      if (!shouldBypassTurnstile) {
        submissionData.cloudflare_turnstile_token = interviewData.cloudflare_turnstile_token;
      } else {
        // Provide a dummy token when bypassing Turnstile to satisfy API validation
        submissionData.cloudflare_turnstile_token = 'bypass-token';
      }

      // Submit the combined data
      await onSubmit(submissionData);

      // Then update interview details
      handleInterviewScheduled({
        date: interviewData.date as string,
        startTime: interviewData.startTime as string,
        endTime: interviewData.endTime as string,
        meetingType: interviewData.meetingType as string,
        location:
          interviewData.meetingType === MeetingType.Virtual
            ? (interviewData.virtualLocation as string)
            : (interviewData.inPersonLocation as string),
      });
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      toast({
        title: 'Error',
        description: `Failed to schedule interview: ${message}`,
        variant: 'destructive',
      });
    }
  };

  const handleInterviewScheduled = (details: {
    date: string;
    startTime: string;
    endTime: string;
    meetingType: string;
    location: string;
  }) => {
    setInterviewDetails(details);
    setFormData((prev) => ({
      ...prev,
      interviewDate: new Date(details.date),
      interviewStartTime: details.startTime,
      interviewEndTime: details.endTime,
      interviewMeetingType: details.meetingType,
      interviewLocation: details.location,
    }));
    setShowScheduleModal(false);
  };

  const getFormattedDefaultValues = () => {
    const currentStepFieldNames = formStepFields[currentStep];
    const formattedData: Record<string, string | string[] | boolean | null> = {};

    // First, add all existing form data
    Object.entries(formData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (key === 'dateOfBirth' && value instanceof Date) {
          formattedData[key] = value.toISOString().split('T')[0];
        } else {
          formattedData[key] = value as string | string[] | boolean | null;
        }
      }
    });

    // Then format the current step's fields
    currentStepFieldNames.forEach((fieldName) => {
      const value = formData[fieldName];
      if (fieldName === 'dateOfBirth' && value instanceof Date) {
        formattedData[fieldName] = value.toISOString().split('T')[0];
      } else if (value !== undefined && value !== null) {
        formattedData[fieldName] = value as string | boolean | string[] | null;
      }
    });

    return formattedData;
  };

  const currentStepFields = advocateApplicationFields.filter((field) => field.step === currentStep);

  // Use the step-specific schema from the config file
  const currentStepSchema = stepSchemas[currentStep];

  const handleFieldChange = (name: string, value: boolean | string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Validate signature against the user's name
  const validateSignature = (signature: string | undefined): { isValid: boolean; message?: string } => {
    if (!signature) {
      return {
        isValid: false,
        message: 'Please provide your electronic signature',
      };
    }

    const expectedSignature = `${formData.firstName} ${formData.lastName}`.trim().toLowerCase();
    const providedSignature = signature.trim().toLowerCase();

    if (providedSignature !== expectedSignature) {
      return {
        isValid: false,
        message: 'Your electronic signature must match your full name (First Name Last Name)',
      };
    }

    return { isValid: true };
  };

  const onSubmit = async (data: FormValues) => {
    try {
      // Validate signature matches name
      const validationResult = validateSignature(data.signature as string);
      if (!validationResult.isValid) {
        toast({
          title: validationResult.message?.includes('Missing') ? 'Missing Signature' : 'Invalid Signature',
          description: validationResult.message,
          variant: 'destructive',
        });
        return;
      }

      // Get tracking token from URL
      const searchParams = new URLSearchParams(window.location.search);
      const trackingToken = searchParams.get('t');
      if (!trackingToken) {
        toast({
          title: 'Error',
          description: 'Missing tracking token. Please complete the interest form first.',
          variant: 'destructive',
        });
        return;
      }

      // Create a complete data object with all form fields
      const allData: Record<string, unknown> = {
        ...formData, // Include all existing form data
        ...data, // Override with any new data
        trackingToken, // Add tracking token
        firstName: data.firstName as string,
        lastName: data.lastName as string,
        email: data.email as string,
        phone: data.phone as string,
        // Convert dateOfBirth to ISO string
        dateOfBirth: data.dateOfBirth ? new Date(data.dateOfBirth as string).toISOString() : null,
        addressStreet: data.addressStreet as string,
        addressCity: data.addressCity as string,
        addressState: data.addressState as string,
        addressPostalcode: data.addressPostalcode as string,
        referredBy: data.referredBy as string,
        canCompleteBackground: data.canCompleteBackground as boolean,
        interests: data.interests as Interest[],
        hasMultiLang: (data.languages as { label: string; value: Language }[])?.length > 0,
        languages: (data.languages as { label: string; value: Language }[])?.map((lang) => lang.value),
        hasExpCrisis: data.hasExpCrisis as boolean,
        hasExpFoster: data.hasExpFoster as boolean,
        hasExpVictims: data.hasExpVictims as boolean,
        hasExpWelfare: data.hasExpWelfare as boolean,
        hasExpChildren: data.hasExpChildren as string,
        personalNote: data.personalNote as string,
        groupPref: data.groupPref as string,
        parentingNote: data.parentingNote as string,
        availability: data.availability as string,
        cloudflare_turnstile_token: data.cloudflare_turnstile_token as string,
        // Convert interviewDate to ISO string
        interviewDate: data.interviewDate ? new Date(data.interviewDate as string).toISOString() : null,
        interviewStartTime: data.interviewStartTime as string,
        interviewEndTime: data.interviewEndTime as string,
        interviewMeetingType: data.interviewMeetingType as string,
        interviewLocation: data.interviewLocation as string,
      };

      // Remove agreement terms and signature fields as they're not part of the API DTO
      const {
        agreeTerms1,
        agreeTerms2,
        agreeTerms3,
        agreeTerms4,
        agreeTerms5,
        disagreeTerms1,
        disagreeTerms2,
        disagreeTerms3,
        disagreeTerms4,
        disagreeTerms5,
        signature,
        ...apiData
      } = allData;

      // Ensure all date fields are ISO strings in the final API data
      if (apiData.dateOfBirth instanceof Date) {
        apiData.dateOfBirth = apiData.dateOfBirth.toISOString();
      }
      if (apiData.interviewDate instanceof Date) {
        apiData.interviewDate = apiData.interviewDate.toISOString();
      }

      // Get the userId from the onboarding data
      try {
        const onboardingResponse = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/v1/advocate-onboarding/validate-token?t=${trackingToken}`,
        );
        if (!onboardingResponse.ok) {
          const errorData = await onboardingResponse.json();
          console.error('Onboarding response error:', errorData);
          throw new Error(`Failed to get onboarding data: ${errorData.message || onboardingResponse.statusText}`);
        }
        const onboardingData = await onboardingResponse.json();

        if (!onboardingData.data?.userId) {
          console.error('No userId in onboarding data:', onboardingData);
          throw new Error('No user ID found in onboarding data');
        }

        apiData.userId = onboardingData.data.userId;
      } catch (error) {
        console.error('Error getting user information:', error);
        toast({
          title: 'Error',
          description: error instanceof Error ? error.message : 'Failed to get user information. Please try again.',
          variant: 'destructive',
        });
        return;
      }

      // Submit the form data
      const requestBody = JSON.stringify(apiData);
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/advocate-onboarding/save-application`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: requestBody,
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API response error:', errorData);
        throw new Error(errorData.message || 'Failed to submit application');
      }

      setIsApplicationSubmitted(true);
      toast({
        title: 'Application Submitted',
        description: 'Thank you for your interest in becoming an advocate!',
      });
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      toast({
        title: 'Error',
        description: `Failed to submit application: ${message}`,
        variant: 'destructive',
      });
    }
  };

  return (
    <div className='mt-7'>
      {isDev && !isApplicationSubmitted && (
        <div className='mb-4 flex flex-wrap gap-2'>
          <Button
            variant='outline'
            onClick={fillAndGoToAgreements}
            className='bg-blue-100 text-blue-900 hover:bg-blue-200'
          >
            Fill & Go to Agreements
          </Button>
        </div>
      )}

      <div className='mb-8'>
        <AdvocateOnboardingProcess activeStep={3} />
      </div>

      {!isApplicationSubmitted && (
        <div className='mb-8 hidden md:block'>
          <div className='mb-2 flex items-center justify-between'>
            <span className='text-sm text-gray-600'>Application Progress</span>
            <span className='text-sm font-medium text-emaBrandSecondary'>
              {Math.round(((Object.keys(steps).indexOf(currentStep) + 1) / Object.keys(steps).length) * 100)}%
            </span>
          </div>
          <Progress
            value={calculateStepProgress(Object.keys(steps).indexOf(currentStep) + 1, Object.keys(steps).length)}
            className='w-full'
          />
        </div>
      )}

      <div className='flex flex-col md:flex-row md:space-x-8'>
        {!isApplicationSubmitted && (
          <AdvocateStepsNav
            currentStep={Object.keys(steps).indexOf(currentStep) + 1}
            steps={Object.values(steps).map((step) => ({
              title: step.title,
            }))}
            onStepClick={handleStepClick}
            highestStep={highestStep}
          />
        )}

        <div className='w-full md:w-[768px]'>
          <Card>
            <CardHeader>
              <CardTitle>{isApplicationSubmitted ? 'Thank You!' : steps[currentStep].title}</CardTitle>
            </CardHeader>
            <CardContent>
              {isApplicationSubmitted ? (
                <div className='space-y-4 py-2'>
                  <p className='text-gray-600'>
                    Your application has been submitted successfully. We will review your application and get back to
                    you soon.
                  </p>
                  {interviewDetails ? (
                    <div className='space-y-4 rounded-lg bg-gray-50 p-4'>
                      <div className='flex items-center space-x-3'>
                        <User className='h-5 w-5 text-gray-500' />
                        <div>
                          <p className='text-sm font-medium'>Name</p>
                          <p className='text-sm text-gray-600'>{`${formData.firstName} ${formData.lastName}`}</p>
                        </div>
                      </div>
                      <div className='flex items-center space-x-3'>
                        <Calendar className='h-5 w-5 text-gray-500' />
                        <div>
                          <p className='text-sm font-medium'>Date & Time</p>
                          <p className='text-sm text-gray-600'>
                            {new Date(interviewDetails.date).toLocaleDateString('en-US', {
                              weekday: 'long',
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric',
                            })}
                          </p>
                          <p className='text-sm text-gray-600'>
                            {formatTime(interviewDetails.startTime)} - {formatTime(interviewDetails.endTime)}
                          </p>
                        </div>
                      </div>
                      <div className='flex items-center space-x-3'>
                        <Video className='h-5 w-5 text-gray-500' />
                        <div>
                          <p className='text-sm font-medium'>Meeting Type</p>
                          <p className='text-sm text-gray-600'>{interviewDetails.meetingType}</p>
                        </div>
                      </div>
                      <div className='flex items-center space-x-3'>
                        {interviewDetails.meetingType === 'Virtual' ? (
                          <LinkIcon className='h-5 w-5 text-gray-500' />
                        ) : (
                          <MapPin className='h-5 w-5 text-gray-500' />
                        )}
                        <div>
                          <p className='text-sm font-medium'>
                            {interviewDetails.meetingType === 'Virtual' ? 'Meeting Link' : 'Location'}
                          </p>
                          {interviewDetails.meetingType === 'Virtual' ? (
                            <a
                              href={interviewDetails.location}
                              target='_blank'
                              rel='noopener noreferrer'
                              className='text-sm text-blue-600 hover:underline'
                            >
                              {interviewDetails.location}
                            </a>
                          ) : (
                            <p className='text-sm text-gray-600'>{interviewDetails.location}</p>
                          )}
                        </div>
                      </div>
                      <div className='flex justify-center pt-4'>
                        <Button
                          onClick={() => {
                            const calendarLink = getCalendarLink(
                              interviewDetails.date,
                              interviewDetails.startTime,
                              interviewDetails.endTime,
                              interviewDetails.meetingType,
                              interviewDetails.location,
                              `ĒMA Advocate Interview with ${formData.firstName} ${formData.lastName}`,
                            );

                            // Create a temporary link element to trigger the download
                            const link = document.createElement('a');
                            link.href = calendarLink;
                            link.download = 'ema-interview.ics';
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                          }}
                        >
                          Add to Calendar
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <Button onClick={() => setShowScheduleModal(true)}>Schedule Interview</Button>
                  )}
                  <div className='flex justify-center pt-4'>
                    <Button asChild variant='outline'>
                      <Link href='/'>Return to Home</Link>
                    </Button>
                  </div>
                </div>
              ) : (
                <div className='space-y-6'>
                  {currentStep === 'interest' ? (
                    <div className='space-y-2'>
                      {steps[currentStep].description.split('\n').map((line, index) => (
                        <p key={index} className='text-sm text-gray-600'>
                          {line}
                        </p>
                      ))}
                    </div>
                  ) : (
                    <p className='text-gray-600'>{steps[currentStep].description}</p>
                  )}

                  {currentStep === 'agreements' ? (
                    <AgreementsStep formData={formData} onChange={handleFieldChange} />
                  ) : (
                    <FormFactory
                      fields={currentStepFields as unknown as FieldConfig[]}
                      schema={currentStepSchema}
                      defaultValues={getFormattedDefaultValues()}
                      onSubmit={handleNext}
                      onBack={currentStep === 'generalInfo' ? undefined : handleBack}
                      actionButtonsConfig={{
                        continueLabel: 'Next',
                        backLabel: 'Back',
                      }}
                    />
                  )}

                  {currentStep === 'agreements' && (
                    <div className='mt-6 flex justify-between'>
                      <Button variant='outline' onClick={handleBack}>
                        Back
                      </Button>
                      <Button
                        onClick={() => {
                          // Validate signature before showing schedule popup
                          const validationResult = validateSignature(formData.signature);
                          if (!validationResult.isValid) {
                            toast({
                              title: 'Invalid Signature',
                              description: validationResult.message,
                              variant: 'destructive',
                            });
                            return;
                          }

                          setShowScheduleModal(true);
                        }}
                        disabled={areButtonsDisabled()}
                      >
                        Schedule Interview
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
      <ScheduleInterviewModal
        isOpen={showScheduleModal}
        onClose={() => setShowScheduleModal(false)}
        applicantName={`${formData.firstName} ${formData.lastName}`}
        onSchedule={handleSchedule}
        isApplicationSubmitted={isApplicationSubmitted}
        onInterviewScheduled={handleInterviewScheduled}
        turnstileSiteKey={turnstileSiteKey}
        shouldBypassTurnstile={shouldBypassTurnstile}
      />
    </div>
  );
}
