import { Check } from 'lucide-react';

interface AdvocateStepsNavProps {
  currentStep: number;
  steps: { title: string; navLabel?: string }[];
  onStepClick?: (stepIndex: number) => void;
  highestStep: number;
}

const AdvocateStepsNav = ({ currentStep, steps, onStepClick, highestStep }: AdvocateStepsNavProps): JSX.Element => {
  return (
    <nav className='mb-4 hidden w-full flex-col text-sm font-semibold leading-5 text-emaTextPrimary md:mb-0 md:flex md:w-[280px]'>
      {steps.map((step, index) => {
        const stepNumber = index + 1;
        const isCurrentStep = currentStep === stepNumber;
        return (
          <div
            key={step.title}
            onClick={() => !isCurrentStep && stepNumber <= highestStep && onStepClick?.(stepNumber)}
            className={`gap-2 self-stretch px-3 py-2 ${index === 0 ? '' : 'mt-1'} w-full ${
              isCurrentStep ? 'border-l-2 border-l-emaBrandSecondary' : ''
            } min-h-[36px] ${
              isCurrentStep
                ? 'text-emaTextPrimary'
                : stepNumber <= highestStep
                  ? 'cursor-pointer hover:text-emaTextSecondary'
                  : 'cursor-not-allowed text-emaTextQuaternary opacity-50'
            }`}
          >
            <div className='flex items-center justify-between'>
              {step.navLabel || step.title}
              {stepNumber < highestStep && <Check className='h-4 w-4 text-emaBrandSecondary' />}
            </div>
          </div>
        );
      })}
    </nav>
  );
};

export default AdvocateStepsNav;
