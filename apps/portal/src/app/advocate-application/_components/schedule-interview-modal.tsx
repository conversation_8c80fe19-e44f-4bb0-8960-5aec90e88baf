import { FormFactoryRef } from '@/app/portal/types';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { getCalendarLink } from '@/lib/utils';
import { type FormValues } from '@/types/form-field';
import { formatTime } from '@suiteapi/models';
import { Calendar, Link, MapPin, User, Video } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

import { MeetingType, scheduleInterviewFields, scheduleInterviewSchema } from './schedule-interview.config';

interface ScheduleInterviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  applicantName: string;
  onSchedule: (data: FormValues) => Promise<void>;
  isApplicationSubmitted: boolean;
  onInterviewScheduled: (details: {
    date: string;
    startTime: string;
    endTime: string;
    meetingType: string;
    location: string;
  }) => void;
  turnstileSiteKey?: string;
  shouldBypassTurnstile?: boolean;
}

interface InterviewFormData extends FormValues {
  name: string;
  date: string;
  startTime: string;
  endTime: string;
  meetingType: MeetingType;
  virtualLocation: string;
  inPersonLocation: string;
}

export default function ScheduleInterviewModal({
  isOpen,
  onClose,
  applicantName,
  onSchedule,
  isApplicationSubmitted,
  onInterviewScheduled,
  turnstileSiteKey,
  shouldBypassTurnstile,
}: ScheduleInterviewModalProps) {
  const [formData, setFormData] = useState<InterviewFormData>({
    name: applicantName,
    date: '',
    startTime: '',
    endTime: '',
    meetingType: MeetingType.Virtual,
    virtualLocation: '',
    inPersonLocation: '',
  });
  const lastStartTimeRef = useRef(formData.startTime);
  const formRef = useRef<FormFactoryRef>(null);
  const isCloudflareTurnstileEnabled =
    shouldBypassTurnstile === undefined
      ? process.env.NEXT_PUBLIC_SHOULD_BYPASS_TURNSTILE_AUTH !== 'true'
      : !shouldBypassTurnstile;
  const [cloudflareTurnstileToken, setCloudflareTurnstileToken] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    setFormData((prev) => ({ ...prev, name: applicantName }));
  }, [applicantName]);

  const handleSubmit = async (data: FormValues) => {
    try {
      if (isCloudflareTurnstileEnabled && !cloudflareTurnstileToken) {
        toast({
          title: 'Security Verification Required',
          description: 'Please complete the security verification before scheduling',
          variant: 'destructive',
        });
        return;
      }

      // Create the data object with the appropriate token
      const dataToSubmit = { ...data };

      // Add the real token if Turnstile is enabled, otherwise add a dummy token
      if (isCloudflareTurnstileEnabled) {
        dataToSubmit.cloudflare_turnstile_token = cloudflareTurnstileToken;
      } else {
        // Provide a dummy token when bypassing Turnstile to satisfy API validation
        dataToSubmit.cloudflare_turnstile_token = 'bypass-token';
      }

      await onSchedule(dataToSubmit);
      onInterviewScheduled({
        date: data.date as string,
        startTime: data.startTime as string,
        endTime: data.endTime as string,
        meetingType: data.meetingType as MeetingType,
        location:
          data.meetingType === MeetingType.Virtual
            ? (data.virtualLocation as string)
            : (data.inPersonLocation as string),
      });
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      toast({
        title: 'Error Scheduling Interview',
        description: message,
        variant: 'destructive',
      });
    }
  };

  const handleClose = () => {
    onClose();
  };

  const handleTimeChange = (values: FormValues) => {
    if (values.startTime && typeof values.startTime === 'string' && values.startTime !== lastStartTimeRef.current) {
      lastStartTimeRef.current = values.startTime;

      const [hours, minutes] = values.startTime.split(':');
      const date = new Date();
      date.setHours(parseInt(hours, 10));
      date.setMinutes(parseInt(minutes, 10));
      date.setHours(date.getHours() + 1);
      const endTime = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;

      setFormData({
        ...formData,
        startTime: values.startTime,
        endTime,
      });

      if (formRef.current) {
        formRef.current.form.setValue('endTime', endTime);
      }
      return;
    }

    const typedOtherValues = { ...values } as Partial<InterviewFormData>;
    delete typedOtherValues.startTime;
    delete typedOtherValues.endTime;
    const hasChanges = Object.keys(typedOtherValues).some(
      (key) => typedOtherValues[key as keyof InterviewFormData] !== formData[key as keyof InterviewFormData],
    );

    if (hasChanges) {
      setFormData((prev) => ({ ...prev, ...typedOtherValues }));
    }
  };

  const handleAddToCalendar = () => {
    if (!formData) return;

    const calendarLink = getCalendarLink(
      formData.date,
      formData.startTime,
      formData.endTime,
      formData.meetingType,
      formData.meetingType === 'Virtual' ? formData.virtualLocation : formData.inPersonLocation,
      `ĒMA Advocate Interview with ${applicantName}`,
    );

    // Create a temporary link element to trigger the download
    const link = document.createElement('a');
    link.href = calendarLink;
    link.download = 'ema-interview.ics';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>{isApplicationSubmitted ? 'Interview Scheduled' : 'Schedule Interview'}</DialogTitle>
        </DialogHeader>
        {isApplicationSubmitted ? (
          <div className='space-y-4'>
            <div className='space-y-2'>
              <p className='text-sm text-gray-600'>Interview Scheduled Successfully!</p>
              <div className='space-y-4 rounded-lg bg-gray-50 p-4'>
                <div className='flex items-center space-x-3'>
                  <User className='h-5 w-5 text-gray-500' />
                  <div>
                    <p className='text-sm font-medium'>Name</p>
                    <p className='text-sm text-gray-600'>{formData.name}</p>
                  </div>
                </div>
                <div className='flex items-center space-x-3'>
                  <Calendar className='h-5 w-5 text-gray-500' />
                  <div>
                    <p className='text-sm font-medium'>Date & Time</p>
                    <p className='text-sm text-gray-600'>
                      {new Date(formData.date).toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                      })}
                    </p>
                    <p className='text-sm text-gray-600'>
                      {formatTime(formData.startTime)} - {formatTime(formData.endTime)}
                    </p>
                  </div>
                </div>
                <div className='flex items-center space-x-3'>
                  <Video className='h-5 w-5 text-gray-500' />
                  <div>
                    <p className='text-sm font-medium'>Meeting Type</p>
                    <p className='text-sm text-gray-600'>{formData.meetingType}</p>
                  </div>
                </div>
                <div className='flex items-center space-x-3'>
                  {formData.meetingType === 'Virtual' ? (
                    <Link className='h-5 w-5 text-gray-500' />
                  ) : (
                    <MapPin className='h-5 w-5 text-gray-500' />
                  )}
                  <div>
                    <p className='text-sm font-medium'>
                      {formData.meetingType === 'Virtual' ? 'Meeting Link' : 'Location'}
                    </p>
                    {formData.meetingType === 'Virtual' ? (
                      <a
                        href={formData.virtualLocation}
                        target='_blank'
                        rel='noopener noreferrer'
                        className='text-sm text-blue-600 hover:underline'
                      >
                        {formData.virtualLocation}
                      </a>
                    ) : (
                      <p className='text-sm text-gray-600'>{formData.inPersonLocation}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <DialogFooter className='flex gap-2'>
              <Button variant='outline' onClick={handleClose}>
                Done
              </Button>
              <Button onClick={handleAddToCalendar}>Add to Calendar</Button>
            </DialogFooter>
          </div>
        ) : (
          <FormFactory
            ref={formRef}
            fields={scheduleInterviewFields}
            schema={scheduleInterviewSchema}
            defaultValues={formData}
            onSubmit={handleSubmit}
            onCancel={onClose}
            onChange={handleTimeChange}
            formFieldSubFieldsWrapperClass='flex gap-4'
            formFieldElClass='w-full'
            actionButtonsConfig={{
              continueLabel: 'Schedule',
              backLabel: 'Cancel',
              cancelLabel: 'Cancel',
            }}
            isCloudflareTurnstileEnabled={isCloudflareTurnstileEnabled}
            cloudflareTurnstileCallback={setCloudflareTurnstileToken}
            turnstileSiteKey={turnstileSiteKey}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
