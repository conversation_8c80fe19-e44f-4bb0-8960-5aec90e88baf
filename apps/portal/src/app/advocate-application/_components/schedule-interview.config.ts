import { type FieldConfig, type FormValues } from '@/types/form-field';
import * as yup from 'yup';

export enum MeetingType {
  Virtual = 'Virtual',
  InPerson = 'InPerson',
}

export const scheduleInterviewFields: FieldConfig[] = [
  {
    name: 'name',
    label: 'Name',
    type: 'text',
    required: true,
    disabled: true,
  },
  {
    name: 'date',
    label: 'Date',
    type: 'date',
    required: true,
    minDate: new Date(new Date().setDate(new Date().getDate() + 1)),
  },
  {
    name: 'timeGroup',
    label: 'Time',
    type: 'row-group',
    subFields: [
      {
        name: 'startTime',
        label: 'Start Time',
        type: 'time',
        required: true,
      },
      {
        name: 'endTime',
        label: 'End Time',
        type: 'time',
        required: true,
      },
    ],
  },
  {
    name: 'meetingType',
    label: 'Meeting Type',
    type: 'button-radio-group',
    options: [
      { label: 'Virtual', value: MeetingType.Virtual },
      { label: 'In Person', value: MeetingType.InPerson },
    ],
    required: true,
  },
  {
    name: 'virtualLocation',
    label: 'Meeting Link',
    type: 'text',
    placeholder: 'Enter meeting link (e.g., Zoom, Google Meet)',
    required: true,
    errorMessage: 'Meeting link is required for virtual meetings',
    hidden: (values?: FormValues) => values?.meetingType !== MeetingType.Virtual,
  },
  {
    name: 'inPersonLocation',
    label: 'Meeting Location',
    type: 'text',
    placeholder: 'Enter meeting location',
    required: true,
    errorMessage: 'Meeting location is required for in-person meetings',
    hidden: (values?: FormValues) => values?.meetingType !== MeetingType.InPerson,
  },
];

export const scheduleInterviewSchema = yup.object().shape({
  name: yup.string().required('Name is required'),
  date: yup
    .string()
    .required('Date is required')
    .test('is-future-date', 'Date must be tomorrow or later', function (value) {
      if (!value) return true;
      const selectedDate = new Date(value);
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);
      return selectedDate >= tomorrow;
    }),
  startTime: yup.string().required('Start time is required'),
  endTime: yup
    .string()
    .required('End time is required')
    .test('is-after-start', 'End time must be after start time', function (value) {
      const { startTime } = this.parent;
      if (!value || !startTime) return true;

      // Parse the time strings (format: "HH:MM")
      const [startHours, startMinutes] = startTime.split(':').map(Number);
      const [endHours, endMinutes] = value.split(':').map(Number);

      // Compare hours and minutes directly
      if (endHours < startHours) return false;
      if (endHours === startHours && endMinutes <= startMinutes) return false;

      return true;
    }),
  meetingType: yup.string().oneOf([MeetingType.Virtual, MeetingType.InPerson]).required('Meeting type is required'),
  virtualLocation: yup.string().when('meetingType', {
    is: MeetingType.Virtual,
    then: (schema) => schema.required('Meeting link is required for virtual meetings'),
  }),
  inPersonLocation: yup.string().when('meetingType', {
    is: MeetingType.InPerson,
    then: (schema) => schema.required('Meeting location is required for in-person meetings'),
  }),
});
