'use client';

import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState, Suspense } from 'react';

import AdvocateApplicationForm from './_components/advocate-application-form';

interface OnboardingData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  city: string;
  state: string;
  zip: string;
  referredBy?: string;
  willingBackgroundCheck: 'yes' | 'no';
}

export default function AdvocateApplicationPage() {
  return (
    <Suspense fallback={
      <div className='flex min-h-screen flex-col bg-emaBgPage p-4 md:p-8'>
        <div className='mx-auto w-full max-w-[768px]'>
          <header className='items-left mb-1 flex flex-col gap-1.5'>
            <Image src='/assets/ema-logo.svg' alt='EMA Logo' className='mb-4' width='130' height='40' />
            <div>
              <h1 className='text-3xl font-semibold text-emaTextPrimary'>Advocate Application</h1>
              <p className='mt-2 text-gray-600'>Complete your application to become an ĒMA advocate</p>
            </div>
          </header>
          <div className='flex items-center space-x-3 py-4'>
            <div className='h-8 w-8 animate-spin rounded-full border-4 border-emaBrandSecondary border-t-transparent'></div>
            <p className='text-gray-600'>Loading your application data, please wait...</p>
          </div>
        </div>
      </div>
    }>
      <AdvocateApplicationPageContent />
    </Suspense>
  );
}

function AdvocateApplicationPageContent() {
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [onboardingData, setOnboardingData] = useState<OnboardingData | undefined>(undefined);

  // turnstile site key and is enabled
  const turnstileSiteKey = process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY;
  const shouldBypassTurnstile = process.env.NEXT_PUBLIC_SHOULD_BYPASS_TURNSTILE_AUTH === 'true' || false;

  useEffect(() => {
    const token = searchParams.get('t');
    if (!token) {
      setError('Please complete the interest form first to access the application.');
      setIsLoading(false);
      return;
    }

    const validateToken = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/v1/advocate-onboarding/validate-token?t=${token}`,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        );
        if (!response.ok) {
          throw new Error('Invalid token');
        }
        const data = await response.json();
        if (!data.data) {
          throw new Error('No data received');
        }
        setOnboardingData(data.data);
      } catch (error) {
        setError('Your previous onboarding data could not be found. Please contact us for help.');
      } finally {
        setIsLoading(false);
      }
    };

    validateToken();
  }, [searchParams]);

  if (isLoading) {
    return (
      <div className='flex min-h-screen flex-col bg-emaBgPage p-4 md:p-8'>
        <div className='mx-auto w-full max-w-[768px]'>
          <header className='items-left mb-1 flex flex-col gap-1.5'>
            <Image src='/assets/ema-logo.svg' alt='EMA Logo' className='mb-4' width='130' height='40' />
            <div>
              <h1 className='text-3xl font-semibold text-emaTextPrimary'>Advocate Application</h1>
              <p className='mt-2 text-gray-600'>Complete your application to become an ĒMA advocate</p>
            </div>
          </header>
          <div className='flex items-center space-x-3 py-4'>
            <div className='h-8 w-8 animate-spin rounded-full border-4 border-emaBrandSecondary border-t-transparent'></div>
            <p className='text-gray-600'>Loading your application data, please wait...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='flex min-h-screen flex-col bg-emaBgPage p-4 md:p-8'>
        <div className='mx-auto w-full max-w-[768px]'>
          <header className='items-left mb-1 flex flex-col gap-1.5'>
            <Image src='/assets/ema-logo.svg' alt='EMA Logo' className='mb-4' width='130' height='40' />
            <div>
              <h1 className='text-3xl font-semibold text-emaTextPrimary'>Advocate Application</h1>
              <p className='mt-2 text-gray-600'>Complete your application to become an ĒMA advocate</p>
            </div>
          </header>
          <div className='rounded-lg bg-red-50 p-4'>
            <p className='text-red-600'>{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='flex min-h-screen flex-col bg-emaBgPage p-4 md:p-8'>
      <div className='mx-auto w-full max-w-[768px]'>
        <header className='items-left mb-1 flex flex-col gap-1.5'>
          <Image src='/assets/ema-logo.svg' alt='EMA Logo' className='mb-4' width='130' height='40' />
          <div>
            <h1 className='text-3xl font-semibold text-emaTextPrimary'>Advocate Application</h1>
            <p className='mt-2 text-gray-600'>Complete your application to become an ĒMA advocate</p>
          </div>
        </header>

        <div className='mt-7'>
          <AdvocateApplicationForm
            turnstileSiteKey={turnstileSiteKey}
            shouldBypassTurnstile={shouldBypassTurnstile}
            initialData={onboardingData}
          />
        </div>
      </div>
    </div>
  );
}
