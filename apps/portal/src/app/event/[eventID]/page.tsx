'use client';

import { WorkshopEvent } from '@/app/portal/[role]/dashboard/events/_components/events-table';
import FormFactory from '@/components/custom/form-factory';
import ImageTitleDescriptionHeader from '@/components/custom/image-title-description-header';
import { Button } from '@/components/ui/button';
import { useFindUniqueEvent } from '@/hooks/generated/event';
import { useCreateEventRespondent } from '@/hooks/generated/event-respondent';
import { useCreateNotification } from '@/hooks/generated/notification';
import { toast } from '@/hooks/use-toast';
import { getCalendarLink } from '@/lib/utils';
import { AlertCircle, Calendar, CheckCircle, Loader2, MapPin } from 'lucide-react';
import { useParams } from 'next/navigation';
import React, { useState } from 'react';
import { FieldValues } from 'react-hook-form';

import {
  externalEventRegistrationFormConfig,
  externalEventRegistrationFormSchema,
} from '../_lib/external-event-registration-form.config';

const RegistrationInfoItem = ({ icon, label, value }: { icon: React.ReactNode; label: string; value: string }) => {
  return (
    <div>
      <h2 className='mb-2'>{label}</h2>
      <div className='flex items-center gap-2'>
        {icon}
        <p className='whitespace-pre-line'>{value}</p>
      </div>
    </div>
  );
};

// Notice component to show when event is unavailable
const EventClosedNotice = ({ message, icon }: { message: string; icon: React.ReactNode }) => {
  return (
    <div className='flex min-h-[500px] flex-col items-center justify-center gap-4 p-6 text-center'>
      <div className='mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-amber-100'>{icon}</div>
      <h3 className='text-xl font-semibold'>Registration Closed</h3>
      <p className='max-w-md text-gray-600'>{message}</p>
    </div>
  );
};

// Define the type for route parameters
type EventParams = {
  eventID: string;
};

const EventRegistrationPage = () => {
  const params = useParams<EventParams>();
  const eventID = params.eventID;
  const [isFormSubmitted, setIsFormSubmitted] = useState(false);
  const createEventRespondent = useCreateEventRespondent();
  const createNotification = useCreateNotification();

  const {
    data: eventInformation,
    isLoading,
    error,
  } = useFindUniqueEvent({
    where: { id: eventID },
    include: { eventRespondents: true },
  });

  const handleRegisterForEvent = async (data: FieldValues) => {
    // Prevent duplicate registrations by email (case-insensitive)
    const duplicate = eventInformation?.eventRespondents?.some(
      (attendee) => attendee.email?.toLowerCase() === String(data.email).toLowerCase(),
    );

    if (duplicate) {
      toast({
        title: 'Duplicate Registration',
        description: 'This email address has already registered for the event.',
        variant: 'destructive',
      });
      return;
    }

    // Check if the registration (including children) would exceed the event capacity
    const currentAttendees = calculateTotalSeatsTaken();
    const registrantCount = 1 + parseInt(data.childrenAttending || '0'); // Person + children
    const maxAttendees = eventInformation?.max_attendees || 0;

    if (currentAttendees + registrantCount > maxAttendees) {
      toast({
        title: 'Capacity Exceeded',
        description: `This registration (you + ${data.childrenAttending} children) would exceed the event capacity. 
          Only ${maxAttendees - currentAttendees} seats are available.`,
        variant: 'destructive',
      });
      return;
    }

    try {
      // First create the event respondent so we can link to it
      // The phone validation and transformation is already handled by the schema,
      // so data.phone will already have the +1 prefix removed
      const eventRespondent = await createEventRespondent.mutateAsync({
        data: {
          event_id: eventID,
          name: data.name,
          email: data.email,
          phone_number: data.phone,
          needsTransport: data.needsTransportation === 'yes',
          childrenCount: parseInt(data.childrenAttending),
          hasBeenInvited: true,
          didRsvp: true,
          didCheckin: false,
        },
      });

      // Create notification record linked to the event respondent (not directly to event)
      if (eventRespondent && eventRespondent.id) {
        // Format event date and time for notification
        const eventName = eventInformation?.event_title || '';
        const eventDate = eventInformation?.start_date
          ? new Date(eventInformation.start_date).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })
          : '';
        const eventStartTime = eventInformation?.start_date
          ? new Date(eventInformation.start_date).toLocaleTimeString('en-US', {
              hour: 'numeric',
              minute: '2-digit',
              hour12: true,
            })
          : '';
        const eventEndTime = eventInformation?.end_date
          ? new Date(eventInformation.end_date).toLocaleTimeString('en-US', {
              hour: 'numeric',
              minute: '2-digit',
              hour12: true,
            })
          : '';

        await createNotification.mutateAsync({
          data: {
            template: 'event_registration',
            status: 'pending',
            event_respondent: {
              connect: { id: eventRespondent.id },
            },
            template_params: {
              eventTitle: eventName,
              eventDate: eventDate,
              eventTime: `${eventStartTime}-${eventEndTime}`,
              eventLocation: eventInformation?.location || 'Virtual Event',
              eventJoinUrl: eventInformation?.join_url || '',
              recipientEmail: data.email,
              recipientPhone: data.phone || '',
              recipientFirstName: data.name.split(' ')[0],
              recipientLastName: data.name.split(' ').slice(1).join(' '),
            },
          },
        });
      } else {
        console.error('Failed to create event respondent');
        toast({
          title: 'Error',
          description: 'Failed to complete registration. Please try again.',
          variant: 'destructive',
        });
        return;
      }

      setIsFormSubmitted(true);
      toast({
        title: 'Success',
        description: 'You have successfully registered for the event',
        variant: 'success',
      });
    } catch (error) {
      console.error('Error registering for event:', error);
      toast({
        title: 'Error',
        description: 'Failed to register for the event. Please try again.',
        variant: 'destructive',
      });
    }
  };

  if (isLoading) {
    return (
      <div className='flex h-screen items-center justify-center'>
        <Loader2 className='mr-2 h-6 w-6 animate-spin' /> Loading...
      </div>
    );
  }

  if (error || !eventInformation) {
    return (
      <div className='flex h-screen items-center justify-center'>
        <p className='text-lg text-gray-500'>Event not found or failed to load</p>
      </div>
    );
  }

  // Determine event format based on join_url
  const eventFormat = eventInformation.join_url ? 'virtual' : 'in-person';

  const calculateTotalSeatsTaken = () => {
    if (!eventInformation.eventRespondents) return 0;
    // Count both attendees and their children
    return eventInformation.eventRespondents.reduce((total, attendee) => {
      return total + 1 + (attendee.childrenCount || 0);
    }, 0);
  };

  // Format event information to match WorkshopEvent interface
  const formattedEvent: WorkshopEvent = {
    id: eventInformation.id,
    name: eventInformation.event_title,
    event_title: eventInformation.event_title,
    description: eventInformation.description || '',
    format: eventFormat,
    date: eventInformation.start_date
      ? new Date(eventInformation.start_date).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        })
      : '',
    startTime: eventInformation.start_date
      ? new Date(eventInformation.start_date).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true,
        })
      : '',
    endTime: eventInformation.end_date
      ? new Date(eventInformation.end_date).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true,
        })
      : '',
    location: eventInformation.location || '',
    join_url: eventInformation.join_url || '',
    max_attendees: eventInformation.max_attendees || 0,
    attendees: calculateTotalSeatsTaken(),
    start_date: eventInformation.start_date,
    end_date: eventInformation.end_date,
  };

  // Format the event format for display
  const displayFormat = eventFormat === 'virtual' ? 'Virtual' : 'In-Person';

  // Check if event is in the past
  const isPastEvent = eventInformation.start_date ? new Date(eventInformation.start_date) < new Date() : false;

  // Check if event is within 24 hours
  const now = new Date();
  const eventDate = eventInformation.start_date ? new Date(eventInformation.start_date) : null;
  const hoursUntilEvent = eventDate ? (eventDate.getTime() - now.getTime()) / (1000 * 60 * 60) : 0;
  const isWithin24Hours = eventDate ? hoursUntilEvent > 0 && hoursUntilEvent <= 24 : false;

  // Check if event is full
  const isEventFull =
    formattedEvent.max_attendees > 0 && (formattedEvent.attendees ?? 0) >= formattedEvent.max_attendees;

  // Determine registration status message
  let registrationClosedMessage = '';
  if (isPastEvent) {
    registrationClosedMessage = 'This event has already taken place and registration is closed.';
  } else if (isWithin24Hours) {
    registrationClosedMessage = 'Registration is closed as the event is less than 24 hours away.';
  } else if (isEventFull) {
    registrationClosedMessage = 'This event has reached its maximum capacity. Registration is closed.';
  }

  return (
    <div className='min-h-screen w-full bg-emaBgPage'>
      <div className='mx-auto max-w-[1200px] px-6 py-12'>
        <ImageTitleDescriptionHeader title={formattedEvent.name || ''} />
        <div className='mt-12 flex flex-col gap-8 md:flex-row'>
          <div className='flex-1 space-y-8'>
            <RegistrationInfoItem label='Description' icon={null} value={formattedEvent.description || ''} />

            <RegistrationInfoItem label='Event Format' icon={null} value={displayFormat} />

            <RegistrationInfoItem
              label='Event Start Date / Time'
              icon={<Calendar className='h-5 w-5' />}
              value={`${formattedEvent.date} ${formattedEvent.startTime}-${formattedEvent.endTime}`}
            />

            <div className='flex justify-start'>
              <a
                href='#'
                onClick={async (e) => {
                  e.preventDefault();
                  const calendarLink = await getCalendarLink(
                    formattedEvent.date || new Date().toLocaleDateString(),
                    formattedEvent.startTime || '00:00',
                    formattedEvent.endTime || '00:00',
                    formattedEvent.format === 'virtual' ? 'Virtual' : 'In Person',
                    formattedEvent.format === 'virtual' ? formattedEvent.join_url || '' : formattedEvent.location || '',
                    formattedEvent.name || 'ĒMA Event',
                  );
                  const link = document.createElement('a');
                  link.href = calendarLink;
                  link.download = 'ema-event.ics';
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }}
                className='inline-flex items-center gap-2 rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90'
              >
                <Calendar className='h-4 w-4' />
                Download Calendar Event
              </a>
            </div>

            <RegistrationInfoItem
              label='Event Location'
              icon={<MapPin className='h-5 w-5' />}
              value={formattedEvent.location || ''}
            />

            <RegistrationInfoItem
              label='Max Attendees'
              icon={null}
              value={`${formattedEvent.attendees || 0}/${formattedEvent.max_attendees || 0} (${Math.max(0, (formattedEvent.max_attendees || 0) - (formattedEvent.attendees || 0))} open slots)`}
            />

            <RegistrationInfoItem label='Sign Up Cutoff' icon={null} value='24 hours before event' />
          </div>
          <div className='h-fit min-h-[500px] flex-1 rounded-lg border border-emaBorderPrimary bg-white p-6'>
            {isPastEvent || isEventFull || isWithin24Hours ? (
              <EventClosedNotice
                message={registrationClosedMessage}
                icon={<AlertCircle className='h-8 w-8 text-amber-600' />}
              />
            ) : isFormSubmitted ? (
              <div className='flex min-h-[500px] flex-col items-center justify-center gap-4'>
                <CheckCircle className='h-12 w-12 text-darkGreen' />
                <p className='text-lg font-semibold'>Registration submitted successfully!</p>
              </div>
            ) : (
              <div>
                <h2 className='mb-6 text-lg font-semibold'>Register for {formattedEvent.name}</h2>
                <FormFactory
                  fields={externalEventRegistrationFormConfig}
                  schema={externalEventRegistrationFormSchema}
                  formWrapperClassName='flex-col'
                  labelWrapperClassName='w-full'
                  onSubmit={handleRegisterForEvent}
                  actionButtonsComponent={
                    <div className='flex justify-end'>
                      <Button type='submit' disabled={isLoading}>
                        {isLoading ? (
                          <>
                            <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                            Registering...
                          </>
                        ) : (
                          'Register'
                        )}
                      </Button>
                    </div>
                  }
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventRegistrationPage;
