import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';

export const externalEventRegistrationFormSchema = yup.object().shape({
  name: yup.string().required('Name is required'),
  email: yup.string().email('Invalid email').required('Email is required'),
  phone: yup
    .string()
    .required('Phone number is required')
    .test('phone', 'Please enter a valid phone number (e.g., (*************)', (value) => {
      if (!value) return false; // Phone is required in this form

      // Remove +1 prefix if present
      let testValue = value;
      if (testValue.startsWith('+1')) {
        testValue = testValue.substring(2);
      }

      // Strip any non-numeric characters
      const numbers = testValue.replace(/\D/g, '');

      // Accept either a raw 10-digit number or properly formatted number
      return numbers.length === 10 || /^\(\d{3}\) \d{3}-\d{4}$/.test(testValue);
    })
    .transform((value) => {
      if (!value) return value;

      // Remove +1 prefix if present
      if (value.startsWith('+1')) {
        value = value.substring(2);
      }

      // Strip any non-numeric characters
      const numbers = value.replace(/\D/g, '');

      // If it's a 10-digit number, format it
      if (numbers.length === 10) {
        return `(${numbers.slice(0, 3)}) ${numbers.slice(3, 6)}-${numbers.slice(6)}`;
      }
      return value;
    }),
  needsTransportation: yup.string().oneOf(['yes', 'no']).required('Transportation preference is required'),
  childrenAttending: yup.string().required('Select number of children'),
});

// Generate options for children attending dropdown
const generateChildrenOptions = (max = 10) => {
  return Array.from({ length: max + 1 }, (_, i) => ({
    value: i.toString(),
    label: i.toString(),
  }));
};

export const externalEventRegistrationFormConfig: FieldConfig[] = [
  {
    name: 'name',
    label: 'Your Name',
    type: 'text',
    placeholder: 'Enter your full name',
    required: true,
  },
  {
    name: 'email',
    label: 'Email',
    type: 'email',
    placeholder: 'Enter your email address',
    required: true,
  },
  {
    name: 'phone',
    label: 'Phone Number',
    type: 'tel',
    placeholder: 'Enter your phone number',
    required: true,
  },
  {
    name: 'needsTransportation',
    label: 'Do you need transportation?',
    type: 'button-radio-group',
    options: [
      { value: 'yes', label: 'Yes' },
      { value: 'no', label: 'No' },
    ],
    defaultValue: 'no',
    required: true,
  },
  {
    name: 'childrenAttending',
    label: 'Number of Children Attending',
    type: 'select',
    placeholder: 'Select number of children',
    defaultValue: '0',
    required: true,
    options: generateChildrenOptions(10),
  },
];
