'use client';

import { useState } from 'react';

/*
  TODO: https://servant-io.atlassian.net/browse/EMA-515
  This is just a placeholder page for now. It's a hacked together form to provide functionality to test the forgot password and password reset apis.
  It needs to be updated to:
  1. Conform to the design provided in our Figma document. https://www.figma.com/design/MnCxmDknmPl08NDxK1MwED/Current-Designs-%2F-Wireframes?node-id=4063-126621&t=OqxShdJmtWiWucAC-4
  2. Use turnstile to protect the forgot-password api.
  3. Possibly break the forgot-password experience into its own page, rather than having it as part of the password-reset page.
*/
export default function ResetPasswordPage() {
  const [newPassword, setNewPassword] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    fetch('/api/en_us/reset-password', {
      method: 'POST',
      body: JSON.stringify({ newPassword }),
    });
  };

  return (
    <div className='flex min-h-screen items-center justify-center bg-gray-50'>
      <div className='w-full max-w-md space-y-8 rounded-lg bg-white p-8 shadow-md'>
        <div>
          <h2 className='mt-6 text-center text-3xl font-extrabold text-gray-900'>Reset Password</h2>
        </div>
        <form className='mt-8 space-y-6' onSubmit={handleSubmit}>
          <div>
            <label htmlFor='new-password' className='block text-sm font-medium text-gray-700'>
              New Password
            </label>
            <input
              id='new-password'
              name='new-password'
              type='password'
              required
              className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500'
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              placeholder='Enter your new password'
            />
          </div>
          <div>
            <button
              type='submit'
              className='flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
            >
              Reset Password
            </button>
          </div>
          <div>
            <button
              className='flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
              onClick={() =>
                fetch('/api/anon/forgot-password', {
                  method: 'POST',
                  body: JSON.stringify({ email: '<EMAIL>' }),
                })
              }
            >
              Forgot Password
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
