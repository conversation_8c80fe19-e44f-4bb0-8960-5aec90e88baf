import Image from 'next/image';

import AdvocateInterestForm from './_components/advocate-interest-form';

export default function AdvocateInterestPage() {
  // turnstile site key and is enabled
  const turnstileSiteKey = process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY;
  const shouldBypassTurnstile = process.env.NEXT_PUBLIC_SHOULD_BYPASS_TURNSTILE_AUTH === 'true' || false;

  return (
    <div className='flex min-h-screen flex-col bg-emaBgPage p-4 md:p-8'>
      <div className='mx-auto w-full max-w-[768px]'>
        <header className='items-left mb-1 flex flex-col gap-1.5'>
          <Image src='/assets/ema-logo.svg' alt='EMA Logo' className='mb-4' width='130' height='40' />
          <div>
            <h1 className='text-3xl font-semibold text-emaTextPrimary'>Advocate Interest Form</h1>
            <p className='mt-2 text-gray-600'>Tell us about yourself and your interest in becoming an advocate.</p>
          </div>
        </header>

        <div className='mt-7'>
          <AdvocateInterestForm turnstileSiteKey={turnstileSiteKey} shouldBypassTurnstile={shouldBypassTurnstile} />
        </div>
      </div>
    </div>
  );
}
