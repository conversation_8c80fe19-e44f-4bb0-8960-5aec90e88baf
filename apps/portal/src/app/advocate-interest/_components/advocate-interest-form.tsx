'use client';

import { FormFactoryRef } from '@/app/portal/types';
import { AdvocateOnboardingProcess } from '@/components/custom/advocate-onboarding-process';
import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { type FormValues } from '@/types/form-field';
import { faker } from '@faker-js/faker';
import Link from 'next/link';
import { useCallback, useEffect, useRef, useState } from 'react';
import { type FieldValues } from 'react-hook-form';



import { advocateInterestFields, advocateInterestSchema } from './advocate-interest.config';





interface AdvocateInterestFormProps {
  turnstileSiteKey?: string;
  shouldBypassTurnstile?: boolean;
  onSubmit?: (values: FieldValues) => void;
}

export interface AdvocateInterestFormValues extends FormValues {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  city: string;
  state: string;
  zip: string;
  referredBy: string;
  willingBackgroundCheck: 'yes' | 'no';
  affiliateId: string;
  [key: string]: FormValues[keyof FormValues];
}

export default function AdvocateInterestForm({
  turnstileSiteKey,
  shouldBypassTurnstile,
  onSubmit,
}: AdvocateInterestFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [trackingToken, setTrackingToken] = useState<string | null>(null);
  const [_isSearchingAffiliates, setIsSearchingAffiliates] = useState(false);
  const [_affiliateOptions, setAffiliateOptions] = useState<Array<{ label: string; value: string }>>([]);
  const [formFields, setFormFields] = useState(advocateInterestFields);
  const { toast } = useToast();
  const isCloudflareTurnstileEnabled =
    shouldBypassTurnstile === undefined
      ? process.env.NEXT_PUBLIC_SHOULD_BYPASS_TURNSTILE_AUTH !== 'true'
      : !shouldBypassTurnstile;
  const [cloudflareTurnstileToken, setCloudflareTurnstileToken] = useState('');
  const isDev = process.env.NODE_ENV === 'development';
  const formRef = useRef<FormFactoryRef>(null);

  const searchAffiliates = useCallback(
    async (zipCode: string) => {
      if (!zipCode || zipCode.length !== 5) {
        toast({
          title: 'Invalid Zip Code',
          description: 'Please enter a valid 5-digit zip code.',
          variant: 'destructive',
        });
        return;
      }

      setIsSearchingAffiliates(true);
      let affiliates = [];

      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/affiliate?zip=${zipCode}&within=50`);

        if (!response.ok) {
          throw new Error('Failed to fetch affiliates');
        }

        const data = await response.json();
        affiliates = data.affiliate_locations || [];

        if (affiliates.length === 0) {
          toast({
            title: 'No Affiliates Found',
            description: 'No affiliates found within 50 miles of the entered zip code.',
            variant: 'destructive',
          });
          setAffiliateOptions([]);

          // Reset the affiliate dropdown when no affiliates are found
          setFormFields((prevFields) =>
            prevFields.map((field) => {
              if (field.name === 'affiliateId') {
                return {
                  ...field,
                  options: [],
                  disabled: true,
                  required: true, // Keep required for validation, but disabled prevents interaction
                  placeholder: 'No affiliates found in your area',
                  key: `affiliateId-empty-${Date.now()}`, // Force re-render
                };
              }
              return field;
            }),
          );

          // Also clear any previously selected affiliate value in the form
          if (formRef.current) {
            formRef.current.form.setValue('affiliateId', '');
            formRef.current.form.clearErrors('affiliateId');
            // Force trigger form validation to update UI
            formRef.current.form.trigger('affiliateId');
          }
        } else {
          const options = affiliates.map((affiliate: any) => ({
            label: affiliate.name,
            value: affiliate.id,
          }));
          setAffiliateOptions(options);

          // Update the form fields to enable the affiliate dropdown and populate options
          setFormFields((prevFields) =>
            prevFields.map((field) => {
              if (field.name === 'affiliateId') {
                return {
                  ...field,
                  options: options,
                  disabled: false,
                  required: true,
                  placeholder: 'Select an affiliate',
                  key: `affiliateId-populated-${Date.now()}`, // Force re-render
                };
              }
              return field;
            }),
          );

          // Clear any previously selected affiliate value when new search results come in
          if (formRef.current) {
            formRef.current.form.setValue('affiliateId', '');
            formRef.current.form.clearErrors('affiliateId');
            // Force trigger form validation to update UI
            formRef.current.form.trigger('affiliateId');
          }

          toast({
            title: 'Affiliates Found',
            description: `Found ${affiliates.length} affiliate(s) in your area.`,
          });
        }
      } catch (error) {
        console.error('Error searching affiliates:', error);

        // Clear affiliate options and reset dropdown on error
        setAffiliateOptions([]);
        setFormFields((prevFields) =>
          prevFields.map((field) => {
            if (field.name === 'affiliateId') {
              return {
                ...field,
                options: [],
                disabled: true,
                required: true, // Keep required for validation, but disabled prevents interaction
                placeholder: 'Error loading affiliates',
                key: `affiliateId-error-${Date.now()}`, // Force re-render
              };
            }
            return field;
          }),
        );

        // Clear any previously selected affiliate value
        if (formRef.current) {
          formRef.current.form.setValue('affiliateId', '');
          formRef.current.form.clearErrors('affiliateId');
          formRef.current.form.trigger('affiliateId');
        }

        toast({
          title: 'Search Failed',
          description: 'Failed to search for affiliates. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsSearchingAffiliates(false);
      }
    },
    [toast],
  );

  const fillWithSampleData = () => {
    const sampleData = {
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      email: faker.internet.email(),
      phone: faker.phone.number({ style: 'international' }),
      city: faker.location.city(),
      state: faker.location.state({ abbreviated: true }),
      zip: faker.location.zipCode(),
      referredBy: faker.helpers.arrayElement(['Friend', 'Family', 'Social Media', 'Website', 'Other']),
      willingBackgroundCheck: faker.helpers.arrayElement(['yes', 'no']),
    };

    // Set the form values using the form ref
    if (formRef.current) {
      Object.entries(sampleData).forEach(([key, value]) => {
        formRef.current?.form.setValue(key, value);
      });
    }
  };

  // Watch for changes to the zip code field and automatically search for affiliates
  useEffect(() => {
    if (!formRef.current) return;

    const subscription = formRef.current.form.watch((value, { name }) => {
      if (name === 'zip' && value.zip && typeof value.zip === 'string' && value.zip.length === 5) {
        // Automatically search for affiliates when a valid 5-digit zip code is entered
        searchAffiliates(value.zip);
      }
    });

    return () => subscription.unsubscribe();
  }, [searchAffiliates]);

  // Force form field updates when formFields change
  useEffect(() => {
    if (!formRef.current) return;

    // Find the affiliate field in the updated formFields
    const affiliateField = formFields.find((field) => field.name === 'affiliateId');
    if (affiliateField) {
      // Force the form to re-render the affiliate field by triggering validation
      formRef.current.form.trigger('affiliateId');
    }
  }, [formFields]);

  const handleTurnstileCallback = (token: string) => {
    setCloudflareTurnstileToken(token);
  };

  const handleSubmit = async (values: FieldValues) => {
    setIsSubmitting(true);
    try {
      if (isCloudflareTurnstileEnabled && !cloudflareTurnstileToken) {
        toast({
          title: 'Security Verification Required',
          description: 'Please complete the security verification before submitting',
          variant: 'destructive',
        });
        setIsSubmitting(false);
        return;
      }

      // Check if affiliate is required but not selected
      const affiliateField = formFields.find((field) => field.name === 'affiliateId');
      if (!affiliateField?.disabled && (!values.affiliateId || values.affiliateId === '')) {
        toast({
          title: 'Affiliate Required',
          description: 'Please select an affiliate before submitting the form.',
          variant: 'destructive',
        });
        setIsSubmitting(false);
        return;
      }

      // Check if no affiliates are available (field is disabled)
      if (affiliateField?.disabled) {
        toast({
          title: 'No Affiliates Available',
          description: 'Please enter a valid zip code to find available affiliates in your area.',
          variant: 'destructive',
        });
        setIsSubmitting(false);
        return;
      }

      // Create the data object with the appropriate token, excluding affiliateZip
      const { affiliateZip, ...submissionData } = values;
      const dataToSubmit = { ...submissionData };
      if (isCloudflareTurnstileEnabled) {
        dataToSubmit.cloudflare_turnstile_token = cloudflareTurnstileToken;
      } else {
        // Provide a dummy token when bypassing Turnstile to satisfy API validation
        dataToSubmit.cloudflare_turnstile_token = 'bypass-token';
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/advocate-onboarding/save-interest`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dataToSubmit),
      });

      if (!response.ok) {
        throw new Error('Failed to submit interest form');
      }

      const data = await response.json();
      setTrackingToken(data.trackingToken);
      setIsSubmitted(true);
      if (onSubmit) {
        onSubmit(values);
      }
      toast({
        title: 'Success',
        description: 'Thank you for your interest in becoming an advocate!',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to submit interest form. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className='space-y-6'>
      <AdvocateOnboardingProcess activeStep={1} />
      {isSubmitted ? (
        <Card>
          <CardContent className='pt-6'>
            <div className='space-y-4'>
              <h1 className='text-2xl font-semibold'>Thanks for your interest in becoming an advocate!</h1>
              <p className='text-emaTextSecondary'>
                Thank you for your interest in supporting mothers through our ĒMA program! A team member will reach out
                within 48 business hours with your next steps.
              </p>
              <p className='text-emaTextSecondary'>
                Up next is to watch the training videos. You will be sent an email with further instructions.
              </p>
              <p className='text-sm text-gray-500'>Questions? Contact us at ema.org</p>
              {/* TODO: remove this button */}
              {trackingToken && (
                <div className='mt-6'>
                  <p className='mb-2 text-sm italic text-gray-500'>
                    Note: This direct link to the application is temporary for testing and will be removed.
                  </p>
                  <Button asChild>
                    <Link href={`/advocate-application?t=${trackingToken}`}>Continue to Application</Link>
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className='pt-6'>
            <div className='mb-6'>
              <h2 className='text-xl font-semibold'>Tell Us About Yourself</h2>
              <p className='mt-1 text-sm text-gray-600'>
                Please fill out the form below to express your interest in becoming an advocate.
              </p>
            </div>
            {isDev && (
              <div className='mb-4'>
                <Button
                  variant='outline'
                  onClick={fillWithSampleData}
                  className='bg-blue-100 text-blue-900 hover:bg-blue-200'
                >
                  Fill with Test Data
                </Button>
              </div>
            )}
            <FormFactory
              ref={formRef}
              fields={formFields}
              schema={advocateInterestSchema}
              onSubmit={handleSubmit}
              actionButtonsConfig={{
                continueLabel: 'Complete',
              }}
              submitDisabled={isSubmitting}
              formFieldElClass='pl-[10px]'
              turnstileSiteKey={turnstileSiteKey}
              isCloudflareTurnstileEnabled={isCloudflareTurnstileEnabled}
              cloudflareTurnstileCallback={handleTurnstileCallback}
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
}