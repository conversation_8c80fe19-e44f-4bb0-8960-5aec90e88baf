import { type StandardFieldConfig } from '@/types/form-field';
import * as yup from 'yup';

// =============================================
// Form Field Configuration
// =============================================
export const advocateInterestFields: StandardFieldConfig[] = [
  // Personal Information
  {
    name: 'name',
    label: 'Your Name',
    type: 'row-group',
    subFields: [
      {
        name: 'firstName',
        label: '',
        placeholder: 'First',
        type: 'text',
        required: true,
      },
      {
        name: 'lastName',
        label: '',
        placeholder: 'Last',
        type: 'text',
        required: true,
      },
    ],
  },
  {
    name: 'contact',
    label: 'Contact Information',
    type: 'row-group',
    subFields: [
      {
        name: 'email',
        label: '',
        placeholder: 'Email',
        type: 'email',
        required: true,
      },
      {
        name: 'phone',
        label: '',
        placeholder: 'Phone Number',
        type: 'tel',
        required: true,
      },
    ],
  },
  {
    name: 'location',
    label: 'Location',
    type: 'row-group',
    subFields: [
      {
        name: 'city',
        label: '',
        placeholder: 'City',
        type: 'text',
        required: true,
      },
      {
        name: 'state',
        label: '',
        placeholder: 'State',
        type: 'text',
        required: true,
      },
      {
        name: 'zip',
        label: '',
        placeholder: 'Zip Code',
        type: 'text',
        required: true,
      },
    ],
  },
  {
    name: 'referredBy',
    label:
      'How did you hear about us? If it was through a church, community group, or organization, please specify which one.',
    type: 'text',
    required: true,
  },
  {
    name: 'willingBackgroundCheck',
    label: 'Are you willing to complete a background check?',
    type: 'radio',
    required: true,
    options: [
      { label: 'Yes', value: 'yes' },
      { label: 'No', value: 'no' },
    ],
  },
  {
    name: 'affiliateId',
    label: 'Select Affiliate',
    type: 'select',
    required: true,
    options: [],
    placeholder: 'Enter your zip code above to see nearby affiliates',
    disabled: true,
    description: 'Affiliates will be automatically loaded based on your zip code',
  },
];

// =============================================
// Form Validation Schema
// =============================================
export const advocateInterestSchema = yup.object().shape({
  firstName: yup.string().required('First name is required'),
  lastName: yup.string().required('Last name is required'),
  email: yup.string().email('Please enter a valid email').required('Email is required'),
  phone: yup.string().required('Phone number is required'),
  city: yup.string().required('City is required'),
  state: yup.string().required('State is required'),
  zip: yup.string().required('Zip code is required'),
  referredBy: yup.string(),
  willingBackgroundCheck: yup
    .string()
    .oneOf(['yes', 'no'])
    .required('Please indicate if you are willing to complete a background check'),
  affiliateId: yup.string(),
});