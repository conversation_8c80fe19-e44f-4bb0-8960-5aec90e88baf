import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';
import { YupSchemas } from '@suiteapi/models';

export const resetPasswordFormFields: FieldConfig[] = [
  {
    name: 'username',
    label: 'Username',
    type: 'text',
    placeholder: 'Enter your username',
  },
  {
    name: 'password',
    label: 'Create new password',
    type: 'password',
    placeholder: 'Enter your password',
  },
  {
    name: 'confirmPassword',
    label: 'Confirm password',
    type: 'password',
    placeholder: 'Enter your password again',
  },
];

export const resetPasswordFormSchema = yup.object().shape({
  username: yup.string().required('Username is required'),
  password: YupSchemas.passwordSchema,
  confirmPassword: yup
    .string()
    .required('Confirm password is required')
    .oneOf([yup.ref('password')], 'Passwords must match'),
});
