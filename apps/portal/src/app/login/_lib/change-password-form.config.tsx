import { FieldConfig } from '@/types/form-field';
import * as yup from 'yup';
import { YupSchemas } from '@suiteapi/models';

export const changePasswordFormFields: FieldConfig[] = [
  {
    name: 'currentPassword',
    label: 'Current password',
    type: 'password',
    placeholder: 'Enter your current password',
  },
  {
    name: 'password',
    label: 'Create new password',
    type: 'password',
    placeholder: 'Enter your password',
  },
  {
    name: 'confirmPassword',
    label: 'Confirm password',
    type: 'password',
    placeholder: 'Enter your password again',
  },
];

export const changePasswordFormSchema = yup.object().shape({
  currentPassword: yup.string().required('Current password is required'),
  password: YupSchemas.passwordSchema,
  confirmPassword: yup
    .string()
    .required('Confirm password is required')
    .oneOf([yup.ref('password')], 'Passwords must match'),
});
