import type { FieldConfig } from '@/types/form-field';

export const loginFormFields: FieldConfig[] = [
  {
    name: 'username',
    label: 'Username',
    type: 'text',
    placeholder: 'Enter your username',
  },
  {
    name: 'password',
    label: 'Password',
    type: 'password',
    placeholder: '••••••••',
  },
];

export const loginErrorMessages = {
  INVALID_CREDENTIALS: 'Invalid username or password.',
  NO_MATCHING_ROLES: 'You do not have access to this portal. Contact your administrator for more information.',
  GENERIC_LOGIN_ERROR: 'There was an error logging in.',
} as const;
