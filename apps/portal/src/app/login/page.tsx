'use client';

import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { useUserInfo } from '@/context/user-info';
import { storeAccessToken } from '@/lib/accessTokenStorageService';
import type { FormValues } from '@/types/form-field';
import { loginFormSchema } from '@/types/schemas/auth';
import { EmaAuthTokenDto } from '@suiteapi/models';
import { jwtDecode } from 'jwt-decode';
import { ArrowRightIcon } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import LoginHeader from '../../components/custom/image-title-description-header';
import { loginErrorMessages, loginFormFields } from './_lib/login-form.config';

export default function LoginPage(): JSX.Element {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);
  const { setRoles, setUserData } = useUserInfo();
  const { INVALID_CREDENTIALS, NO_MATCHING_ROLES, GENERIC_LOGIN_ERROR } = loginErrorMessages;

  async function handleLogin(data: FormValues): Promise<void> {
    setLoginError(null);
    setIsSubmitting(true);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
        credentials: 'include', // Including credentials is important here to receive the refresh token cookie from the login call.
      });

      if (response.ok) {
        const { access_token } = await response.json();
        const decodedToken = jwtDecode<EmaAuthTokenDto>(access_token);

        storeAccessToken(access_token);
        setRoles(decodedToken.roles);
        setUserData(decodedToken);

        let role;

        if (decodedToken.roles.includes('supervisor')) {
          role = 'supervisor';
        } else if (decodedToken.roles.includes('coordinator')) {
          role = 'coordinator';
        } else if (decodedToken.roles.includes('advocate')) {
          role = 'advocate';

          // TODO: Prospective advocate solution not designed yet from backend/SuiteCRM perspective - refactor if necessary
        } else if (decodedToken.roles.includes('prospective-advocate')) {
          role = 'prospective-advocate';
        } else {
          role = '';
        }
        if (role) {
          // Note: The redirect needs to be delayed slightly so that the cookie gets set first as part of the current rendering cycle.
          setTimeout(() => {
            window.location.href = `/portal/${role}/dashboard`;
          }, 500);
        } else {
          setLoginError(NO_MATCHING_ROLES);
          setIsSubmitting(false);
        }
      } else {
        const errorMessage = response.status === 401 ? INVALID_CREDENTIALS : GENERIC_LOGIN_ERROR;
        setLoginError(errorMessage);
        setIsSubmitting(false);
      }
    } catch (error) {
      setLoginError(GENERIC_LOGIN_ERROR);
      setIsSubmitting(false);
    }
  }

  useEffect(() => {
    // clear React context and browser session storage on page load
    setRoles([]);
    setUserData(null);
    sessionStorage.clear();
  }, [setRoles, setUserData]);

  return (
    <section className='flex flex-row'>
      <div className='flex h-screen flex-1 flex-col items-center justify-center gap-4'>
        <div className='flex max-w-[360px] flex-col gap-4 px-6'>
          <LoginHeader
            title='Log in to your account'
            description={[`Welcome back! Enter your details below and let's continue the journey together.`]}
            alignment='left'
          />
          <FormFactory
            fields={loginFormFields}
            schema={loginFormSchema}
            onSubmit={handleLogin}
            actionButtonsComponent={
              <div className='flex flex-col gap-4'>
                <Button
                  className='bg-emaBrandPrimary hover:bg-emaBrandPrimaryHover'
                  type='submit'
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Signing in...' : 'Sign in'}
                </Button>
                <Button
                  onClick={() => router.push('/login/forgot-password')}
                  variant='link'
                  type='button'
                  className='justify-start pl-0 text-left text-sm font-semibold'
                >
                  Forgot Password?
                </Button>
                <Button
                  onClick={() => router.push('/login/change-password')}
                  variant='link'
                  type='button'
                  className='-mt-4 justify-start pl-0 text-left text-sm font-semibold'
                >
                  Change Password <ArrowRightIcon className='ml-2 h-4 w-4' />
                </Button>

                {loginError ? <Label className='text-destructive'>{loginError}</Label> : null}
              </div>
            }
          />
        </div>
      </div>

      <div className='relative ml-auto hidden md:block md:w-1/2'>
        <Image
          src='/assets/ema-moms.jpg'
          alt='Thank you'
          className='h-full'
          fill
          sizes='(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
          style={{
            objectFit: 'cover',
          }}
          priority
        />
      </div>
    </section>
  );
}
