import FormFactory from '@/components/custom/form-factory';
import { But<PERSON> } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { removeAccessToken } from '@/lib/accessTokenStorageService';
import { sfetch } from '@/lib/sfetch';
import { type FormValues } from '@/types/form-field';
import { ArrowLeftIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';
import { useState } from 'react';
import { type FieldValues, type SubmitHandler } from 'react-hook-form';

import { changePasswordFormFields, changePasswordFormSchema } from '../../_lib/change-password-form.config';

const ChangePassword = ({ actionItemsButton }: { actionItemsButton?: React.ReactNode }) => {
  const router = useRouter();
  const [formValues, setFormValues] = useState({ password: '', confirmPassword: '' });

  const handleResetPassword: SubmitHandler<FieldValues> = async (values) => {
    try {
      const resp = await sfetch('/v1/auth/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword: values.currentPassword,
          newPassword: values.password,
        }),
      });

      if (!resp.ok) {
        throw new Error('Failed to change password');
      }

      const data = await resp.json();

      if (!data.wasSuccessful) {
        throw new Error('Failed to change password');
      }

      removeAccessToken();
      sessionStorage.clear();

      toast({
        title: 'Success',
        description: 'Your password has been successfully changed',
        variant: 'success',
      });

      setTimeout(() => {
        router.push('/login');
      }, 1000);
    } catch (error) {
      console.error(error);
      toast({
        title: 'Error',
        description: 'There was an error changing your password. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleFormChange = (values: FormValues) => {
    setFormValues(values as { password: string; confirmPassword: string });
  };

  const handleBackToLogin = () => {
    router.push('/login');
  };
  const interactedWithForm = formValues.password || formValues.confirmPassword;
  const passwordsMatch = formValues.password && formValues.password === formValues.confirmPassword;
  return (
    <FormFactory
      fields={changePasswordFormFields}
      schema={changePasswordFormSchema}
      onSubmit={handleResetPassword}
      onChange={handleFormChange}
      actionButtonsComponent={
        actionItemsButton ? (
          actionItemsButton
        ) : (
          <div className='flex flex-col gap-4'>
            {interactedWithForm && !passwordsMatch ? (
              <p className='text-center font-semibold text-destructive'>Passwords do not match</p>
            ) : null}
            <Button type='submit' disabled={!passwordsMatch}>
              Change password
            </Button>
            <Button variant='link' type='button' onClick={handleBackToLogin}>
              <ArrowLeftIcon className='mr-2 h-4 w-4' /> Back to login
            </Button>
          </div>
        )
      }
    />
  );
};

export default ChangePassword;
