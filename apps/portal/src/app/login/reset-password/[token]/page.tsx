'use client';

import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { sfetch } from '@/lib/sfetch';
import { type FormValues } from '@/types/form-field';
import { useMutation } from '@tanstack/react-query';
import { ArrowLeftIcon } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';

import LoginHeader from '../../../../components/custom/image-title-description-header';
import { resetPasswordFormFields, resetPasswordFormSchema } from '../../_lib/reset-password-form-fields';

interface ResetPasswordFormValues extends FormValues {
  password: string;
  confirmPassword: string;
}

const ResetPasswordPage = () => {
  const { token } = useParams();

  const router = useRouter();
  const [formValues, setFormValues] = useState<ResetPasswordFormValues>({ password: '', confirmPassword: '' });

  const handleResetPassword = (data: FormValues) => handleResetPasswordMutation(data as ResetPasswordFormValues);

  const { mutate: handleResetPasswordMutation } = useMutation({
    mutationFn: async (data: FormValues) => {
      const { password, confirmPassword, username } = data as ResetPasswordFormValues;

      const response = await sfetch(`/v1/auth/forgot-reset-password`, {
        method: 'POST',
        body: JSON.stringify({ password, confirmPassword, username, token }),
        headers: {
          'Content-Type': 'application/json',
        },
      });
      if (!response.ok) {
        throw new Error(response.statusText);
      }
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Your password has been successfully reset',
        variant: 'success',
      });

      setTimeout(() => {
        router.push('/login');
      }, 3000);
    },
    onError: (error) => {
      console.error('Error resetting password:', error);
      toast({
        title: 'Error',
        description: 'There was an error resetting your password. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const handleFormChange = (values: FormValues) => {
    setFormValues(values as ResetPasswordFormValues);
  };

  const handleBackToLogin = () => {
    router.push('/login');
  };

  const interactedWithForm = formValues.password || formValues.confirmPassword;
  const passwordsMatch = formValues.password && formValues.password === formValues.confirmPassword;

  return (
    <div className='flex h-full w-full justify-center'>
      <div className='mt-20 flex h-screen max-w-[400px] flex-1 flex-col gap-4 px-6'>
        <LoginHeader title='Enter a new password' />
        <FormFactory
          fields={resetPasswordFormFields}
          schema={resetPasswordFormSchema}
          onSubmit={handleResetPassword}
          onChange={handleFormChange}
          actionButtonsComponent={
            <div className='flex flex-col gap-4'>
              {interactedWithForm && !passwordsMatch ? (
                <p className='text-center font-semibold text-destructive'>Passwords do not match</p>
              ) : null}
              <Button type='submit' disabled={!passwordsMatch}>
                Reset password
              </Button>
              <Button variant='link' type='button' onClick={handleBackToLogin}>
                <ArrowLeftIcon className='mr-2 h-4 w-4' /> Back to login
              </Button>
            </div>
          }
        />
      </div>
    </div>
  );
};

export default ResetPasswordPage;
