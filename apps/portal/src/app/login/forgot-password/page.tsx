'use client';

import FormFactory from '@/components/custom/form-factory';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { sfetch } from '@/lib/sfetch';
import { type FormValues } from '@/types/form-field';
import { useMutation } from '@tanstack/react-query';
import { ArrowLeftIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';

import LoginHeader from '../../../components/custom/image-title-description-header';
import { resetForgotPasswordFormFields } from '../_lib/reset-forgot-password-form.config';
import { resetForgotPasswordFormSchema } from '../_lib/reset-forgot-password-form.config';

const ForgotPasswordPage = () => {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [linkSent, setLinkSent] = useState(false);

  const { mutate: handleResetForgotPassword } = useMutation({
    mutationFn: async (data: FormValues) => {
      setIsSubmitting(true);
      const { username } = data;

      const response = await sfetch('/v1/auth/forgot-password', {
        method: 'POST',
        body: JSON.stringify({ username }),
        headers: {
          'Content-Type': 'application/json',
        },
      });
      if (!response.ok) {
        throw new Error('Failed to send password reset link');
      }
      return response.json();
    },
    onSuccess: () => {
      setIsSubmitting(false);
      setLinkSent(true);
      toast({
        title: 'Success',
        description: 'A password reset link has been successfully sent to your email',
        variant: 'success',
      });
    },
    onError: (error) => {
      console.error(error);
      setIsSubmitting(false);
      toast({
        title: 'Error',
        description: 'There was an error sending the password reset link. Please try again.',
        variant: 'destructive',
      });
    },
  });

  return (
    <div className='flex h-full w-full justify-center'>
      <div className='mt-20 flex h-screen max-w-[400px] flex-1 flex-col gap-4 px-6'>
        {linkSent ? (
          <>
            <LoginHeader
              title='Link Sent'
              description={["You're almost back in!", 'Check your email for the next steps to reset your password.']}
            />
            <Button variant='link' type='button' onClick={() => router.push('/login')}>
              <ArrowLeftIcon className='mr-2 h-4 w-4' /> Back to login
            </Button>
          </>
        ) : (
          <LoginHeader
            title='Forgot password?'
            description={[
              "Let's make sure you can stay connected. Enter your username, and we'll help you get back in.",
            ]}
          />
        )}
        {!linkSent ? (
          <FormFactory
            fields={resetForgotPasswordFormFields}
            schema={resetForgotPasswordFormSchema}
            onSubmit={(data) => handleResetForgotPassword(data)}
            actionButtonsComponent={
              <div className='flex flex-col gap-4'>
                <Button type='submit' disabled={isSubmitting}>
                  {isSubmitting ? 'Sending...' : 'Reset password'}
                </Button>
                <Button variant='link' type='button' onClick={() => router.push('/login')}>
                  <ArrowLeftIcon className='mr-2 h-4 w-4' /> Back to login
                </Button>
              </div>
            }
          />
        ) : null}
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
