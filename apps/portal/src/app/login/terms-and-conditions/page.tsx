'use client';

import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { sfetch } from '@/lib/sfetch';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import LoginHeader from '../../../components/custom/image-title-description-header';

export default function TermsAndConditionsPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handleAccept = async () => {
    setIsSubmitting(true);
    try {
      const response = await sfetch('/v1/auth/accept-terms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error('Failed to accept terms');
      }
      
      // Redirect to dashboard
      const data = await response.json();
      const role = data.roles?.[0] || 'user';
      router.push(`/portal/${role}/dashboard`);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to accept terms and conditions',
        variant: 'destructive',
      });
      setIsSubmitting(false);
    }
  };
  
  const handleDecline = async () => {
    // Log the user out
    try {
      await sfetch('/v1/auth/logout', {
        method: 'POST',
      });
      router.push('/login');
    } catch (error) {
      console.error('Error logging out:', error);
      router.push('/login');
    }
  };

  return (
    <div className='flex h-full w-full justify-center'>
      <div className='mt-10 flex max-w-[800px] flex-1 flex-col gap-6 px-6'>
        <LoginHeader
          title='Terms and Conditions'
          description={['Please review and accept our terms and conditions to continue.']}
        />
        
        <div className='rounded-md border border-gray-200 bg-white p-6 shadow-sm'>
          <div className='max-h-[400px] overflow-y-auto mb-6 text-sm'>
            <h2 className='text-lg font-semibold mb-4'>Terms of Service</h2>
            <p className='mb-4'>
              Welcome to Trellis! By accessing or using our platform, you agree to be bound by these Terms of Service.
            </p>
            
           <h3 className='text-md font-semibold mb-4'>Privacy Policy</h3>
            <p className='mb-4'>
              Your privacy is important to us. Please review our Privacy Policy for more information.
            </p>
            <p className='mb-4'>
            Bacon ipsum dolor amet burgdoggen cupim kielbasa turducken frankfurter venison tri-tip pork chop beef shoulder pig t-bone corned beef biltong buffalo. Ball tip alcatra chislic brisket swine bresaola. Rump flank tri-tip prosciutto porchetta, landjaeger t-bone venison picanha doner beef ribs meatloaf shank short ribs. Brisket short loin pork pork loin, bacon shoulder shankle pork chop burgdoggen strip steak leberkas meatloaf jerky jowl. Bacon porchetta salami alcatra. Buffalo cow tail, pancetta kielbasa tenderloin jerky pork loin shankle t-bone strip steak sirloin tri-tip salami. Andouille drumstick pork loin filet mignon picanha.

            Swine prosciutto fatback filet mignon. Tongue boudin buffalo bresaola pork chop. Jowl biltong kevin prosciutto drumstick pastrami. Tenderloin pork loin ball tip drumstick, tri-tip pastrami shankle short ribs bresaola t-bone pig. Tongue ham turducken ham hock fatback.
            </p>
          </div>
          
          <div className='flex flex-col gap-4 sm:flex-row sm:justify-end'>
            <Button 
              variant='outline' 
              onClick={handleDecline}
              disabled={isSubmitting}
            >
              Decline & Logout
            </Button>
            <Button 
              className='bg-emaBrandPrimary hover:bg-emaBrandPrimaryHover'
              onClick={handleAccept}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Processing...' : 'Accept & Continue'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}