'use client';

import AssessmentWrapper from '@/app/portal/[role]/dashboard/people/moms/[id]/assessments/_components/assessment-wrapper';
import { postAssessmentFormsConfig } from '@/app/portal/[role]/dashboard/people/moms/[id]/assessments/post-assessment/_lib/post-assessment-form.config';
import { FormValues } from '@/types/form-field';
import { useState } from 'react';

const SelfPostAssessment = ({ params }: { params: { momID: string } }) => {
  const [isFilledOut, setIsFilledOut] = useState(false);
  const handleSubmit = async (data: FormValues) => {
    await fetch(`/api/moms/${params.momID}/post-assessment`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  };

  const fetchInitialData = async () => {
    // TODO: Update this API to whatever it is supposed to be
    const response = await fetch(`/api/moms/${params.momID}/post-assessment`);
    if (!response.ok) {
      // If no existing data, return empty object
      if (response.status === 404) return {};
      throw new Error('Failed to fetch post-assessment data');
    }
    const data = await response.json();
    // Set isFilledOut to true if we have data
    if (Object.keys(data).length > 0) {
      setIsFilledOut(true);
    }
    return data;
  };

  return (
    <>
      {isFilledOut ? (
        <div className='flex h-screen flex-col items-center justify-center'>
          <h1 className='text-2xl font-bold'>Thank you, You have already filled out the post-assessment.</h1>
        </div>
      ) : (
        <AssessmentWrapper
          title='Post-Assessment'
          config={postAssessmentFormsConfig}
          onSubmit={handleSubmit}
          fetchInitialData={fetchInitialData}
          selfAssessment={true}
        />
      )}
    </>
  );
};

export default SelfPostAssessment;
