'use client';

import AssessmentWrapper from '@/app/portal/[role]/dashboard/people/moms/[id]/assessments/_components/assessment-wrapper';
import { preAssessmentFormsConfig } from '@/app/portal/[role]/dashboard/people/moms/[id]/assessments/pre-assessment/_lib/pre-assessment-forms.config';
import { FormValues } from '@/types/form-field';
import { useState } from 'react';

const SelfPreAssessment = ({ params }: { params: { momID: string } }) => {
  const [isFilledOut, setIsFilledOut] = useState(false);
  const handleSubmit = async (data: FormValues) => {
    await fetch(`/api/moms/${params.momID}/pre-assessment`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  };

  const fetchInitialData = async () => {
    // TODO:Update this API to whatever it is supposed to be
    const response = await fetch(`/api/moms/${params.momID}/pre-assessment`);
    if (!response.ok) {
      // If no existing data, return empty object
      if (response.status === 404) return {};
      throw new Error('Failed to fetch post-assessment data');
    }
    const data = await response.json();
    // Set isFilledOut to true if we have data
    if (Object.keys(data).length > 0) {
      setIsFilledOut(true);
    }
    return data;
  };

  return (
    <>
      {isFilledOut ? (
        <div className='flex h-screen flex-col items-center justify-center'>
          <h1 className='text-2xl font-bold'>Thank you, you have already filled out the pre-assessment.</h1>
        </div>
      ) : (
        <AssessmentWrapper
          title='Pre-Assessment'
          config={preAssessmentFormsConfig}
          onSubmit={handleSubmit}
          fetchInitialData={fetchInitialData}
          selfAssessment={true}
        />
      )}
    </>
  );
};

export default SelfPreAssessment;
