import { ObjectSchema } from 'yup';
import * as yup from 'yup';

import type { FieldConfig } from '../form-field';

// Shape of UI form configuration object
export interface ReferralFormConfig {
  header: {
    title: string;
    description: string;
  };
  steps: Record<
    string,
    {
      title: string;
      sideNavLabel?: string;
      fields: FieldConfig[];
      schema: ObjectSchema<Record<string, unknown>> | yup.AnySchema; // multi-step form config will contain subset of full schema for each step
    }
  >;
}
