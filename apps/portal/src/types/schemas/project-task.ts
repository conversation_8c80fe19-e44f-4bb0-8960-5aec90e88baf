import * as yup from 'yup';

export const projectTaskApiFieldsSchema = {
  id: yup.string().optional(), // Optional seems weird here but it needs to be optional b/c create requests (as one example) won't include an id.
  order_number: yup.number().optional(),
  name: yup.string().required(),
  project_id: yup.string().required(),
  description: yup.string().optional(),
  status: yup.string().optional().oneOf(['not_started', 'in_progress', 'completed', 'pending_input', 'deferred']),
  type_c: yup
    .string()
    .transform((v) => (v === '' ? undefined : v))
    .optional()
    .oneOf(['Session', 'Goal', 'Lesson', 'Action_Item']),
  projecttask_projecttask_1projecttask_ida: yup.string().optional(), // For Action Items, this will be the related Goal's Id.
  am_tasktemplates_id_c: yup.string().optional(), // For Lessons, this will be the related Project Task Template's Id. Which can be used to query Documents for lesson-related resources.
  date_due: yup.string().optional(),
};

export const projectTaskSchema = yup.object().shape({
  ...projectTaskApiFieldsSchema,
});

export type ProjectTaskSchema = yup.InferType<typeof projectTaskSchema>;

export const createGoalSchema = yup
  .array()
  .of(
    yup.object().shape({
      ...projectTaskApiFieldsSchema,
      action_items: yup.array().of(projectTaskSchema).optional(),
    }),
  )
  .required()
  .min(1, 'At least one goal is required');

export const updateActionItemSchema = yup.object().shape({
  ...projectTaskApiFieldsSchema,
  project_id: yup.string().optional(),
  name: yup.string().optional(),
});
