import * as yup from 'yup';

export const surveyApiFieldSchemas = {
  id: yup.string().optional(),
  deleted: yup.boolean().optional(),
  survey_id: yup.string().optional(),
  survey_name: yup.string().optional(),
  survey_status: yup.string().optional(),
  survey_scale: yup.array().optional(),
  survey_affiliate_id: yup.string().optional(),
  survey_course: yup.string().optional(),
  survey_type: yup.string().optional(),
  survey_locale: yup.string().optional(),
};

export const surveySchema = yup.object().shape(surveyApiFieldSchemas);
export type SurveySchema = yup.InferType<typeof surveySchema>;

export const questionApiFieldSchemas = {
  id: yup.string().optional(),
  deleted: yup.boolean().optional(),
  q_id: yup.string().optional(),
  q_name: yup.string().optional(),
  q_type: yup.string().optional(),
  q_sort_order: yup.number().optional(),
  q_page_name: yup.string().optional(),
  q_options: yup
    .array()
    .of(
      yup.object({
        qo_id: yup.string().optional(),
        qo_name: yup.string().optional(),
        qo_sort_order: yup.number().optional(),
      }),
    )
    .optional(),
  q_answer: yup.string().optional(),
};

export const questionSchema = yup.object().shape(questionApiFieldSchemas);
export type QuestionSchema = yup.InferType<typeof questionSchema>;

export const questionResponseApiFieldSchemas = {
  ...questionApiFieldSchemas,
  qr_id: yup.string().optional(),
  qr_answer: yup.string().optional(),
};

export const questionResponseSchema = yup.object().shape(questionResponseApiFieldSchemas);
export type QuestionResponseSchema = yup.InferType<typeof questionResponseSchema>;

const createQuestionResponseApiFieldSchemas = {
  ...surveyApiFieldSchemas,
  survey_questions: yup
    .array()
    .of(
      yup
        .object()
        .shape({ ...questionApiFieldSchemas, q_answer: yup.string().optional(), qr_id: yup.string().optional() }),
    )
    .required(),
};

export const createQuestionResponseSchema = yup.object().shape(createQuestionResponseApiFieldSchemas);
export type CreateQuestionResponseSchema = yup.InferType<typeof createQuestionResponseSchema>;
