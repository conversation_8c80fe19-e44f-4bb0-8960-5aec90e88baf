import * as yup from 'yup';

export const connectionLogSchema = yup.object().shape({
  id: yup.string().optional(),
  mom_id: yup.string().required(),
  mom: yup.object().required(),
  summary_c: yup.string().required(),
  contact_method_c: yup.string().required(),
  is_visible_to_advocates_c: yup.boolean().required(),
  date_created_c: yup.string().required(),
  created_by_name: yup.string().required(),
  name: yup.string().optional(),
});

export type ConnectionLogSchema = yup.InferType<typeof connectionLogSchema>;
