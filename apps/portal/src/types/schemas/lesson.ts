import * as yup from 'yup';

export const lessonSchema = yup.object().shape({
  id: yup.string().optional(),
  title: yup.string().optional(),
  description: yup.string().optional(),
  order: yup.number().optional(),
  status: yup.string().oneOf(['not_started', 'in_progress', 'completed']).optional(),
  pairing_id: yup.string().optional(),
});

export type LessonSchema = yup.InferType<typeof lessonSchema>;
