import { YupSchemas } from '@suiteapi/models';
import * as yup from 'yup';

import { GroupSchema, groupSchema } from './group';
import { lessonSchema } from './lesson';
import { TrackSchema, trackSchema } from './track';

export const pairingApiFieldSchemas = {
  id: yup.string().optional(),
  isDeleted: yup.boolean().optional(),
  description: yup.string().optional(),
  name: yup.string().optional(),
  status: yup.string().optional().oneOf(['waiting_to_be_paired', 'paired', 'pairing_complete']),
  trackId: yup.string().optional(),
  track: trackSchema.optional(),
  advocateUser: yup.object().shape({
    id: yup.string().optional(),
    firstName: yup.string().optional(),
    lastName: yup.string().optional(),
  }),
  created_at: yup.string().optional(),
  lessons: yup.array().of(lessonSchema),
};

export const pairingSchema = yup.object().shape(pairingApiFieldSchemas);

// Custom list pairings API maps in the associated mom (client) data
export const pairingResponseSchema = yup.object().shape({
  ...pairingApiFieldSchemas,
  mom: YupSchemas.momFullSchema.optional(),
  group: groupSchema.optional(),
  track: trackSchema.optional(),
  created_at: yup.string().optional(),
});

export type PairingSchema = yup.InferType<typeof pairingSchema>;
export type PairingResponseSchema = yup.InferType<typeof pairingResponseSchema>;

export const pairingOrMomSchema = yup.object().shape({
  id: yup.string().required(),
  mom: YupSchemas.momFullSchema.pick(['id', 'first_name', 'last_name', 'assigned_user_id']).optional(),
});

export type PairingOrMomSchema = yup.InferType<typeof pairingOrMomSchema>;

export type RobustPairingSchema = PairingSchema & {
  mom?: YupSchemas.MomType | null;
  group?: GroupSchema | null;
  track?: TrackSchema | null;
};
