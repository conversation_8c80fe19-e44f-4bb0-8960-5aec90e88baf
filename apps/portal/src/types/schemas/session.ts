import { YupSchemas } from '@suiteapi/models';
import * as yup from 'yup';

export const SessionTypes = ['Support_Session', 'Track_Session', 'Referral_Session'] as const;

export enum SessionType {
  Support_Session = 'Support_Session',
  Track_Session = 'Track_Session',
  Referral_Session = 'Referral_Session',
}

export const sessionApiFieldSchemas = {
  id: yup.string().optional(),
  created_by_name: yup.string().optional().nullable(),
  deleted: yup.boolean().optional(),
  date_start: yup.string().optional(),
  date_end: yup.string().optional(),
  description: yup.string().optional().nullable(),
  name: yup.string().optional().nullable(),
  location: yup.string().optional().nullable(), // For in-person sessions
  join_url: yup.string().optional().nullable(), // For virtual sessions
  status: yup.string().optional().nullable(),
  is_group_session: yup.boolean().optional(),
  session_type: yup.string().required(YupSchemas.REQUIRED).oneOf(SessionTypes),
  projecttask_meetings_1_name: yup.string().optional(), // This is the name of the related lesson Project Task
  projecttask_meetings_1projecttask_ida: yup.string().optional(), // This is the ID of the related lesson Project Task
  session_group_id: yup.string().optional().nullable(),
  session_note: yup
    .object()
    .shape({
      covered_lesson: yup
        .object()
        .shape({
          title: yup.string().optional(),
        })
        .optional()
        .nullable(),
    })
    .optional()
    .nullable(),
  pairing: yup
    .object()
    .shape({
      mom: yup
        .object()
        .shape({
          first_name: yup.string().optional(),
          last_name: yup.string().optional(),
        })
        .optional()
        .nullable(),
    })
    .optional()
    .nullable(),
};

export const sessionSchema = yup.object().shape(sessionApiFieldSchemas);
export type SessionSchema = yup.InferType<typeof sessionSchema>;

export const sessionSchemaWithSessionNote = yup.object().shape({
  ...sessionSchema.fields,
  session_note: yup.object().shape({
    id: yup.string().optional(),
    status: yup.string().optional(),
    note: yup.string().optional(),
    attendance_and_promptness: yup.string().optional(),
    moms_engagement_c: yup.string().optional(),
    new_attempt: yup.boolean().optional(),
    new_attempt_example: yup.string().optional(),
    date_submitted_c: yup.string().optional(),
  }),
});

export type SessionSchemaWithSessionNote = yup.InferType<typeof sessionSchemaWithSessionNote>;

// Add the combined schema
export const combinedSessionSchema = yup.object().shape({
  ...sessionSchema.fields,
  session_note: yup
    .object()
    .shape({
      id: yup.string().optional(),
      status: yup.string().optional(),
      note: yup.string().optional(),
      attendance_and_promptness: yup.string().optional(),
      moms_engagement_c: yup.string().optional(),
      new_attempt: yup.boolean().optional(),
      new_attempt_example: yup.string().optional(),
      date_submitted_c: yup.string().optional(),
      covered_lesson: yup
        .object()
        .shape({
          title: yup.string().optional(),
        })
        .optional()
        .nullable(),
    })
    .optional()
    .nullable(),
});

export type CombinedSessionSchema = yup.InferType<typeof combinedSessionSchema>;

const createSessionApiFieldSchemas = {
  pairing_id: yup.string().required(),
  mom_id: yup.string().required(YupSchemas.REQUIRED), // client-side validation added for select menu on schedule session form
  mom_name: yup.string().required(),
  duration_hours: yup.string().optional(),
  duration_minutes: yup.string().optional(),
  ...sessionApiFieldSchemas,
};

export const createSessionSchema = yup.object().shape(createSessionApiFieldSchemas);
export type CreateSessionSchema = yup.InferType<typeof createSessionSchema>;

const scheduleSessionFieldSchemas = {
  mom_id: createSessionSchema.fields.mom_id,
  session_type: createSessionSchema.fields.session_type,
  current_lesson: yup.string().optional(), // TODO: How is this handled in SuiteCRM?
  date_start: yup.string().required(YupSchemas.REQUIRED),
  start_time: YupSchemas.getStartTimeSchema('end_time').required(YupSchemas.REQUIRED), // Used to derive value of date_start
  end_time: YupSchemas.getEndTimeSchema('start_time').required(YupSchemas.REQUIRED), // Used to derive value of date_end
  meeting_type: yup.string().oneOf(['In_Person', 'Virtual']).required(YupSchemas.REQUIRED), // TODO: How is this handled in SuiteCRM?
  location: createSessionSchema.fields.location,
  join_url: yup.string().optional(),
};

export const scheduleSessionFormSchema = yup.object().shape(scheduleSessionFieldSchemas);

// Remove mom_id, top-level start_time and end_time from scheduleSessionFieldSchemas to create a new schema for scheduleMultipleSessionFormSchema
const { mom_id: _momId, ...scheduleSessionFieldSchemasWithoutMomId } = scheduleSessionFieldSchemas;

const scheduleMultipleSessionFieldSchema = {
  ...scheduleSessionFieldSchemasWithoutMomId,
  pairing_or_mom_ids: yup
    .array()
    .of(
      yup.object().shape({
        value: yup.string().required(YupSchemas.REQUIRED),
        label: yup.string().optional(),
      }),
    )
    .required(YupSchemas.REQUIRED),
};

export const scheduleMultipleSessionFormSchema = yup.object().shape(scheduleMultipleSessionFieldSchema);
export type ScheduleMultipleSessionFormSchema = yup.InferType<typeof scheduleMultipleSessionFormSchema>;

const createReferralSessionApiFieldSchemas = {
  ...sessionApiFieldSchemas,
  referral_id: yup.string().required(),
  duration_hours: yup.string().optional(),
  duration_minutes: yup.string().optional(),
};

export const createReferralSessionSchema = yup.object().shape(createReferralSessionApiFieldSchemas);
export type CreateReferralSessionSchema = yup.InferType<typeof createReferralSessionSchema>;
