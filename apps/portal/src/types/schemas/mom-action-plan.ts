export interface ActionItem {
  id?: string;
  name?: string;
  description?: string;
  status?: string;
  due_date?: string | Date;
  created_at?: string | Date;
  updated_at?: string | Date;
  completed?: boolean;
  goalId?: string;
}

export interface MomActionPlanGoal {
  id?: string;
  name?: string;
  description?: string;
  status?: string;
  due_date?: string | Date;
  created_at?: string | Date;
  updated_at?: string | Date;
  momId?: string;
  completed?: boolean;
  actionItems: ActionItem[];
}
