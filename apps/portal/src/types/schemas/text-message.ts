import { YupSchemas } from '@suiteapi/models';
import * as yup from 'yup';

export const sendTextMessageToOneContactUiFieldSchema = yup.object().shape({
  contact_name: yup.string().required(YupSchemas.REQUIRED), // used for display only - API accepts an array of contact_ids
  contact_phone: YupSchemas.phoneSchema.required(YupSchemas.REQUIRED), // used for display only - API uses contact_id
  message_text: yup.string().required(YupSchemas.REQUIRED),
});

const sendTextMessageApiFieldSchemas = {
  contact_ids: yup.array().of(yup.string().required(YupSchemas.REQUIRED)), // array of ids will be sent even when only one contact is selected
  message_text: yup.string().required(YupSchemas.REQUIRED),
};

export const sendTextMessageSchema = yup.object().shape(sendTextMessageApiFieldSchemas);
export type SendTextMessageSchema = yup.InferType<typeof sendTextMessageSchema>;
