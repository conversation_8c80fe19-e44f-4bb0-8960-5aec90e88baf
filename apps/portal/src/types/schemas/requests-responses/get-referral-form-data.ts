import { serviceSchema } from '@/types/schemas/service';
import { YupSchemas } from '@suiteapi/models';
import * as yup from 'yup';

export const getReferralFormDataResponseSchema = yup.object().shape({
  affiliate_locations: yup.array().of(YupSchemas.affiliateMinimalSchema).required(),
  services: yup.array().of(serviceSchema).required(),
});

export type GetReferralFormDataResponse = yup.InferType<typeof getReferralFormDataResponseSchema>;
