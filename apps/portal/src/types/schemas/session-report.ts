import * as yup from 'yup';

const sessionReportApiFieldSchemas = {
  id: yup.string().optional(),
  name: yup.string().required(),
  description: yup.string().optional().nullable(),
  note: yup.string().optional().nullable(),
  attendance_and_promptness: yup.string().optional().nullable().oneOf(['On_Time', 'Late', 'No_Show', '']),
  moms_engagement_c: yup.string().optional().nullable().oneOf(['Full', 'Partial', 'None', '']),
  new_attempt: yup.boolean().optional().nullable(),
  new_attempt_example: yup.string().optional().nullable(),
  date_submitted_c: yup.date().optional().nullable(),
  status_c: yup
    .string()
    .transform((v) => (v === '' ? undefined : v))
    .optional()
    .nullable()
    .oneOf(['submitted', 'new', 'rejected', 'approved']),
};

export const sessionReportSchema = yup.object().shape(sessionReportApiFieldSchemas);
export type SessionReportSchema = yup.InferType<typeof sessionReportSchema>;

const updateSessionReportApiFieldSchemas = {
  description: yup.string().optional(),
  note: yup.string().optional(),
  attendance_and_promptness: yup.string().optional().oneOf(['On_Time', 'Late', 'No_Show', '']),
  status: yup
    .string()
    .transform((v) => (v === '' ? undefined : v))
    .optional()
    .oneOf(['submitted', 'new', 'rejected', 'approved']),
  moms_engagement_c: yup.string().optional().oneOf(['Full', 'Partial', 'None', '']),
  new_attempt: yup.boolean().optional(),
  new_attempt_example: yup.string().optional(),
  date_submitted_c: yup.string().optional(),
};

export const updateSessionReportSchema = yup.object().shape(updateSessionReportApiFieldSchemas);
export type UpdateSessionReportSchema = yup.InferType<typeof updateSessionReportSchema>;
