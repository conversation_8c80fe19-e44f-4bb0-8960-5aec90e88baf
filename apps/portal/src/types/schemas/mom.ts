import { z } from 'zod';

export const MomSchema = z.object({
  id: z.string().optional(),
  first_name: z.string().nullable().optional(),
  last_name: z.string().nullable().optional(),
  name: z.string().nullable().optional(),
  email1: z.string().nullable().optional(),
  phone_other: z.string().nullable().optional(),
  birthdate: z.date().nullable().optional(),
  status: z.string().nullable().optional(),
  caregiver_type_c: z.string().nullable().optional(),
  discharge_reason_c: z.string().nullable().optional(),
  date_entered: z.string().nullable().optional(),
  closed_date_c: z.date().nullable().optional(),
  language_preference_c: z.string().nullable().optional(),
  language_notes_c: z.string().nullable().optional(),
  languages_c: z.array(z.string()).nullable().optional(),
  primary_address_street: z.string().nullable().optional(),
  primary_address_city: z.string().nullable().optional(),
  primary_address_state: z.string().nullable().optional(),
  primary_address_postalcode: z.string().nullable().optional(),
  primary_address_county_c: z.string().nullable().optional(),
  address_access_c: z.string().nullable().optional(),
  connected_benevolance_c: z.boolean().nullable().optional(),
  connected_childcare_c: z.boolean().nullable().optional(),
  connected_closet_c: z.boolean().nullable().optional(),
  connected_education_c: z.boolean().nullable().optional(),
  connected_health_c: z.boolean().nullable().optional(),
  connected_housing_c: z.boolean().nullable().optional(),
  connected_legal_c: z.boolean().nullable().optional(),
  connected_mental_health_c: z.boolean().nullable().optional(),
  connected_substance_c: z.boolean().nullable().optional(),
  referral_type_c: z.string().nullable().optional(),
  supports_court_order_c: z.boolean().nullable().optional(),
  service_selected_c: z.string().nullable().optional(),
  need_details_c: z.string().nullable().optional(),
  created_by_name: z.string().nullable().optional(),
  account_name: z.string().nullable().optional(),
  currently_pregnant_c: z.string().nullable().optional(),
  number_of_children_c: z.number().nullable().optional(),
  preferred_contact_method_c: z.string().nullable().optional(),
  assigned_user_id: z.string().nullable().optional(),
  converted: z.string().nullable().optional(),
  full_name: z.string().nullable().optional(),
  prospect_status: z.string().nullable().optional(),
  photoUrl: z.string().optional(),
  photoS3FileName: z.string().optional(),
  thumbnailUrl: z.string().optional(),
  thumbnailS3FileName: z.string().optional(),
  iconUrl: z.string().optional(),
  iconS3FileName: z.string().optional(),
  affiliate_id: z.string().nullable().optional(),
});

export type MomType = z.infer<typeof MomSchema>;
