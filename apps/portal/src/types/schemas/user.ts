import { YupSchemas } from '@suiteapi/models';
import * as yup from 'yup';

export const advocateStatuses = [
  '',
  'Applied',
  'Interested',
  'Rejected',
  'Inactive',
  'Active',
  'In_Training',
  'Awaiting_Pairing',
];

export type AdvocateStatus = (typeof advocateStatuses)[number];

export const currentUserSchema = yup.object({
  id: yup.string().optional(),
  user_name: yup.string().required(),
  first_name: yup.string().required(),
  last_name: yup.string().required(),
  full_name: yup.string().required(),
  name: yup.string().required(),
  is_admin: yup.boolean().required(),
  receive_notifications: yup.boolean().required(),
  status: yup.string().required(),
  employee_status: yup.string().optional(),
  isDeleted: yup.boolean().required(),
  email1: yup.string().required(),
  accounts_users_1accounts_ida: yup.string().optional(),
});

export const updateAdvocateStatusSchema = yup.object({
  id: yup.string().optional(),
  advocate_status: yup.string().required().oneOf(advocateStatuses),
});

export type UpdateAdvocateStatus = yup.InferType<typeof updateAdvocateStatusSchema>;

export type CurrentUser = yup.InferType<typeof currentUserSchema>;

export const userSchema = yup.object({
  id: yup.string().optional().nullable(),
  created_at: yup.date().optional().nullable(),
  advocate_status: yup.string().optional().nullable().oneOf(advocateStatuses),
  date_entered: yup.string().optional().nullable(),
  email: yup.string().optional().nullable(),
  firstName: yup.string().optional().nullable(),
  lastName: yup.string().optional().nullable(),
  phone: yup.string().optional().nullable(),
  secondary_phone: yup.string().optional().nullable(),
  address_street: yup.string().optional().nullable(),
  address_city: yup.string().optional().nullable(),
  address_state: yup.string().optional().nullable(),
  address_postalcode: yup.string().optional().nullable(),
  description: yup.string().optional().nullable(),
  accounts_users_1accounts_ida: yup.string().optional().nullable(),
  isDeleted: yup.boolean().optional().nullable(),
  phone_work: yup.string().optional().nullable(),
  phone_mobile: yup.string().optional().nullable(),
  phone_home: yup.string().optional().nullable(),
  phone_other: yup.string().optional().nullable(),
  pairings: yup.array().optional().nullable(),
  username: yup.string().optional().nullable(),
  secondary_email: yup.string().optional().nullable(),
  home_church: yup.string().optional().nullable(),
  communication_preference: yup.string().optional().nullable(),
  sms_message_opt_in: yup.boolean().optional().nullable(),
  date_of_birth: yup.date().optional().nullable(),
  timezone: yup.string().optional().nullable(),
  status: yup.string().optional().nullable(),
  advocate_capacity_for_moms: yup.number().optional().nullable(),
  affiliateId: yup.string().optional().nullable(),
  userRoles: yup
    .array()
    .of(
      yup.object().shape({
        value: yup.string().oneOf(['advocate', 'coordinator', 'administrator'], 'Invalid user role'),
        label: yup.string(),
      }),
    )
    .optional(),
  affiliate: YupSchemas.affiliateSchema.optional().nullable(),
  assignedCoordinators: yup
    .array()
    .of(
      yup.object().shape({
        id: yup.string(),
        firstName: yup.string().nullable(),
        lastName: yup.string().nullable(),
      }),
    )
    .nullable(),
  assignedAdvocates: yup
    .array()
    .of(
      yup.object().shape({
        id: yup.string(),
        firstName: yup.string().nullable(),
        lastName: yup.string().nullable(),
      }),
    )
    .nullable(),
  availability: yup.string().optional().nullable(),
  language_preference_c: yup.string().optional().nullable(),
  languages_c: yup
    .array()
    .of(
      yup.object().shape({
        label: yup.string().required(),
        value: yup.string().required(),
      }),
    )
    .nullable(),
  photoUrl: yup.string().optional().nullable(),
  photoS3FileName: yup.string().optional().nullable(),
  thumbnailUrl: yup.string().optional().nullable(),
  thumbnailS3FileName: yup.string().optional().nullable(),
  iconUrl: yup.string().optional().nullable(),
  iconS3FileName: yup.string().optional().nullable(),
  roles: yup.array().of(yup.string()).optional(),
  account_verified: yup.boolean().optional().nullable(),
  email_verified: yup.boolean().optional().nullable(),
  must_change_password: yup.boolean().optional().nullable(),
  affiliate_id: yup.string().optional().nullable(),
  advocateOnboarding: yup.object().optional().nullable(),
});

export type User = yup.InferType<typeof userSchema>;
