import type { LucideIcon } from 'lucide-react';
import { CaptionLayout } from 'react-day-picker';
import type { Accept } from 'react-dropzone';
import * as yup from 'yup';

import { type AvailabilityMatrixOptions } from './availability';

export type FieldType =
  | 'select'
  | 'text'
  | 'number'
  | 'email'
  | 'password'
  | 'tel'
  | 'zip'
  | 'date'
  | 'date-dropdown'
  | 'time'
  | 'radio'
  | 'button-radio-group'
  | 'checkbox'
  | 'checkbox-group'
  | 'textarea'
  | 'files'
  | 'custom'
  | 'action-item-list'
  | 'multi-select'
  | 'row-group'
  | 'summary-select'
  | 'summary-date'
  | 'availability-matrix'
  | 'combo-box';

export type FormFieldValue = unknown;

export type FormValues = Record<string, FormFieldValue>;

export type FieldOptionObj = { value: unknown; label: string };

export type FieldOptions = FieldOptionObj[];

type FileUploadOptions = { maxSize: number; maxFiles: number; accept?: Accept };

export interface FieldConfig {
  name: string;
  label: string;
  type: FieldType;
  placeholder?: string;
  defaultValue?: FormFieldValue;
  min?: number;
  max?: number;
  minDate?: Date;
  maxDate?: Date;
  fromDate?: Date;
  toDate?: Date;
  minYears?: number;
  maxYears?: number;
  fromYear?: number;
  toYear?: number;
  displayFormat?: string;
  description?: string;
  checkboxLabel?: string;
  heading?: string;
  descriptionToHeading?: string;
  labelDescription?: string;
  captionLayout?: CaptionLayout;
  required?: boolean;
  /*
   * `options`: Array of options, either static, or dynamically set (if `dynamicOptionsKey` is provided)
   * Set to `null` to indicate fetching dynamic options failed
   */
  options?: FieldOptions | null;
  dynamicOptionsKey?: string; // key to allow looking up dynamic options stored higher up in form context
  component?: React.ComponentType<unknown>;
  icon?: LucideIcon;
  bottomDivider?: boolean;
  bottomContent?: React.ReactNode;
  // add an optional callback to hide bottom content based on form values
  bottomContentHidden?: (values?: FormValues) => boolean;
  subtext?: string;
  schema?: yup.Schema;
  subFields?: FieldConfig[];
  fileUploadOptions?: FileUploadOptions;
  hidden?: (values?: FormValues) => boolean;
  disabled?: ((values?: FormValues) => boolean) | boolean;
  onChange?: (values?: FormValues) => void;
  errorMessage?: string;
  overrideStrings?: {
    selectSomeItems?: string;
    allItemsAreSelected?: string;
    selectAll?: string;
    search?: string;
  };
  listItemPlaceholder?: string; // Placeholder for each list item input
  addButtonLabel?: string; // Label for the button to add a new list item
  minItems?: number; // Minimum number of items required in the list
  maxItems?: number; // Maximum number of items allowed in the list
  hasSelectAll?: boolean;
  labelledBy?: string;
  customOptionLabel?: string; // Label for the custom option in combo-box
}

export interface StandardFieldConfig extends FieldConfig {
  type: Exclude<FieldType, 'availability-matrix'>;
  options?: FieldOptions | null;
}

export interface AvailabilityMatrixFieldConfig extends Omit<FieldConfig, 'options'> {
  type: 'availability-matrix';
  options: AvailabilityMatrixOptions;
}

export type FieldConfigUnion = StandardFieldConfig | AvailabilityMatrixFieldConfig;

export const generateFieldsSchema = (
  fields: FieldConfigUnion[],
  formValues: FormValues | undefined,
): yup.ObjectSchema<yup.AnyObject> => {
  return yup.object(
    fields.reduce(
      (acc, field) => ({
        ...acc,
        ...(field.schema && !field.hidden?.(formValues) ? { [field.name]: field.schema } : {}),
      }),
      {} as { [name: string]: yup.Schema },
    ),
  );
};

export interface FormActionButtonsConfig {
  continueLabel: string;
  cancelLabel?: string;
  backLabel?: string;
}
