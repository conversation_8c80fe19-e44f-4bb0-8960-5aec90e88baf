// auth middleware
import { EmaAuthTokenDto } from '@suiteapi/models';
import { jwtVerify } from 'jose';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

import { retrieveAccessToken } from './lib/accessTokenStorageService';

// Base routes that do not require authentication to access
const publicRoutes = ['/login', '/referral', '/ml/'];

export async function middleware(req: NextRequest): Promise<NextResponse> {
  const url = req.nextUrl.clone();

  // if pathname starts with one of the public routes, skip auth checks
  if (publicRoutes.some((route) => url.pathname.startsWith(route))) {
    return NextResponse.next();
  }

  // check for current authenticated session
  const token = retrieveAccessToken(req);

  if (!token) {
    url.pathname = '/login';
    return NextResponse.redirect(url);
  }

  try {
    // Verify token signature using the shared JWT secret
    const jwtSecret = process.env.JWT_SIGNING_SECRET;
    if (!jwtSecret) {
      throw new Error('JWT_SIGNING_SECRET is not configured');
    }

    const { payload: decodedToken } = await jwtVerify<EmaAuthTokenDto>(token, new TextEncoder().encode(jwtSecret));
    if (!decodedToken || !decodedToken.sub) {
      throw new Error('Invalid token');
    }

    // Check if mustChangePassword exists and is true
    if (
      decodedToken &&
      typeof decodedToken === 'object' &&
      'mustChangePassword' in decodedToken &&
      decodedToken.mustChangePassword
    ) {
      url.pathname = '/login/change-password';
      return NextResponse.redirect(url);
    }

    return NextResponse.next();
  } catch (error) {
    // Token is invalid or expired
    console.error('Token validation failed:', error);
    url.pathname = '/login';
    return NextResponse.redirect(url);
  }
}

export const config = {
  matcher: ['/portal/:path*'],
};
