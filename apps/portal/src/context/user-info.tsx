'use client';

import { retrieveAccessToken } from '@/lib/accessTokenStorageService';
import { EmaAuthTokenDto } from '@suiteapi/models';
import { jwtDecode } from 'jwt-decode';
import { type ReactNode, createContext, useContext, useEffect, useState } from 'react';

interface UserInfoContextValue {
  isInitialized: boolean;
  roles: string[];
  setRoles: (roles: string[]) => void;
  isInRole: (role: string) => boolean;
  isAtLeastCoordinator: boolean;
  isAdmin: boolean;
  userData: EmaAuthTokenDto | null;
  setUserData: (userData: EmaAuthTokenDto | null) => void;
  userProfileImageSrc: string | null;
  setUserProfileImageSrc: (userProfileImageSrc: string | null) => void;
}

const UserInfoContext = createContext<UserInfoContextValue | undefined>(undefined);

const SESSION_STORAGE_KEYS = {
  ROLES: 'ema_roles',
  USER_DATA: 'ema_userData',
};

export const UserInfoProvider = ({ children }: { children: ReactNode }): JSX.Element => {
  const [roles, setRoles] = useState<string[]>([]);
  const [userData, setUserData] = useState<EmaAuthTokenDto | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [userProfileImageSrc, setUserProfileImageSrc] = useState<string | null>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedRoles = sessionStorage.getItem(SESSION_STORAGE_KEYS.ROLES);
      const storedUserData = sessionStorage.getItem(SESSION_STORAGE_KEYS.USER_DATA);

      if (!storedRoles || !storedUserData) {
        const accessToken = retrieveAccessToken();
        if (accessToken) {
          const decodedToken = jwtDecode<EmaAuthTokenDto>(accessToken);
          setRoles(decodedToken.roles);
          setUserData(decodedToken);
          setIsInitialized(true);
          return;
        }
      }

      setRoles(storedRoles ? JSON.parse(storedRoles) : []);
      setUserData(storedUserData ? JSON.parse(storedUserData) : null);
      setIsInitialized(true);
    }
  }, []);

  const isInRole = (role: string): boolean => [...roles].includes(role);

  const isAtLeastCoordinator = roles.some((role) => ['coordinator', 'supervisor', 'administrator'].includes(role));

  const isAdmin = roles.includes('administrator');

  // Update sessionStorage whenever roles change
  useEffect(() => {
    if (isInitialized && typeof window !== 'undefined') {
      sessionStorage.setItem(SESSION_STORAGE_KEYS.ROLES, JSON.stringify(roles));
    }
  }, [roles, isInitialized]);

  // Update sessionStorage whenever userData changes
  useEffect(() => {
    if (isInitialized && typeof window !== 'undefined') {
      sessionStorage.setItem(SESSION_STORAGE_KEYS.USER_DATA, JSON.stringify(userData));
    }
  }, [userData, isInitialized]);

  return (
    <UserInfoContext.Provider
      value={{
        isInitialized,
        roles,
        setRoles,
        isInRole,
        isAtLeastCoordinator,
        isAdmin,
        userData,
        setUserData,
        userProfileImageSrc,
        setUserProfileImageSrc,
      }}
    >
      {children}
    </UserInfoContext.Provider>
  );
};

export const useUserInfo = (): UserInfoContextValue => {
  const context = useContext(UserInfoContext);
  if (!context) {
    throw new Error('useUserInfo must be used within a UserInfoProvider');
  }
  return context;
};
