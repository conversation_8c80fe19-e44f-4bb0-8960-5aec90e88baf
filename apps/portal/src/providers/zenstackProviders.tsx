'use client';

import QueryClientProvider from '@/components/QueryClientProvider';
import { Provider as ZenStackQueryProvider } from '@/hooks/generated';
import { sfetch } from '@/lib/sfetch';

const ZenstackProviders = ({ children }: { children: React.ReactNode }): JSX.Element => {
  return (
    <ZenStackQueryProvider value={{ endpoint: `${process.env.NEXT_PUBLIC_API_URL}/v1/zen`, fetch: sfetch }}>
      <QueryClientProvider>{children}</QueryClientProvider>
    </ZenStackQueryProvider>
  );
};
export default ZenstackProviders;
