import { FieldConfig, FormValues } from '@/types/form-field';

import { WellBeingAssessmentFormData } from '../app/portal/types';

const extractNumber = (value: unknown) => {
  const num = typeof value === 'string' ? parseInt(value) : 0;
  return !Number.isNaN(num) ? num : 0;
};

const calculateScoreForSingleSelect = (wellBeingFormValues: FormValues, fieldName: string) => {
  const value = wellBeingFormValues[fieldName];
  return extractNumber(value);
};

const calculateScoreForMultiSelect = (wellBeingFormValues: FormValues, fieldName: string) => {
  const value = wellBeingFormValues[fieldName];
  const arr = Array.isArray(value) ? value : [];
  return arr
    .map((value) => value.value)
    .map(extractNumber)
    .reduce((acc, curr) => acc + curr, 0);
};

const calculateSum = (...nums: number[]) => {
  return nums.reduce((acc, curr) => acc + curr, 0);
};

const calculateAvg = (...nums: number[]) => {
  return nums.length ? Math.floor(calculateSum(...nums) / nums.length) : 0;
};

export const assignSummaryFieldsAndCalculateScores = (formData: WellBeingAssessmentFormData, fields: FieldConfig[]) => {
  let newWellBeingFormValues = formData.wellBeingForm.values;
  let updateRequired = false;

  // Form: Well-Being Assessment

  // Section: Child Welfare

  // Field: Children Home Status
  const totalNumberOfChildren = formData.childrenForm.values.number_of_children_c;
  const totalNumberOfChildrenInHome = formData.childrenForm.values.number_of_children_in_home_c;
  if (typeof totalNumberOfChildren === 'number' && typeof totalNumberOfChildrenInHome === 'number') {
    const homeStatus =
      (totalNumberOfChildren === totalNumberOfChildrenInHome && '20_in_home') ||
      (totalNumberOfChildrenInHome === 0 && '0_out_home') ||
      '10_in_out_home';

    if (newWellBeingFormValues.cw_home_status !== homeStatus) {
      newWellBeingFormValues = {
        ...newWellBeingFormValues,
        cw_home_status: homeStatus,
      };
      updateRequired = true;
    }
  }

  // Field: Active Child Welfare Involvement
  const activeInvolvement = fields
    .find((field) => field.name === 'cw_active_involvement')
    ?.options?.find((option) =>
      formData.childForms.some((childForm) => childForm.values.active_child_welfare_involvement === option.value),
    )?.value;

  if (newWellBeingFormValues.cw_active_involvement !== activeInvolvement) {
    newWellBeingFormValues = {
      ...newWellBeingFormValues,
      cw_active_involvement: activeInvolvement || undefined,
    };
    updateRequired = true;
  }

  // Field: Date of Child Welfare Involvement
  const [dateOfInvolvement] = formData.childForms
    .map((childForm) => childForm.values.date_of_child_welfare_involvement)
    .filter(Boolean)
    .sort((a, b) => String(b).localeCompare(String(a)));

  if (
    newWellBeingFormValues.cw_date_of_involvement instanceof Date && dateOfInvolvement instanceof Date
      ? newWellBeingFormValues.cw_date_of_involvement.getTime() !== dateOfInvolvement.getTime()
      : newWellBeingFormValues.cw_date_of_involvement !== dateOfInvolvement
  ) {
    newWellBeingFormValues = {
      ...newWellBeingFormValues,
      cw_date_of_involvement: dateOfInvolvement || undefined,
    };
    updateRequired = true;
  }

  // Field: Family Preservation Goal
  const familyPreservationGoal = fields
    .find((field) => field.name === 'cw_fp_goal')
    ?.options?.find((option) =>
      formData.childForms.some((childForm) => childForm.values.family_preservation_goal === option.value),
    )?.value;

  if (newWellBeingFormValues.cw_fp_goal !== familyPreservationGoal) {
    newWellBeingFormValues = {
      ...newWellBeingFormValues,
      cw_fp_goal: familyPreservationGoal || undefined,
    };
    updateRequired = true;
  }

  // Field: Family Preservation Impact
  const familyPreservationImpact = fields
    .find((field) => field.name === 'cw_fp_impact')
    ?.options?.find((option) =>
      formData.childForms.some((childForm) => childForm.values.family_preservation_impact === option.value),
    )?.value;

  if (newWellBeingFormValues.cw_fp_impact !== familyPreservationImpact) {
    newWellBeingFormValues = {
      ...newWellBeingFormValues,
      cw_fp_impact: familyPreservationImpact || undefined,
    };
    updateRequired = true;
  }

  // Field: Housing Category Summary
  let homeCategory = newWellBeingFormValues.home_category;
  switch (newWellBeingFormValues.home_type) {
    case '0_incarcerated':
    case '0_hotel':
    case '1_shelter':
      homeCategory = 'transitional';
      break;
    case '5_temporarily_neighbor':
    case '12_rental_subsidy':
      homeCategory = 'temporary';
      break;
    case '10_permanently_neighbor':
    case '15_rental':
    case '15_owns':
      homeCategory = 'permanent';
      break;
  }
  if (newWellBeingFormValues.home_category !== homeCategory) {
    newWellBeingFormValues = {
      ...newWellBeingFormValues,
      home_category: homeCategory,
    };
    updateRequired = true;
  }

  // Score: Child Welfare
  const cwScore =
    calculateScoreForSingleSelect(newWellBeingFormValues, 'cw_home_status') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'cw_active_involvement');

  if (newWellBeingFormValues.cw_score !== cwScore) {
    newWellBeingFormValues = {
      ...newWellBeingFormValues,
      cw_score: cwScore,
    };
    updateRequired = true;
  }

  // Section: Care of Children

  // Score: Care of Children
  const ccScore =
    calculateScoreForSingleSelect(newWellBeingFormValues, 'cc_reliable_care') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'cc_backup_care');

  if (newWellBeingFormValues.cc_score !== ccScore) {
    newWellBeingFormValues = {
      ...newWellBeingFormValues,
      cc_score: ccScore,
    };
    updateRequired = true;
  }

  // Section: Housing

  // Score: Housing
  const homeScore =
    calculateScoreForSingleSelect(newWellBeingFormValues, 'home_type') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'home_name_on_lease') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'home_security_concerns') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'home_recent_homeless') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'home_perc_toward');

  if (newWellBeingFormValues.home_score !== homeScore) {
    newWellBeingFormValues = {
      ...newWellBeingFormValues,
      home_score: homeScore,
    };
    updateRequired = true;
  }

  // Section: Transportation

  // Score: Transportation
  const trnprtScore =
    calculateScoreForSingleSelect(newWellBeingFormValues, 'trnprt_access') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'trnprt_affordable') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'trnprt_license');

  if (newWellBeingFormValues.trnprt_score !== trnprtScore) {
    newWellBeingFormValues = {
      ...newWellBeingFormValues,
      trnprt_score: trnprtScore,
    };
    updateRequired = true;
  }

  // Section: Resilience

  // Field: Functioning/Resilience PFS Summary
  const resFrPfsSummaryScoreAvg = calculateAvg(
    calculateScoreForSingleSelect(newWellBeingFormValues, 'res_positive_outlook'),
    calculateScoreForSingleSelect(newWellBeingFormValues, 'res_communication'),
    calculateScoreForSingleSelect(newWellBeingFormValues, 'res_traditions'),
  );

  const resFrPfsSummary =
    (resFrPfsSummaryScoreAvg === 0 && '0_no') ||
    (resFrPfsSummaryScoreAvg === 1 && '1_rarely') ||
    (resFrPfsSummaryScoreAvg === 2 && '2_sometimes') ||
    (resFrPfsSummaryScoreAvg === 3 && '3_often') ||
    '4_always';

  // Field: Flourishing Satisfaction Summary
  const resSatSummaryScoreSum =
    calculateScoreForSingleSelect(newWellBeingFormValues, 'res_overall_sat') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'res_happiness');

  const resSatSummary =
    (resSatSummaryScoreSum <= 6 && 'low') ||
    (resSatSummaryScoreSum >= 7 && resSatSummaryScoreSum <= 14 && 'medium') ||
    'high';

  // Field: Flourishing Purpose Summary
  const resPurposeSummaryScoreSum =
    calculateScoreForSingleSelect(newWellBeingFormValues, 'res_fulfillment') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'res_purpose');

  const resPurposeSummary =
    (resPurposeSummaryScoreSum <= 6 && 'low') ||
    (resPurposeSummaryScoreSum >= 7 && resPurposeSummaryScoreSum <= 14 && 'medium') ||
    'high';

  // Field: Flourishing Virtue Summary
  const resVirtueSummaryScoreSum =
    calculateScoreForSingleSelect(newWellBeingFormValues, 'res_goodness') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'res_sacrifice');

  const resVirtueSummary =
    (resVirtueSummaryScoreSum <= 6 && 'low') ||
    (resVirtueSummaryScoreSum >= 7 && resVirtueSummaryScoreSum <= 14 && 'medium') ||
    'high';

  // Score: Resilience
  const resScore = 4 * resFrPfsSummaryScoreAvg;

  if (
    newWellBeingFormValues.res_fr_pfs_summary !== resFrPfsSummary ||
    newWellBeingFormValues.res_sat_summary !== resSatSummary ||
    newWellBeingFormValues.res_purpose_summary !== resPurposeSummary ||
    newWellBeingFormValues.res_virtue_summary !== resVirtueSummary ||
    newWellBeingFormValues.res_score !== resScore
  ) {
    newWellBeingFormValues = {
      ...newWellBeingFormValues,
      res_fr_pfs_summary: resFrPfsSummary,
      res_sat_summary: resSatSummary,
      res_purpose_summary: resPurposeSummary,
      res_virtue_summary: resVirtueSummary,
      res_score: resScore,
    };
    updateRequired = true;
  }

  // Section: Wellness

  // Field: GAD7 Anxiety Summary
  const wellGadScoreSum =
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_gad_q1') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_gad_q2') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_gad_q3') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_gad_q4') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_gad_q5') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_gad_q6') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_gad_q7');

  const wellGadTotal =
    (wellGadScoreSum <= 4 && '5_minimal') ||
    (wellGadScoreSum >= 5 && wellGadScoreSum <= 9 && '3_mild') ||
    (wellGadScoreSum >= 10 && wellGadScoreSum <= 14 && '1_moderate') ||
    '0_severe';

  // Field: Depression Summary
  const wellPhqScoreSum =
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_phq_q1') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_phq_q2') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_phq_q3') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_phq_q4') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_phq_q5') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_phq_q6') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_phq_q7') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_phq_q8') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_phq_q9');

  const wellPhqTotal =
    (wellPhqScoreSum <= 9 && '5_minimal') ||
    (wellPhqScoreSum >= 10 && wellPhqScoreSum <= 14 && '3_mild') ||
    (wellPhqScoreSum >= 15 && wellPhqScoreSum <= 19 && '1_moderate') ||
    '0_severe';

  // Field: Occurrence
  const wellDiscouragementSummary =
    (newWellBeingFormValues['well_discouragement'] === 'yes' && '1_yes') ||
    (newWellBeingFormValues['well_discouragement'] === 'not_last_6_months' && '5_not_last_6_months') ||
    (newWellBeingFormValues['well_discouragement'] === 'not_last_year' && '10_not_last_12_years') ||
    (newWellBeingFormValues['well_discouragement'] === 'no' && '10_not_last_12_years') ||
    null;

  // Field: Counseling Support
  const wellCounselingSummary =
    (newWellBeingFormValues['well_counseling'] === 'past_again' && '0_need_support') ||
    (newWellBeingFormValues['well_counseling'] === 'past_not_now' && '4_historically') ||
    (newWellBeingFormValues['well_counseling'] === 'now' && '9_currently') ||
    (newWellBeingFormValues['well_counseling'] === 'no_need' && '10_not_required') ||
    null;

  // Field: Demonstrate Capacity/Treatment
  const wellStrategiesSummary =
    (newWellBeingFormValues['well_strategies'] === 'no' && '0_none') ||
    (newWellBeingFormValues['well_strategies'] === 'yes_not_using' && '5_not_current') ||
    (newWellBeingFormValues['well_strategies'] === 'yes_regularly' && '10_current') ||
    (newWellBeingFormValues['well_strategies'] === 'no_need' && '10_none_required') ||
    null;

  // Field: Flourishing Health Summary
  const wellHealthSummaryScoreSum =
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_phys_health') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_mental_health');

  const wellHealthSummary =
    (wellHealthSummaryScoreSum <= 6 && 'low') ||
    (wellHealthSummaryScoreSum >= 7 && wellHealthSummaryScoreSum <= 14 && 'medium') ||
    'high';

  // Score: Wellness
  const wellScore =
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_gad_total') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_phq_total') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_discouragement_summary') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_counseling_summary') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'well_strategies_summary');

  if (
    newWellBeingFormValues.well_gad_total !== wellGadTotal ||
    newWellBeingFormValues.well_phq_total !== wellPhqTotal ||
    newWellBeingFormValues.well_discouragement_summary !== wellDiscouragementSummary ||
    newWellBeingFormValues.well_counseling_summary !== wellCounselingSummary ||
    newWellBeingFormValues.well_strategies_summary !== wellStrategiesSummary ||
    newWellBeingFormValues.well_health_summary !== wellHealthSummary ||
    newWellBeingFormValues.well_score !== wellScore
  ) {
    newWellBeingFormValues = {
      ...newWellBeingFormValues,
      well_gad_total: wellGadTotal,
      well_phq_total: wellPhqTotal,
      well_discouragement_summary: wellDiscouragementSummary,
      well_counseling_summary: wellCounselingSummary,
      well_strategies_summary: wellStrategiesSummary,
      well_health_summary: wellHealthSummary,
      well_score: wellScore,
    };
    updateRequired = true;
  }

  // Section: Social support

  // Field: WAST-SF Domestic Violence Summary
  const socWastSfScoreSum =
    calculateScoreForSingleSelect(newWellBeingFormValues, 'soc_tension') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'soc_resolve_arguments');

  const socWastSf = socWastSfScoreSum <= 5 ? 'discussion_recommended' : 'low';

  // Field: Social Support PFS Summary
  const socPfsSummaryScoreAvg = calculateAvg(
    calculateScoreForSingleSelect(newWellBeingFormValues, 'soc_pfs_supportive_rels'),
    calculateScoreForSingleSelect(newWellBeingFormValues, 'soc_pfs_supportive_advice'),
    calculateScoreForSingleSelect(newWellBeingFormValues, 'soc_pfs_support_goals'),
    calculateScoreForSingleSelect(newWellBeingFormValues, 'soc_pfs_emergency_contact'),
    Math.min(4, calculateScoreForMultiSelect(newWellBeingFormValues, 'soc_trusted_network')),
  );

  const socPfsSummary =
    (socPfsSummaryScoreAvg === 0 && '0_no') ||
    (socPfsSummaryScoreAvg === 1 && '1_not_much') ||
    (socPfsSummaryScoreAvg === 2 && '2_some') ||
    (socPfsSummaryScoreAvg === 3 && '3_a_lot') ||
    '4_full';

  // Field: Flourishing Relationships Summary
  const socFrsSummaryScoreSum = calculateSum(
    calculateScoreForSingleSelect(newWellBeingFormValues, 'soc_frs_content_with_rels'),
    calculateScoreForSingleSelect(newWellBeingFormValues, 'soc_frs_rel_sat'),
  );

  const socFrsSummary =
    (socFrsSummaryScoreSum <= 6 && 'low') ||
    (socFrsSummaryScoreSum >= 7 && socFrsSummaryScoreSum <= 14 && 'medium') ||
    'high';

  // Score: Social support
  const socScore = socWastSfScoreSum + 4 * socPfsSummaryScoreAvg;

  if (
    newWellBeingFormValues.soc_wast_sf !== socWastSf ||
    newWellBeingFormValues.soc_pfs_summary_score !== socPfsSummary ||
    newWellBeingFormValues.soc_frs_summary !== socFrsSummary ||
    newWellBeingFormValues.soc_score !== socScore
  ) {
    newWellBeingFormValues = {
      ...newWellBeingFormValues,
      soc_wast_sf: socWastSf,
      soc_pfs_summary_score: socPfsSummary,
      soc_frs_summary: socFrsSummary,
      soc_score: socScore,
    };
    updateRequired = true;
  }

  // Section: Employment and Financial

  // Field: Concrete Supports PFS Summary
  const empConcretePfsTotalComponents = [
    Math.max(0, Math.min(4, 4 + calculateScoreForMultiSelect(newWellBeingFormValues, 'emp_fin_struggles'))),
    Math.max(0, Math.min(4, 4 + calculateScoreForMultiSelect(newWellBeingFormValues, 'emp_challenges'))),
    calculateScoreForSingleSelect(newWellBeingFormValues, 'emp_difficulty'),
    calculateScoreForSingleSelect(newWellBeingFormValues, 'emp_difficulty'),
  ];

  const empConcretePfsTotalScoreAvg = calculateAvg(...empConcretePfsTotalComponents);

  const empConcretePfsTotal =
    (empConcretePfsTotalScoreAvg === 0 && 'no') ||
    (empConcretePfsTotalScoreAvg === 1 && 'rarely') ||
    (empConcretePfsTotalScoreAvg === 2 && 'sometimes') ||
    (empConcretePfsTotalScoreAvg === 3 && 'often') ||
    'always';

  // Score: Employment and Financial
  const empScore =
    calculateSum(...empConcretePfsTotalComponents) +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'emp_diff_manage_bills') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'emp_emergency_funds') +
    calculateScoreForSingleSelect(newWellBeingFormValues, 'emp_status');

  if (
    newWellBeingFormValues.emp_concrete_pfs_total !== empConcretePfsTotal ||
    newWellBeingFormValues.emp_score !== empScore
  ) {
    newWellBeingFormValues = {
      ...newWellBeingFormValues,
      emp_concrete_pfs_total: empConcretePfsTotal,
      emp_score: empScore,
    };
    updateRequired = true;
  }

  // Section: Nurturing and Attachment

  // Field: Nurturing and Attachment PFS Summary
  const naaNurturePfsSummaryScoreAvg = calculateAvg(
    calculateScoreForSingleSelect(newWellBeingFormValues, 'naa_child_behavior'),
    calculateScoreForSingleSelect(newWellBeingFormValues, 'naa_discipline'),
    calculateScoreForSingleSelect(newWellBeingFormValues, 'naa_power_struggles'),
    calculateScoreForSingleSelect(newWellBeingFormValues, 'naa_emotions'),
  );

  const naaNurturePfsSummary =
    (naaNurturePfsSummaryScoreAvg === 0 && '0_no') ||
    (naaNurturePfsSummaryScoreAvg === 1 && '1_rarely') ||
    (naaNurturePfsSummaryScoreAvg === 2 && '2_sometimes') ||
    (naaNurturePfsSummaryScoreAvg === 3 && '3_often') ||
    '4_always';

  // Score: Nurturing and Attachment
  const naaScore = 4 * naaNurturePfsSummaryScoreAvg;

  if (
    newWellBeingFormValues.naa_nurture_pfs_summary !== naaNurturePfsSummary ||
    newWellBeingFormValues.naa_score !== naaScore
  ) {
    newWellBeingFormValues = {
      ...newWellBeingFormValues,
      naa_nurture_pfs_summary: naaNurturePfsSummary,
      naa_score: naaScore,
    };
    updateRequired = true;
  }

  return { newWellBeingFormValues, updateRequired };
};
