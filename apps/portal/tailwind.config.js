/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class'],
  content: ['./pages/**/*.{ts,tsx}', './components/**/*.{ts,tsx}', './app/**/*.{ts,tsx}', './src/**/*.{ts,tsx}'],
  prefix: '',
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },

        darkGreen: '#153839', // leaving as a HEX color because that is the way it is in the design
        offGrey: '#f2f4f7', // #f2f4f7 - Off Grey

        // EMA Brand Colors
        emaBrandPrimary: 'hsl(var(--brand-primary))', // #eb462d - Heavy Orange
        emaBrandSecondary: 'hsl(var(--brand-secondary))', // #143637 - Deep Teal
        emaBrandTertiary: 'hsl(var(--brand-tertiary))', // #f2a035 - Warm Amber

        emaBgPrimary: 'hsl(var(--bg-primary))', // #ffffff - White
        emaBgQuaternary: 'hsl(var(--bg-quaternary))', // #e4e7eb - Light Gray
        emaBgPage: 'var(--bg-page)', // #fefbf9 - Off White
        emaBgBeige: 'hsl(var(--bg-beige))', // #f9f5f0 - Beige

        emaTextPrimary: 'hsl(var(--text-primary))', // #0f1828 - Charcoal
        emaTextSecondary: 'hsl(var(--text-secondary))', // #344055 - Dark Grayish Blue
        emaTextTertiary: 'hsl(var(--text-tertiary))', // #475366 - Slate Gray
        emaTextQuaternary: 'hsl(var(--text-quaternary))', // #666f84 - Medium Gray
        emaTextPlaceholder: 'hsl(var(--text-quaternary))', // #666f84 - Medium Gray (for placeholder)

        emaBorderPrimary: 'hsl(var(--border-primary))', // #cfd4dc - Soft Gray
        emaBorderSecondary: 'hsl(var(--border-secondary))', // #e4e7eb - Light Gray (matches emaBgQuaternary)

        emaBrandPrimaryHover: 'hsl(var(--brand-primary-hover))', // #d4422b - Darkened Heavy Orange
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
};
