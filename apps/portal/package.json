{"name": "portal", "version": "1.0.0", "description": "", "keywords": [], "license": "ISC", "author": "", "main": "index.js", "scripts": {"build": "next build", "check-types": "tsc --noEmit", "dev": "next dev", "lint": "next lint", "start": "next start", "clean": "rm -rf .next && rm -rf .turbo", "nuke": "pnpm run clean && rm -rf node_modules"}, "dependencies": {"@aws-sdk/client-ses": "^3.693.0", "@aws-sdk/client-sns": "^3.693.0", "@faker-js/faker": "^9.6.0", "@hookform/resolvers": "^3.9.0", "@marsidev/react-turnstile": "^1.0.2", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@sodaru/yup-to-json-schema": "^2.0.1", "@suiteapi/api": "workspace:api@*", "@suiteapi/api-client": "workspace:api-client@*", "@suiteapi/models": "workspace:models@*", "@tanstack/react-query": "^5.64.2", "@zenstackhq/runtime": "2.11.4", "@zenstackhq/tanstack-query": "^2.11.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "embla-carousel-react": "^8.3.0", "file-saver": "^2.0.5", "handlebars": "^4.7.8", "html2canvas": "^1.4.1", "ics": "^3.8.1", "jose": "^6.0.11", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.426.0", "next": "^14.2.26", "next-swagger-doc": "^0.4.0", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-hook-form": "^7.52.2", "react-markdown": "^10.1.0", "react-multi-select-component": "^4.3.4", "react-phone-number-input": "^3.4.8", "recharts": "^2.15.3", "schema-to-yup": "^1.12.18", "server-only": "^0.0.1", "sharp": "^0.34.1", "swagger-ui-react": "^5.17.14", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.1", "yup": "^1.4.0", "zod": "^3.23.8", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.14.0", "@next/eslint-plugin-next": "^14.2.26", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/eslint__js": "^8.42.3", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.7", "@types/lodash": "^4.17.7", "@types/node": "^22.1.0", "@types/react": "^18.3.3", "@types/react-dom": "^19.1.3", "@types/swagger-ui-react": "^4.18.3", "@typescript-eslint/parser": "^8.2.0", "autoprefixer": "^10.4.20", "csv-parse": "^5.5.6", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-next": "^14.2.26", "eslint-config-prettier": "^9.1.0", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react-hooks": "^5.2.0", "glob": "^11.0.0", "postcss": "^8.4.41", "prettier": "^3.3.3", "prettier-eslint": "^16.3.0", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.7", "typescript": "^5.5.4", "typescript-eslint": "^8.14.0"}, "peerDependencies": {"eslint-config-airbnb": "^19.0.4"}}